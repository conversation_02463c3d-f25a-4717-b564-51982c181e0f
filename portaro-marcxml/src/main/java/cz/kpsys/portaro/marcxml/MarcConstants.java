package cz.kpsys.portaro.marcxml;

import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;

public class MarcConstants {
    public static final SimpleDateFormat MARCXML_DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");
    public static final DateTimeFormatter MARCXML_LOCAL_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter MARCXML_INSTANT_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
}
