package cz.kpsys.portaro.marcxml.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.util.List;

public record StrictVerbisDatafieldMarcDto(

        @JacksonXmlProperty(localName = "tag", isAttribute = true)
        @NonNull
        @NotEmpty
        String tag,

        @JacksonXmlProperty(localName = "ind1", isAttribute = true)
        @NonNull
        @NotBlank // we communicate with appserver with standard, "#" format indicators, not with " "
        String ind1,

        @JacksonXmlProperty(localName = "ind2", isAttribute = true)
        @NonNull
        @NotBlank // we communicate with appserver with standard, "#" format indicators, not with " "
        String ind2,

        @JacksonXmlProperty(localName = "subfield", namespace = "http://www.loc.gov/MARC21/slim")
        @JacksonXmlElementWrapper(useWrapping = false)
        @NonNull
        List<StrictVerbisSubfieldMarcDto> subfields

) implements DatafieldMarcDto {}
