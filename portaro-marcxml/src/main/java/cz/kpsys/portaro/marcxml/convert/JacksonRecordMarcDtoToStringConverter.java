package cz.kpsys.portaro.marcxml.convert;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.commons.convert.ConversionException;
import cz.kpsys.portaro.marcxml.model.StrictRecordMarcDto;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class JacksonRecordMarcDtoToStringConverter implements Converter<StrictRecordMarcDto, String> {

    @NonNull ObjectMapper objectMapper = JacksonRecordMarcUtil.createXmlMapper();

    @Override
    public String convert(@NonNull StrictRecordMarcDto record) {
        try {
            return objectMapper.writeValueAsString(record);
        } catch (JsonProcessingException e) {
            throw new ConversionException("Cannot serialize record %s to string (probably marc xml)".formatted(record), e);
        }
    }
}
