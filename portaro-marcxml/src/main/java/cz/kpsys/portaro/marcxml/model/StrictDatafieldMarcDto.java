package cz.kpsys.portaro.marcxml.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.util.List;

public record StrictDatafieldMarcDto(

        @JacksonXmlProperty(localName = "tag", isAttribute = true)
        @NonNull
        @NotEmpty
        String tag,

        @JacksonXmlProperty(localName = "ind1", isAttribute = true)
        @NonNull
        @NotEmpty
        String ind1,

        @JacksonXmlProperty(localName = "ind2", isAttribute = true)
        @NonNull
        @NotEmpty
        String ind2,

        @JacksonXmlProperty(localName = "subfield", namespace = "http://www.loc.gov/MARC21/slim")
        @JacksonXmlElementWrapper(useWrapping = false)
        @NonNull
        List<StrictSubfieldMarcDto> subfields

) implements DatafieldMarcDto {}
