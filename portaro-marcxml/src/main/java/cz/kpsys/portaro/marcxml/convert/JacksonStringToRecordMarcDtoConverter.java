package cz.kpsys.portaro.marcxml.convert;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.JacksonXmlModule;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import cz.kpsys.portaro.commons.convert.ConversionException;
import cz.kpsys.portaro.marcxml.model.LenientRecordMarcDto;
import cz.kpsys.portaro.marcxml.model.LenientVerbisRecordMarcDto;
import cz.kpsys.portaro.marcxml.model.RecordMarcDto;
import cz.kpsys.portaro.marcxml.model.StrictVerbisRecordMarcDto;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class JacksonStringToRecordMarcDtoConverter<E extends RecordMarcDto> implements Converter<String, E> {

    @NonNull ObjectMapper objectMapper;
    @NonNull Class<E> valueType;

    private JacksonStringToRecordMarcDtoConverter(@NonNull Class<E> valueType) {
        this(createDefaultXmlMapper(), valueType);
    }

    public static JacksonStringToRecordMarcDtoConverter<StrictVerbisRecordMarcDto> ofStrict() {
        return new JacksonStringToRecordMarcDtoConverter<>(StrictVerbisRecordMarcDto.class);
    }

    public static JacksonStringToRecordMarcDtoConverter<LenientRecordMarcDto> ofLenient() {
        return new JacksonStringToRecordMarcDtoConverter<>(LenientRecordMarcDto.class);
    }

    public static JacksonStringToRecordMarcDtoConverter<LenientVerbisRecordMarcDto> ofLenientVerbis() {
        return new JacksonStringToRecordMarcDtoConverter<>(LenientVerbisRecordMarcDto.class);
    }

    private static ObjectMapper createDefaultXmlMapper() {
        JacksonXmlModule jacksonXmlModule = new JacksonXmlModule();
        // Setting default value for XML Text Element
        // Class field with @JacksonXmlText annotation must have same name as value set with this method.
        jacksonXmlModule.setXMLTextElementName("value");
        // !!! Module must be registered from constructor, or it won't register.
        return new XmlMapper(jacksonXmlModule);
    }

    @Override
    public E convert(@NonNull String xml) {
        try {
            return objectMapper.readValue(xml, valueType);
        } catch (JsonProcessingException e) {
            throw new ConversionException("Cannot deserialize to %s from string %s".formatted(valueType.getSimpleName(), xml), e);
        }
    }
}
