package cz.kpsys.portaro.marcxml.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlText;
import lombok.NonNull;

@JsonIgnoreProperties(value = "id")
public record StrictSubfieldMarcDto(

        @JacksonXmlProperty(localName = "code", isAttribute = true)
        @NonNull
        String code,

        @JacksonXmlText
        @NonNull
        String value

) implements SubfieldMarcDto {

    public static StrictSubfieldMarcDto ofWithoutRecord(@NonNull String code, @NonNull String value) {
        return new StrictSubfieldMarcDto(code, value);
    }
}
