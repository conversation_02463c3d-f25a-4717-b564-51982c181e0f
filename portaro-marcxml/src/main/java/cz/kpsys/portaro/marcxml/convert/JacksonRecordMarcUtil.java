package cz.kpsys.portaro.marcxml.convert;

import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

public class JacksonRecordMarcUtil {

    public static XmlMapper createXmlMapper() {
        XmlMapper xmlMapper = XmlMapper.builder()
//                .enable(ToXmlGenerator.Feature.WRITE_XML_DECLARATION)
                .enable(SerializationFeature.INDENT_OUTPUT)
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .build();
        xmlMapper.registerModule(new JavaTimeModule());
//        xmlMapper.getFactory()
//                .getXMLOutputFactory()
//                .setProperty(com.ctc.wstx.api.WstxOutputProperties.P_USE_DOUBLE_QUOTES_IN_XML_DECL, true);
        return xmlMapper;
    }

}
