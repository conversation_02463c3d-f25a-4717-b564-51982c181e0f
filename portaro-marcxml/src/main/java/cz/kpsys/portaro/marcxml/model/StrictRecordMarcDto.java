package cz.kpsys.portaro.marcxml.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.NonNull;
import lombok.With;

import java.util.List;
import java.util.function.Predicate;

@JacksonXmlRootElement(localName = "record", namespace = "http://www.loc.gov/MARC21/slim")
@With
public record StrictRecordMarcDto(

        @JacksonXmlProperty(localName = "leader", namespace = "http://www.loc.gov/MARC21/slim")
        @NonNull
        String leader,

        @JacksonXmlProperty(localName = "controlfield", namespace = "http://www.loc.gov/MARC21/slim")
        @JacksonXmlElementWrapper(useWrapping = false)
        @NonNull
        List<StrictControlfieldMarcDto> controlfields,

        @JacksonXmlProperty(localName = "datafield", namespace = "http://www.loc.gov/MARC21/slim")
        @JacksonXmlElementWrapper(useWrapping = false)
        @NonNull
        List<StrictDatafieldMarcDto> datafields

) implements RecordMarcDto {

    public StrictRecordMarcDto withAddedDatafields(List<StrictDatafieldMarcDto> addedDatafields) {
        return withDatafields(ListUtil.union(datafields(), addedDatafields));
    }

    public StrictRecordMarcDto withFilteredDatafields(Predicate<StrictDatafieldMarcDto> datafieldFilter) {
        return withDatafields(ListUtil.filter(datafields(), datafieldFilter));
    }
}
