package cz.kpsys.portaro.marcxml.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.NonNull;
import lombok.With;
import org.springframework.lang.Nullable;

import java.util.List;

@JacksonXmlRootElement(localName = "record", namespace = "http://www.loc.gov/MARC21/slim")
@JsonIgnoreProperties(ignoreUnknown = true)
@With
public record LenientRecordMarcDto(

        @JacksonXmlProperty(localName = "leader", namespace = "http://www.loc.gov/MARC21/slim")
        @Nullable
        String leader,

        @JacksonXmlProperty(localName = "controlfield", namespace = "http://www.loc.gov/MARC21/slim")
        @JacksonXmlElementWrapper(useWrapping = false)
        @NonNull
        List<LenientControlfieldMarcDto> controlfields,

        @JacksonXmlProperty(localName = "datafield", namespace = "http://www.loc.gov/MARC21/slim")
        @JacksonXmlElementWrapper(useWrapping = false)
        @NonNull
        List<LenientVerbisDatafieldMarcDto> datafields

) implements RecordMarcDto {}
