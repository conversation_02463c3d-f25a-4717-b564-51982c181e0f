package cz.kpsys.portaro.marcxml.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.Objects;
import java.util.UUID;

public interface VerbisSubfieldMarcDto extends SubfieldMarcDto {

    @Nullable
    UUID recordId();

    @Nullable
    Integer fondId();

    @Nullable
    UUID originId();

    @Nullable
    Integer originFondId();

    @JsonIgnore
    default @NonNull Integer existingFondId() {
        return Objects.requireNonNull(fondId(), () -> "fond_id is null (recordId = %s)".formatted(recordId()));
    }

    @JsonIgnore
    default @NonNull Integer existingOriginFondId() {
        return Objects.requireNonNull(originFondId(), () -> "origin_fond_id is null (recordId = %s)".formatted(recordId()));
    }

}
