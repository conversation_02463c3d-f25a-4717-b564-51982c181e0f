package cz.kpsys.portaro.marcxml.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.NonNull;
import lombok.With;

import java.util.List;

@JacksonXmlRootElement(localName = "record", namespace = "http://www.loc.gov/MARC21/slim")
@With
public record StrictVerbisRecordMarcDto(

        @JacksonXmlProperty(localName = "leader", namespace = "http://www.loc.gov/MARC21/slim")
        @NonNull
        String leader,

        @JacksonXmlProperty(localName = "controlfield", namespace = "http://www.loc.gov/MARC21/slim")
        @JacksonXmlElementWrapper(useWrapping = false)
        @NonNull
        List<StrictControlfieldMarcDto> controlfields,

        @JacksonXmlProperty(localName = "datafield", namespace = "http://www.loc.gov/MARC21/slim")
        @JacksonXmlElementWrapper(useWrapping = false)
        @NonNull
        List<StrictVerbisDatafieldMarcDto> datafields

) implements RecordMarcDto {}
