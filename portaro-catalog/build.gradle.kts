dependencies {
    testImplementation(project(":portaro-test-environment"))
    testImplementation(project(":portaro-core-template-velocity"))
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.junit.jupiter:junit-jupiter-params:+")
    testImplementation("org.hamcrest:hamcrest-library:+")
    testImplementation("org.mockito:mockito-core:+")
    testImplementation("org.apache.velocity:velocity-engine-core:2.+")
    testImplementation("org.springframework:spring-test:6.+")
    testImplementation(testFixtures(project(":portaro-record")))

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")
    implementation("org.mapstruct:mapstruct:+")
    annotationProcessor("org.mapstruct:mapstruct-processor:+")

    implementation(project(":portaro-appserver"))
    implementation(project(":portaro-auth"))
    implementation(project(":portaro-auth-anonymous"))
    implementation(project(":portaro-comment"))
    implementation(project(":portaro-commons"))
    implementation(project(":portaro-commons-db"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-database-structure"))
    implementation(project(":portaro-exemplar"))
    implementation(project(":portaro-export"))
    implementation(project(":portaro-erp"))
    implementation(project(":portaro-file"))
    implementation(project(":portaro-file-text"))
    implementation(project(":portaro-form"))
    implementation(project(":portaro-form-annotation"))
    implementation(project(":portaro-form-config"))
    implementation(project(":portaro-finance"))
    implementation(project(":portaro-inventory"))
    implementation(project(":portaro-licence"))
    implementation(project(":portaro-loan"))
    implementation(project(":portaro-loan-ill"))
    implementation(project(":portaro-loan-mail"))
    implementation(project(":portaro-message"))
    implementation(project(":portaro-payment"))
    implementation(project(":portaro-record"))
    implementation(project(":portaro-record-citation"))
    implementation(project(":portaro-record-collection"))
    implementation(project(":portaro-record-export"))
    implementation(project(":portaro-sdi"))
    implementation(project(":portaro-security"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-search-factory"))
    implementation(project(":portaro-search-ui"))
    implementation(project(":portaro-sql-generator"))
    implementation(project(":portaro-user"))
    implementation(project(":portaro-user-impl"))
    implementation(project(":portaro-user-preferences"))
    implementation(project(":portaro-web"))
    implementation(project(":portaro-verbisbox"))

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation("org.springframework:spring-context:6.+")
    implementation("org.springframework:spring-jdbc:6.+")
    implementation("org.springframework:spring-web:6.+")
    implementation("org.springframework.security:spring-security-web:6.+")
    implementation("org.springframework.boot:spring-boot-actuator:3.+")
    implementation("org.springframework.data:spring-data-commons:3.+")

    implementation("org.slf4j:slf4j-api:+")
    implementation("org.apache.commons:commons-lang3:3.+")
    implementation("com.google.guava:guava:+")
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.+")
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.+")
    implementation("org.jdom:jdom2:2.+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
}
