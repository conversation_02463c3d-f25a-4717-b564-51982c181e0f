package cz.kpsys.portaro.record.fond;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.record.RecordWellKnownFields;
import cz.kpsys.portaro.record.detail.By;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.edit.RecordValidator;
import cz.kpsys.portaro.suitability.CompositeSuitability;
import cz.kpsys.portaro.suitability.StaticWeightedValue;
import cz.kpsys.portaro.suitability.Suitability;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class BestSuitableFondResolver {

    @NonNull AllValuesProvider<Fond> enabledFondsProvider;
    @NonNull Provider<@NonNull Fond> defaultNonPeriodicalDocumentFondProvider;
    @NonNull Provider<@NonNull Fond> defaultPeriodicalDocumentFondProvider;
    @NonNull Provider<@NonNull Fond> defaultAuthorityFondProvider;
    @NonNull RecordValidator recordValidator;

    public List<Suitability<Fond>> resolveBestSuitableFondsByRecordDetail(FieldContainer detail) {
        Collection<Suitability<Fond>> suitabilities = getSuitabilities(detail);

        return suitabilities.stream()
                .sorted(Suitability.COMPARATOR.reversed())
                .toList();
    }

    @NonNull
    private Collection<Suitability<Fond>> getSuitabilities(FieldContainer detail) {

        boolean hasDocumentName = detail.getFirstField(By.typeId(RecordWellKnownFields.DocumentTitle.TYPE_ID), By.typeId(RecordWellKnownFields.DocumentTitle.Name.TYPE_ID)).isPresent();
        boolean hasIsbn = detail.getFirstField(By.typeId(RecordWellKnownFields.DocumentIsbn.TYPE_ID), By.typeId(RecordWellKnownFields.DocumentIsbn.Value.TYPE_ID)).isPresent();
        boolean hasIssn = detail.getFirstField(By.typeId(RecordWellKnownFields.DocumentIssn.TYPE_ID), By.typeId(RecordWellKnownFields.DocumentIssn.Value.TYPE_ID)).isPresent();

        return enabledFondsProvider.getAll().stream()
                .map(fond -> {
                    double kindSuitability = getKindSuitability(fond, hasDocumentName, hasIsbn, hasIssn);
                    double defaultFondSuitability = getDefaultFondSuitability(fond);
                    double fondRecordableSuitability = getFondRecordableSuitability(fond);
                    double fondOrderSuitability = getFondOrderSuitability(fond);
                    double perioTypeSuitability = getPerioTypeSuitability(fond, hasIsbn, hasIssn);
                    double entryElementSuitability = getEntryElementSuitability(fond, detail);
                    return CompositeSuitability.of(fond,
                            new StaticWeightedValue(fondRecordableSuitability, 0.3, "fond recordable"),
                            new StaticWeightedValue(entryElementSuitability, 0.2, "entry element presence"),
                            new StaticWeightedValue(kindSuitability, 0.20, "kind by specific fields presence"),
                            new StaticWeightedValue(perioTypeSuitability, 0.20, "perio/non-perio"),
                            new StaticWeightedValue(defaultFondSuitability, 0.05, "default fond"),
                            new StaticWeightedValue(fondOrderSuitability, 0.05, "fond order")
                    );
                })
                .collect(Collectors.toUnmodifiableSet());
    }

    private double getKindSuitability(Fond fond, boolean hasDocumentName, boolean hasIsbn, boolean hasIssn) {
        double kindSuitability = 0.5;
        if (fond.isOfDocument()) {
            if (hasDocumentName) {
                kindSuitability += 0.25;
            }
            if (hasIsbn || hasIssn) {
                kindSuitability += 0.25;
            }
        }
        if (fond.isOfAuthority()) {
            if (hasDocumentName) {
                kindSuitability = 0;
            }
            if (hasIsbn || hasIssn) {
                kindSuitability = 0;
            }
        }
        return kindSuitability;
    }

    private double getDefaultFondSuitability(Fond fond) {
        if (fond.isOfAuthority()) {
            if (fond.equals(defaultAuthorityFondProvider.get())) {
                return Suitability.MAX_VALUE;
            }
        }

        if (fond.isOfDocument()) {
            if (fond.isPeriodical()) {
                if (fond.equals(defaultPeriodicalDocumentFondProvider.get())) {
                    return Suitability.MAX_VALUE;
                }
            } else {
                if (fond.equals(defaultNonPeriodicalDocumentFondProvider.get())) {
                    return Suitability.MAX_VALUE;
                }
            }
        }

        return Suitability.ZERO_VALUE;
    }

    private double getFondRecordableSuitability(Fond fond) {
        if (fond.isRecordable()) {
            return Suitability.MAX_VALUE;
        }
        return Suitability.ZERO_VALUE;
    }

    private double getFondOrderSuitability(Fond fond) {
        List<Fond> orderedFonds = enabledFondsProvider.getAll().stream()
                .sorted(Fond.COMPARATOR)
                .toList();
        int indexOfFond = orderedFonds.indexOf(fond);
        int fondCount = orderedFonds.size();
        return 1 - (indexOfFond / (double) (fondCount - 1));
    }

    private double getPerioTypeSuitability(Fond fond, boolean hasIsbn, boolean hasIssn) {
        double suitability = 0.5;

        if (fond.isOfDocument()) {
            if (fond.isPeriodical()) {
                if (hasIsbn) {
                    suitability -= 0.25;
                }
                if (hasIssn) {
                    suitability += 0.25;
                }
            } else {
                if (hasIsbn) {
                    suitability += 0.25;
                }
                if (hasIssn) {
                    suitability -= 0.25;
                }
            }

        }
        return suitability;
    }

    private double getEntryElementSuitability(Fond fond, FieldContainer detail) {
        if (!fond.isRecordable()) {
            return Suitability.ZERO_VALUE;
        }
        if (!fond.hasEntryFieldType()) {
            return Suitability.ZERO_VALUE;
        }
        return recordValidator.validate(fond, detail).isOk() ? Suitability.MAX_VALUE : Suitability.ZERO_VALUE;
    }

}
