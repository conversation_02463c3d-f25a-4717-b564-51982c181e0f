package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.Field;
import lombok.NonNull;

import java.time.LocalDate;
import java.util.Optional;
import java.util.Set;

public record LocalDateFieldValue(

    @NonNull
    LocalDate value,

    @NonNull
    String label,

    @NonNull
    Text text,

    @NonNull
    Set<RecordIdFondPair> origins

) implements ScalarFieldValue<LocalDate> {

    public static @NonNull LocalDateFieldValue of(@NonNull LocalDate date, @NonNull Set<RecordIdFondPair> origins) {
        return new LocalDateFieldValue(
                date,
                LocalDateTypedValueConverter.formatLabel(date),
                Texts.ofNative(LocalDateTypedValueConverter.formatText(date)),
                origins
        );
    }

    public static Optional<LocalDate> extract(@NonNull Field<LocalDateFieldValue> field) {
        return field.typedValue(LocalDateFieldValue.class).map(LocalDateFieldValue::value);
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        return o instanceof LocalDateFieldValue that && value.equals(that.value);
    }

    @Override
    public int hashCode() {
        return value().hashCode();
    }

}
