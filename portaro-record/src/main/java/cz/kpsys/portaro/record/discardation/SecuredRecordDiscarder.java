package cz.kpsys.portaro.record.discardation;

import cz.kpsys.portaro.record.RecordSecurityActions;
import cz.kpsys.portaro.security.SecurityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SecuredRecordDiscarder implements RecordDiscarder {

    @NonNull RecordDiscarder delegate;
    @NonNull SecurityManager securityManager;

    @Override
    public void discard(RecordDiscardationCommand command) {
        securityManager.throwIfCannot(RecordSecurityActions.RECORD_HOLDING_DISCARD_OF_RECORD, command.currentAuth(), command.ctx(), command.record());
        delegate.discard(command);
    }
}
