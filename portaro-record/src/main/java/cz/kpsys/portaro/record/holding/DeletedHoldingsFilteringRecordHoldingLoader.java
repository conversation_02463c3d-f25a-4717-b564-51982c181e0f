package cz.kpsys.portaro.record.holding;

import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Predicate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DeletedHoldingsFilteringRecordHoldingLoader implements RecordHoldingLoader {

    @NonNull RecordHoldingLoader delegate;
    Predicate<RecordHolding> recordHoldingIsNotDeletedPredicate = recordHolding -> !recordHolding.isDeleted();

    @Override
    public RecordHolding getById(@NonNull UUID id) throws ItemNotFoundException {
        return Optional.of(delegate.getById(id)).filter(recordHoldingIsNotDeletedPredicate).orElseThrow(() -> new ItemNotFoundException(RecordHolding.class, id));
    }

    @Override
    public List<RecordHolding> getAllByRecordId(@NonNull UUID recordId) {
        return ListUtil.filter(delegate.getAllByRecordId(recordId), recordHoldingIsNotDeletedPredicate);
    }

    @Override
    public List<RecordHolding> getAllByRecordIdAndRootDepartment(@NonNull UUID recordId, @NonNull Department rootDepartment) {
        return ListUtil.filter(delegate.getAllByRecordIdAndRootDepartment(recordId, rootDepartment), recordHoldingIsNotDeletedPredicate);
    }

    @Override
    public Optional<RecordHolding> getByRecordIdAndDepartment(@NonNull UUID recordId, @NonNull Department department) {
        return delegate.getByRecordIdAndDepartment(recordId, department).filter(recordHoldingIsNotDeletedPredicate);
    }

    @Override
    public List<RecordHolding> getAllByRecordIdsAndDepartments(@NonNull Collection<UUID> recordIds, @NonNull Collection<Department> departments) {
        return ListUtil.filter(delegate.getAllByRecordIdsAndDepartments(recordIds, departments), recordHoldingIsNotDeletedPredicate);
    }

    @Override
    public List<RecordHolding> getAllByDepartments(@NonNull Collection<Department> departments) {
        return ListUtil.filter(delegate.getAllByDepartments(departments), recordHoldingIsNotDeletedPredicate);
    }
}
