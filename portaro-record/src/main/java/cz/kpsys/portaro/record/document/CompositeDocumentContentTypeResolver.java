package cz.kpsys.portaro.record.document;

import cz.kpsys.portaro.record.Record;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

public class CompositeDocumentContentTypeResolver implements DocumentContentTypeResolver {

    private static final ContentType DEFAULT = ContentType.BOOK;

    @NonNull List<Function<Record, Optional<ContentType>>> resolvers = List.of(
            new ByField007DocumentContentTypeResolver(),
            new ByFondNameDocumentContentTypeResolver(),
            new ByFondIdDocumentContentTypeResolver()
    );

    @Override
    public ContentType resolve(Record r) {
        for (Function<Record, Optional<ContentType>> resolver : resolvers) {
            Optional<ContentType> result = resolver.apply(r);
            if (result.isPresent()) {
                return result.get();
            }
        }
        return DEFAULT;
    }

}
