package cz.kpsys.portaro.record.merge;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.core.NestedRuntimeException;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RecordMergeException extends NestedRuntimeException implements UserFriendlyException {

    @NonNull Record target;
    @NonNull Record source;

    public RecordMergeException(@NonNull Record target, @NonNull Record source, @NonNull Throwable cause) {
        super("Problem while merging detail of record %s to %s".formatted(source, target), cause);
        this.target = target;
        this.source = source;
    }

    public Text getText() {
        return Texts.ofMessageCodedWithLocalizedArgs("record.merge.ErrorWhileMergingDetailOfXToY", source.getText(), target.getText());
    }
}
