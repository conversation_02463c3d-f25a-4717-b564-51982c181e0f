package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.record.RecordIdentifier;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.spec.RecordSpec;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.Set;

public record AbsentLinkingFieldsFail(

        @NonNull
        Text text,

        @NonNull
        FieldTypeId missingLinkingRecordsLinkFieldTypeId,

        @NonNull
        RecordIdentifier missingLinkingRecordsLink,

        @NonNull
        FieldTypeId missingLinkingRecordsValueFieldTypeId,

        @Nullable
        Set<Integer> linkingRecordsFondIds

) implements AbsentDataFail {

    public static @NonNull AbsentLinkingFieldsFail of(FieldTypeId linkingRecordsLinkFieldTypeId, RecordIdentifier linkingRecordsLink, FieldTypeId missingLinkingRecordsValueFieldTypeId, @Nullable Set<Integer> linkingRecordsFondIds) {
        return new AbsentLinkingFieldsFail(
                Texts.ofNative("Missing fields linking to this record in " + linkingRecordsLinkFieldTypeId),
                linkingRecordsLinkFieldTypeId,
                linkingRecordsLink,
                missingLinkingRecordsValueFieldTypeId,
                linkingRecordsFondIds
        );
    }

    @Override
    public @NonNull RecordSpecSet toSpec() {
        return RecordSpecSet.of(RecordSpec.ofLinkingRecordsFields(missingLinkingRecordsLinkFieldTypeId, missingLinkingRecordsLink, missingLinkingRecordsValueFieldTypeId, linkingRecordsFondIds));
    }

    @Override
    public String toString() {
        return "AbsentLinkingFields[{" + missingLinkingRecordsLinkFieldTypeId + ":link = " + missingLinkingRecordsLink.abbr() + "}:" + missingLinkingRecordsValueFieldTypeId + "]";
    }

}
