package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.RecordDb.RECORD.*;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@With
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class RecordEntity implements Identified<UUID> {

    @Id
    @Column(name = ID)
    @EqualsAndHashCode.Include
    @NonNull
    UUID id;

    @Column(name = MASTER_RECORD_ID)
    @Nullable
    UUID masterRecordId;

    @Column(name = FOND_ID)
    @NonNull
    Integer fondId;

    @Column(name = CREATION_EVENT_ID)
    @NonNull
    UUID creationEventId;

    @Column(name = ACTIVATION_EVENT_ID)
    @Nullable
    UUID activationEventId;

    @Column(name = DELETION_EVENT_ID)
    @Nullable
    UUID deletionEventId;

    @Column(name = DIRECTORY_ID)
    @Nullable
    Integer directoryId;

    @Column(name = PRIMARY_FILE_ID)
    @Nullable
    Long primaryFileId;

    @Column(name = NAME)
    @NullableNotBlank
    String name;
}
