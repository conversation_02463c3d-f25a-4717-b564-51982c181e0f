package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.value.FieldValue;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public interface BinaryFunction<RES extends FieldValue<?>> extends ResolvableFunction<RES> {

    @NonNull
    Formula<?> first();

    @NonNull
    Formula<?> second();

    @NonNull
    @NotEmpty
    default Set<LookupDefinition> dependencies() {
        return Stream.of(first().dependencies(), second().dependencies())
                .flatMap(Set::stream)
                .collect(Collectors.toUnmodifiableSet());
    }

    @Override
    @NonNull
    default Stream<? extends Formula<?>> subformulas() {
        return Stream.of(first(), second())
                .flatMap(f -> Stream.concat(Stream.of(f), f.subformulas()));
    }
}
