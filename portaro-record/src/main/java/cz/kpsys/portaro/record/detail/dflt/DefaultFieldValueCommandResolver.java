package cz.kpsys.portaro.record.detail.dflt;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.value.FieldValueCommand;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.NonNull;

import java.util.Optional;

public interface DefaultFieldValueCommandResolver {

    boolean hasDefault(@NonNull FieldTypeId fieldTypeId, @NonNull Fond fond, @NonNull Department ctx);

    Optional<FieldValueCommand> findOn(@NonNull FieldTypeId fieldTypeId, @NonNull Fond fond, @NonNull Department ctx, @NonNull UserAuthentication currentAuth);

    static DefaultFieldValueCommandResolver noneReturning() {
        return new DefaultFieldValueCommandResolver() {
            @Override
            public boolean hasDefault(@NonNull FieldTypeId fieldTypeId, @NonNull Fond fond, @NonNull Department ctx) {
                return false;
            }

            @Override
            public Optional<FieldValueCommand> findOn(@NonNull FieldTypeId fieldTypeId, @NonNull Fond fond, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
                return Optional.empty();
            }
        };
    }
}
