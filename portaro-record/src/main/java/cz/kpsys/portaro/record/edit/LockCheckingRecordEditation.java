package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Delegate;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class LockCheckingRecordEditation implements RecordEditation {

    @Delegate
    @NonNull
    RecordEditation target;

    @NonNull
    ContextualFunction<Record, Department, @NonNull Boolean> recordLockedContextualPredicate;


    @Override
    public RecordEditation publish(@NonNull Department ctx, @NonNull UserAuthentication currentAuth) throws IllegalStateException {
        assertRecordIsEditable(ctx, this.getRecord());
        target.publish(ctx, currentAuth);
        return this;
    }

    @Override
    public RecordEditation saveIfModified(@NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        assertRecordIsEditable(ctx, this.getRecord());
        target.saveIfModified(ctx, currentAuth);
        return this;
    }

    private void assertRecordIsEditable(@NonNull Department ctx, Record record) {
        if (recordLockedContextualPredicate.getOn(record, ctx)) {
            throw new RecordIsLockedException("Record is locked for editation", record);
        }
    }
}
