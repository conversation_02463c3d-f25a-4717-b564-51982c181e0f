package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static cz.kpsys.portaro.databasestructure.RecordDb.DEF_INDIK_ROOT.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbIndicatorTypeLoader implements AllValuesProvider<IndicatorType>, ResultSetExtractor<List<IndicatorType>> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    boolean forAuthority;
    @NonNull String table;

    @Override
    public List<IndicatorType> getAll() {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(table);
        sq.orderBy().addAsc(CIS_POL);
        return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
    }

    @Override
    public List<IndicatorType> extractData(ResultSet rs) throws SQLException, DataAccessException {
        Map<FieldTypeId, List<Indicator>> res = new LinkedHashMap<>();
        while (rs.next()) {
            String fieldNumber = String.valueOf(rs.getInt(CIS_POL));
            String ind1 = ObjectUtil.firstNotNull(StringUtil.notBlankTrimmedString(rs.getString(I1)), " ");
            String ind2 = ObjectUtil.firstNotNull(StringUtil.notBlankTrimmedString(rs.getString(I2)), " ");
            String nazev = rs.getString(NAZEV);
            boolean first = StringUtil.hasTrimmedLength(ind1);
            String hodnota = first ? ind1 : ind2;
            boolean defaultni = rs.getBoolean(JE_DEFAULT);

            FieldTypeId topfieldTypeId = FieldTypeId.recordField(forAuthority, fieldNumber);
            String fieldCode = first ? FieldTypes.IND_1_FIELD_CODE : FieldTypes.IND_2_FIELD_CODE;
            FieldTypeId fieldTypeId = topfieldTypeId.sub(fieldCode);
            List<Indicator> indicators = res.computeIfAbsent(fieldTypeId, _ -> new ArrayList<>());

            //pridame indikator do seznamu. pokud je ale indikator 'N', pridame do seznamu 10 indikatoru '0'-'9'
            if (hodnota.equals(IndicatorType.NUMERIC_TYPE_PLACEHOLDER)) {
                for (int i = 0; i < 9; i++) {
                    defaultni = (defaultni && i == 0); //pokud je indikator 'N' defaultni, je defaultni '0'
                    indicators.add(Indicator.createNumeric(nazev, i, fieldTypeId, defaultni));
                }
            } else {
                indicators.add(Indicator.createStandard(nazev, hodnota, fieldTypeId, defaultni));
            }
        }

        return res.entrySet().stream()
                .map(fieldTypeIdListEntry -> IndicatorType.ofDefaultName(fieldTypeIdListEntry.getKey(), fieldTypeIdListEntry.getValue()))
                .toList();
    }
}
