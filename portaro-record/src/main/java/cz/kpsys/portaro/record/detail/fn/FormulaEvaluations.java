package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.record.detail.value.*;
import lombok.NonNull;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public record FormulaEvaluations<SUCCESS extends FieldValue<?>>(

        @NonNull List<SUCCESS> successes,
        @NonNull List<AbsentDataFail> absentFails,
        @NonNull List<NonAbsentDataFail> nonAbsentFails

) {

    @SafeVarargs
    public static <SUCCESS extends FieldValue<?>> FormulaEvaluations<SUCCESS> extract(@NonNull FormulaEvaluation<SUCCESS>... evals) {
        return extract(List.of(evals));
    }

    public static <SUCCESS extends FieldValue<?>> FormulaEvaluations<SUCCESS> extract(@NonNull Collection<FormulaEvaluation<SUCCESS>> evals) {
        List<NonAbsentDataFail> nonAbsentFails = List.of();
        List<AbsentDataFail> absentFails = List.of();
        List<SUCCESS> successes = List.of();

        for (FormulaEvaluation<SUCCESS> eval : evals) {
            if (eval.isSuccess()) {
                if (successes.isEmpty()) {
                    successes = new ArrayList<>(evals.size());
                }
                successes.add(eval.existingSuccess());
            } else if (eval.existingFail() instanceof AbsentDataFail) {
                if (absentFails.isEmpty()) {
                    absentFails = new ArrayList<>(evals.size());
                }
                absentFails.add(eval.existingFailOf(AbsentDataFail.class));
            } else {
                if (nonAbsentFails.isEmpty()) {
                    nonAbsentFails = new ArrayList<>(evals.size());
                }
                nonAbsentFails.add(eval.existingFailOf(NonAbsentDataFail.class));
            }
        }

        return new FormulaEvaluations<>(successes, absentFails, nonAbsentFails);
    }

    public boolean hasNonAbsentFails() {
        return !nonAbsentFails.isEmpty();
    }

    public boolean hasAbsentFails() {
        return !absentFails.isEmpty();
    }

    public boolean hasFail() {
        return hasNonAbsentFails() || hasAbsentFails();
    }

    public FormulaEvaluation<SUCCESS> toMostCriticalMultiFailEval() {
        if (hasNonAbsentFails()) {
            return toNonAbsentDataMultiFailEval();
        }
        if (hasAbsentFails()) {
            return toAbsentDataMultiFailEval();
        }
        throw new IllegalStateException("Cannot call toMostCriticalMultiFail - there is no fail");
    }

    public FormulaEvaluation<SUCCESS> toNonAbsentDataMultiFailEval() {
        Assert.state(hasNonAbsentFails(), "Cannot call toNonAbsentDataMultiFail - there is no absent fail");
        if (nonAbsentFails.size() == 1) {
            return FormulaEvaluation.failure(nonAbsentFails.getFirst());
        }
        return FormulaEvaluation.failure(MultiNonAbsentFailFail.of(nonAbsentFails));
    }

    public FormulaEvaluation<SUCCESS> toAbsentDataMultiFailEval() {
        Assert.state(hasAbsentFails(), "Cannot call toAbsentDataMultiFail - there is no absent fail");
        if (absentFails.size() == 1) {
            return FormulaEvaluation.failure(absentFails.getFirst());
        }
        return FormulaEvaluation.failure(MultiAbsentDataFail.of(absentFails));
    }

}
