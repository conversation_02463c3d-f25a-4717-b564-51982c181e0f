package cz.kpsys.portaro.record.view;

import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CompositeRecordShowListener implements RecordShowListener {

    @NonNull List<RecordShowListener> listeners = new ArrayList<>();

    public void addListener(RecordShowListener listener) {
        listeners.add(listener);
    }

    public void removeListener(RecordShowListener listener) {
        listeners.remove(listener);
    }

    @Override
    public void recordShowed(Record record) {
        for (RecordShowListener listener : listeners) {
            listener.recordShowed(record);
        }
    }
}
