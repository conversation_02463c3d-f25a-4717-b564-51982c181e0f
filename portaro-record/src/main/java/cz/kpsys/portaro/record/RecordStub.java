package cz.kpsys.portaro.record;

import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.UUID;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class RecordStub implements RecordDescriptor {

    public static final String STUB_NAME = "STUB";

    @Getter
    @NonNull UUID id;

    @Getter
    @Nullable Integer kindedId;

    @Override
    public String getName() {
        return STUB_NAME;
    }

}
