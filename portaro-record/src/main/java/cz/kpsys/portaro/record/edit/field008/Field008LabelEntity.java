package cz.kpsys.portaro.record.edit.field008;

import cz.kpsys.portaro.commons.object.Ordered;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.Value;

import java.io.Serializable;

@Value
@AllArgsConstructor
public class Field008LabelEntity implements Serializable, Ordered {

    @NonNull
    Integer order;

    @NonNull
    String documentTypeCode;

    @NonNull
    String description;

    @NonNull
    Boolean isDefined;
}
