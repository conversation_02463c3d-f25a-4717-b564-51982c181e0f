package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.commons.util.PeriodWaiter;
import cz.kpsys.portaro.commons.web.PlaceholderTemplate;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.LoadedFile;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.extract.RecordNormalizedIsbnExtractor;
import cz.kpsys.portaro.record.extract.RecordValueExtractor;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.Duration;

import static cz.kpsys.portaro.record.Record.TYPE_DOCUMENT;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class OpenLibraryCoverLoader implements NamedCoverLoader {

    public static final PlaceholderTemplate API_URL = new PlaceholderTemplate("http://covers.openlibrary.org/b/ISBN/{ISBN}-M.jpg");
    public static final String SERVICE_NAME = "OpenLibrary";
    private static final Duration PERIODA_MEZI_NACTENIMI = Duration.ofSeconds(5);
    private static final Duration PERIODA_MEZI_NACTENIMI_NAHODNY_PRICITANEC = Duration.ofSeconds(5);

    @NonNull PeriodWaiter periodWaiter = new PeriodWaiter(PERIODA_MEZI_NACTENIMI, PERIODA_MEZI_NACTENIMI_NAHODNY_PRICITANEC);
    @NonNull CoverDownloader coverDownloader;
    @NonNull RecordValueExtractor recordNormalizedIsbnExtractor = new RecordNormalizedIsbnExtractor();
    @Getter @NonNull String serviceName = SERVICE_NAME;

    @Override
    public LoadedFile getCover(Record record, Department currentDepartment) throws CoverSearchException {
        if (!record.getType().equals(TYPE_DOCUMENT)) {
            throw new CoverSearchException(getServiceName(), CoverSearchException.NOT_A_DOCUMENT);
        }

        var isbns = recordNormalizedIsbnExtractor.extractValue(record).toList();
        if (isbns.isEmpty()) {
            throw new CoverSearchException(getServiceName(), CoverSearchException.CHYBEJICI_ISBN);
        }

        CoverSearchException thrownException = null;

        for (var isbn : isbns) {
            try {
                return tryGetCover(record, getCoverUrl(isbn));
            } catch (CoverSearchException e) {
                thrownException = e;
            }
        }

        throw thrownException;
    }

    private LoadedFile tryGetCover(Record record, String url) throws CoverSearchException {
        return coverDownloader.getCoverFromUrl(record, getServiceName(), url);
    }

    private String getCoverUrl(String isbn) {
        periodWaiter.sleepUntilNextTick();
        return API_URL
                .withParameter("ISBN", isbn)
                .build();
    }

}
