package cz.kpsys.portaro.record.edit;

import com.fasterxml.jackson.annotation.JsonFormat;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Collection;
import java.util.List;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum FieldDisplayType implements LabeledIdentified<Integer> {

    DISABLED(0, Texts.ofMessageCoded("record.detail.fieldDisplayType.Off")),
    SHOW_ONLY(1, Texts.ofMessageCoded("record.detail.fieldDisplayType.PreviewOnly")),
    EDIT_ONLY(2, Texts.ofMessageCoded("record.detail.fieldDisplayType.EditingOnly")),
    ALWAYS(3, Texts.ofMessageCoded("record.detail.fieldDisplayType.Always")),
    WHEN_FILLED(4, Texts.ofMessageCoded("record.detail.fieldDisplayType.NotEmptyField")),
    GENERATED(5, Texts.ofMessageCoded("record.detail.fieldDisplayType.Generated")), //pole 1,3,5
    HIDDEN(99, Texts.ofMessageCoded("record.detail.fieldDisplayType.HiddenFields"));

    public static final Codebook<FieldDisplayType, Integer> CODEBOOK = new StaticCodebook<>(values());

    public static final List<FieldDisplayType> ORDERED_BY_BEST_FOR_EDIT = List.of(
            ALWAYS,
            EDIT_ONLY,
            WHEN_FILLED,
            SHOW_ONLY,
            GENERATED,
            HIDDEN,
            DISABLED
    );

    public static final Collection<FieldDisplayType> USABLE_IN_GRID = List.of(
            ALWAYS,
            EDIT_ONLY,
            SHOW_ONLY,
            GENERATED
    );

    public static final Collection<FieldDisplayType> ALL_EDITABLE = List.of(
            ALWAYS,
            EDIT_ONLY,
            WHEN_FILLED
    );

    public static final Collection<FieldDisplayType> ALL_SHOWABLE = List.of(
            SHOW_ONLY,
            EDIT_ONLY,
            ALWAYS,
            WHEN_FILLED,
            GENERATED
    );

    public static final Collection<FieldDisplayType> ALL_LOADABLE = List.of(
            SHOW_ONLY,
            EDIT_ONLY,
            ALWAYS,
            WHEN_FILLED,
            GENERATED,
            HIDDEN
    );

    @NonNull Integer id;
    @NonNull Text text;

}
