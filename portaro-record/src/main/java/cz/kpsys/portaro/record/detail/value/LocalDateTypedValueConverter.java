package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.RecordDetailConstants;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class LocalDateTypedValueConverter implements TypedFieldValueConverter<DateValueCommand, FieldPayload<LocalDateFieldValue>, LocalDateFieldValue> {

    public static final DateTimeFormatter rawDateFormat = RecordDetailConstants.RAW_LOCAL_DATE_FORMAT;
    public static final DateTimeFormatter textDateFormat = RecordDetailConstants.TEXT_LOCAL_DATE_FORMAT;

    @NonNull
    @Override
    public FieldPayload<LocalDateFieldValue> convert(@NonNull DateValueCommand command, @NonNull FieldType<?> fieldType, @NonNull RecordFieldId fieldId) {
        return FieldPayload.of(LocalDateFieldValue.of(command.value(), Set.of(fieldId.recordIdFondPair())));
    }

    public static @NonNull String formatText(@NonNull LocalDate date) {
        return textDateFormat.format(date);
    }

    public static @NonNull String formatLabel(@NonNull LocalDate date) {
        return rawDateFormat.format(date);
    }

    @Override
    public String toString() {
        return getClass().getSimpleName();
    }
}
