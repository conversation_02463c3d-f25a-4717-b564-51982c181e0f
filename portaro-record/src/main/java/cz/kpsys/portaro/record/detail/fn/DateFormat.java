package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.detail.value.StringFieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import lombok.NonNull;

import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.function.Function;

import static cz.kpsys.portaro.CoreConstants.Datatype.TEXT;

public record DateFormat<T extends ScalarFieldValue<? extends TemporalAccessor>>(

        @NonNull
        String format,

        @NonNull
        Formula<T> operand

) implements UnaryFunction<StringFieldValue> {

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<StringFieldValue>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        return TEXT;
    }

    @Override
    public @NonNull FormulaEvaluation<StringFieldValue> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {
        FormulaEvaluation<T> resolvedOperandValue = evaluator.resolveFormulaValue(sourceNode, operand());
        if (resolvedOperandValue.isFailure()) {
            return resolvedOperandValue.castedFailed();
        }

        return compute(resolvedOperandValue.existingSuccess());
    }

    public @NonNull FormulaEvaluation<StringFieldValue> compute(@NonNull T operandValue) {
        String formatted = DateTimeFormatter.ofPattern(format).format(operandValue.getValue());
        return FormulaEvaluation.success(StringFieldValue.of(formatted, operandValue.origins()));
    }
}
