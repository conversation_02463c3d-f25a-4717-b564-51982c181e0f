package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.record.RecordIdFondPair;
import lombok.NonNull;

import java.util.Set;

public record DateRangeFieldValue(

    @NonNull
    DateRange value,

    @NonNull
    String label,

    @NonNull
    Text text,

    @NonNull
    Set<RecordIdFondPair> origins

) implements ScalarFieldValue<DateRange> {

    public static @NonNull DateRangeFieldValue of(@NonNull DateRange value, @NonNull Set<RecordIdFondPair> origins) {
        return new DateRangeFieldValue(
                value,
                DateRangeTypedValueConverter.formatLabel(value),
                Texts.ofDateRange(value),
                origins
        );
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        return o instanceof DateRangeFieldValue that && value.equals(that.value);
    }

    @Override
    public int hashCode() {
        return value().hashCode();
    }

}
