package cz.kpsys.portaro.record.print;

import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.detail.ViewableFieldContainer;
import cz.kpsys.portaro.record.detail.ViewableFieldable;
import cz.kpsys.portaro.record.query.ValFieldGroup;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.Locale;


public class RecordDetailRawPrinter extends RecordDetailAbstractPrinter {

    public RecordDetailRawPrinter(@Nullable Translator<Department> translator, @Nullable Department currentDepartment, @Nullable Locale locale) {
        super(translator, currentDepartment, locale);
    }
    
    public static String printGroup(ValFieldGroup group) {
        return new RecordDetailRawPrinter(null, null, null).group(group);
    }



    private String getFieldText(ViewableFieldable field) {
        return field.getRaw();
    }


    /* IMPLEMENTACE HLAVNICH METOD */

    @Override
    public String writeRecordLinkSubfield(ViewableFieldable sf) {
        return getFieldText(sf);
    }

    @Override
    public String writeUrlSubfield(@NonNull ViewableFieldable sf, @NonNull ViewableFieldContainer originalDetail) {
        return getFieldText(sf);
    }

    @Override
    public String writeNormalField(ViewableFieldable sf, @NonNull ViewableFieldContainer originalDetail) {
        return getFieldText(sf);
    }

}
