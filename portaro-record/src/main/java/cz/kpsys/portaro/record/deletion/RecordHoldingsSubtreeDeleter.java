package cz.kpsys.portaro.record.deletion;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.repo.Deleter;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.holding.RecordHolding;
import cz.kpsys.portaro.record.holding.RecordHoldingLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordHoldingsSubtreeDeleter implements Deleter<RecordHoldingsSubtreeDeletionCommand> {

    @NonNull HierarchyLoader<Department> departmentAccessor;
    @NonNull RecordHoldingLoader recordHoldingLoader;
    @NonNull Deleter<RecordHoldingDeletionCommand> recordHoldingDeleter;

    @Override
    public void delete(RecordHoldingsSubtreeDeletionCommand command) {
        var parentChainDepartments = departmentAccessor.getAllByScope(command.ctx(), HierarchyLoadScope.ANCESTORS_ONLY);
        var holdingsInParentChain = recordHoldingLoader.getAllByRecordIdsAndDepartments(List.of(command.record().getId()), parentChainDepartments);
        Assert.isTrue(holdingsInParentChain.isEmpty(), () -> "Can not delete holdings of %s in subtree of %s because there are holdings in parent departments".formatted(command.record(), command.ctx()));

        var holdingsInSubtree = recordHoldingLoader.getAllByRecordIdAndRootDepartment(command.record().getId(), command.ctx());
        holdingsInSubtree.stream()
                .map(holding -> createHoldingDeletionCommand(holding, command.ctx(), command.auth()))
                .forEach(recordHoldingDeleter::delete);
    }

    private RecordHoldingDeletionCommand createHoldingDeletionCommand(RecordHolding recordHolding, Department ctx, UserAuthentication auth) {
        return new RecordHoldingDeletionCommand(
                recordHolding,
                ctx,
                auth,
                true,
                false,
                true,
                true
        );
    }
}
