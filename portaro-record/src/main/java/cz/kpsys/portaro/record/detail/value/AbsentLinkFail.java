package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.record.detail.spec.RecordDatableId;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.util.Set;

import static cz.kpsys.portaro.record.detail.spec.RecordSpecCollectors.recordSpecsToSpecSet;

/// Reprezentuje primary dep, tzn. chybejici pole s linkem na record
public record AbsentLinkFail(

        @NonNull
        Text text,

        @NonNull
        Set<? extends RecordDatableId> missingLinkFieldIds

) implements AbsentDataFail {

    public static @NonNull AbsentLinkFail of(@NonNull @NotEmpty Set<? extends RecordDatableId> missingLinkFieldIds) {
        return new AbsentLinkFail(
                Texts.ofNative("Missing link field " + StringUtil.listToString(missingLinkFieldIds, ", ")),
                missingLinkFieldIds
        );
    }

    @Override
    public @NonNull RecordSpecSet toSpec() {
        return missingLinkFieldIds.stream()
                .map(RecordDatableId::toRecordSpec)
                .collect(recordSpecsToSpecSet());
    }

    @Override
    public String toString() {
        return "AbsentLink[" + missingLinkFieldIds + ']';
    }
}
