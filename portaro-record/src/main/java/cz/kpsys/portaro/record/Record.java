package cz.kpsys.portaro.record;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.id.IDdFile;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.prop.ObjectProperties;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.export.RecordViewFunctions;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.fond.FondTypeResolver;
import cz.kpsys.portaro.record.query.FieldGroupFillerImpl;
import cz.kpsys.portaro.record.query.MarqueryParser;
import cz.kpsys.portaro.record.query.MultipleValFieldGroup;
import cz.kpsys.portaro.record.query.ValFieldGroup;
import cz.kpsys.portaro.record.view.DownloadLinkResolver;
import cz.kpsys.portaro.record.view.DownloadLinkResolverBy856Fields;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.jspecify.annotations.Nullable;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;

@Schema(
        description = "Record specification",
        type = "string",
        format = "uuid",
        example = Record.SCHEMA_EXAMPLE_DOCUMENT_ID
)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class Record extends BasicKindedIdRecordDescriptor implements Serializable, RecordDescriptor, FieldStreamSource<Field<?>> {

    public static final String TYPE_DOCUMENT = "document";
    public static final String TYPE_AUTHORITY = "authority";

    public static final int RECORD_KEY_FLAT_PRIMARY_NAME_VAL_MAX_LENGTH = 100;
    public static final int RECORD_KEY_FLAT_ALTERNATIVE_NAME_VAL_MAX_LENGTH = 100;
    public static final int RECORD_KEY_VAL_MAX_LENGTH = 250;

    public static final String SCHEMA_EXAMPLE_DOCUMENT_ID = "bbc957aa-8a27-4b05-91f6-ce20634b458b";

    private static final DownloadLinkResolver DOWNLOAD_LINK_RESOLVER = new DownloadLinkResolverBy856Fields();

    @NonNull
    RecordIdFondPair recordIdFondPair;

    @JsonIgnore
    @Getter
    @Setter
    @Nullable // TODO: měl by být NonNull
    UUID creationEventId;

    @JsonIgnore
    @Getter
    @Setter
    @Nullable
    UUID activationEventId;

    @JsonIgnore
    @Getter
    @Setter
    @Nullable
    UUID deletionEventId;

    @Getter
    @Setter
    boolean external;

    @JsonIgnore
    @Getter
    @Setter
    FieldContainer detail;

    @Getter
    @Setter
    RecordStatus status = RecordStatus.NOT_DEFINED;

    @Getter
    @Setter
    boolean valid = true;

    /**
     * Deprecated - use RecordViewFunctions (e.g. RecordViewFunctions.normalizedIsbns). Use ObjectProperties only for frontend
     */
    @Deprecated
    @Getter
    @NonNull
    final ObjectProperties props;

    @Getter
    @Setter
    private Integer directoryId;

    @Getter
    @Setter
    private IDdFile cover;

    @NonNull
    @NonFinal
    protected Function<FieldContainer, Optional<String>> nameGenerator = _ -> Optional.empty();

    final MarqueryParser queryParser = new MarqueryParser();

    public Record(@NonNull RecordIdFondPair recordIdFondPair,
                  @Nullable Integer kindedId,
                  @Nullable UUID creationEventId,
                  boolean external,
                  @NullableNotBlank String name,
                  @Nullable FieldContainer detail,
                  @Deprecated @NonNull ObjectProperties props) {
        super(recordIdFondPair.id().id(), name, kindedId);
        this.recordIdFondPair = recordIdFondPair;
        this.creationEventId = creationEventId;
        this.external = external;
        this.detail = detail;
        this.props = props;
    }

    public Record(@NonNull Record original, @NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId) {
        this(
                newRecordIdFondPair,
                kindedId,
                original.creationEventId,
                original.external,
                original.getName(),
                ObjectUtil.elvis(original.detail, existingDetail -> existingDetail.copy(newRecordIdFondPair, kindedId)),
                original.props
        );
        this.activationEventId = original.activationEventId;
        this.deletionEventId = original.deletionEventId;
        this.status = original.status;
        this.valid = original.valid;
        this.directoryId = original.directoryId;
        this.cover = original.cover;
        this.nameGenerator = original.nameGenerator;
    }

    public static Record createAuthority(@NonNull RecordIdFondPair recordIdFondPair,
                                         @Nullable Integer kindedId,
                                         @Nullable UUID creationEventId,
                                         boolean external,
                                         @NullableNotBlank String name,
                                         @Nullable FieldContainer detail,
                                         @NonNull ObjectProperties props) {
        Record record = new Record(recordIdFondPair, kindedId, creationEventId, external, name, detail, props);
        record.nameGenerator = RecordViewFunctions::authorityName;
        record.setStatus(RecordStatus.NOT_NATIONAL_CREATED_BY_RECONS);
        return record;
    }

    public static Record createDocument(@NonNull RecordIdFondPair recordIdFondPair,
                                        @Nullable Integer kindedId,
                                        @Nullable UUID creationEventId,
                                        boolean external,
                                        @NullableNotBlank String name,
                                        @Nullable FieldContainer detail,
                                        @NonNull ObjectProperties props) {
        Record record = new Record(recordIdFondPair, kindedId, creationEventId, external, name, detail, props);
        record.nameGenerator = RecordViewFunctions::documentName;
        return record;
    }

    /**
     * Vytvori novou autoritu z patternu (zkopiruje vse krome id). Napriklad pro vytvoreni nove autority ze z-authority
     */
    public static Record createNewByPattern(@NonNull Record pattern) {
        Assert.isTrue(pattern.getFond().isOfAuthority(), () -> "Pattern record fond " + pattern + " must be of authority");
        RecordIdFondPair recordIdFondPair = RecordIdFondPair.of(UuidGenerator.forIdentifier(), pattern.getFond());
        return Record.createAuthority(recordIdFondPair, null, null, false, pattern.getName(), pattern.getDetail(), ObjectProperties.empty());
    }

    public static Record createDraft(@NonNull RecordIdFondPair recordIdFondPair, boolean external, @NonNull FieldContainer detail) {
        Record record = FondTypeResolver.isDocumentFond(recordIdFondPair.fond())
                ? Record.createDocument(recordIdFondPair, null, null, external, null, detail, ObjectProperties.empty())
                : Record.createAuthority(recordIdFondPair, null, null, external, null, detail, ObjectProperties.empty());
        record.setStatus(RecordStatus.DRAFT);
        return record;
    }

    @Override
    public @NullableNotBlank String getName() {
        String name = super.getName();
        if (name != null) {
            return name;
        }
        if (getDetail() != null) {
            Optional<String> fromDetail = nameGenerator.apply(getDetail());
            if (fromDetail.isPresent()) {
                return fromDetail.get();
            }
        }
        return null;
    }

    @JsonIgnore
    public @NonNull RecordIdFondPair idFondPair() {
        return recordIdFondPair;
    }

    @JsonIgnore
    public boolean isMerged() {
        return idFondPair().id().masterId() != null;
    }

    @NonNull
    public String getType() {
        if (getFond().isOfAuthority()) {
            return TYPE_AUTHORITY;
        }
        return TYPE_DOCUMENT;
    }

    public @NonNull Fond getFond() {
        return recordIdFondPair.fond();
    }

    public void setFond(Fond fond) {
        recordIdFondPair = recordIdFondPair.withFond(fond);
    }

    @Override
    public List<Field<?>> getFields() {
        return getDetail() == null ? null : getDetail().getFields();
    }

    /**
     * Vrati vsechna opakovani poli daneho cisla.
     */
    public List<Field<?>> getFields(String fieldNumber) {
        return getDetail() == null ? null : getDetail().getFields(By.code(fieldNumber));
    }

    public ValFieldGroup query(String q) {
        return getDetail() == null ? MultipleValFieldGroup.empty() : queryParser.parse(q, FieldGroupFillerImpl.ofFlattedDetail(getDetail()));
    }

    @JsonIgnore
    public Record copy(@NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId) {
        ObjectUtil.assertCalledClassIsExactly(this, Record.class);
        return new Record(this, newRecordIdFondPair, kindedId);
    }

    public boolean isActive() {
        return getActivationEventId() != null;
    }

    public boolean isDeleted() {
        return getDeletionEventId() != null;
    }

    /**
     * Vrati, zda ma dokument (jen nebo i) elektronickou podobu.
     */
    public boolean isDownloadable() {
        return !DOWNLOAD_LINK_RESOLVER.resolveLinks(this).isEmpty();
    }

    public boolean isPeriodical() {
        return getFond().isPeriodical();
    }

    public boolean isVolumeable() {
        return isPeriodical();
    }

    /**
     * Zda ma dokument nejake exemplare (nebo svazky u periodik).
     */
    public boolean isExemplarable() {
        return getFond().isWithExemplars();
    }

    /**
     * Zda ma dokument nejake clanky. <br/>
     * Clanky to jsou asi jen u periodik, u monografii to jsou treba recenze. <br/>
     * Bacha na to ze clanky nemaj nic spolecnyho s exemplarema!
     * Deprecated - migrate to recordProperties
     */
    @Deprecated
    public boolean canHaveArticles() {
        return FondTypeResolver.isDocumentFond(getFond());
    }

    /**
     * Zda je dokument cast nejakeho vicedilneho dila.
     * Deprecated - migrate to recordProperties
     */
    @Deprecated
    public boolean canHaveParts() {
        return FondTypeResolver.isDocumentFond(getFond());
    }

    public <T extends ScalarFieldValue<?>, R> Optional<R> findFirst(@NonNull RecordFieldFinder<T, R> finder) {
        return finder.findFirstIn(this);
    }

    public <T extends ScalarFieldValue<?>, R> R findFirstOrNull(@NonNull RecordFieldFinder<T, R> finder) {
        return this.findFirst(finder).orElse(null);
    }

    public <T extends ScalarFieldValue<?>, R> R getFirst(@NonNull RecordFieldFinder<T, R> finder) {
        return finder.getFirstIn(this);
    }

    public <T extends ScalarFieldValue<?>, R> boolean hasFirstValue(@NonNull RecordFieldFinder<T, R> finder, R value) {
        return findFirst(finder).filter(Predicate.isEqual(value)).isPresent();
    }

    public static Record getByRecordId(@NonNull Collection<Record> records, @NonNull UUID id, boolean tryFindInMasterRecordId, @NonNull Supplier<String> notFoundErrorMessageSupplier) {
        Optional<Record> idMatching = ListUtil.findFirstMatching(records, r -> r.getId().equals(id));
        if (idMatching.isPresent()) {
            return idMatching.get();
        }
        if (tryFindInMasterRecordId) {
            Optional<Record> masterIdMatching = ListUtil.findFirstMatching(records, r -> id.equals(r.idFondPair().id().masterId()));
            if (masterIdMatching.isPresent()) {
                return masterIdMatching.get();
            }
        }
        String notFoundErrorMessage = notFoundErrorMessageSupplier.get();
        throw new ItemNotFoundException(Record.class, "record uuid %s".formatted(id), notFoundErrorMessage, Texts.ofNative(notFoundErrorMessage));
    }

    public static List<UUID> getListOfUuids(@NonNull Collection<Record> records) {
        return ListUtil.convert(records, Record::getId);
    }

    @Override
    public String toString() {
        return "@" + getFond().getId() + " " + UuidGenerator.abbr6(getId()) + " (" + StringUtil.limitCharsAndTrimWithoutEllipsis(getFond().getName(), 10, true) + " " + StringUtil.limitCharsAndTrimWithoutEllipsis(StringUtil.notNullString(getName()), 10, false) + ")";
    }

}
