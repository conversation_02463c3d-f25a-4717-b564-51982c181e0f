package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.FieldValueDatatypeResolvingInfiniteLoopException;
import cz.kpsys.portaro.record.detail.FieldValueDatatypeResolvingInfiniteLoopOfAllOperandsException;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.FieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

public non-sealed interface ResolvableFunction<RES extends FieldValue<?>> extends Formula<RES> {

    @NonNull
    ScalarDatatype resolveResultDatatype(Function<Formula<RES>, @NonNull ScalarDatatype> operandResultDatatypeResolver);

    @NonNull
    FormulaEvaluation<RES> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs);

    /// resolves operand datatypes with catching infinite-loops exception
    static <RES extends FieldValue<?>> @NonNull List<ScalarDatatype> resolveOperandDatatypes(@NonNull @NotEmpty List<Formula<RES>> operands, Function<Formula<RES>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        List<ScalarDatatype> resolvedDatatypes = new ArrayList<>(operands.size());
        Map<Formula<RES>, FieldValueDatatypeResolvingInfiniteLoopException> infiniteLoops = new HashMap<>(operands.size());

        for (Formula<RES> operand : operands) {
            try {
                ScalarDatatype resolvedDatatype = operandResultDatatypeResolver.apply(operand);
                resolvedDatatypes.add(resolvedDatatype);
            } catch (FieldValueDatatypeResolvingInfiniteLoopException e) {
                infiniteLoops.put(operand, e);
            }
        }

        if (resolvedDatatypes.isEmpty()) {
            Assert.notEmpty(infiniteLoops, "This should never happen - there should be at least one operand");
            throw new FieldValueDatatypeResolvingInfiniteLoopOfAllOperandsException(infiniteLoops);
        }

        return resolvedDatatypes;
    }
}
