package cz.kpsys.portaro.record.search.restriction;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.RawRestriction;
import cz.kpsys.portaro.search.restriction.modifier.RestrictionModifier;

public class RawQueryAddingConjunctionModifier implements RestrictionModifier<MapBackedParams> {

    @Override
    public Conjunction<SearchField> modify(Conjunction<SearchField> rootConjunction, MapBackedParams p, Department ctx) {
        rootConjunction.addIfHas(p, CoreSearchParams.FINAL_RAW_QUERY, RawRestriction::new);
        rootConjunction.addIfHas(p, CoreSearchParams.RAW_QUERY, RawRestriction::new);
        return rootConjunction;
    }

}
