package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Text;
import lombok.NonNull;

import java.util.ArrayList;
import java.util.List;

public record VectorFieldValue<SCALAR_ITEM extends ScalarFieldValue<VALUE>, VALUE>(

    @NonNull
    List<SCALAR_ITEM> values,

    @NonNull
    Text text

) implements FieldValue<List<SCALAR_ITEM>> {

    public static <ITEM extends ScalarFieldValue<VALUE>, VALUE> VectorFieldValue<ITEM, VALUE> of(@NonNull List<ITEM> value) {
        MultiText text = MultiText.ofTexts(value).withCommaSpaceDelimiter();
        return new VectorFieldValue<>(value, text);
    }

    public static VectorFieldValue<?, ?> ofUnknownTypes(@NonNull List<ScalarFieldValue<?>> value) {
        MultiText text = MultiText.ofTexts(value).withCommaSpaceDelimiter();

        List<ScalarFieldValue<Object>> vals = new ArrayList<>();
        for (ScalarFieldValue<?> holder : value) {
            vals.add((ScalarFieldValue<Object>) holder);
        }

        return new VectorFieldValue<>(vals, text);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        return o instanceof VectorFieldValue that && values.equals(that.values);
    }

    @Override
    public int hashCode() {
        return values().hashCode();
    }

}
