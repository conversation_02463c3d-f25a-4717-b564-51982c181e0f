package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.record.RecordIdentifier;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotBlank;
import lombok.NonNull;

import java.time.Instant;

public record LegacyRecordFieldEntity(

        @NonNull
        Integer id,

        @NonNull
        RecordIdentifier recordIdentifier,

        @NonNull
        @NotBlank
        String fieldNumberPrefix,

        @NonNull
        Integer fieldNumber,

        @NonNull
        Integer index,

        @Nullable
        @NullableNotBlank
        String ind1,

        @Nullable
        @NullableNotBlank
        String ind2,

        @NonNull
        @NotBlank
        String content,

        @NonNull
        Instant lastUpdateDate,

        @NonNull
        Integer lastUpdateUserId

) implements IdentifiedRecord<Integer> {

    public static final String SUBFIELDS_DELIMITER_PREFIX = "^";
    public static final String SIMPLE_AUTHORITY_LINK_PREFIX = "\u0002";
    public static final String COMPLEX_AUTHORITY_LINK_PREFIX = "\u0003";

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equalsIdentified(this, o, LegacyRecordFieldEntity.class);
    }

    @Override
    public int hashCode() {
        return getId().hashCode();
    }

}
