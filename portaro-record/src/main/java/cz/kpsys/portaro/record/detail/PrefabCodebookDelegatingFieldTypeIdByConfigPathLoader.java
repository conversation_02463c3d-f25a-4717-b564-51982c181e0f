package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class PrefabCodebookDelegatingFieldTypeIdByConfigPathLoader {

    @NonNull Codebook<PrefabFieldType, FieldTypeId> prefabFieldTypeLoader;

    public Optional<FieldTypeId> findByConfigPath(@NonNull FieldTypeId configPath) {
        Optional<PrefabFieldType> prefabFieldType = ListUtil.findFirstMatching(prefabFieldTypeLoader.getAll(), pft -> configPath.equals(pft.configPath()));
        return prefabFieldType.map(PrefabFieldType::id);
    }

}
