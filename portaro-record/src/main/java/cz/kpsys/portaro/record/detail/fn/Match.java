package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.datatype.DatatypeUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.EmptyMatchFail;
import cz.kpsys.portaro.record.detail.value.FalseCaseEvaluation;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import lombok.NonNull;
import org.springframework.util.Assert;

import java.util.List;
import java.util.function.Function;
import java.util.stream.IntStream;

public record Match<T extends ScalarFieldValue<?>>(
        @NonNull
        List<Case<T>> operands
) implements NaryOperator<T> {

    public Match {
        List<Integer> defaultIndexes = IntStream.range(0, operands.size())
                .filter(i -> operands.get(i) instanceof DefaultCase)
                .boxed()
                .toList();

        Assert.state(defaultIndexes.size() <= 1, "There must be at most one DefaultCase");

        if (!defaultIndexes.isEmpty()) {
            Assert.state(defaultIndexes.getFirst() == operands.size() - 1, "DefaultCase must be the last operand");
        }
    }

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<T>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        List<@NonNull Formula<T>> caseValues = toCaseValue();
        List<ScalarDatatype> operandDatatypes = ResolvableFunction.resolveOperandDatatypes(caseValues, operandResultDatatypeResolver);
        return DatatypeUtil.getMostPreciseDatatypeIfNumberOrRequireSingleUniqueDatatype(operandDatatypes, getClass().getSimpleName());
    }

    @Override
    public @NonNull FormulaEvaluation<T> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {

        for (Formula<T> operand : operands) {
            FormulaEvaluation<T> resolvedOperandValue = evaluator.resolveFormulaValue(sourceNode, operand);
            if (resolvedOperandValue.isSuccess()) {
                return resolvedOperandValue;
            }
            if (resolvedOperandValue.isFailure() && resolvedOperandValue.existingFail() instanceof FalseCaseEvaluation) {
                continue;
            }

            if (resolvedOperandValue.isFailure()) {
                return resolvedOperandValue;
            }
        }

        return FormulaEvaluation.failure(EmptyMatchFail.of(sourceNode.id().toRecordFieldTypeId()));
    }

    private @NonNull List<Formula<T>> toCaseValue() {
        return operands.stream().map(caseOperand -> switch (caseOperand) {
            case DefaultCase<T> v -> v.operand();
            case WhenCase<T> when -> when.second();
        }).toList();
    }

}
