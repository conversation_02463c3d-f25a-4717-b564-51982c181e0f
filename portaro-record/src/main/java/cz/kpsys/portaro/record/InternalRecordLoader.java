package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import lombok.NonNull;

import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.commons.object.repo.DataUtils.getByIdUsingAllByIds;

public interface InternalRecordLoader extends IdAndIdsLoadable<Record, UUID> {

    @Override
    default Record getById(@NonNull UUID id) throws ItemNotFoundException {
        return getByIdUsingAllByIds(this, Record.class, id);
    }

    List<Record> getDocumentsByKindedIds(@NonNull List<Integer> kindedIds);

    List<Record> getAuthoritiesByKindedIds(@NonNull List<Integer> kindedIds);
}
