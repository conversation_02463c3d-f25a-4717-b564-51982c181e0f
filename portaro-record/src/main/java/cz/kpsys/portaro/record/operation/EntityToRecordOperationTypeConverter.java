package cz.kpsys.portaro.record.operation;

import cz.kpsys.portaro.database.DtoToLabeledObjectConverter;

public class EntityToRecordOperationTypeConverter extends DtoToLabeledObjectConverter<RecordOperationTypeEntity, RecordOperationType, Integer> {

    public EntityToRecordOperationTypeConverter() {
        withTrimLabel();
    }

    @Override
    protected RecordOperationType create(Integer id, String name, RecordOperationTypeEntity source) {
        return new RecordOperationType(id, name);
    }
}
