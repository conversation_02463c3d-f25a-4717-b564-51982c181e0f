package cz.kpsys.portaro.record.detail.dflt;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.NonNull;

import java.io.Serializable;
import java.util.Optional;

public record DefaultFieldValueId(

        @NonNull
        FieldTypeId fieldTypeId,

        @NonNull
        Department department,

        @NonNull
        Optional<Fond> fond

) implements Serializable {}
