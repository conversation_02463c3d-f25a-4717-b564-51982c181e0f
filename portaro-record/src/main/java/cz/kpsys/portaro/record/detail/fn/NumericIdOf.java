package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.AcceptableValueFieldValue;
import cz.kpsys.portaro.record.detail.value.NumberFieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import lombok.NonNull;
import org.springframework.core.ParameterizedTypeReference;

import java.math.BigDecimal;
import java.util.function.Function;

import static cz.kpsys.portaro.CoreConstants.Datatype.NUMBER;

public record NumericIdOf(

        @NonNull
        Formula<AcceptableValueFieldValue<Identified<Integer>>> operand

) implements UnaryFunction<NumberFieldValue> {

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<NumberFieldValue>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        return NUMBER;
    }

    @Override
    public @NonNull FormulaEvaluation<NumberFieldValue> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {
        FormulaEvaluation<AcceptableValueFieldValue<Identified<Integer>>> resolvedOperandValue = evaluator.resolveFormulaValue(sourceNode, operand).ensureOf(new ParameterizedTypeReference<>() {}, sourceNode);
        if (resolvedOperandValue.isFailure()) {
            return resolvedOperandValue.castedFailed();
        }

        FormulaEvaluation<AcceptableValueFieldValue<Identified<Integer>>> castedOperandValue = resolvedOperandValue.success().evaluateAssertIdType(Integer.class, sourceNode.id().toRecordFieldTypeId(), operand);
        if (castedOperandValue.isFailure()) {
            return castedOperandValue.castedFailed();
        }

        return compute(castedOperandValue.existingSuccess());
    }

    public @NonNull FormulaEvaluation<NumberFieldValue> compute(@NonNull AcceptableValueFieldValue<Identified<Integer>> operandValue) {
        Integer id = operandValue.value().getId();
        return FormulaEvaluation.success(NumberFieldValue.of(BigDecimal.valueOf(id), operandValue.origins()));
    }

}
