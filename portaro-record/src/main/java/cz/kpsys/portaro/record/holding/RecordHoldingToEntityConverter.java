package cz.kpsys.portaro.record.holding;

import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

public class RecordHoldingToEntityConverter implements Converter<RecordHolding, RecordHoldingEntity> {

    @Override
    public RecordHoldingEntity convert(@NonNull RecordHolding recordHolding) {
        return new RecordHoldingEntity(
                recordHolding.getId(),
                recordHolding.getRecordId(),
                recordHolding.getDepartment().getId(),
                recordHolding.getCreationEventId(),
                recordHolding.getDiscardionEventId(),
                recordHolding.getDeletionEventId()
        );
    }

}
