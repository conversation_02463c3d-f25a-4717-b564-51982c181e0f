package cz.kpsys.portaro.record.deletion;

import cz.kpsys.portaro.commons.object.repo.Deleter;
import cz.kpsys.portaro.record.RecordSecurityActions;
import cz.kpsys.portaro.security.SecurityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SecuredRecordHoldingDeleter implements Deleter<RecordHoldingDeletionCommand> {

    @NonNull Deleter<RecordHoldingDeletionCommand> delegate;
    @NonNull SecurityManager securityManager;

    @Override
    public void delete(RecordHoldingDeletionCommand command) {
        securityManager.throwIfCannot(RecordSecurityActions.RECORD_HOLDING_DELETE, command.currentAuth(), command.currentDepartment(), command.recordHolding());
        delegate.delete(command);
    }
}
