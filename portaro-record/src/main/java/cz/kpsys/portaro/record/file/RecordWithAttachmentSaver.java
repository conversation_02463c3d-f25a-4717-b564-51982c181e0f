package cz.kpsys.portaro.record.file;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.file.FileUtils;
import cz.kpsys.portaro.commons.file.FileViewForm;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.*;
import cz.kpsys.portaro.file.directory.BasicDirectory;
import cz.kpsys.portaro.file.directory.Directory;
import cz.kpsys.portaro.file.directory.ParentableDirectoryCreateCommand;
import cz.kpsys.portaro.file.directory.ParentableDirectoryCreator;
import cz.kpsys.portaro.file.security.IdentifiedFileSaveCommand;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordDescriptor;
import cz.kpsys.portaro.record.export.RecordViewFunctions;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.UpdateQuery;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.StandardException;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import java.time.Instant;
import java.util.Objects;

import static cz.kpsys.portaro.databasestructure.RecordDb.KAT1_4;
import static cz.kpsys.portaro.databasestructure.RecordDb.KAT1_4.TABLE;
import static cz.kpsys.portaro.databasestructure.RecordDb.KATAUT_4;
import static cz.kpsys.portaro.record.Record.TYPE_AUTHORITY;
import static cz.kpsys.portaro.record.Record.TYPE_DOCUMENT;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class RecordWithAttachmentSaver implements Saver<RecordWithAttachmentSaveCommand, IdentifiedFile> {

    @NonNull Saver<Record, ?> recordHeaderSaver;
    @NonNull Saver<IdentifiedFileSaveCommand, IdentifiedFile> loadedIdentifiedFileSaver;
    @NonNull Provider<@NonNull FileAccessType> defaultFileAccessTypeProvider;
    @NonNull ParentableDirectoryCreator parentableDirectoryCreator;
    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull TransactionTemplate readwriteTransactionTemplate;

    @Override
    public @NonNull IdentifiedFile save(@NonNull RecordWithAttachmentSaveCommand command) {
        return Objects.requireNonNull(readwriteTransactionTemplate.execute(_ -> {
            Record record = command.record();
            Integer directoryId = ensureDirectory(record).getId();

            IdentifiedFile resultFile = saveFileIfNecessary(extractIdentifiedFile(command), directoryId, command.currentAuth(), command.ctx());

            if (command.setAsPrimaryFile()) {
                Assert.isTrue(resultFile.hasForm(FileViewForm.IMAGE), () -> "File %s is not image!".formatted(resultFile));

                record.setCover(resultFile);
                recordHeaderSaver.save(record); // Also deletes record from the cache

                createRecordUpdates(resultFile, record, command.currentAuth().getActiveUser());
            }

            return resultFile;
        }));
    }

    private IdentifiedFile extractIdentifiedFile(@NonNull RecordWithAttachmentSaveCommand command) {
        var file = command.file();
        if (file instanceof LoadedIdentifiedFile loadedFile) {
            return new ByteArrayLoadedIdentifiedFile(IdentifiedFileImpl.newFrom(loadedFile), FileUtils.getBytesOfInputStream(loadedFile.getInputStream()));
        }

        if (file instanceof IdentifiedFile iddFile) {
            return iddFile;
        }
        throw new IllegalArgumentException("Cannot use resource that is not already in DB or does not have data!");
    }

    private IdentifiedFile saveFileIfNecessary(IdentifiedFile identifiedFile, Integer directoryId, UserAuthentication currentAuth, Department ctx) {
        if (identifiedFile.getDirectory() != null && (! directoryId.equals(identifiedFile.getDirectory().getId()))) {
            throw new WrongDirectoryException(directoryId, identifiedFile);
        }

        IdentifiedFile savedFile;
        if (identifiedFile.getId() == null || identifiedFile.getDirectory() == null) {
            if (identifiedFile.getDirectory() == null) {
                identifiedFile.setDirectory(BasicDirectory.createOnlyWithId(directoryId));
            }
            savedFile = loadedIdentifiedFileSaver.save(new IdentifiedFileSaveCommand(identifiedFile, currentAuth, ctx)); // TODO: možná zbytečně provolává těžkotonážní ukládání souborů
        } else {
            savedFile = identifiedFile;
        }

        Objects.requireNonNull(savedFile.getId());
        return savedFile;
    }


    /**
     * @deprecated Use {@code record.setCover(file);} followed by {@code recordSaver.save(record);} instead
     * to save the image.
     */
    @Deprecated
    private void createRecordUpdates(IdentifiedFile file, Record record, BasicUser activeUser) {

        if (record.getType().equals(TYPE_DOCUMENT)) {
            createDocumentUpdates(file, record, activeUser);
            return;
        }
        if (record.getType().equals(TYPE_AUTHORITY)) {
            createAuthorityUpdates(file, record, activeUser);
            return;
        }
        throw new RuntimeException("Unknown record type. This should not happen");
    }

    private void createDocumentUpdates(IdentifiedFile file, RecordDescriptor record, BasicUser activeUser) {
        UpdateQuery uqDocument = queryFactory.newUpdateQuery();
        uqDocument.update(TABLE);
        uqDocument.where().eq(KAT1_4.RECORD_ID, record.getId());
        uqDocument.set().add(KAT1_4.FK_FULLTEXT_IMAGE, file.getId());
        uqDocument.set().add(KAT1_4.FK_UZIV, activeUser.getId());
        uqDocument.set().add(KAT1_4.DATCAS, Instant.now());
        notAutoCommittingJdbcTemplate.update(uqDocument.getSql(), uqDocument.getParamMap());
    }

    private void createAuthorityUpdates(IdentifiedFile file, RecordDescriptor record, BasicUser activeUser) {
        UpdateQuery uqAuthority = queryFactory.newUpdateQuery();
        uqAuthority.update(KATAUT_4.TABLE);
        uqAuthority.where().eq(KATAUT_4.RECORD_ID, record.getId());
        uqAuthority.set().add(KATAUT_4.FK_FULLTEXT_IMAGE, file.getId());
        uqAuthority.set().add(KATAUT_4.FK_UZIV, activeUser.getId());
        uqAuthority.set().add(KATAUT_4.DATCAS, Instant.now());
        notAutoCommittingJdbcTemplate.update(uqAuthority.getSql(), uqAuthority.getParamMap());
    }

    private @NonNull Directory ensureDirectory(@NonNull Record record) {
        if (record.getDirectoryId() != null && record.getDirectoryId() != 0) {
            return BasicDirectory.createOnlyWithId(record.getDirectoryId());
        }

        String name = StringUtil.limitCharsAndTrimWithEllipsis(
                RecordViewFunctions.documentName(record.getDetail()).orElse("RECORD_ID:" + record.getId()),
                Directory.NAME_MAX_LENGTH, true);
        assert name != null; // NOOP, potlačení varování z IDEY
        var recordDirectory = parentableDirectoryCreator.create(ParentableDirectoryCreateCommand.ofNewTopDirectory(name, defaultFileAccessTypeProvider.get()));
        record.setDirectoryId(recordDirectory.getId());
        recordHeaderSaver.save(record);
        return recordDirectory;
    }

    @StandardException
    public static class WrongDirectoryException extends RuntimeException {

        public WrongDirectoryException(Integer directoryId, IdentifiedFile file) {
            super(String.format("Target record has directory ID \"%s\". Cannot assign file \"%s\" from directory \"%s\"!", directoryId, file, file.getDirectory()));
        }

    }

}
