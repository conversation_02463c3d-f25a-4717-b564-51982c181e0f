package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.record.LabeledRecordRef;
import cz.kpsys.portaro.record.detail.frontend.ErroredFieldResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.stream.Stream;

import static java.util.function.Predicate.isEqual;
import static java.util.function.Predicate.not;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class FieldToViewableFieldConverter implements Converter<FieldContainer, List<ViewableField>> {

    boolean flatVirtualFields;

    public static FieldToViewableFieldConverter ofFlatten() {
        return new FieldToViewableFieldConverter(true);
    }

    public static FieldToViewableFieldConverter ofNotFlatten() {
        return new FieldToViewableFieldConverter(false);
    }

    @Override
    public @NonNull List<ViewableField> convert(@NonNull FieldContainer parent) {
        return parent.streamFields().flatMap(this::convertField).toList();
    }

    private Stream<ViewableField> convertField(@NonNull Field<?> field) {
        if (flatVirtualFields && field.getType().isVirtualGroup()) {
            return field.streamFields().flatMap(this::convertField);
        }
        return Stream.of(mapToSingleField(field));
    }

    private @NonNull ViewableField mapToSingleField(@NonNull Field<?> field) {
        var thisRecordId = field.getRecordFieldId().recordIdFondPair();
        var recordLink = field.getMaxOneOrigin()
                .or(field::getRecordLink)
                .filter(not(isEqual(thisRecordId)))
                .map(link -> LabeledRecordRef.ofRecordLink(link, field.getText()))
                .orElse(null);
        return new ViewableField(
                field.getId(),
                field.getCode(),
                field.getRepetition(),
                field.getFieldRepetition(),
                field.getTypeId(),
                field.getType().getFieldTypeId(),
                field.getFieldTypeText(),
                convert(field),
                field.getText(),
                ErroredFieldResponse.map(field.getError()),
                recordLink,
                field.getRaw(),
                field.getFieldId(),
                field.isUrl(),
                field.getType().isVirtualGroup()
        );
    }

}
