package cz.kpsys.portaro.record.query;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.RecordWellKnownFields;
import cz.kpsys.portaro.record.detail.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldGroupFillerImpl implements FieldGroupFiller {

    @NonNull ViewableFieldContainer viewableDetail;
    @NonNull ValFieldGroup group;

    public FieldGroupFillerImpl(@NonNull ViewableFieldContainer viewableDetail) {
        this.viewableDetail = viewableDetail;
        this.group = new MultipleValFieldGroup(viewableDetail);
    }

    public static FieldGroupFillerImpl ofFlattedDetail(@NonNull FieldContainer recordDetail) {
        List<ViewableField> viewableFields = FieldToViewableFieldConverter.ofFlatten().convert(recordDetail);
        return new FieldGroupFillerImpl(new SimpleViewableFieldContainer(viewableFields));
    }

    @Override
    public void add(@NonNull List<String> topfieldCodes, @NonNull List<Integer> fieldReps, @NonNull List<String> subfieldCodes, @NonNull List<Integer> subfieldReps) {
        group.add(getValuedFields(getFields(topfieldCodes, fieldReps), subfieldCodes, subfieldReps));
    }
    
    @Override
    public ValFieldGroup getResult() {
        if (group.getSize() == 1) {
            return group.getFirst();
        }
        return group;
    }
    
    
    
    
    private static boolean isAll(List<?> list) {
        return list.isEmpty();
    }
    
    private List<? extends ViewableField> getFields(List<String> topfieldCodes, List<Integer> fieldReps) {
        topfieldCodes = temporaryFixPotentiallyMissingFieldCodePrefixes(topfieldCodes);
        return filterAndSort(viewableDetail, topfieldCodes, fieldReps);
    }

    /**
     * https://support.kpsys.cz/issues/22178
     * Pote, co se vsechna pouziti query("{cislo}") preklopi na query("{a|d}{cislo}"), muzeme vyhodit (nebo jeste treba pak jeste chvili i logovat)
     */
    private List<String> temporaryFixPotentiallyMissingFieldCodePrefixes(List<String> topfieldCodes) {
        topfieldCodes = ListUtil.convertStrict(topfieldCodes, code -> FieldHelper.convertLegacyCodeToNewPrefixedCode(code, () -> {
            if (viewableDetail.streamFields().anyMatch(viewableField -> viewableField.getCode().equals(FieldTypes.DOCUMENT_LEADER_FIELD_CODE) || viewableField.getCode().equals(RecordWellKnownFields.DocumentTitle.CODE))) {
                return false;
            }
            return viewableDetail.streamFields().anyMatch(viewableField -> viewableField.getCode().equals(FieldTypes.AUTHORITY_LEADER_FIELD_CODE) || viewableField.getCode().equals(RecordWellKnownFields.AuthorityPersonName.CODE));
        }));
        return topfieldCodes;
    }

    private List<ViewableField> getValuedFields(List<? extends ViewableField> fields, List<String> subfieldCodes, List<Integer> subfieldReps) {
        List<ViewableField> valuedFields = new ArrayList<>();

        for (ViewableField f : fields) {
            if (f.getFields().isEmpty()) {
                //leader a controlfield pridame jen kdyz nejsou definovany kody podpoli ani opakovani podpoli
                if (isAll(subfieldCodes) && isAll(subfieldReps)) {
                    valuedFields.add(f);
                }
            } else {
                valuedFields.addAll(getSubfields(f, subfieldCodes, subfieldReps));
            }
        }
        
        return valuedFields;
    }
    
    private List<? extends ViewableField> getSubfields(ViewableField field, List<String> subfieldCodes, List<Integer> subfieldReps) {
        return filterAndSort(field, subfieldCodes, subfieldReps);
    }

    private List<? extends ViewableField> filterAndSort(ViewableFieldContainer parent, List<String> childCodes, List<Integer> childReps) {
        List<? extends ViewableField> subfields;
        if (isAll(childCodes)) {
            subfields = parent.getFields();
        } else {
            subfields = parent.getFields(By.anyCode(childCodes));
            subfields = ListUtil.filterAndSortByRule(subfields, childCodes, WithCode::getCode);
        }

        if (isAll(childReps)) {
            return subfields;
        }
        return ListUtil.filterAndSortByRule(subfields, childReps, WithRepetition::getRepetition);
    }
    
}
