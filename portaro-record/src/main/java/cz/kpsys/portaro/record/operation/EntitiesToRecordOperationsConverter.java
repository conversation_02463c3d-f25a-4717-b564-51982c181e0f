package cz.kpsys.portaro.record.operation;

import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.BatchFiller;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EntitiesToRecordOperationsConverter implements Converter<List<RecordOperationEntity>, List<? extends RecordOperation>> {

    @NonNull ByIdLoadable<RecordOperationType, Integer> recordOperationTypeLoader;
    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull AllByIdsLoadable<Record, UUID> nonDetailedRecordLoader;
    @NonNull AllByIdsLoadable<BasicUser, Integer> basicUserLoader;

    @Override
    public List<RecordOperation> convert(@NonNull List<RecordOperationEntity> entities) {
        Map<RecordOperationEntity, @NonNull Record> entityToRecordMap = BatchFiller.of(nonDetailedRecordLoader).load(entities, RecordOperationEntity::getRecordId);
        Map<RecordOperationEntity, @NonNull BasicUser> entityToUserMap = BatchFiller.of(basicUserLoader).load(entities, RecordOperationEntity::getUserId);

        return ListUtil.convert(entities, entity -> new RecordOperation(
                entity.getId(),
                entity.getEventId(),
                entityToRecordMap.get(entity),
                departmentLoader.getById(entity.getDepartmentId()),
                entityToUserMap.get(entity),
                recordOperationTypeLoader.getById(entity.getRecordOperationTypeId()),
                entity.getDate()
        ));
    }

}
