package cz.kpsys.portaro.record.view;

import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.web.UrlCreator;
import cz.kpsys.portaro.file.FileConstants;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordMediaViewerUrlGenerator<CTX> implements ContextualFunction<Record, CTX, @NullableNotBlank String> {

    @NonNull ContextualProvider<CTX, @NonNull String> serverUrlProvider;
    @NonNull ContextualProvider<CTX, Converter<UUID, @NonNull String>> recordIdToRecordDetailUrlConverterProvider;

    @NullableNotBlank
    @Override
    public String getOn(@NonNull Record record, CTX ctx) {
        if (record.getDirectoryId() == null) {
            return null;
        }

        return new UrlCreator()
                .serverBaseUrl(serverUrlProvider.getOn(ctx))
                .path(FileConstants.Web.MEDIA_VIEWER_PATH)
                .addParameter(FileConstants.Web.MEDIA_VIEWER_ROOT_DIR_PARAM, record.getDirectoryId())
                .addParameter(FileConstants.Web.MEDIA_VIEWER_ORIGIN_PARAM, recordIdToRecordDetailUrlConverterProvider.getOn(ctx).convert(record.getId()))
                .build();
    }
}
