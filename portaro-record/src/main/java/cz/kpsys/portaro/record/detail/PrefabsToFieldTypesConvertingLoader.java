package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.collection.ListCutter;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.SeveritedException;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.DataAccessException;
import cz.kpsys.portaro.commons.object.repo.SeveritedItemNotFoundException;
import cz.kpsys.portaro.datatype.DatatypeUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.fn.BackRef;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.fn.Ref;
import cz.kpsys.portaro.record.detail.fn.ResolvableFunction;
import cz.kpsys.portaro.record.detail.link.*;
import cz.kpsys.portaro.record.detail.link.def.*;
import cz.kpsys.portaro.record.detail.value.FieldValueConverter;
import cz.kpsys.portaro.record.detail.value.FieldValueConverterFactory;
import cz.kpsys.portaro.record.edit.FieldSettings;
import cz.kpsys.portaro.record.edit.FondedFieldSettingLoader;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cz.kpsys.portaro.record.detail.fn.Formulas.ref;
import static java.util.Objects.requireNonNull;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class PrefabsToFieldTypesConvertingLoader implements AllValuesProvider<FieldType<?>> {

    @NonNull Codebook<PrefabFieldType, FieldTypeId> prefabFieldTypeLoader;
    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;
    @NonNull FieldValueConverterFactory fieldValueConverterFactory;
    @NonNull FieldAcceptableValuesResolver fieldAcceptableValuesResolver;
    @NonNull Function<Fond, List<Fond>> enabledLoadableFondsExpander;
    @NonNull FondedFieldSettingLoader fondedFieldSettingLoader;
    @NonNull FieldStorageBehaviourResolver fieldStorageBehaviourResolver;

    private enum Kind {
        CONTROLFIELD,
        DATAFIELD,
        SUBFIELD
    }

    @Override
    public List<FieldType<?>> getAll() {
        List<PrefabFieldType> prefabs = prefabFieldTypeLoader.getAll();
        ListCutter<PrefabFieldType> fieldTypesCutter = ListCutter.ofCopyOf(prefabs);

        List<PrefabFieldType> prefabTopfields = fieldTypesCutter.cut(PrefabFieldType::isTopfield);

        Mapper mapper = new Mapper(prefabFieldTypeLoader, fieldTypesCutter);

        return prefabTopfields.stream()
                .map(mapper::map)
                .sorted(Comparator.comparing(FieldType::getCode))
                .collect(Collectors.toUnmodifiableList());
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    private class Mapper {

        @NonNull Codebook<PrefabFieldType, FieldTypeId> prefabFieldTypeLoader;
        @NonNull ListCutter<PrefabFieldType> subfieldTypesCutter;
        @NonNull Map<FieldTypeId, List<PrefabFieldType>> parentToSubfieldTypesCache;
        @NonNull DatatypeResolver datatypeResolver = new DatatypeResolver();

        public Mapper(@NonNull Codebook<PrefabFieldType, FieldTypeId> prefabFieldTypeLoader, @NonNull ListCutter<PrefabFieldType> subfieldTypesCutter) {
            this.prefabFieldTypeLoader = prefabFieldTypeLoader;
            this.subfieldTypesCutter = subfieldTypesCutter;
            this.parentToSubfieldTypesCache = subfieldTypesCutter.remainingItems().stream()
                    .collect(Collectors.groupingBy(prefabFieldType -> prefabFieldType.id().existingParent()));
        }

        private FieldType<?> map(@NonNull PrefabFieldType prefabFieldType) {
            return createFieldTypeIncludingSubfieldTypesRethrowing(prefabFieldType);
        }

        private FieldType<?> createFieldTypeIncludingSubfieldTypesRethrowing(@NonNull PrefabFieldType prefabFieldType) {
            try {
                return createFieldTypeIncludingSubfieldTypes(prefabFieldType);
            } catch (Exception e) {
                throw new DataAccessException("Cannot map field type %s: %s".formatted(prefabFieldType.id(), e.getMessage()), prefabFieldType.id().toString(), e);
            }
        }

        private FieldType<?> createFieldTypeIncludingSubfieldTypes(@NonNull PrefabFieldType prefab) {
            List<PrefabFieldType> prefabSubfieldTypes = subfieldTypesCutter.cut(prefabSubfield -> prefabSubfield.hasParent(prefab.id()));

            Kind fieldKind = getFieldKind(prefab);
            Optional<Formula<?>> formula = getFormula(prefab);
            Optional<ScalarDatatype> valueDatatype = datatypeResolver.getValueDatatype(prefab);
            Optional<Fond> linkRootFond = Optional.ofNullable(prefab.linkRootFondConstraint()).or(() -> FieldTypes.NO_LINK_FOND);
            Optional<Codebook<? extends LabeledIdentified<?>, ?>> codebook = switch (fieldKind) {
                case DATAFIELD -> FieldTypes.NO_CODEBOOK;
                case CONTROLFIELD, SUBFIELD -> fieldAcceptableValuesResolver.resolve(valueDatatype.orElseThrow());
            };
            Function<FieldType<?>, FieldValueConverter> converterFactory = switch (fieldKind) {
                case CONTROLFIELD -> _ -> FieldValueConverterFactory.createDefaultForControlfield();
                case DATAFIELD -> _ -> FieldValueConverterFactory.createDefaultForDatafield();
                case SUBFIELD -> fieldValueConverterFactory::createForSimpleSubfield;
            };
            FieldType<?> fieldType = new SimpleFieldType<>(
                    prefab.id(),
                    prefab.nativeName(),
                    prefab.virtualGroup(),
                    prefab.repeatable(),
                    prefab.autonomous(),
                    formula,
                    codebook,
                    prefab.fieldSource(),
                    prefab.transferType(),
                    valueDatatype,
                    linkRootFond,
                    prefab.fieldExportSetting(),
                    converterFactory,
                    prefab.pic(),
                    prefab.generation(),
                    fieldStorageBehaviourResolver.resolve(prefab.generation())
            );

            prefabSubfieldTypes.stream()
                    .map(this::createFieldTypeIncludingSubfieldTypesRethrowing)
                    .forEach(fieldType::addSubfieldType);

            return fieldType;
        }

        private @NonNull Kind getFieldKind(@NonNull PrefabFieldType prefab) {
            if (prefab.id().hasParent()) {
                return Kind.SUBFIELD;
            }
            List<PrefabFieldType> prefabSubfieldTypes = parentToSubfieldTypesCache.getOrDefault(prefab.id(), List.of());
            return prefabSubfieldTypes.isEmpty() ? Kind.CONTROLFIELD : Kind.DATAFIELD;
        }

        private @NonNull Optional<Formula<?>> getFormula(@NonNull PrefabFieldType prefab) {
            FieldTypeId id = prefab.id();
            return switch (prefab.linkDef()) {
                case NoLinkDef _ -> Optional.empty();
                case FormulaDef<?> def -> Optional.of(def.formula());
                case SelfRecordDef def -> Optional.of(ref(LookupDefinition.ofSelfRecord(def.targetFieldTypeId())));
                case SpecificFieldLinkDef def -> Optional.of(ref(LookupDefinition.ofSpecificFieldLink(def.linkFieldTypeId(), def.linkedRecordFieldTypeId())));
                case ParentVirtualGroupToCustomEntrySubfieldLinkDef def -> Optional.of(ref(LookupDefinition.ofVirtualGroupToCustomEntrySubfield(id.existingParent(), def.linkedRecordCustomEntrySubfieldCode())));
                case SimpleEntryCustomSubfieldLinkDef def -> Optional.of(ref(LookupDefinition.ofSelfLink(def.linkedRecordCustomEntrySubfieldCode())));
                case NativeNameLinkDef _ -> Optional.of(ref(LookupDefinition.ofSelfLinkNativeName()));
            };
        }

        @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
        @RequiredArgsConstructor
        private class DatatypeResolver {

            @NonNull Resolvings resolvings = new Resolvings();

            private Optional<ScalarDatatype> getValueDatatype(@NonNull PrefabFieldType prefab) {
                if (prefab.id().value().equals("d1641.c") || prefab.id().value().equals("d1642.c") || prefab.id().value().equals("d1650.c") || prefab.id().value().equals("d1651.c") || prefab.id().value().equals("d1656.c")) {
                    return Optional.of(CoreConstants.Datatype.NUMBER_DECIMAL_2);
                }
                FieldValueDatatypeResolving resolving = resolvings.getOrCreate(prefab);
                if (resolving.isFinished()) {
                    return resolving.finishedResultOrRethrowFail();
                }
                resolving.start();

                Kind fieldKind = getFieldKind(prefab);
                switch (fieldKind) {
                    case DATAFIELD -> resolving.successNoDatatype();
                    case CONTROLFIELD, SUBFIELD -> {
                        Optional<Formula<?>> formula = getFormula(prefab);
                        if (formula.isPresent()) {
                            resolving.wrap(() -> getFormulaResultDatatype(prefab, formula.get()), formula.get());
                        } else {
                            resolving.success(requireNonNull(prefab.explicitValueDatatype()));
                        }
                    }
                }
                return resolving.finishedResultOrRethrowFail();
            }

            private @NonNull ScalarDatatype getValueDatatype(FieldTypeId id, int missingErrorSeverity) {
                Optional<? extends PrefabFieldType> linkedType = prefabFieldTypeLoader.findById(id);
                if (linkedType.isEmpty()) {
                    throw new SeveritedItemNotFoundException(PrefabFieldType.class.getSimpleName(), id, missingErrorSeverity);
                }
                return getValueDatatype(linkedType.get()).orElseThrow(() -> new IllegalStateException("Empty value datatype of field type %s".formatted(id)));
            }

            private @NonNull ScalarDatatype getFormulaResultDatatype(@NonNull PrefabFieldType prefab, @NonNull Formula<?> formula) {
                return switch (formula) {
                    case ResolvableFunction<?> fn -> fn.resolveResultDatatype(f -> getFormulaResultDatatype(prefab, f));
                    case Ref<?>(LookupDefinition lookupDef) -> switch (lookupDef.linkedFieldSpec()) {
                        case CustomEntrySubfieldLinkedFieldSpec _,
                             NativeEntrySubfieldLinkedFieldSpec _ -> {
                            Set<Fond> possibleLinkRootFonds = getPossibleLinkRootFonds(prefab, lookupDef);
                            Set<FieldTypeId> allPossibleLinkedFieldTypes = getAllPossibleLinkedFieldTypes(possibleLinkRootFonds, lookupDef.linkedFieldSpec());
                            Set<ScalarDatatype> allPossibleDatatypes = getAllPossibleDatatypes(allPossibleLinkedFieldTypes);
                            yield DatatypeUtil.getLessPreciseDatatype(allPossibleDatatypes);
                        }
                        case SpecificLinkedFieldSpec spec -> getValueDatatype(spec.targetFieldTypeId(), SeveritedException.SEVERITY_ERROR);
                    };
                    case BackRef<?> backRef -> getValueDatatype(backRef.def().linkingRecordsValueFieldTypeId(), SeveritedException.SEVERITY_ERROR);
                };
            }

            private @NonNull Set<Fond> getPossibleLinkRootFonds(@NonNull PrefabFieldType prefab, @NonNull LookupDefinition lookupDef) {
                return switch (lookupDef.linkFieldSpec().linkFieldSearchMode()) {
                    case SELF_LINK -> Set.of(requireNonNull(prefab.linkRootFondConstraint(), () -> "Link field %s does not have linked record root fond constraint".formatted(prefab.id())));
                    case SELF_RECORD -> {
                        Map<Fond, FieldSettings> fondsContainingGivenPrefab = fondedFieldSettingLoader.getEffectiveFondedByFieldTypeId(prefab.id());
                        yield fondsContainingGivenPrefab.keySet();
                    }
                    case VIRTUAL_GROUP_FIELD_LINK, SPECIFIC_FIELD_LINK -> {
                        FieldTypeId linkFieldTypeId = lookupDef.linkFieldSpec().existingFieldTypeId();
                        PrefabFieldType linkFieldType = prefabFieldTypeLoader.getById(linkFieldTypeId);
                        yield Set.of(requireNonNull(linkFieldType.linkRootFondConstraint(), () -> "Link field %s does not have linked record root fond constraint".formatted(linkFieldType.id())));
                    }
                };
            }

            private @NonNull Set<FieldTypeId> getAllPossibleLinkedFieldTypes(@NonNull Set<Fond> possibleLinkRootFonds, @NonNull LinkedFieldSpec linkedFieldSpec) {
                Set<Fond> allRecordableSubfonds = possibleLinkRootFonds.stream()
                        .map(enabledLoadableFondsExpander)
                        .flatMap(Collection::stream)
                        .filter(Fond::isRecordable)
                        .collect(Collectors.toSet());
                Assert.notEmpty(allRecordableSubfonds, () -> "Cannot get all possible linked field types (of %s), because there are no recordable fond under (loadable) any of root fonds %s".formatted(linkedFieldSpec, possibleLinkRootFonds));
                return allRecordableSubfonds.stream()
                        .map(subfond -> recordEntryFieldTypeIdResolver.getLinkedFieldType(subfond, linkedFieldSpec))
                        .collect(Collectors.toUnmodifiableSet());
            }

            private @NonNull Set<ScalarDatatype> getAllPossibleDatatypes(Set<FieldTypeId> allPossibleLinkedFieldTypes) {
                Assert.notEmpty(allPossibleLinkedFieldTypes, "Cannot get all possible datatypes for empty set of linked field types");
                return allPossibleLinkedFieldTypes.stream()
                        .map(id -> getValueDatatype(id, SeveritedException.SEVERITY_WARNING))
                        .collect(Collectors.toUnmodifiableSet());
            }



            @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
            @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
            private static final class Resolvings {

                @NonNull HashMap<FieldTypeId, FieldValueDatatypeResolving> map = new HashMap<>();

                public FieldValueDatatypeResolving getOrCreate(@NonNull PrefabFieldType prefab) {
                    FieldTypeId id = prefab.id();
                    return map.computeIfAbsent(id, _ -> FieldValueDatatypeResolving.create());
                }
            }


        }

    }

}

