package cz.kpsys.portaro.record.creation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.NonNull;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public record CreateRecordsFromFilesCommand(

        @NonNull
        Fond fond,

        @NonNull
        List<MultipartFile> files,

        @NonNull
        Department ctx,

        @NonNull
        UserAuthentication currentAuth

) {
}
