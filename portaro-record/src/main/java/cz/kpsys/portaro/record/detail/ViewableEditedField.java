package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.record.LabeledRecordRef;
import jakarta.validation.constraints.NotBlank;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.UUID;

public record ViewableEditedField(

        @NonNull
        UUID id,

        @NonNull
        String code,

        @NonNull
        @NotBlank
        String idPath,

        @NonNull
        String typeId,

        @NonNull
        Text fieldTypeText,

        @NonNull
        List<ViewableEditedField> fields,

        @NonNull
        Text text,

        @Nullable
        LabeledRecordRef recordLink,

        @Nullable
        Object value

) implements IdentifiedRecord<UUID> {

    public static final String ID_PATH_DELIMITER = "/";
}
