package cz.kpsys.portaro.record.document;

import cz.kpsys.portaro.record.Record;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;

public class ByFondNameDocumentContentTypeResolver implements Function<Record, Optional<ContentType>> {

    private static final List<Function<String, Optional<ContentType>>> LOWERED_FOND_NAME_RESOLVERS = List.of(
            byOneOfInfixes(Set.of("monografie", "monography"), ContentType.BOOK),
            byOneOfStrings(Set.of("mapy", "maps"), ContentType.MAP),
            byOneOfInfixes(Set.of("články", "articles"), ContentType.ARTICLE),
            byOneOfStrings(Set.of("recenze", "review"), ContentType.REVIEW)
    );


    @Override
    public Optional<ContentType> apply(Record d) {
        String loweredFondName = d.getFond().getName().toLowerCase();
        for (Function<String, Optional<ContentType>> resolver : LOWERED_FOND_NAME_RESOLVERS) {
            Optional<ContentType> result = resolver.apply(loweredFondName);
            if (result.isPresent()) {
                return result;
            }
        }
        return Optional.empty();
    }


    private static Function<String, Optional<ContentType>> byOneOfStrings(Set<String> fondNames, ContentType contentType) {
        return byPredicate(contentType, fondNames::contains);
    }

    private static Function<String, Optional<ContentType>> byOneOfInfixes(Set<String> infixes, ContentType contentType) {
        return byPredicate(contentType, normalizedFondName -> infixes.stream().anyMatch(normalizedFondName::contains));
    }

    private static Function<String, Optional<ContentType>> byPredicate(ContentType contentType, Predicate<String> normalizedFondNamePredicate) {
        return normalizedFondName -> normalizedFondNamePredicate.test(normalizedFondName) ? Optional.of(contentType) : Optional.empty();
    }

}
