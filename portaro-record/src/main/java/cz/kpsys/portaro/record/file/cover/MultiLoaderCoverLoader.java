package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.LoadedFile;
import cz.kpsys.portaro.record.Record;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class MultiLoaderCoverLoader implements NamedCoverLoader {

    @Getter
    private final String serviceName;
    private final List<? extends CoverLoader> loaders;
    private boolean throwExceptions = true;

    public MultiLoaderCoverLoader(String serviceName, CoverLoader...loaders) {
        this.serviceName = serviceName;
        this.loaders = List.of(loaders);
    }
    
    public MultiLoaderCoverLoader(String serviceName, List<? extends CoverLoader> loaders) {
        this.serviceName = serviceName;
        this.loaders = loaders;
    }

    public void setThrowExceptions(boolean throwExceptions) {
        this.throwExceptions = throwExceptions;
    }
    
    
    
    @Override
    public LoadedFile getCover(Record record, Department currentDepartment) throws CoverSearchException {
        var loaderCopy = List.copyOf(loaders); // Just in case that someone changes active loaders
        for (CoverLoader loader : loaderCopy) {
            try {
                LoadedFile c = loader.getCover(record, currentDepartment);
                if (c != null) {
                    return c;
                }
            } catch (CoverSearchException e) {
                if (throwExceptions) {
                    throw e;
                }
                log.info("Cannot load cover by {}", loader.getClass().getSimpleName(), e);
            }
        }
        throw new CoverSearchException(serviceName, CoverSearchException.CHYBEJICI_OBALKA);
    }
    
}
