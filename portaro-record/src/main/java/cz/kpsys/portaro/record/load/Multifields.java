package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.collection.ListCutter;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.By;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import cz.kpsys.portaro.record.detail.spec.RecordIndexedId;
import cz.kpsys.portaro.record.detail.spec.RecordMultifieldId;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.edit.FieldDisplayType;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class Multifields {

    @NonNull RecordIdFondPair record;
    @NonNull RecordIndexedId id;
    @NonNull List<EditableFieldType<?>> fieldTypes;
    @NonNull Map<FieldTypeId, Multifield> items;
    @NonNull @NonFinal RecordSpecSet remainingExtraSpecs = RecordSpecSet.ofEmptyModifiable();

    public enum Ordering {
        NUMERICALLY_COMPATIBLE_ORDER,
        INSERTION_ORDER
    }

    public static Multifields create(@NonNull RecordIdFondPair record, @NonNull RecordIndexedId id, @NonNull List<EditableFieldType<?>> fieldTypes, @NonNull Ordering ordering) {
        Assert.state(id.recordIdFondPair().equals(record), () -> "Cannot create multifields for different record: " + id + " vs " + record);
        Map<FieldTypeId, Multifield> map = switch (ordering) {
            case NUMERICALLY_COMPATIBLE_ORDER -> new TreeMap<>(FieldTypeId.NUMERICALLY_COMPATIBLE_SORTER);
            case INSERTION_ORDER -> new LinkedHashMap<>();
        };
        return new Multifields(record, id, fieldTypes, map);
    }

    public static Multifields createWithMultifields(@NonNull RecordIdFondPair record, @NonNull RecordIndexedId id, @NonNull List<EditableFieldType<?>> fieldTypes, @NonNull Ordering ordering) {
        Multifields multifields = create(record, id, fieldTypes, ordering);
        multifields.createMultifields();
        return multifields;
    }

    private void createMultifields() {
        for (EditableFieldType<?> fieldType : fieldTypes) {
            if (FieldDisplayType.ALL_LOADABLE.contains(fieldType.getDisplayType()) && fieldType.getFieldStorageBehaviour().wholeFieldGenerated()) {
                getOrCreateMultifield(record, fieldType);
            }
        }
    }

    public void setRemainingExtraSpecs(@NonNull RecordSpecSet remainingExtraSpecs) {
        this.remainingExtraSpecs = remainingExtraSpecs;
    }

    public void addFields(@NonNull List<Field<?>> fields) {
        Assert.state(fields.stream().allMatch(field -> field.getRecordIdFondPair().equals(id.recordIdFondPair())), () -> "Some fields are for another record than record adding %s".formatted(id.recordIdFondPair()));
        ListCutter<Field<?>> loadedSubfields = ListCutter.ofCopyOf(fields); // nepouzivame SetCutter, protoze pole jsou serazena

        for (Multifield requiredMultifield : values()) {
            List<Field<?>> sameTypeSubfields = loadedSubfields.cut(By.type(requiredMultifield.fieldType()));
            requiredMultifield.addOrMerge(sameTypeSubfields);
        }

        if (loadedSubfields.hasRemainingItems()) {
            for (Field<?> field : loadedSubfields.remainingItems()) {
                addUndefinedField(field);
            }
        }
    }

    private void addUndefinedField(Field<?> field) {
        Multifield multifield = getOrCreateMultifield(field.getRecordIdFondPair(), field.getType());
        multifield.addOrMerge(field);
    }

    public @NonNull Multifield getOrCreateMultifield(@NonNull RecordIdFondPair record, @NonNull EditableFieldType<?> type) {
        return items.computeIfAbsent(type.getFieldTypeId(), _ -> {
            return Multifield.createEmpty(record, id.toSubmultifield(type.getFieldTypeId()), type);
        });
    }

    public RecordSpecSet updateLoadedAndGetNextSpecs(@NonNull LoadedRecords loadedRecords, @NonNull RecordSpecSet searchedRecordSpecs) {
        remainingExtraSpecs = remainingExtraSpecs.minus(searchedRecordSpecs);
        RecordSpecSet result = RecordSpecSet.ofCopyOf(remainingExtraSpecs);
        for (Multifield requiredMultifield : List.copyOf(values())) {
            result.addAll(requiredMultifield.updateLoadedAndGetNextSpecs(loadedRecords, searchedRecordSpecs));
        }
        return result;
    }

    public Optional<Multifield> find(@NonNull FieldTypeId fieldTypeId) {
        Multifield multifield = items.get(fieldTypeId);
        if (multifield == null) {
            return Optional.empty();
        }
        return Optional.of(multifield);
    }

    public List<Multifield> findMultifieldsByFieldTypeId(@NonNull FieldTypeId fieldTypeId) {
        Optional<Multifield> multifieldOpt = findTopMultifieldFor(fieldTypeId);
        if (multifieldOpt.isEmpty()) {
            return List.of();
        }
        Multifield multifield = multifieldOpt.get();
        if (multifield.fieldType().getFieldTypeId().equals(fieldTypeId)) {
            return List.of(multifield);
        }
        return multifield.findMultifieldsByFieldTypeId(fieldTypeId);
    }

    public Optional<FieldSlot> findSingleByFieldId(@NonNull RecordFieldId recordFieldId) {
        Optional<Multifield> multifield = findTopMultifieldFor(recordFieldId.fieldId().toFieldTypeId());
        if (multifield.isEmpty()) {
            return Optional.empty();
        }
        if (multifield.get().id().equals(recordFieldId.existingParentMultifieldId())) {
            return Optional.of(multifield.get().getSlot(recordFieldId.fieldId().getRepetition()));
        }
        return multifield.get().findSingleByFieldId(recordFieldId);
    }

    public List<FieldSlot> findAllByMultifieldId(@NonNull RecordMultifieldId recordMultifieldId) {
        Optional<Multifield> multifield = findTopMultifieldFor(recordMultifieldId.fieldTypeId());
        if (multifield.isEmpty()) {
            return List.of();
        }
        if (multifield.get().id().equals(recordMultifieldId)) {
            return multifield.get().getAllSlots();
        }
        return multifield.get().findAllByMultifieldId(recordMultifieldId);
    }

    private Optional<Multifield> findTopMultifieldFor(@NonNull FieldTypeId fieldTypeId) {
        FieldTypeId sameLevelWantedFieldTypeId = fieldTypeId.getOfLevel(id.nestingLevel() + 1);
        return find(sameLevelWantedFieldTypeId);
    }

    public boolean isEmpty() {
        return items.isEmpty();
    }

    public @NonNull Stream<Multifield> stream() {
        return values().stream();
    }

    public @NonNull Collection<Multifield> values() {
        return items.values();
    }

    public @NonNull RecordIndexedId id() {
        return id;
    }

    @Override
    public String toString() {
        String countString = items.isEmpty() ? "empty" : items.size() + " items";
        String itemsString = items.entrySet().stream().limit(5).map(Map.Entry::getValue).map(Multifield::toString).collect(Collectors.joining(", "));
        return "Multifields{" + id + " " + countString + (itemsString.isEmpty() ? "" : ": " + itemsString) + " ...}";
    }
}
