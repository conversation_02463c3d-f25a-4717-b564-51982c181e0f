package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.appserver.Appserver;
import cz.kpsys.portaro.appserver.AppserverRequest;
import cz.kpsys.portaro.appserver.XmlAppserverRequest;
import cz.kpsys.portaro.appserver.mapping.AppserverResponseHandler;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.util.TimeMeter;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.convert.MarcXmlToDetailConverter;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class AppserverRecordDetailLoader implements AllByIdsLoadable<IdentifiedFieldContainer, Record> {

    private static final String PATH = Appserver.APIPATH_DETAIL_DOKUMENTU_VRACENI;

    @NonNull MappingAppserverService mappingAppserver;
    boolean exportMode;
    @NonNull MarcXmlToDetailConverter marcXmlToDetailConverter;
    @NonNull ByIdLoadable<Fond, Integer> fondLoader;

    @Override
    public List<IdentifiedFieldContainer> getAllByIds(@NonNull List<Record> records) {
        if (records.isEmpty()) {
            return List.of();
        }

        AppserverResponseHandler<List<IdentifiedFieldContainer>> successResponseMapper = xml -> marcXmlToDetailConverter.convertMultipleRecords(marcRecord -> fondLoader.getById(marcRecord.getFondId().orElseThrow()), xml);

        AppserverRequest<?> request = XmlAppserverRequest.byGet(PATH)
                .withUriParam("portaro", "1")
                .withUriParam("uuid", StringUtil.listToStringOfIds(records, ","))
                .withUriParam("export", exportMode);

        return TimeMeter.measureAndLog(() -> mappingAppserver.call(request, successResponseMapper), log, () -> "Fetch detail of %s records: %s".formatted(records.size(), records), Duration.ofSeconds(5), Duration.ofSeconds(10));
    }
}
