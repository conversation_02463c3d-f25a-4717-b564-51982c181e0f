package cz.kpsys.portaro.record.bulkedit;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.bulkedit.complex.*;
import cz.kpsys.portaro.record.detail.By;
import cz.kpsys.portaro.record.detail.CreatableFieldContainer;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.value.StringValueCommand;
import cz.kpsys.portaro.record.edit.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.ConversionService;
import org.springframework.util.Assert;

import java.util.List;
import java.util.function.Predicate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RecordBulkeditor {

    @NonNull ConversionService conversionService;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordFieldEditor recordFieldEditor;


    public RecordEditation edit(RecordComplexBulkeditRequest request, Department ctx, UserAuthentication currentAuth) {
        Assert.state(request.reusing().size() == 1, "More than 1 record in complex bulk editation is not supported yet");
        Record record = conversionService.convert(request.reusing().getFirst().id(), Record.class);
        RecordEditation editation = createEditationForRecord(record, ctx, currentAuth);

        for (FieldRequest fieldRequest : request.value()) {
            List<Field<?>> affectedFields = getAffectedFields(fieldRequest, editation);
            for (Field<?> affectedField : affectedFields) {
                for (FieldRequest subfieldRequest : ((SubfieldsFieldValueRequest) fieldRequest.set().value()).fields()) {
                    List<Field<?>> affectedSubfields = getAffectedFields(subfieldRequest, affectedField);
                    for (Field<?> affectedSubfield : affectedSubfields) {
                        FieldEditationCommand command = FieldEditationCommand.of(editation.getRecord(), affectedSubfield.getFieldId(), new StringValueCommand(((StringFieldValueRequest) subfieldRequest.set().value()).value(), ctx, currentAuth))
                                .notCreateMissingHierarchy();
                        recordFieldEditor.editField(editation, command);
                    }
                }
            }
        }

        editation.saveIfModified(ctx, currentAuth);
        return editation;
    }

    private RecordEditation createEditationForRecord(Record record, Department ctx, UserAuthentication currentAuth) {
        return recordEditationFactory
                .on(ctx)
                .ofExisting(record)
                .build(currentAuth);
    }

    private List<Field<?>> getAffectedFields(FieldRequest fieldRequest, CreatableFieldContainer fieldContainer) {
        Predicate<Field<?>> fieldDeduplicationSelector = getDeduplicationSelector(fieldRequest.reusing());
        List<Field<?>> affectedFields = fieldContainer.getFields(fieldDeduplicationSelector);
        if (affectedFields.isEmpty()) {
            affectedFields = List.of(fieldContainer.createField(EmptyFieldCreation.toEnd(FieldTypeId.parse(fieldRequest.set().type()))));
        }
        return affectedFields;
    }

    private Predicate<Field<?>> getDeduplicationSelector(@NotNull @NotEmpty List<@Valid FieldReusingRequest> dedup) {
        Assert.state(dedup.size() == 1, "More than 1 dedup in complex bulk editation is not supported yet");
        Predicate<Field<?>> deduplicationSelector = field -> true;
        for (FieldReusingRequest fieldDedup : dedup) {
            if (fieldDedup.type() != null) {
                deduplicationSelector = deduplicationSelector.and(By.typeId(FieldTypeId.parse(fieldDedup.type())));
            }
            if (fieldDedup.havingField() != null) {
                deduplicationSelector = deduplicationSelector.and(By.havingSubfieldWithValue(FieldTypeId.parse(fieldDedup.havingField().type()), fieldDedup.havingField().value()));
            }
        }
        return deduplicationSelector;
    }
}
