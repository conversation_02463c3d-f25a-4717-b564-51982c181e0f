package cz.kpsys.portaro.record.detail.constraints;

import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;

public record LinkedRecordAsRelatedRecordConstraintDef(

        @NonNull MatchingRecordDef recordMatcher,
        @NonNull FieldTypeId linkFieldTypeId,
        @NonNull OnMissing onMissing

) implements RecordConstraintDef {

    public static LinkedRecordAsRelatedRecordConstraintDef throwWhenMissing(MatchingRecordDef matchingRecordDef, FieldTypeId linkFieldTypeId) {
        return new LinkedRecordAsRelatedRecordConstraintDef(matchingRecordDef, linkFieldTypeId, OnMissing.THROW);
    }

    public static LinkedRecordAsRelatedRecordConstraintDef allowWhenMissing(MatchingRecordDef matchingRecordDef, FieldTypeId linkFieldTypeId) {
        return new LinkedRecordAsRelatedRecordConstraintDef(matchingRecordDef, linkFieldTypeId, OnMissing.ALLOW);
    }

    public static LinkedRecordAsRelatedRecordConstraintDef forbidWhenMissing(MatchingRecordDef matchingRecordDef, FieldTypeId linkFieldTypeId) {
        return new LinkedRecordAsRelatedRecordConstraintDef(matchingRecordDef, linkFieldTypeId, OnMissing.FORBID);
    }

    @Override
    public String toString() {
        return "constrainedRecord.relatedRecord = {" + recordMatcher + "}:" + linkFieldTypeId + ":link";
    }

}
