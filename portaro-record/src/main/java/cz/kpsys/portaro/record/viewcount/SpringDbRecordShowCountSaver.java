package cz.kpsys.portaro.record.viewcount;

import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.database.JdbcTemplateUtils;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.view.RecordShowListener;
import cz.kpsys.portaro.sql.generator.InsertQuery;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import cz.kpsys.portaro.sql.generator.UpdateQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.time.Duration;
import java.util.Date;
import java.util.UUID;
import java.util.concurrent.ExecutorService;

import static cz.kpsys.portaro.databasestructure.RecordDb.OPAC_RATING.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SpringDbRecordShowCountSaver implements RecordShowListener {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull ExecutorService executorService;

    @Override
    public void recordShowed(Record record) {
        executorService.submit(() -> {
            try {
                Thread.sleep(Duration.ofSeconds(10));
                runUpdate(record.getId());
            } catch (Exception ex) {
                log.error("Error while record show count increment (record {})", record.getId(), ex);
            }
        });
    }


    private synchronized void runUpdate(UUID recordId) {
        //nejdrive zjistime jestli uz radek existuje (a pokud ano, zaroven zjistime pocetZobrazeni)
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(COUNTER);
        sq.from(OPAC_RATING);
        sq.where().eq(RECORD_ID, recordId);
        
        //pokud radek neexistuje, nastavime counter na -1
        int counter;
        try {
            counter = JdbcTemplateUtils.intResult(jdbcTemplate, sq);
        } catch (ItemNotFoundException | EmptyResultDataAccessException e) {
            counter = -1;
        }
        
        if (counter == -1) {
            //insertujeme
            InsertQuery iq = queryFactory.newInsertQuery();
            iq.insert(OPAC_RATING);
            iq.values().add(RECORD_ID, recordId);
            iq.values().add(COUNTER, 1);
            iq.values().add(DATUM, new Date());
            jdbcTemplate.update(iq.getSql(), iq.getParamMap());
            
        } else {
            //updatujeme
            UpdateQuery uq = queryFactory.newUpdateQuery();
            uq.update(OPAC_RATING);
            uq.set().add(COUNTER, counter + 1);
            uq.set().add(DATUM, new Date());
            uq.where().eq(RECORD_ID, recordId);
            jdbcTemplate.update(uq.getSql(), uq.getParamMap());
            
        }
    }
    
}
