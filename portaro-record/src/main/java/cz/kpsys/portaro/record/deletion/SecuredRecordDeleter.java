package cz.kpsys.portaro.record.deletion;

import cz.kpsys.portaro.record.RecordSecurityActions;
import cz.kpsys.portaro.security.SecurityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SecuredRecordDeleter implements RecordDeleter {

    @NonNull RecordDeleter delegate;
    @NonNull SecurityManager securityManager;

    @Override
    public void delete(RecordDeletionCommand command) {
        securityManager.throwIfCannot(RecordSecurityActions.RECORD_HOLDING_DELETE_OF_RECORD, command.currentAuth(), command.ctx(), command.record());
        delegate.delete(command);
    }
}
