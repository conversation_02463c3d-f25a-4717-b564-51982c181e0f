package cz.kpsys.portaro.record.print;

import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.detail.ViewableFieldContainer;
import cz.kpsys.portaro.record.detail.ViewableFieldable;
import cz.kpsys.portaro.record.query.ValFieldGroup;
import lombok.NonNull;

import java.util.Locale;

/**
 * Slouzi hlavne pro generovani citaci.
 * <br/>
 * Nevypisuje html a elementy, u autoritnich subfieldu vypisuje plain text, u subfieldu s url se vypisuje plain text url
 *
 * <AUTHOR>
 */
public class RecordDetailHtmlWithoutLinksPrinter extends RecordDetailPlainHtmlPrinter {

    public RecordDetailHtmlWithoutLinksPrinter() {
        super();
    }

    public RecordDetailHtmlWithoutLinksPrinter(Translator<Department> translator, Department currentDepartment, Locale locale) {
        super(translator, currentDepartment, locale, NO_BASE_URL_CONVERTER);
    }
    
    
    public static String printGroup(ValFieldGroup group) {
        return new RecordDetailHtmlWithoutLinksPrinter().group(group);
    }

    /**
     * Autoritni subfieldy se negeneruji jako linky
     */
    @Override
    public String writeRecordLinkSubfield(ViewableFieldable sf) {
        return getFieldText(sf);
    }

    /**
     * Url v subfieldech vypisuje normalne jako url
     */
    @Override
    public String writeUrlSubfield(@NonNull ViewableFieldable sf, @NonNull ViewableFieldContainer originalDetail) {
        return sf.getRaw();
    }
    
}
