package cz.kpsys.portaro.record.evaluation;

import cz.kpsys.portaro.commons.cache.CacheDeletableById;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.sql.generator.InsertQuery;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import cz.kpsys.portaro.sql.generator.UpdateQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.time.Instant;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbRatingLoaderAndSaver implements RatingLoader, RatingSaver {

    public static final String HODNOCENI = "OPAC_HODNOCENI";
    public static final String ID_OPAC_HODNOCENI = "ID_OPAC_HODNOCENI";
    public static final String SEQ_ID_OPAC_HODNOCENI = "SEQ_ID_OPAC_HODNOCENI";
    public static final String RECORD_ID = "RECORD_ID";
    public static final String POCET = "POCET";
    public static final String HODNOTA = "HODNOTA";
    public static final String NAPOSLEDY = "NAPOSLEDY";
    public static final int DOCUMENT_RATING_IS_DEFAULT = -1;

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull CacheDeletableById recordCacheDeletableById;
    @NonNull RowMapper<DocumentRating> documentRatingRowMapper = new DocumentRatingRowMapper();


    @Override
    public DocumentRating getByRecordId(@NonNull UUID recordId) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(HODNOCENI);
        sq.where().eq(RECORD_ID, recordId);
        try {
            return jdbcTemplate.queryForObject(sq.getSql(), sq.getParamMap(), documentRatingRowMapper);
        } catch (ItemNotFoundException | EmptyResultDataAccessException e) {
            return new DocumentRating(DOCUMENT_RATING_IS_DEFAULT, recordId, 0, 0.0, Instant.now());
        }
    }

    @Override
    public void saveRating(@NonNull UUID recordId, double value) {
        DocumentRating originalRating = getByRecordId(recordId);

        if (originalRating.getId() == DOCUMENT_RATING_IS_DEFAULT) {
            //dokument jeste nebyl ohodnocen -> insert
            InsertQuery iq = queryFactory.newInsertQuery();
            iq.insert(HODNOCENI);
            iq.values().addNow(NAPOSLEDY);
            iq.values().addSequence(ID_OPAC_HODNOCENI, SEQ_ID_OPAC_HODNOCENI);
            iq.values().add(RECORD_ID, recordId);
            iq.values().add(POCET, 1);
            iq.values().add(HODNOTA, value);
            jdbcTemplate.update(iq.getSql(), iq.getParamMap());

        } else {
            //dokument uz byl hodnocen -> update
            int newRatingCount = originalRating.getCount() + 1;
            double newRatingValue = (originalRating.getCount() * originalRating.getValue() + value) / newRatingCount;

            UpdateQuery uq = queryFactory.newUpdateQuery();
            uq.update(HODNOCENI);
            uq.set().addNow(NAPOSLEDY);
            uq.set().add(POCET, newRatingCount);
            uq.set().add(HODNOTA, newRatingValue);
            uq.where().eq(RECORD_ID, recordId);
            jdbcTemplate.update(uq.getSql(), uq.getParamMap());

        }

        if (recordCacheDeletableById instanceof CacheDeletableById) {
            ((CacheDeletableById) recordCacheDeletableById).deleteFromCacheById(recordId);
        }
    }
}
