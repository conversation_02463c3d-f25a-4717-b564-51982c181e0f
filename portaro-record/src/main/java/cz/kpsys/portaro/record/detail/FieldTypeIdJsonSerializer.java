package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.io.IOException;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldTypeIdJsonSerializer extends JsonSerializer<FieldTypeId> {

    @Override
    public void serialize(FieldTypeId fieldTypeId, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeString(fieldTypeId.value());
    }
}