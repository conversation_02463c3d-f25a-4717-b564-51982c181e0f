package cz.kpsys.portaro.record.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.NativeText;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.record.LabeledRecordRef;
import cz.kpsys.portaro.record.detail.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.jspecify.annotations.Nullable;

import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SingleValFieldGroup extends ValFieldGroup implements ViewableFieldable, Identified<Object> {

    @Getter
    @NonNull
    Object id;

    @JsonIgnore
    @Getter
    @NonNull
    FieldId fieldId;

    @Getter
    String parentCode;

    @Getter
    @NonNull
    String code;

    @Getter
    @NonNull
    Integer repetition;

    @Deprecated
    @Getter
    Integer fieldRepetition;

    @JsonIgnore
    @Getter
    @Nullable
    LabeledRecordRef recordLink;

    @Getter
    boolean url;

    @JsonIgnore
    @Getter
    @NonNull
    FieldTypeId fieldTypeId;

    @Getter
    @NonNull
    Text fieldTypeText;

    @JsonIgnore
    @Getter
    @Nullable
    UUID recordId;

    @NonNull
    @Getter
    @NonFinal
    String raw;

    @Getter
    @NonNull
    @NonFinal
    Text text;

    public SingleValFieldGroup(@NonNull ViewableFieldContainer originalDetail, @NonNull ViewableField singleField) {
        super(originalDetail);
        this.id = singleField.getId();
        this.fieldId = singleField.getFieldId();
        this.parentCode = singleField.getFieldTypeId().hasParent() ? singleField.getFieldTypeId().getParent().getCode() : null;
        this.code = singleField.getCode();
        this.repetition = singleField.getRepetition();
        this.fieldRepetition = singleField.getFieldRepetition();
        this.recordLink = singleField.getRecordLink();
        this.url = singleField.isUrl();
        this.fieldTypeId = singleField.getFieldTypeId();
        this.fieldTypeText = singleField.getFieldTypeText();
        this.recordId = singleField.getRecordId();
        this.raw = StringUtil.notNullString(singleField.getRaw());
        this.text = singleField.getText();
    }


    @Override
    public List<SingleValFieldGroup> getAll() {
        return List.of(this);
    }

    @Override
    protected Stream<SingleValFieldGroup> stream() {
        return Stream.of(this);
    }

    @Override
    public boolean isEmpty() {
        return raw.isEmpty();
    }

    @JsonIgnore
    @Override
    public @NonNull FieldTypeId fieldTypeId() {
        throw new IllegalStateException("Method fieldTypeId is deprecated in template engine");
    }

    @JsonIgnore
    @Override
    public @NonNull FieldType<?> getType() {
        throw new IllegalStateException("Method getType is forbidden to use in template engine");
    }

    public void modify(String newRaw) {
        String textString = text instanceof NativeText ? ((NativeText) text).getValue() : text.toString();
        if (ObjectUtil.nullSafeEquals(textString, raw)) { //text je stejny jako raw, takze zmenime i text
            text = Texts.ofNativeOrEmpty(newRaw);
        }
        raw = StringUtil.notNullString(newRaw);
    }

}
