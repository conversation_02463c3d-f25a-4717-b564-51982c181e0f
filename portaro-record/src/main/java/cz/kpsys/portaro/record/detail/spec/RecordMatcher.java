package cz.kpsys.portaro.record.detail.spec;

import cz.kpsys.portaro.commons.util.SealedComparator;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.Comparator;

public sealed interface RecordMatcher permits RecordIdentifierRecordMatcher, LinkingRecordRecordMatcher {

    SealedComparator<RecordMatcher> COMPARATOR = new SealedComparator<RecordMatcher>()
            .registerSubtypeComparator(RecordIdentifierRecordMatcher.class, 1, Comparator.comparing(RecordIdentifierRecordMatcher::recordIdFondPair))
            .registerSubtypeComparator(LinkingRecordRecordMatcher.class, 2, Comparator.comparing(LinkingRecordRecordMatcher::linkingRecordsLinkFieldTypeId, FieldTypeId.NUMERICALLY_COMPATIBLE_SORTER));

    @Deprecated
    @Nullable
    RecordIdFondPair recordIdFondPair();

    boolean matches(@NonNull Field<?> field);
}
