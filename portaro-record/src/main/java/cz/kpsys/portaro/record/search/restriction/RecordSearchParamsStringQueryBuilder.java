package cz.kpsys.portaro.record.search.restriction;

import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.fond.FondType;
import cz.kpsys.portaro.record.fond.FondTypeResolver;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.SearchParams;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.lucene.StringQueryBuilder;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Disjunction;
import cz.kpsys.portaro.search.restriction.FalseRestriction;
import cz.kpsys.portaro.search.restriction.Restriction;
import cz.kpsys.portaro.search.restriction.modifier.RestrictionModifier;
import cz.kpsys.portaro.search.restriction.modifier.TypedRestrictionModifier;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.ArrayList;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordSearchParamsStringQueryBuilder<PARAMS extends MapBackedParams> implements StringQueryBuilder<PARAMS> {

    @NonNull Converter<Restriction<? extends SearchField>, String> restrictionToQueryConverter;
    @NonNull Codebook<Fond, Integer> fondLoader;
    @NonNull List<TypedRestrictionModifier<?>> rootConjunctionModifiers = new ArrayList<>();
    @NonNull List<TypedRestrictionModifier<?>> documentConjunctionModifiers = new ArrayList<>();
    @NonNull List<TypedRestrictionModifier<?>> authorityConjunctionModifiers = new ArrayList<>();


    /**
     * Adds additional restriction-creating function which adds created restriction to the resulting conjunction
     */
    public <ADD_PARAMS extends SearchParams> RecordSearchParamsStringQueryBuilder<PARAMS> withRestrictionModifierFor(Class<ADD_PARAMS> paramsClass, RestrictionModifier<ADD_PARAMS> modifier) {
        rootConjunctionModifiers.add(new TypedRestrictionModifier<>(paramsClass, modifier));
        return this;
    }

    public <ADD_PARAMS extends SearchParams> RecordSearchParamsStringQueryBuilder<PARAMS> withDocumentConjunctionModifier(Class<ADD_PARAMS> paramsClass, RestrictionModifier<ADD_PARAMS> modifier) {
        documentConjunctionModifiers.add(new TypedRestrictionModifier<>(paramsClass, modifier));
        return this;
    }

    public <ADD_PARAMS extends SearchParams> RecordSearchParamsStringQueryBuilder<PARAMS> withAuthorityConjunctionModifier(Class<ADD_PARAMS> paramsClass, RestrictionModifier<ADD_PARAMS> modifier) {
        authorityConjunctionModifiers.add(new TypedRestrictionModifier<>(paramsClass, modifier));
        return this;
    }


    @Override
    public String buildFinalQuery(PARAMS p, Department ctx) {
        Restriction<? extends SearchField> finalRestriction = buildFinalRestriction(p, ctx);
        return restrictionToQueryConverter.convert(finalRestriction);
    }


    protected Conjunction<SearchField> buildDocumentConjunction(PARAMS p, Department ctx) {
        Conjunction<SearchField> conjunction = new Conjunction<>();
        return modify(documentConjunctionModifiers, conjunction, p, ctx);
    }


    protected Conjunction<SearchField> buildAuthorityConjunction(PARAMS p, Department ctx) {
        Conjunction<SearchField> conjunction = new Conjunction<>();
        return modify(authorityConjunctionModifiers, conjunction, p, ctx);
    }


    public Restriction<? extends SearchField> buildFinalRestriction(PARAMS p, Department ctx) {
        Conjunction<SearchField> rootConjunction = new Conjunction<>();

        rootConjunction.addIfNotNull(p.get(CoreSearchParams.QT));


        Disjunction<SearchField> documentAndAuthorityDisjunction = new Disjunction<>();

        //build document/authority restrictions
        if (p.hasNotNull(RecordConstants.SearchParams.FOND)) { // there don't have to be FOND (e.g. when searching SDI, there is only raw restriction)
            List<Fond> fonds = p.get(RecordConstants.SearchParams.FOND);
            buildRestrictions(p, ctx, fonds, documentAndAuthorityDisjunction, rootConjunction);
        }

        //build document/authority restrictions
        if (p.hasNotNull(RecordConstants.SearchParams.FOND_TYPE)) { // there don't have to be FOND (e.g. when searching SDI, there is only raw restriction)
            FondType fondType = p.get(RecordConstants.SearchParams.FOND_TYPE);
            List<Fond> fonds = ListUtil.filter(fondLoader.getAll(), fondType::matches);
            buildRestrictions(p, ctx, fonds, documentAndAuthorityDisjunction, rootConjunction);
        }

        rootConjunction = modify(rootConjunctionModifiers, rootConjunction, p, ctx);

        return rootConjunction;
    }

    private void buildRestrictions(PARAMS p, Department ctx, List<Fond> fonds, Disjunction<SearchField> documentAndAuthorityDisjunction, Conjunction<SearchField> rootConjunction) {
        if (fonds.stream().anyMatch(FondTypeResolver::isDocumentFond)) {
            Conjunction<SearchField> documentConjunction = buildDocumentConjunction(p, ctx);
            documentAndAuthorityDisjunction.addIf(!documentConjunction.isEmpty(), documentConjunction);
        }

        if (fonds.stream().anyMatch(FondTypeResolver::isAuthorityFond)) {
            Conjunction<SearchField> authorityConjunction = buildAuthorityConjunction(p, ctx);
            documentAndAuthorityDisjunction.addIf(!authorityConjunction.isEmpty(), authorityConjunction);
        }

        if (documentAndAuthorityDisjunction.isEmpty() && !p.hasNotNull(CoreSearchParams.FINAL_RAW_QUERY)) { // pokud posilame FINAL_RAW_QUERY, fondy tam uz nedavame - pak ale chceme normalne vyhledavat.
            rootConjunction.add(new FalseRestriction<>());
        } else {
            if (documentAndAuthorityDisjunction.getItems().size() == 1) {
                rootConjunction.add(documentAndAuthorityDisjunction.getItems().getFirst());
            } else {
                rootConjunction.add(documentAndAuthorityDisjunction);
            }
        }
    }


    private Conjunction<SearchField> modify(List<TypedRestrictionModifier<?>> modifiers, Conjunction<SearchField> root, SearchParams params, Department ctx) {
        for (TypedRestrictionModifier<?> modifier : modifiers) {
            root = modifier.modify(root, params, ctx);
        }
        return root;
    }


}
