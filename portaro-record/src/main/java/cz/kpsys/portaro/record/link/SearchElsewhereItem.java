package cz.kpsys.portaro.record.link;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import cz.kpsys.portaro.commons.convert.JsonToObjectConverter;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.util.UrlUtils;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.extract.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SearchElsewhereItem {

    @NonNull String name;
    @NonNull String urlTemplate;
    @NonNull RecordValueExtractor recordNormalizedIsbnExtractor = new RecordNormalizedIsbnExtractor();
    @NonNull RecordValueExtractor recordNormalizedIssnExtractor = new RecordNormalizedIssnExtractor();
    @NonNull RecordValueExtractor recordRawIsbnExtractor = new RecordRawIsbnExtractor();
    @NonNull RecordValueExtractor recordCcnbExtractor = new RecordCcnbExtractor();
    @NonNull RecordValueExtractor recordNameExtractor = new RecordNameExtractor();

    public static Converter<String, List<SearchElsewhereItem>> createConverter() {
        return new JsonToObjectConverter<>(new TypeReference<>() {});
    }

    @JsonCreator
    public SearchElsewhereItem(@JsonProperty("name") @NonNull String name,
                               @JsonProperty("template") @NonNull String urlTemplate) {
        this.name = name;
        this.urlTemplate = urlTemplate;
    }

    public String getUrl(Record record) {
        String url = urlTemplate;

        if (url.contains("{isbn}")) {
            var isbns = recordNormalizedIsbnExtractor.extractValue(record).toList();
            if (isbns.isEmpty()) {
                return null;
            }
            url = url.replace("{isbn}", UrlUtils.encodeUrlParameter(ListUtil.getFirst(isbns)));
        }

        if (url.contains("{rawIsbn}")) {
            var rawIsbns = recordRawIsbnExtractor.extractValue(record).toList();
            if (rawIsbns.isEmpty()) {
                return null;
            }
            url = url.replace("{rawIsbn}", UrlUtils.encodeUrlParameter(ListUtil.getFirst(rawIsbns)));
        }

        if (url.contains("{issn}")) {
            var issns = recordNormalizedIssnExtractor.extractValue(record).toList();
            if (issns.isEmpty()) {
                return null;
            }
            url = url.replace("{issn}", UrlUtils.encodeUrlParameter(ListUtil.getFirst(issns)));
        }

        if (url.contains("{ccnb}")) {
            var ccnbs = recordCcnbExtractor.extractValue(record).toList();
            if (ccnbs.isEmpty()) {
                return null;
            }
            url = url.replace("{ccnb}", UrlUtils.encodeUrlParameter(ListUtil.getFirst(ccnbs)));
        }

        if (url.contains("{name}")) {
            var names = recordNameExtractor.extractValue(record).toList();
            if (names.isEmpty()) {
                return null;
            }
            url = url.replace("{name}", UrlUtils.encodeUrlParameter(ListUtil.getFirst(names)));
        }

        return url;
    }

    @NonNull
    public String getName() {
        return name;
    }

    public boolean isShowable(Record d) {
        return StringUtil.hasLength(getUrl(d));
    }

}
