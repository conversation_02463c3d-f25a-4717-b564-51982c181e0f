package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.record.detail.source.FieldSource;
import cz.kpsys.portaro.record.detail.value.FieldValueConverterFactory;
import cz.kpsys.portaro.record.detail.value.StringFieldValue;

public class TestingFieldTypeFactory {

    private static final FieldStorageBehaviourResolver fieldStorageBehaviourResolver = new FieldStorageBehaviourResolver();

    public static SimpleFieldType<StringFieldValue> controlfieldType(FieldTypeId id) {
        return new SimpleFieldType<>(
                id,
                id.toString(),
                false,
                true,
                true,
                FieldTypes.EMPTY_FORMULA,
                FieldTypes.NO_CODEBOOK,
                FieldSource.common(),
                TransferType.OVERWRITE,
                FieldTypes.TEXT_DATATYPE,
                FieldTypes.NO_LINK_FOND,
                FieldExportSetting.toField(id),
                _ -> FieldValueConverterFactory.createDefaultForControlfield(),
                FieldTypes.NO_PIC,
                FdefGeneration.PHYSICAL,
                fieldStorageBehaviourResolver.resolve(FdefGeneration.PHYSICAL)
        );
    }

    public static SimpleFieldType<?> datafieldType(FieldTypeId id, String nativeName) {
        return new SimpleFieldType<>(
                id,
                nativeName,
                false,
                true,
                true,
                FieldTypes.EMPTY_FORMULA,
                FieldTypes.NO_CODEBOOK,
                FieldSource.common(),
                TransferType.OVERWRITE,
                FieldTypes.NO_DATATYPE,
                FieldTypes.NO_LINK_FOND,
                FieldExportSetting.toField(id),
                _ -> FieldValueConverterFactory.createDefaultForDatafield(),
                FieldTypes.NO_PIC,
                FdefGeneration.PHYSICAL,
                fieldStorageBehaviourResolver.resolve(FdefGeneration.PHYSICAL)
        );
    }

    public static SimpleFieldType<?> datafieldType(FieldTypeId id) {
        return datafieldType(id, "df_" + id);
    }

    public static SimpleFieldType<?> standardSubfieldType(FieldTypeId id) {
        FieldTypeId configPath = id.existingParent().getCode().equals(FieldTypes.VIRTUAL_GROUP_FIELD_CODE) ? id.existingParent().withCode(id.getCode()) : id;
        FieldTypeId exportedFieldTypeId = configPath;
        return subfieldType(id, "Testing field %s".formatted(id), FieldExportSetting.toSubfield(exportedFieldTypeId), FdefGeneration.PHYSICAL);
    }

    public static SimpleFieldType<?> subfieldType(FieldTypeId id, String nativeName, FieldExportSetting exportSetting, FdefGeneration generation) {
        return new SimpleFieldType<>(
                id,
                nativeName,
                false,
                true,
                true,
                FieldTypes.EMPTY_FORMULA,
                FieldTypes.NO_CODEBOOK,
                FieldSource.common(),
                TransferType.OVERWRITE,
                FieldTypes.TEXT_DATATYPE,
                FieldTypes.NO_LINK_FOND,
                exportSetting,
                _ -> FieldValueConverterFactory.createDefaultForSimpleSubfield(),
                FieldTypes.NO_PIC,
                generation,
                fieldStorageBehaviourResolver.resolve(generation)
        );
    }

}
