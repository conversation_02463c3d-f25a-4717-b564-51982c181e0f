package cz.kpsys.portaro.record.detail.dflt;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CompositeDefaultFieldValueResolver implements DefaultFieldValueResolver {

    @NonNull List<DefaultFieldValueResolver> resolvers;

    @Override
    public Optional<@NotBlank String> findOn(@NonNull FieldTypeId fieldTypeId, @NonNull Fond fond, @NonNull Department ctx) {
        return resolvers.stream()
                .map(resolver -> resolver.findOn(fieldTypeId, fond, ctx))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .findFirst();
    }

}
