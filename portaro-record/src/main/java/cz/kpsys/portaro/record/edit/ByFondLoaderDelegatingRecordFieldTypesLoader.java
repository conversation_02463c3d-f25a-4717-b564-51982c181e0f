package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ByFondLoaderDelegatingRecordFieldTypesLoader implements RecordFieldTypesLoader {

    @NonNull FieldTypesByFondLoader fieldTypesByFondLoader;

    @Override
    public List<EditableFieldType<?>> getFieldTypesByRecord(@NonNull Record record) throws EmptyWorksheetException {
        Fond fond = Objects.requireNonNull(record.getFond(), "Record must have not-null fond to get editable field types");
        List<EditableFieldType<?>> fieldTypesByFond = fieldTypesByFondLoader.getTopfieldTypesByFond(fond);
        if (fieldTypesByFond.isEmpty()) {
            throw new EmptyWorksheetException("Cannot load field types for record %s: Editable field type list (style/worksheet) is empty for fond %s".formatted(record, fond), fond);
        }
        return fieldTypesByFond;
    }
}
