package cz.kpsys.portaro.record.fond;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Enablable;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.OrderedNamedLabeledIdentified;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static cz.kpsys.portaro.record.RecordWellKnownFields.AuthorityPersonName;
import static cz.kpsys.portaro.record.RecordWellKnownFields.DocumentTitle;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
public class Fond extends OrderedNamedLabeledIdentified<Integer> implements Identified<Integer>, Serializable, LabeledIdentified<Integer>, Enablable {

    public static final int MONOGRAPHY_FOND_ID = 1;
    public static final int PERIODICAL_FOND_ID = 3;

    public static final String SCHEMA_EXAMPLE_MONOGRAPHY_ID = "1";

    @NonNull public static OrderedIdentifiedComparator<Fond, Integer> COMPARATOR = new OrderedIdentifiedComparator<>();

    @Nullable
    Integer parentId;

    @JsonIgnore
    @Nullable
    @NullableNotBlank
    String binding;

    boolean ofAuthority;
    boolean enabled;
    boolean editable;
    boolean periodical;
    boolean recordable;
    boolean threadable;

    @JsonIgnore
    @NonNull
    FondType fondType;

    @Nullable
    FieldTypeId entryFieldTypeConfigId;

    Integer z39Typ;
    String documentCategories;
    String documentTypes;
    boolean withExemplars;
    String recordType;
    String bibliographicLevel;
    boolean forSourceDocument;
    boolean adoptingLinked;
    boolean adoptingEdited;

    public Fond(Integer id,
                String name,
                Integer order,
                @Nullable Integer parentId,
                @Nullable String binding,
                boolean ofAuthority,
                boolean enabled,
                boolean editable,
                boolean periodical,
                boolean recordable, @NonNull FondType fondType,
                @Nullable FieldTypeId entryFieldTypeConfigId,
                Integer z39Typ,
                String documentCategories,
                String documentTypes,
                boolean withExemplars,
                String recordType,
                String bibliographicLevel,
                boolean forSourceDocument,
                boolean adoptingLinked,
                boolean adoptingEdited) {
        super(id, name, order);
        this.parentId = parentId;
        this.binding = binding;
        this.ofAuthority = ofAuthority;
        this.enabled = enabled;
        this.editable = editable;
        this.periodical = periodical;
        this.recordable = recordable;
        this.fondType = fondType;
        this.entryFieldTypeConfigId = entryFieldTypeConfigId;
        this.z39Typ = z39Typ;
        this.documentCategories = documentCategories;
        this.documentTypes = documentTypes;
        this.withExemplars = withExemplars;
        this.recordType = recordType;
        this.bibliographicLevel = bibliographicLevel;
        this.forSourceDocument = forSourceDocument;
        if (forSourceDocument) {
            Assert.state(ofAuthority, "Fond %d must be of authority when forSourceDocument".formatted(id));
        }
        this.adoptingLinked = adoptingLinked;
        this.adoptingEdited = adoptingEdited;
        this.threadable = fondType != FondType.THREAD;
    }

    public static Fond testingPerson() {
        return new Fond(31, "Person", 31, null, null, true, true, true, false, true, FondType.AUTHORITY, AuthorityPersonName.Name.CONFIG_TYPE_ID, null, null, null, false, null, null, false, true, true);
    }

    public static Fond testingTopic() {
        return new Fond(51, "Topic", 31, null, null, true, true, true, false, true, FondType.AUTHORITY, AuthorityPersonName.Name.CONFIG_TYPE_ID, null, null, null, false, null, null, false, true, true);
    }

    public static Fond testingPerio() {
        return new Fond(3, "Perio", 3, null, null, false, true, true, true, true, FondType.DOCUMENT, DocumentTitle.Name.CONFIG_TYPE_ID, null, null, null, true, null, null, false, true, true);
    }

    public static Fond testingMonography() {
        return new Fond(1, "Monography", 1, null, null, false, true, true, false, true, FondType.DOCUMENT, DocumentTitle.Name.CONFIG_TYPE_ID, null, null, null, true, null, null, false, true, true);
    }


    public boolean isOfDocument() {
        return !ofAuthority;
    }

    public boolean hasEntryFieldType() {
        return entryFieldTypeConfigId != null;
    }

    public @NonNull FieldTypeId existingEntryFieldTypeConfigId() {
        return Objects.requireNonNull(entryFieldTypeConfigId, () -> "Fond %d has no entry field type".formatted(getId()));
    }


    @Override
    public Text getText() {
        return Texts.ofColumnMessageCodedOrNative(getName(), "DEF_FOND", "NAZEV", getId());
    }

    @JsonIgnore
    public FieldTypeId createFieldTypeId(@NonNull String topfieldNumber) {
        return FieldTypeId.recordField(isOfAuthority(), topfieldNumber);
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof final Fond other)) {
            return false;
        }
        return (this.getId() == null) ? (other.getId() == null) : this.getId().equals(other.getId());
    }

    @Override
    public String toString() {
        return "Fond #" + getId() + " (" + getName() + ")";
    }


    public static Optional<Fond> getFirstDocumentFond(Collection<Fond> fonds) {
        return fonds.stream()
                .filter(Fond::isOfDocument)
                .findFirst();
    }

    public static List<Fond> filterDocumentFonds(List<Fond> fonds) {
        return ListUtil.filter(fonds, Fond::isOfDocument);
    }

    public static List<Fond> filterAuthorityFonds(List<Fond> fonds) {
        return ListUtil.filter(fonds, Fond::isOfAuthority);
    }

    public static List<Fond> filterRecordable(List<Fond> fonds) {
        return ListUtil.filter(fonds, Fond::isRecordable);
    }

    public static List<Fond> filterExemplarable(List<Fond> fonds) {
        return ListUtil.filter(fonds, Fond::isWithExemplars);
    }

    public static List<Fond> filterPeriodical(List<Fond> fonds) {
        return ListUtil.filter(fonds, Fond::isPeriodical);
    }

    public static List<Fond> filterNonperiodical(List<Fond> fonds) {
        return ListUtil.filter(fonds, fond -> !fond.isPeriodical());
    }

    public static List<Fond> filterNonperiodicalDocument(List<Fond> fonds) {
        return ListUtil.filter(fonds, fond -> fond.isOfDocument() && !fond.isPeriodical());
    }

    public List<Fond> createThisAndGivenFondsList(@NonNull List<Fond> fonds) {
        return ListUtil.createNewListPrepending(this, fonds);
    }

    public boolean is(@NonNull FondType fondType) {
        return this.fondType == fondType;
    }
}
