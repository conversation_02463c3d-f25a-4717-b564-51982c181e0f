package cz.kpsys.portaro.record.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.print.RecordDetailPlainHtmlPrinter;
import cz.kpsys.portaro.record.print.RecordDetailRawPrinter;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.Nullable;

import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Stream;

/**
 * Skupina poli s hodnotou (kontrolnich poli a podpoli).
 */
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public abstract class ValFieldGroup implements Iterable<SingleValFieldGroup> {

    @JsonIgnore
    @Getter
    @NonNull
    ViewableFieldContainer originalDetail;

    public abstract List<SingleValFieldGroup> getAll();

    protected Stream<SingleValFieldGroup> stream() {
        return getAll().stream();
    }
    

    public SingleValFieldGroup getSingle(int index) {
        if (index >= getSize()) {
            throw new IllegalArgumentException("Index is out of bounds of valfieldgroup");
        }
        if (getAll().isEmpty()) {
            throw new IllegalArgumentException("Valfieldgroup is empty");
        }
        return getAll().get(index);
    }


    public ValFieldGroup get(int index) {
        if (index >= getSize()) {
            return new MultipleValFieldGroup(originalDetail);
        }
        return getAll().get(index);
    }
    
    
    /**
     * Nikdy nevraci null, maximalne prazdny SingleValFieldGroup.
     */
    public ValFieldGroup getFirst() {
        return get(0);
    }

    
    public int getSize() {
        return getAll().size();
    }
    
    
    public boolean isEmpty() {
        return getAll().stream().allMatch(ValFieldGroup::isEmpty);
    }
    
    
    public boolean isAny() {
        return !isEmpty();
    }
    

    @Deprecated
    public boolean containsField(int fieldNumber) {
        log.warn("Deprecated usage of {}.containsField({}) ! Use containsOfCode()", getClass().getSimpleName(), fieldNumber);
        return getAll().stream().anyMatch(By.code(FieldTypeId.NEW_DOCUMENT_NUMBER_PREFIX + fieldNumber)); // tady si tipujeme, ze se jedna o dokument
    }

    public boolean containsOfCode(String code) {
        return getAll().stream().anyMatch(By.code(code));
    }
    
    
    public void add(List<ViewableField> valfields) {
        getAll().addAll(ListUtil.convertStrict(valfields, singleField -> new SingleValFieldGroup(originalDetail, singleField)));
    }
    
    
    public String getRaw() {
        return RecordDetailRawPrinter.printGroup(this);
    }


    @JsonIgnore
    @Nullable
    public UUID getRecordId() {
        return null;
    }
    
    public String getTrimmedValidedRaw() {
        return RecordDetailPlainHtmlPrinter.printGroup(this);
    }
    
    @Override
    public Iterator<SingleValFieldGroup> iterator() {
        return getAll().iterator();
    }
    
    @Override
    public String toString() {
        return getRaw();
    }
    
    
    
    public boolean contains(final String infix) {
        return ListUtil.isAnyTrue(getAll(), single -> single.getRaw().contains(infix));
    }


    public boolean startsWith(final String prefix) {
        return ListUtil.isAnyTrue(getAll(), single -> single.getRaw().startsWith(prefix));
    }


    public ValFieldGroup replaceAll(String regex, String replacement) {
        for (SingleValFieldGroup single : this) {
            single.modify(single.getRaw().replaceAll(regex, replacement));
        }
        return this;
    }


    public ValFieldGroup replaceFirst(String regex, String replacement) {
        for (SingleValFieldGroup single : this) {
            single.modify(single.getRaw().replaceFirst(regex, replacement));
        }
        return this;
    }


    public ValFieldGroup getSiblingsOfCode(@NullableNotBlank String code) {
        List<ViewableField> resultFields = stream()
                .flatMap(single -> createCommonParentFinder(single, code).findIn(originalDetail))
                .toList();
        return new MultipleValFieldGroup(originalDetail, resultFields);
    }


    /**
     * Oseka na koncich lomitka, dvojtecky, rovnitka, carky a stredniky.
     */
    public ValFieldGroup purify() {
        for (SingleValFieldGroup single : this) {
            String s = single.getRaw();
            s = s.trim();
            Set<String> blacklist = Set.of("/", ":", "=", ",", ";", ".");
            Set<String> allowedAbbreviations = Set.of(
                    "kol.", "ed.", "red.", "rev.", "překl.", "přel.", "il.", "fot.", "graf.", "vyd.", "komp.", "zprac.",
                    "aut.", "scén.", "rež.", "hud.", "např.", "apod.", "atd.", "aj.", "et al.", "trans.", "comp.", "dir."
            );
            String lower = s.toLowerCase();

            boolean endsWithAbbreviation = false;
            for (String abbr : allowedAbbreviations) {
                if (lower.endsWith(abbr)) {
                    endsWithAbbreviation = true;
                    break;
                }
            }

            if (!endsWithAbbreviation) {
                while (!s.isEmpty() && blacklist.contains(String.valueOf(s.charAt(s.length() - 1)))) {
                    s = s.substring(0, s.length() - 1).trim();
                }
            }
            single.modify(s);
        }
        return this;
    }

    
    public ValFieldGroup filter(Predicate<? super SingleValFieldGroup> filter) {
        List<SingleValFieldGroup> collect = getAll().stream().filter(filter).toList();
        return MultipleValFieldGroup.create(originalDetail, collect);
    }

    
    public ValFieldGroup modify(Converter<SingleValFieldGroup, SingleValFieldGroup> converter) {
        return MultipleValFieldGroup.create(originalDetail, ListUtil.convert(getAll(), converter));
    }

    
    public ValFieldGroup filterStartsWith(final String prefix) {
        return filter(f -> f.startsWith(prefix));
    }

    
    public ValFieldGroup filterContains(final String infix) {
        return filter(vf -> vf.contains(infix));
    }


    public ValFieldGroup filterAutonomous() {
        return filter(By.autonomous());
    }


    public ValFieldGroup filterHasCode(final String code) {
        return filter(vf -> StringUtil.notNullString(vf.getCode()).equals(code));
    }


    public ValFieldGroup filterHasAnyCode(final Collection<String> codes) {
        return filter(By.anyCode(codes));
    }


    /**
     * Vrati podpole z pole, ktere obsahuje podpole obsahujici dany retezec.
     * Neboli pokud nektere z poli jednoho opakovani pole obsahuje dany retezec, vrati vsechna pole.
     */
    public ValFieldGroup filterSiblingContains(@NonNull String infix) {
        return filterSiblingContains(infix, null);
    }


    /**
     * Vrati podpole z pole, ktere obsahuje podpole obsahujici dany retezec.
     * Neboli pokud nektere z poli jednoho opakovani pole obsahuje dany retezec, vrati vsechna pole.
     * @param codeToSearchIn zda hledat ve vsech nebo jen v danem podpoli
     */
    public ValFieldGroup filterSiblingContains(@NonNull String infix, @NullableNotBlank String codeToSearchIn) {
        List<SingleValFieldGroup> resultSingles = new ArrayList<>();

        ValFieldGroup matchingSiblings = getSiblingsOfCode(codeToSearchIn).filterContains(infix);

        for (final SingleValFieldGroup matchingSibling : matchingSiblings) {
            //najdeme vsechny sourozence tohoto podpole
            ValFieldGroup allSiblings = filter(By.parentFieldId(matchingSibling.getFieldId().existingParent()));

            //sourozence pridame do resultu, pokud v nem jiz nejsou
            for (SingleValFieldGroup sibling : allSiblings) {
                ListUtil.addIfNotContains(resultSingles, sibling);
            }
        }

        return MultipleValFieldGroup.create(originalDetail, resultSingles);
    }


    /**
     * Vrati podpole, ktera nejsou v danem valFieldGroup
     */
    public ValFieldGroup filterNotIn(final ValFieldGroup valFieldGroup) {
        return filter(vf -> valFieldGroup.getAll().stream().noneMatch(singleValFieldGroup -> singleValFieldGroup.getId().equals(vf.getId())));
    }


    public ValFieldGroup filterEquals(final String val) {
        return filter(vf -> vf.getRaw().equals(val));
    }

    private static @NonNull FieldFinder<ViewableFieldContainer, ViewableField> createCommonParentFinder(@NonNull ViewableFieldable field, @NullableNotBlank String codeToSearchIn) {
        if (codeToSearchIn == null) {
            return by(By.siblingOf(field.getFieldId()));
        }
        return by(By.siblingOfCode(field.getFieldId(), codeToSearchIn));
    }

    public static <F extends ViewableField> FieldFinder<ViewableFieldContainer, F> by(@NonNull Predicate<ViewableField> predicate) {
        return new FieldFinder<>() {

            @Override
            public Stream<F> findIn(ViewableFieldContainer fieldContainer) {
                return (Stream<F>) ListUtil.nestedBrowseExt(fieldContainer.streamFields(), streamChildrenFunction())
                        .filter(viewableField -> predicate.test(viewableField));
            }

            private static <F extends ViewableFieldContainer> @NonNull Function<F, Stream<? extends F>> streamChildrenFunction() {
                return f -> (Stream<F>) f.streamFields();
            }

            @Override
            public String toString() {
                return "By{%s}".formatted(predicate);
            }
        };
    }
    
}
