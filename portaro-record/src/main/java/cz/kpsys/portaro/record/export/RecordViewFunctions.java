package cz.kpsys.portaro.record.export;

import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Labeled;
import cz.kpsys.portaro.commons.object.LabeledName;
import cz.kpsys.portaro.commons.object.LabeledRefOrName;
import cz.kpsys.portaro.commons.object.LabeledValue;
import cz.kpsys.portaro.commons.util.NumberUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.record.LabeledRecordRef;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.isbn.Isbn;
import cz.kpsys.portaro.record.isbn.IsbnChecker;
import cz.kpsys.portaro.record.query.FieldGroupFillerImpl;
import cz.kpsys.portaro.record.query.MarqueryParser;
import cz.kpsys.portaro.record.query.SingleValFieldGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.NonNull;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cz.kpsys.portaro.record.RecordWellKnownFields.*;

public class RecordViewFunctions {

    static final MarqueryParser queryParser = new MarqueryParser();

    @NonNull
    public static Optional<String> controlNumber(FieldContainer detail) {
        return stream(DocumentControlNumber.QUERY_CODE, detail).map(SingleValFieldGroup::getRaw).findFirst();
    }

    @NonNull
    public static Optional<String> documentName(FieldContainer detail) {
        return stream(DocumentTitle.Name.QUERY_CODE, detail).map(SingleValFieldGroup::getRaw).findFirst();
    }

    @NonNull
    public static List<Labeled> subtitle(FieldContainer detail) {
        return stream("d245.[b,n,p,s]", detail).map(RecordViewFunctions::subfieldToLabeledRefOrStringValue).toList();
    }

    @NonNull
    public static Optional<Labeled> responsibilityStatement(FieldContainer detail) {
        return stream(DocumentTitle.ResponsibilityStatement.QUERY_CODE, detail).map(RecordViewFunctions::subfieldToLabeledRefOrStringValue).findFirst();
    }

    @NonNull
    public static List<LabeledRefOrName> primaryAuthors(FieldContainer detail) {
        List<LabeledRefOrName> d100ab = FieldFinders.<FieldContainer, Field<?>>byDeepTypeId(DocumentMainAuthor.Main.TYPE_ID).findIn(detail)
                .map(field -> subfieldToLabeledRefOrName(field, By.anyCode(DocumentMainAuthor.Main.Name.CODE, DocumentMainAuthor.Main.RomanNumber.CODE)))
                .toList();
        if (!d100ab.isEmpty()) {
            return d100ab;
        }
        return Stream.concat(stream("d110.a,d111.a,d130.a", detail), stream("d150.a,d151.a,d155.a", detail)).map(RecordViewFunctions::subfieldToLabeledRefOrName).toList();
    }

    @NonNull
    public static Optional<@NotBlank String> authorityName(FieldContainer detail) {
        String string = Stream.concat(stream("a100.[a,d],a110.a,a111.a,a130.a", detail), stream("a150.a,a151.a,a155.a", detail)).map(SingleValFieldGroup::getRaw).collect(Collectors.joining(", "));
        String notBlankTrimmedString = StringUtil.notBlankTrimmedString(string);
        return Optional.ofNullable(notBlankTrimmedString);
    }

    @NonNull
    public static List<LabeledRefOrName> publishers(FieldContainer detail) {
        return stream("d260.b,d264.b", detail).map(RecordViewFunctions::subfieldToLabeledRefOrName).toList();
    }

    @NonNull
    public static Optional<LabeledValue<Integer>> publicationStartYear(FieldContainer detail) {
        return stream("d260.c,d264.c", detail).map(RecordViewFunctions::subfieldToLabeledIntegerOpt).flatMap(Optional::stream).findFirst();
    }

    @NonNull
    public static List<LabeledValue<String>> normalizedIsbns(FieldContainer detail) {
        return stream(DocumentIsbn.Value.QUERY_CODE, detail)
                .map(subfield -> new Isbn(subfield.getRaw()))
                .filter(Isbn::isValid)
                .filter(isxn -> IsbnChecker.isValidIsbn(isxn.getNormalizedValue()))
                .map(isxn -> LabeledValue.of(isxn.getNormalizedValue(), Texts.ofNative(isxn.getValue())))
                .toList();
    }

    @NonNull
    public static List<LabeledValue<String>> normalizedIssns(FieldContainer detail) {
        return stream(DocumentIssn.Value.QUERY_CODE, detail)
                .map(subfield -> new Isbn(subfield.getRaw()))
                .filter(Isbn::isValid)
                .filter(isxn -> IsbnChecker.isValidIssn(isxn.getNormalizedValue()))
                .map(isxn -> LabeledValue.of(isxn.getNormalizedValue(), Texts.ofNative(isxn.getValue())))
                .toList();
    }

    @NonNull
    public static Optional<String> primaryCpkId(FieldContainer detail) {
        return stream(CpkRecordId.Value.QUERY_CODE, detail).map(RecordViewFunctions::subfieldToLabeledStringValue).map(LabeledValue::value).findFirst();
    }

    private static Stream<SingleValFieldGroup> stream(String q, FieldContainer detail) {
        return queryParser.parse(q, FieldGroupFillerImpl.ofFlattedDetail(detail))
                .purify()
                .getAll()
                .stream();
    }

    private static @NonNull LabeledRefOrName subfieldToLabeledRefOrName(Field<?> field, FieldCodeFilter<Field<?>> subfieldMatcher) {
        MultiText text = MultiText.ofTexts(field.getFields(subfieldMatcher).stream().map(Field::getText).toList()).withSpaceDelimiter();
        if (field.hasRecordLink()) {
            return LabeledRecordRef.ofRecordLink(field.getExistingRecordLink(), text);
        }
        String raw = field.getFields(subfieldMatcher).stream().map(Field::getRaw).collect(Collectors.joining(" "));
        return new LabeledName(raw, field.getText());
    }

    @NonNull
    private static LabeledRefOrName subfieldToLabeledRefOrName(SingleValFieldGroup subfield) {
        if (subfield.hasRecordLink()) {
            return Objects.requireNonNull(subfield.getRecordLink());
        }
        return new LabeledName(subfield.getRaw(), subfield.getText());
    }

    @NonNull
    private static Labeled subfieldToLabeledRefOrStringValue(SingleValFieldGroup subfield) {
        if (subfield.hasRecordLink()) {
            return Objects.requireNonNull(subfield.getRecordLink());
        }
        return subfieldToLabeledStringValue(subfield);
    }

    @NonNull
    private static LabeledValue<String> subfieldToLabeledStringValue(SingleValFieldGroup subfield) {
        return new LabeledValue<>(subfield.getRaw(), subfield.getText());
    }

    @NonNull
    private static Optional<LabeledValue<Integer>> subfieldToLabeledIntegerOpt(SingleValFieldGroup subfield) {
        if (StringUtil.hasCharsAndAllDigits(subfield.getRaw())) {
            int number = Integer.parseInt(subfield.getRaw());
            if (NumberUtil.isPositive(number)) {
                return Optional.of(LabeledValue.ofNumber(number));
            }
        }
        return Optional.empty();
    }

    @NonNull
    public static List<String> authorityAlternativeNames(FieldContainer detail) {
        return Stream.concat(
                stream(AuthorityPersonAlternativeName.Value.QUERY_CODE, detail),
                stream(AuthorityTopicAlternativeName.Value.QUERY_CODE, detail)
        ).map(SingleValFieldGroup::getRaw).toList();
    }
}
