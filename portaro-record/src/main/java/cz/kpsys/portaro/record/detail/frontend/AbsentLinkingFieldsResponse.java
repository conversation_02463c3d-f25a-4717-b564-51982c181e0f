package cz.kpsys.portaro.record.detail.frontend;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.record.RecordIdentifier;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;

public record AbsentLinkingFieldsResponse(

        @NonNull Text text,
        @NonNull FailedResultType type,

        @NonNull
        FieldTypeId missingLinkingRecordsLinkFieldTypeId,

        @NonNull
        RecordIdentifier missingLinkingRecordsLink,

        @NonNull
        FieldTypeId missingLinkingRecordsValueFieldTypeId

) implements ErroredFieldResponse {}
