package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.CommonsConstants;
import cz.kpsys.portaro.commons.date.DatetimeRange;
import cz.kpsys.portaro.commons.date.DatetimeRangeToStringConverter;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DatetimeRangeTypedValueConverter implements TypedFieldValueConverter<DatetimeRangeValueCommand, FieldPayload<DatetimeRangeFieldValue>, DatetimeRangeFieldValue> {

    public static final DatetimeRangeToStringConverter rawDateFormat = DatetimeRangeToStringConverter.ofInternalFormat(StaticProvider.of(CommonsConstants.UTC));

    @NonNull
    @Override
    public FieldPayload<DatetimeRangeFieldValue> convert(@NonNull DatetimeRangeValueCommand command, @NonNull FieldType<?> fieldType, @NonNull RecordFieldId fieldId) {
        return FieldPayload.of(DatetimeRangeFieldValue.of(command.value(), Set.of(fieldId.recordIdFondPair())));
    }

    public static @NonNull String formatLabel(@NonNull DatetimeRange range) {
        return rawDateFormat.convert(range);
    }

    @Override
    public String toString() {
        return getClass().getSimpleName();
    }
}
