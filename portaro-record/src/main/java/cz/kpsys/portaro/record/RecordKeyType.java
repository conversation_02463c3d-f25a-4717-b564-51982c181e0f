package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.object.BasicNamedLabeledIdentified;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticAllValuesProvider;
import cz.kpsys.portaro.commons.object.repo.AllValuesProvidedCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RecordKeyType extends BasicNamedLabeledIdentified<String> implements Identified<String> {

    public static final RecordKeyType NUMERIC_ID = new RecordKeyType("numeric_id");
    public static final RecordKeyType EXTERNAL_ID = new RecordKeyType("external_id");

    public static final RecordKeyType ORIG_PRIMARY_NAME = new RecordKeyType("orig_primary_name");
    public static final RecordKeyType ORIG_ALTERNATIVE_NAME = new RecordKeyType("orig_alternative_name");
    public static final RecordKeyType ORIG_AUTHOR_NAME = new RecordKeyType("orig_author_name");
    public static final RecordKeyType ORIG_PRIMARY_AUTHOR = new RecordKeyType("orig_primary_author");
    public static final RecordKeyType ORIG_SECONDARY_AUTHOR = new RecordKeyType("orig_secondary_author");
    public static final RecordKeyType ORIG_PUBLISHER_NAME = new RecordKeyType("orig_publisher_name");

    public static final RecordKeyType FLAT_PRIMARY_NAME = new RecordKeyType("flat_primary_name");
    public static final RecordKeyType FLAT_ALTERNATIVE_NAME = new RecordKeyType("flat_alternative_name");

    public static final RecordKeyType PRIMARY_AUTHOR_LIFE_FROM_YEAR = new RecordKeyType("primary_author_life_from_year");
    public static final RecordKeyType PRIMARY_AUTHOR_LIFE_TO_YEAR = new RecordKeyType("primary_author_life_to_year");

    public static final RecordKeyType SECONDARY_AUTHOR_LIFE_FROM_YEAR = new RecordKeyType("sedondary_author_life_from_year");
    public static final RecordKeyType SECONDARY_AUTHOR_LIFE_TO_YEAR = new RecordKeyType("sedondary_author_life_to_year");

    public static final RecordKeyType PUBLICATION_END_YEAR = new RecordKeyType("publication_end_year");
    public static final RecordKeyType PUBLICATION_START_YEAR = new RecordKeyType("publication_start_year");
    public static final RecordKeyType PUBLICATION_PLACE = new RecordKeyType("publication_place");

    public static final RecordKeyType SORT_PRIMARY_NAME = new RecordKeyType("sort_primary_name");
    public static final RecordKeyType SORT_ALTERNATIVE_NAME = new RecordKeyType("sort_alternative_name");
    public static final RecordKeyType SORT_AUTHOR_NAME = new RecordKeyType("sort_author_name");
    public static final RecordKeyType SORT_PRIMARY_AUTHOR = new RecordKeyType("sort_primary_author");
    public static final RecordKeyType SORT_SECONDARY_AUTHOR = new RecordKeyType("sort_secondary_author");
    public static final RecordKeyType SORT_PUBLICATION_START_YEAR = new RecordKeyType("sort_publication_start_year");

    public static final RecordKeyType CORE_ISSN = new RecordKeyType("core_issn");
    public static final RecordKeyType ORIG_ISSN = new RecordKeyType("orig_issn");
    public static final RecordKeyType CORE_ISBN = new RecordKeyType("core_isbn");
    public static final RecordKeyType ORIG_ISBN = new RecordKeyType("orig_isbn");
    public static final RecordKeyType CNA_OR_ICO = new RecordKeyType("cna");
    public static final RecordKeyType CNB = new RecordKeyType("cnb");
    public static final RecordKeyType OCLC = new RecordKeyType("oclc");

    public RecordKeyType(String id) {
        super(id, id);
    }

    public static final Codebook<RecordKeyType, String> RECORD_KEY_TYPE_CODEBOOK = AllValuesProvidedCodebook.ofIdentified(StaticAllValuesProvider.of(
            NUMERIC_ID,
            EXTERNAL_ID,
            ORIG_PRIMARY_NAME,
            ORIG_ALTERNATIVE_NAME,
            ORIG_AUTHOR_NAME,
            ORIG_PRIMARY_AUTHOR,
            ORIG_SECONDARY_AUTHOR,
            ORIG_PUBLISHER_NAME,
            FLAT_PRIMARY_NAME,
            FLAT_ALTERNATIVE_NAME,
            PRIMARY_AUTHOR_LIFE_FROM_YEAR,
            PRIMARY_AUTHOR_LIFE_TO_YEAR,
            SECONDARY_AUTHOR_LIFE_FROM_YEAR,
            SECONDARY_AUTHOR_LIFE_TO_YEAR,
            PUBLICATION_END_YEAR,
            PUBLICATION_START_YEAR,
            PUBLICATION_PLACE,
            SORT_PRIMARY_NAME,
            SORT_ALTERNATIVE_NAME,
            SORT_AUTHOR_NAME,
            SORT_PRIMARY_AUTHOR,
            SORT_SECONDARY_AUTHOR,
            SORT_PUBLICATION_START_YEAR,
            CORE_ISSN,
            ORIG_ISSN,
            CORE_ISBN,
            ORIG_ISBN,
            CNA_OR_ICO,
            CNB,
            OCLC
    ));
}
