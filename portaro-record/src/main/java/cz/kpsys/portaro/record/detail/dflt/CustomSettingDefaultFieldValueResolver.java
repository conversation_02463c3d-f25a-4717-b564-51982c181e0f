package cz.kpsys.portaro.record.detail.dflt;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.IdentifiedValue;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.HierarchyFinder;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CustomSettingDefaultFieldValueResolver implements DefaultFieldValueResolver {

    @NonNull AllValuesProvider<IdentifiedValue<DefaultFieldValueId, @NotBlank String>> allDefaultFieldValuesLoader;
    @NonNull HierarchyFinder<Department> hierarchyFinder;

    @Override
    public Optional<@NotBlank String> findOn(@NonNull FieldTypeId fieldTypeId, @NonNull Fond fond, @NonNull Department ctx) {
        return find(ctx, fieldTypeFilter(fieldTypeId), fondFilter(fond))
                .or(() -> find(ctx, fieldTypeFilter(fieldTypeId), nullFondFilter()));
    }

    private Optional<@NotBlank String> find(@NonNull Department ctx,
                                            @NonNull Predicate<IdentifiedValue<DefaultFieldValueId, String>> fieldTypeFilter,
                                            @NonNull Predicate<IdentifiedValue<DefaultFieldValueId, String>> fondFilter) {
        Map<Department, @NotBlank String> departmentToValueMap = getDepartmentToValueMap(fieldTypeFilter, fondFilter);
        return hierarchyFinder.findOn(ctx, departmentToValueMap);
    }

    private Map<Department, @NotBlank String> getDepartmentToValueMap(@NonNull Predicate<IdentifiedValue<DefaultFieldValueId, String>> fieldTypeFilter,
                                                                      @NonNull Predicate<IdentifiedValue<DefaultFieldValueId, String>> fondFilter) {
        List<IdentifiedValue<DefaultFieldValueId, @NotBlank String>> defaultFieldValues = allDefaultFieldValuesLoader.getAll();
        return defaultFieldValues.stream()
                .filter(fieldTypeFilter)
                .filter(fondFilter)
                .collect(Collectors.toMap(
                        defaultFieldValue -> defaultFieldValue.id().department(),
                        IdentifiedValue::value
                ));
    }

    @NonNull
    private static Predicate<IdentifiedValue<DefaultFieldValueId, String>> fieldTypeFilter(@NonNull FieldTypeId fieldTypeId) {
        return customSetting -> customSetting.getId().fieldTypeId().equals(fieldTypeId);
    }

    @NonNull
    private static Predicate<IdentifiedValue<DefaultFieldValueId, String>> fondFilter(@NonNull Fond fond) {
        return customSetting -> customSetting.id().fond().isPresent() && customSetting.id().fond().get().equals(fond);
    }

    @NonNull
    private static Predicate<IdentifiedValue<DefaultFieldValueId, String>> nullFondFilter() {
        return customSetting -> customSetting.id().fond().isEmpty();
    }

}
