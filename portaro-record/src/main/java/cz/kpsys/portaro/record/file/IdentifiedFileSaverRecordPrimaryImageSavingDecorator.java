package cz.kpsys.portaro.record.file;

import cz.kpsys.portaro.commons.file.FileViewForm;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.file.FilepathIdentifiedFile;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.LoadedIdentifiedFile;
import cz.kpsys.portaro.file.directory.AllIdsByDirectoryProvider;
import cz.kpsys.portaro.file.directory.Directory;
import cz.kpsys.portaro.file.security.IdentifiedFileSaveCommand;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class IdentifiedFileSaverRecordPrimaryImageSavingDecorator implements Saver<IdentifiedFileSaveCommand, IdentifiedFile> {

    @NonNull Saver<IdentifiedFileSaveCommand, IdentifiedFile> loadedIdentifiedFileSaver;
    @NonNull Saver<IdentifiedFile, IdentifiedFile> identifiedFileSaver;
    @NonNull AllIdsByDirectoryProvider allRecordIdsByDirectoryProvider;
    @NonNull AllIdsByDirectoryProvider allAuthorityIdsByDirectoryProvider;
    @NonNull AllByIdsLoadable<Record, UUID> nonDetailedRichRecordLoader;
    @NonNull Saver<Record, ?> recordSaver;
    @NonNull Saver<PrimaryImageToRecordsSaveCommand, List<Record>> primaryImageToRecordsSaver;

    @Override
    public @NonNull IdentifiedFile save(@NonNull IdentifiedFileSaveCommand command) {
        IdentifiedFile saved;

        if (command.file() instanceof LoadedIdentifiedFile loadedIdentifiedFile) {
            saved = loadedIdentifiedFileSaver.save(new IdentifiedFileSaveCommand(loadedIdentifiedFile, command.currentAuth(), command.ctx()));
        } else if (command.file() instanceof FilepathIdentifiedFile filepathIdentifiedFile) {
            saved = loadedIdentifiedFileSaver.save(new IdentifiedFileSaveCommand(filepathIdentifiedFile, command.currentAuth(), command.ctx()));
        } else {
            saved = identifiedFileSaver.save(command.file());
        }

        try {
            checkAndSavePrimaryImage(saved);
        } catch (Exception e) {
            log.error("Error while saving document primary image {}", saved, e);
        }

        return saved;
    }


    private void checkAndSavePrimaryImage(@NonNull IdentifiedFile file) {
        if (file.hasForm(FileViewForm.IMAGE)) {
            Directory directory = file.getDirectory();
            if (directory != null) {
                List<Record> recordsOfDirWithoutPrimaryImage = getRecordsOfDirWithoutPrimaryImage(directory);
                recordsOfDirWithoutPrimaryImage.forEach(record -> {
                    record.setCover(file);
                    recordSaver.save(record);
                });
                primaryImageToRecordsSaver.save(new PrimaryImageToRecordsSaveCommand(recordsOfDirWithoutPrimaryImage, file));
            }
        }
    }


    private List<Record> getRecordsOfDirWithoutPrimaryImage(@NonNull Directory directory) {
        var documentIdsByDirectory = allRecordIdsByDirectoryProvider.getAllByDirectories(List.of(directory.getId()));
        var authorityIdsByDirectory = allAuthorityIdsByDirectoryProvider.getAllByDirectories(List.of(directory.getId()));
        List<UUID> recordIdsByDirectory = ListUtil.union(documentIdsByDirectory, authorityIdsByDirectory);
        if (!recordIdsByDirectory.isEmpty()) {

            List<Record> recordsWithThisDirectory = nonDetailedRichRecordLoader.getAllByIds(recordIdsByDirectory);

            return recordsWithThisDirectory.stream()
                    .filter(record -> record.getCover() == null)
                    .toList();
        }
        return List.of();
    }

}
