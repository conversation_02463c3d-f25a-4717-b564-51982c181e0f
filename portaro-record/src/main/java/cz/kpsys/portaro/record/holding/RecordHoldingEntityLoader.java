package cz.kpsys.portaro.record.holding;

import lombok.NonNull;
import org.springframework.data.repository.CrudRepository;

import java.util.Collection;
import java.util.List;
import java.util.UUID;

public interface RecordHoldingEntityLoader extends CrudRepository<RecordHoldingEntity, UUID> {

    List<RecordHoldingEntity> findAllByRecordId(@NonNull UUID recordId);

    List<RecordHoldingEntity> findAllByRecordIdInAndDepartmentIdIn(@NonNull Collection<UUID> recordIds, @NonNull Collection<Integer> departmentIds);

    List<RecordHoldingEntity> findAllByDepartmentIdIn(@NonNull Collection<Integer> departmentIds);

}
