package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class BooleanTypedValueConverter implements TypedFieldValueConverter<BooleanValueCommand, FieldPayload<BooleanFieldValue>, BooleanFieldValue> {

    @NonNull
    @Override
    public FieldPayload<BooleanFieldValue> convert(@NonNull BooleanValueCommand command, @NonNull FieldType<?> fieldType, @NonNull RecordFieldId fieldId) {
        return FieldPayload.of(BooleanFieldValue.of(command.value(), Set.of(fieldId.recordIdFondPair())));
    }

    @Override
    public String toString() {
        return getClass().getSimpleName();
    }

}
