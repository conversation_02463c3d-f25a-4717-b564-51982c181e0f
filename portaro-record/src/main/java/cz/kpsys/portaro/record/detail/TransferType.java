package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.DefaultProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum TransferType implements LabeledIdentified<Integer> {

    NO_TRANSFER(0, Texts.ofMessageCoded("record.detail.transferType.NoTransfer")),
    OVERWRITE(1, Texts.ofMessageCoded("record.detail.transferType.TransferByOverwriting")),
    ADD(2, Texts.ofMessageCoded("record.detail.transferType.TransferByAddingRepetiton")),
    DELETE_WHEN_EMPTY(3, Texts.ofMessageCoded("record.detail.transferType.DeleteEmpty")),
    REWRITE_WHEN_CREATING(4, Texts.ofMessageCoded("record.detail.transferType.TransferOnlyNR"));

    public static final Codebook<TransferType, Integer> CODEBOOK = new StaticCodebook<>(values());
    public static final Provider<@NonNull TransferType> DEFAULT_PROVIDER = DefaultProvider.byId(CODEBOOK, OVERWRITE.getId());

    @NonNull Integer id;
    @NonNull Text text;
    
}
