package cz.kpsys.portaro.record.view;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.web.Link;
import cz.kpsys.portaro.commons.web.StaticLink;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordWellKnownFields;
import cz.kpsys.portaro.record.RecordWellKnownFields.F911;
import cz.kpsys.portaro.record.RecordWellKnownFields.Links;
import cz.kpsys.portaro.record.detail.By;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.detail.value.StringFieldValue;
import cz.kpsys.portaro.record.fond.FondTypeResolver;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;


public class DownloadLinkResolverBy856Fields implements DownloadLinkResolver {

    private static final Set<String> FULLTEXT_ACCEPTABLE_INFIXES = Set.of(
            "plný text",
            "plny text",
            "plné texty",
            "plne texty",
            "full text",
            "fulltext",
            "digitalizovaný dokument",
            "digitalizovany dokument",
            "digitální dokument",
            "digitalni dokument",
            "digitální kopie",
            "etiketa",
            "zvuk",
            "esbírky",
            "esbirky"
    );

    @Override
    public List<Link> resolveLinks(Record record) {
        if (FondTypeResolver.isAuthorityFond(record)) {
            return List.of();
        }
        List<Link> result = new ArrayList<>();
        result.addAll(resolveLinksFromFieldAndSubfield(record, F911.CODE, F911.UrlAddress.CODE, List.of(F911.Description.CODE), true));
        result.addAll(resolveLinksFromFieldAndSubfield(record, Links.CODE, Links.UrlAddress.CODE, List.of(Links.Description.CODE), isDocumentElectronicForm(record)));
        return result;
    }


    /**
     * @param isAlwaysFulltext zda ma byt url vzdy povazovano za fulltext (pokud je false, hleda se infix z FULLTEXT_ACCEPTABLE_INFIXES)
     */
    private List<Link> resolveLinksFromFieldAndSubfield(Record record, String fieldCode, String linkUrlSubfieldCode, List<String> linkTextSubfieldCodes, boolean isAlwaysFulltext) {
        List<Link> result = new ArrayList<>();

        // check if record has detail (non-detailed records`getFields` returns null)
        List<Field<?>> fieldsByFieldNumber = record.getFields() == null ? List.of() : record.getFields(By.code(fieldCode));
        for (Field<?> dataField : fieldsByFieldNumber) {
            // najdeme url
            Optional<String> linkUrl = findLinkUrlFromAcceptableSubfields(dataField, linkUrlSubfieldCode);
            if (linkUrl.isEmpty()) {
                continue;
            }

            // najdeme text odkazu - budto z nektereho z podpoli y (to by ale melo byt jen jedno) - v nich hledame infix,
            // pokud je dokument jen v elektronicke forme, tak nehledame infix a pokud zaroven chybi podpole y, tak povazujeme url za plny text
            Optional<Text> linkText = findLinkTextFromAcceptableSubfields(dataField, linkTextSubfieldCodes, isAlwaysFulltext);
            if (linkText.isPresent()) {
                result.add(new StaticLink(linkUrl.get(), linkText.get()));
            } else if (isAlwaysFulltext) {
                result.add(new StaticLink(linkUrl.get()));
            }
        }

        return result;
    }

    private Optional<String> findLinkUrlFromAcceptableSubfields(Field<?> datafield, String subfieldCode) {
        return datafield.streamFields(By.code(subfieldCode))
                .filter(Field::isUrl)
                .findFirst()
                .map(Field::getRaw);
    }


    private Optional<Text> findLinkTextFromAcceptableSubfields(Field<?> datafield, List<String> subfieldCodes, boolean isAlwaysFulltext) {
        return datafield.streamFields(By.anyCode(subfieldCodes))
                .filter(Field::hasValueHolder)
                .filter(subfield -> {
                    if (isAlwaysFulltext) {
                        return true; // skip infixes check if is fulltext
                    }
                    ScalarFieldValue<?> fieldValue = subfield.getExistingValueHolder();
                    if (!(fieldValue instanceof StringFieldValue stringFieldValue)) {
                        return false;
                    }
                    String lowerCasedRaw = stringFieldValue.value().toLowerCase();
                    return FULLTEXT_ACCEPTABLE_INFIXES.stream().anyMatch(lowerCasedRaw::contains);
                })
                .findFirst()
                .map(Field::getText);
    }


    private boolean isDocumentElectronicForm(Record record) {
        return Optional.ofNullable(record.getDetail())
                .map(detail -> detail.streamFields(By.typeId(RecordWellKnownFields.DocumentPhysicalDescription.TYPE_ID)))
                .orElseGet(Stream::empty)
                .filter(Field::hasValueHolder)
                .map(Field::getExistingValueHolder)
                .map(StringFieldValue.class::cast)
                .map(StringFieldValue::value)
                .anyMatch(physicalDescription -> physicalDescription.length() >= 2 && physicalDescription.charAt(0) == 'c' && physicalDescription.charAt(1) == 'r');
    }


}
