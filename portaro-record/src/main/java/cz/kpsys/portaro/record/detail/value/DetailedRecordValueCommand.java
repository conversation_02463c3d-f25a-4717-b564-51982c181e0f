package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import lombok.NonNull;

public record DetailedRecordValueCommand(

        @NonNull
        Record record,

        @NonNull
        Department ctx,

        @NonNull
        UserAuthentication currentAuth

) implements RecordFieldValueCommand {

    @Override
    public boolean equals(Object o) {
        return o instanceof DetailedRecordValueCommand that && ctx.equals(that.ctx) && record.equals(that.record) && currentAuth.equals(that.currentAuth);
    }

    @Override
    public int hashCode() {
        return record.hashCode();
    }

    @Override
    public String toString() {
        return "DetailedRecordValueCommand[" + record + ']';
    }
}
