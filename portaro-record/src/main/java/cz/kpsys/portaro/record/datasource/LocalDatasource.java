package cz.kpsys.portaro.record.datasource;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.NonNull;

import java.util.Collection;
import java.util.Objects;

public record LocalDatasource(

        @NonNull
        String id,

        @NonNull
        Text text,

        @JsonIgnore
        @NonNull
        Collection<String> supportedSubkinds,

        @JsonIgnore
        @NonNull
        AllValuesProvider<Fond> supportedFondsProvider

) implements Datasource {

    @Override
    public Collection<Fond> supportedFonds() {
        return supportedFondsProvider.getAll();
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equalsIdentified(this, o, LocalDatasource.class);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }
}
