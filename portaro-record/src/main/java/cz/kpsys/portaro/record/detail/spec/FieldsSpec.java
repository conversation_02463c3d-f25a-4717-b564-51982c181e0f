package cz.kpsys.portaro.record.detail.spec;

import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.util.Assert;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public record FieldsSpec(

        @Nullable
        Set<FieldSpec> specs

) {

    private static final FieldsSpec EMPTY = new FieldsSpec(Set.of());
    private static final FieldsSpec ALL = new FieldsSpec(null);

    public static FieldsSpec ofAll() {
        return ALL;
    }

    public static FieldsSpec ofEmpty() {
        return EMPTY;
    }

    public static FieldsSpec of(@NonNull Set<FieldSpec> specs) {
        if (specs.isEmpty()) {
            return ofEmpty();
        }
        return new FieldsSpec(specs);
    }

    public static FieldsSpec of(@NonNull FieldSpec... specs) {
        if (specs.length == 0) {
            return ofEmpty();
        }
        return new FieldsSpec(Set.of(specs));
    }

    public static FieldsSpec of(@NonNull FieldSpec spec) {
        return new FieldsSpec(Set.of(spec));
    }

    public static FieldsSpec ofFieldType(@NonNull FieldTypeId fieldTypeId) {
        return of(FieldSpec.ofFieldType(fieldTypeId));
    }

    public static FieldsSpec ofField(@NonNull FieldId fieldId) {
        return of(FieldSpec.ofField(fieldId));
    }

    public boolean covers(@NonNull FieldsSpec otherFieldsSpec) {
        if (isForAll()) {
            return true;
        }
        if (otherFieldsSpec.isForAll()) {
            return false;
        }
        for (FieldSpec otherSpecField : otherFieldsSpec.existingSpecs()) {
            if (existingStream().noneMatch(thisSpecField -> thisSpecField.covers(otherSpecField))) {
                return false;
            }
        }
        return true;
    }

    public @NonNull Stream<FieldSpec> existingStream() {
        return existingSpecs().stream();
    }

    public @NonNull Set<FieldSpec> existingSpecs() {
        Assert.state(!isForAll(), "Cannot call existingSpecs() on *");
        return specs;
    }

    public boolean isForAll() {
        return specs == null;
    }

    public FieldsSpec add(FieldSpec other) {
        if (isEmpty()) {
            return FieldsSpec.of(other);
        }
        if (covers(FieldsSpec.of(other))) {
            return this;
        }
        HashSet<FieldSpec> res = new HashSet<>(existingSpecs());
        res.removeIf(other::covers);
        res.add(other);
        return FieldsSpec.of(res);
    }

    public FieldsSpec addAll(FieldsSpec other) {
        if (other.isEmpty()) {
            return this;
        }
        if (isEmpty()) {
            return other;
        }
        if (covers(other)) {
            return this;
        }
        if (other.isForAll()) {
            return other;
        }
        if (true) {
            throw new UnsupportedOperationException("Not yet implemented - must implement merging particular specs");
        }
        Set<FieldSpec> res = new HashSet<>(specs);
        res.addAll(other.existingSpecs());
        return FieldsSpec.of(res);
    }

    public FieldsSpec addAll(Set<FieldSpec> others) {
        return addAll(FieldsSpec.of(others));
    }

    public boolean isEmpty() {
        return specs != null && specs.isEmpty();
    }

    public FieldsSpec minus(@NonNull FieldsSpec other) {
        if (other.isForAll()) {
            return FieldsSpec.ofEmpty();
        }
        if (other.isEmpty()) {
            return this;
        }
        FieldsSpec res = this;
        for (FieldSpec otherFieldSpec : other.existingSpecs()) {
            res = res.minus(otherFieldSpec);
        }
        return res;
    }

    private FieldsSpec minus(@NonNull FieldSpec other) {
        if (isEmpty()) {
            return this;
        }
        Set<FieldSpec> result = new HashSet<>(existingSpecs().size());
        for (FieldSpec thisSpec : existingSpecs()) {
            FieldSpec fieldSpecResult = thisSpec.minus(other);
            if (fieldSpecResult != null) {
                result.add(fieldSpecResult);
            }
        }
        return FieldsSpec.of(result);
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof FieldsSpec that)) {
            return false;
        }
        return Objects.equals(specs(), that.specs());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(specs);
    }

    @Override
    public String toString() {
        if (isForAll()) {
            return "*";
        }
        if (isEmpty()) {
            return "NONE";
        }
        String prefix = "";
        String suffix = "";
        if (specs.size() > 1) {
            prefix = "[";
            suffix = "]";
        }
        return existingStream().sorted(FieldSpec.NUMERICALLY_COMPATIBLE_SORTER).map(FieldSpec::toString).collect(Collectors.joining(",", prefix, suffix));
    }

}
