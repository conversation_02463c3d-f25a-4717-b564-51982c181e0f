package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import org.springframework.core.convert.converter.Converter;
import org.springframework.util.Assert;

public class RecordToEntityConverter implements Converter<Record, RecordEntity> {

    @Override
    public RecordEntity convert(Record source) {
        // TODO: creationEventId je v DB nonnull, ale to nedokážeme zatím udělat z Portara. Vyřešit časem.
        Assert.notNull(source.getCreationEventId(), "Tried to save new record. That is not supported yet!");
        return new RecordEntity(
                source.getId(),
                source.idFondPair().id().masterId(),
                source.idFondPair().fond().getId(),
                source.getCreationEventId(),
                source.getActivationEventId(),
                source.getDeletionEventId(),
                source.getDirectoryId(),
                ObjectUtil.elvis(source.getCover(), Identified::getId),
                source.getName()
        );
    }

}
