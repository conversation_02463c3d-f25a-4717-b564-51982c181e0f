package cz.kpsys.portaro.record.merge;

import cz.kpsys.portaro.appserver.dml.DmlAppserverService;
import cz.kpsys.portaro.appserver.dml.TableWrite;
import cz.kpsys.portaro.appserver.dml.TableWriteGenerator;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.deletion.RecordDeleter;
import cz.kpsys.portaro.record.deletion.RecordDeletionCommand;
import cz.kpsys.portaro.record.holding.RecordHoldingMergeTableWriteGenerator;
import cz.kpsys.portaro.record.operation.RecordOperation;
import cz.kpsys.portaro.record.operation.RecordOperationType;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

import static cz.kpsys.portaro.databasestructure.AcquisitionDb.POL_FAK;
import static cz.kpsys.portaro.databasestructure.AcquisitionDb.ROZDELOVNIK;
import static cz.kpsys.portaro.databasestructure.ExemplarDb.KAT1_5;
import static cz.kpsys.portaro.databasestructure.ExemplarDb.VOLUME;
import static cz.kpsys.portaro.databasestructure.ForeignOccurrenceDb.CENTRAL_PROPOJ;
import static cz.kpsys.portaro.databasestructure.RecordDb.KAT1_7;
import static cz.kpsys.portaro.databasestructure.RecordDb.RECORD;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class TableWritesDocumentMerger implements DocumentMerger {

    @NonNull DmlAppserverService dmlService;
    @NonNull RecordDeleter recordDeleter;
    @NonNull RecordHoldingMergeTableWriteGenerator recordHoldingMergeTableWriteGenerator;
    @NonNull ByIdLoadable<RecordOperationType, Integer> recordOperationTypeLoader;
    @NonNull TableWriteGenerator<RecordOperation> recordOperationTableWriteGenerator;

    @Override
    public void merge(@NonNull Record target, @NonNull List<Record> sources, @NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth) {
        validate(sources, target);

        // presunuti v tabulkach
        List<TableWrite> preDeletionTableWrites = createPreDeletionTableWrites(currentDepartment, currentAuth, sources, target);
        dmlService.executeStatement(preDeletionTableWrites);

        //smazani sloucenych zaznamu
        List<RuntimeException> documentDeletionExceptions = new ArrayList<>();
        List<Record> successfullyDeletedRecords = new ArrayList<>();
        for (Record source : sources) {
            try {
                recordDeleter.delete(RecordDeletionCommand.withoutHoldingsAndExemplars(source, currentDepartment, currentAuth)); // withoutHoldingsAndExempars - everything should be moved or deleted already
                successfullyDeletedRecords.add(source);
            } catch (RuntimeException e) {
                log.error("Cannot delete record {}: {}", source, e.getMessage(), e);
                documentDeletionExceptions.add(e);
            }
        }

        List<TableWrite> afterDeletionTableWrites = createAfterDeletionTableWrites(successfullyDeletedRecords, target);
        if (!successfullyDeletedRecords.isEmpty()) {
            dmlService.executeStatement(afterDeletionTableWrites);
        }

        if (!documentDeletionExceptions.isEmpty()) {
            throw new RuntimeException("Cannot delete some of records (merged but then not deleted)", documentDeletionExceptions.getFirst());
        }
    }

    private void validate(List<Record> sources, Record target) {
        Assert.state(!sources.contains(target), "Source records must not contain target record");
    }

    private @NonNull List<TableWrite> createPreDeletionTableWrites(@NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth, @NonNull List<Record> sources, @NonNull Record target) {
        List<TableWrite> tableWrites = new ArrayList<>();
        for (Record source : sources) {
            tableWrites.addAll(moveHoldings(source, target));
            tableWrites.add(moveExemplars(source, target));
            tableWrites.add(moveVolumes(source, target));
            tableWrites.add(moveInvoices(source, target));
            tableWrites.add(moveForeignOccurrenceRelations(source, target));
            tableWrites.add(deleteExemplarDistributionDefinition(source));
            tableWrites.add(deleteDocumentAuthoritiesRelations(source));
            tableWrites.addAll(addMergeOperation(currentDepartment, currentAuth.getActiveUser(), source));
        }
        tableWrites.addAll(addMergeOperation(currentDepartment, currentAuth.getActiveUser(), target));
        return tableWrites;
    }

    private List<TableWrite> createAfterDeletionTableWrites(@NonNull List<Record> sources, @NonNull Record target) {
        List<TableWrite> tableWrites = new ArrayList<>();
        for (Record successfullyDeletedSourceRecord : sources) {
            tableWrites.add(makeSourceRecordMerged(successfullyDeletedSourceRecord, target));
            tableWrites.add(makeSourceDependentRecordsMerged(successfullyDeletedSourceRecord, target));
        }
        return tableWrites;
    }

    private TableWrite makeSourceRecordMerged(Record sourceRecord, Record targetRecord) {
        return TableWrite.createUpdate(RECORD.TABLE)
                .addWhereCol(RECORD.ID, sourceRecord.getId())
                .addCol(RECORD.MASTER_RECORD_ID, targetRecord.getId());
    }

    private TableWrite makeSourceDependentRecordsMerged(Record sourceRecord, Record targetRecord) {
        return TableWrite.createUpdate(RECORD.TABLE)
                .addWhereCol(RECORD.MASTER_RECORD_ID, sourceRecord.getId())
                .addCol(RECORD.MASTER_RECORD_ID, targetRecord.getId());
    }

    private List<TableWrite> moveHoldings(Record sourceRecord, Record targetRecord) {
        return recordHoldingMergeTableWriteGenerator.generateWrites(sourceRecord, targetRecord);
    }

    private TableWrite moveExemplars(Record sourceRecord, Record targetRecord) {
        return TableWrite.createUpdate(KAT1_5.TABLE)
                .addWhereCol(KAT1_5.RECORD_ID, sourceRecord.getId())
                .addCol(KAT1_5.RECORD_ID, targetRecord.getId());
    }

    private TableWrite moveVolumes(Record sourceRecord, Record targetRecord) {
        return TableWrite.createUpdate(VOLUME.VOLUME)
                .addWhereCol(VOLUME.RECORD_ID, sourceRecord.getId())
                .addCol(VOLUME.RECORD_ID, targetRecord.getId());
    }

    private TableWrite moveInvoices(Record sourceRecord, Record targetRecord) {
        return TableWrite.createUpdate(POL_FAK.TABLE)
                .addWhereCol(POL_FAK.FK_ZAZ, sourceRecord.getKindedId())
                .addCol(POL_FAK.FK_ZAZ, targetRecord.getKindedId());
    }

    private TableWrite moveForeignOccurrenceRelations(Record sourceRecord, Record targetRecord) {
        return TableWrite.createUpdate(CENTRAL_PROPOJ.TABLE)
                .addWhereCol(CENTRAL_PROPOJ.CENTRAL_RECORD_ID, sourceRecord.getId())
                .addCol(CENTRAL_PROPOJ.CENTRAL_RECORD_ID, targetRecord.getId());
    }

    private TableWrite deleteExemplarDistributionDefinition(Record sourceRecord) {
        return TableWrite.createDelete(ROZDELOVNIK.TABLE)
                .addWhereCol(ROZDELOVNIK.FK_ZAZ, sourceRecord.getKindedId());
    }

    private TableWrite deleteDocumentAuthoritiesRelations(Record sourceDocument) {
        return TableWrite.createDelete(KAT1_7.KAT1_7)
                .addWhereCol(KAT1_7.SOURCE_RECORD_ID, sourceDocument.getId());
    }

    private List<TableWrite> addMergeOperation(@NonNull Department currentDepartment, BasicUser initiator, Record affectedRecord) {
        RecordOperationType mergingOperationType = recordOperationTypeLoader.getById(RecordOperationType.ID_MERGING);
        RecordOperation operation = new RecordOperation(UuidGenerator.forIdentifier(), null, affectedRecord, currentDepartment, initiator, mergingOperationType, Instant.now());
        return recordOperationTableWriteGenerator.generateWrites(operation);
    }

}
