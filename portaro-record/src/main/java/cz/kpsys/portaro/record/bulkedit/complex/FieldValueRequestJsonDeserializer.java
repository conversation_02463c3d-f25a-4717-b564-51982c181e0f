package cz.kpsys.portaro.record.bulkedit.complex;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdScalarDeserializer;

import java.io.IOException;
import java.util.List;

public class FieldValueRequestJsonDeserializer extends StdScalarDeserializer<FieldValueRequest> {

    public FieldValueRequestJsonDeserializer() {
        super(FieldValueRequest.class);
    }

    @Override
    public FieldValueRequest deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        if (p.getCurrentToken() == JsonToken.VALUE_STRING) {
            return new StringFieldValueRequest(p.getText());
        }
        if (p.getCurrentToken() == JsonToken.START_ARRAY) {
            List<FieldRequest> fields = ctxt.readValue(p, ctxt.getTypeFactory().constructCollectionType(List.class, FieldRequest.class));
            return new SubfieldsFieldValueRequest(fields);
        }
        return (FieldValueRequest) ctxt.handleUnexpectedToken(getValueType(ctxt), p);
    }
}