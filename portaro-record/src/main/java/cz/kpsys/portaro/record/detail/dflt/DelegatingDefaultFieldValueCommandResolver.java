package cz.kpsys.portaro.record.detail.dflt;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.value.FieldValueCommand;
import cz.kpsys.portaro.record.detail.value.StringValueCommand;
import cz.kpsys.portaro.record.edit.DefaultFieldValueGenerator;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DelegatingDefaultFieldValueCommandResolver implements DefaultFieldValueCommandResolver {

    @NonNull DefaultFieldValueResolver defaultFieldValueResolver;
    @NonNull DefaultFieldValueGenerator defaultFieldValueGenerator;

    @Override
    public boolean hasDefault(@NonNull FieldTypeId fieldTypeId, @NonNull Fond fond, @NonNull Department ctx) {
        return defaultFieldValueResolver.findOn(fieldTypeId, fond, ctx).isPresent();
    }

    @Override
    public Optional<FieldValueCommand> findOn(@NonNull FieldTypeId fieldTypeId, @NonNull Fond fond, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        return defaultFieldValueResolver.findOn(fieldTypeId, fond, ctx)
                .flatMap(defaultValue -> resolveDefaultValue(defaultValue, ctx, currentAuth));
    }

    private Optional<? extends FieldValueCommand> resolveDefaultValue(String defaultValue, Department ctx, UserAuthentication currentAuth) {
        if (defaultFieldValueGenerator.isGenerated(defaultValue)) {
            return defaultFieldValueGenerator.generate(defaultValue, ctx, currentAuth);
        }
        return Optional.of(new StringValueCommand(defaultValue, ctx, currentAuth));
    }


}
