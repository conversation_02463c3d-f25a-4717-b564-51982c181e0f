package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.IdentifiedFieldContainer;
import lombok.NonNull;

import java.util.List;

public interface RecordFieldsLoader {

    List<IdentifiedFieldContainer> load(@NonNull List<RecordIdFondPair> recordIdFondPairs);

    IdentifiedFieldContainer refresh(@NonNull Record record);
}
