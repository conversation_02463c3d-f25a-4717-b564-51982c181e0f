package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.date.DateRangeToStringConverter;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DateRangeTypedValueConverter implements TypedFieldValueConverter<DateRangeValueCommand, FieldPayload<DateRangeFieldValue>, DateRangeFieldValue> {

    public static final DateRangeToStringConverter rawDateFormat = DateRangeToStringConverter.ofInternalFormat();

    @NonNull
    @Override
    public FieldPayload<DateRangeFieldValue> convert(@NonNull DateRangeValueCommand command, @NonNull FieldType<?> fieldType, @NonNull RecordFieldId fieldId) {
        return FieldPayload.of(DateRangeFieldValue.of(command.value(), Set.of(fieldId.recordIdFondPair())));
    }

    public static @NonNull String formatLabel(@NonNull DateRange range) {
        return rawDateFormat.convert(range);
    }

    @Override
    public String toString() {
        return getClass().getSimpleName();
    }
}
