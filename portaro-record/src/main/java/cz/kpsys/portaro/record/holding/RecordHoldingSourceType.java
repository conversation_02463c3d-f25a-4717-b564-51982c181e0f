package cz.kpsys.portaro.record.holding;

import com.fasterxml.jackson.annotation.JsonFormat;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum RecordHoldingSourceType implements LabeledIdentified<Integer> {

    CREATION(1, Texts.ofMessageCoded("record.holdingSource.Creation")),
    ADOPTION(2, Texts.ofMessageCoded("record.holdingSource.Takeover")),
    EXTERNAL_LOAN_RECORD(3, Texts.ofMessageCoded("record.holdingSource.ExternalSource"));

    public static final Codebook<RecordHoldingSourceType, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer id;
    @NonNull Text text;
    
}
