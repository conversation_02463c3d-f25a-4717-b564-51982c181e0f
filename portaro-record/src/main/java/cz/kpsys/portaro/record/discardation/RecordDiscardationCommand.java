package cz.kpsys.portaro.record.discardation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import lombok.NonNull;

import java.util.Set;

public record RecordDiscardationCommand(

        @NonNull
        Record record,

        @NonNull
        Set<Department> holdingDepartments,

        @NonNull
        Set<Department> exemplarDepartments,

        @NonNull
        Department ctx,

        @NonNull
        UserAuthentication currentAuth
) {

    public static RecordDiscardationCommand withoutHoldingsAndExemplars(@NonNull Record record, @NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth) {
        return new RecordDiscardationCommand(record, Set.of(), Set.of(), currentDepartment, currentAuth);
    }

}
