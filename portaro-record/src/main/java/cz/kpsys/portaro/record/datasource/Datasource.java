package cz.kpsys.portaro.record.datasource;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.object.LabeledIdentifiedRecord;
import cz.kpsys.portaro.record.fond.Fond;

import java.util.Collection;

public interface Datasource extends LabeledIdentifiedRecord<String> {

    String DATASOURCE_LOCAL_ID = "local";
    String DATASOURCE_LOCAL_ALL_DEPARTMENTS_ID = "local-root";
    String DATASOURCE_EXTERNAL_GROUP = "external-";

    @JsonIgnore
    Collection<Fond> supportedFonds();

    default boolean supportsFond(Fond desiredFond) {
        return supportedFonds().stream()
                .anyMatch(desiredFond::equals);
    }

    default boolean supportsAnyOfFonds(Collection<Fond> desiredFonds) {
        return desiredFonds.stream()
                .anyMatch(this::supportsFond);
    }

    @JsonIgnore
    Collection<String> supportedSubkinds();

    default boolean supportsSubkind(String desiredSubkind) {
        return supportedSubkinds().stream()
                .anyMatch(desiredSubkind::equals);
    }

    default boolean supportsAnyOfSubkind(Collection<String> desiredSubkinds) {
        return desiredSubkinds.stream()
                .anyMatch(this::supportsSubkind);
    }

    default boolean isOfGroup(String group) {
        return getId().startsWith(group);
    }
}
