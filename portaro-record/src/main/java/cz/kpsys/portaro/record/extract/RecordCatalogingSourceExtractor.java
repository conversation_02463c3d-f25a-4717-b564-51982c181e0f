package cz.kpsys.portaro.record.extract;

import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.By;
import cz.kpsys.portaro.record.detail.Field;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.stream.Stream;

import static cz.kpsys.portaro.record.RecordWellKnownFields.DocumentCatalogingSource;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordCatalogingSourceExtractor implements RecordValueExtractor {

    @Override
    public Stream<String> extractValue(Record record) {
        return record.getDetail().getFields(By.typeId(DocumentCatalogingSource.TYPE_ID), By.<Field<?>>notEmpty().and(By.typeId(DocumentCatalogingSource.Value.TYPE_ID)))
                .stream()
                .map(Field::getRaw);
    }
}
