package cz.kpsys.portaro.record.detail.binding;

import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.value.AcceptableValueFieldValue;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FondBoundFieldsLoader implements Function<Set<RecordIdFondPair>, Map<RecordIdFondPair, ? extends ScalarFieldValue<?>>> {

    @Override
    public Map<RecordIdFondPair, AcceptableValueFieldValue<Fond>> apply(@NonNull Set<RecordIdFondPair> recordIdentifiers) {
        Map<RecordIdFondPair, AcceptableValueFieldValue<Fond>> recordIdFondPairBindingMap = new HashMap<>();
        for (RecordIdFondPair bindingToLoadRecord : recordIdentifiers) {
            recordIdFondPairBindingMap.put(bindingToLoadRecord, AcceptableValueFieldValue.ofGeneric(bindingToLoadRecord.fond(), Set.of(bindingToLoadRecord)));
        }
        return recordIdFondPairBindingMap;
    }
}
