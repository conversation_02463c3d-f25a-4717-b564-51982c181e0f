package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.commons.image.ImageData;
import cz.kpsys.portaro.commons.image.ImageUtils;
import cz.kpsys.portaro.commons.io.*;
import cz.kpsys.portaro.file.ImageDownloader;
import cz.kpsys.portaro.file.LoadedFile;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestClientResponseException;

import java.io.Serializable;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class FailoveredCoverDownloader implements CoverDownloader, Serializable {

    @NonNull ImageDownloader imageDownloader;
    @NonNull Failoverer failoverer;
    @NonNull CoverFactory coverFactory;

    public @NonNull LoadedFile getCoverFromUrl(@NonNull Record record, @NonNull String service,
                                               @NonNull String url) throws CoverSearchException {
        log.debug("Downloading cover from {}", url);

        ImageData image;
        try {
            image = failoverer.call(url, new ExternalImageDownloadCaller(imageDownloader));
        } catch (ExternalServiceResponseException e) {
            if (e.getCause() instanceof RestClientResponseException restEx) {
                if (restEx.getStatusCode().isSameCodeAs(HttpStatus.FORBIDDEN)) {
                    throw new CoverSearchException(service, CoverSearchException.DOCASNE_ZABLOKOVANO, e);
                }
                if (restEx.getStatusCode().isSameCodeAs(HttpStatus.BAD_REQUEST)) {
                    throw new MissingCoverException(service);
                }
                if (restEx.getStatusCode().isSameCodeAs(HttpStatus.NOT_FOUND)) {
                    throw new MissingCoverException(service);
                }
            }
            log.warn("Cover download connection problem (document {}, url {})", record.getId(), url);
            throw new CoverSearchException(service, CoverSearchException.CONNECTION_PROBLEM, e);
        } catch (ExternalServiceException e) {
            log.warn("Cover download connection problem (document {}, url {})", record.getId(), url);
            throw new CoverSearchException(service, CoverSearchException.CONNECTION_PROBLEM, e);
        }

        if (image.isValid()) {
            byte[] imageBytes = ImageUtils.extractDataFromImage(image.toBufferedImage());
            return coverFactory.createJustDownloaded(record, imageBytes, url);
        }

        throw new MissingCoverException(service);
    }

    @RequiredArgsConstructor
    private static class ExternalImageDownloadCaller implements ExternalServiceCaller<ImageData> {

        private final @NonNull ImageDownloader imageDownloader;

        @Override
        public @NonNull ImageData call(@NonNull String url) throws ExternalServiceException {
            try {
                return imageDownloader.downloadImage(url);
            } catch (ResourceAccessException e) {
                throw new ExternalServiceAccessException("Rest IO error", e);
            } catch (RestClientResponseException e) {
                if (e.getStatusCode().is5xxServerError()) {
                    throw new ExternalServiceAccessException("Rest server error", e);
                }
                throw new ExternalServiceResponseException("Rest error", e);
            }
        }
    }
    
}
