package cz.kpsys.portaro.record.deletion;

import cz.kpsys.portaro.appserver.config.AppserverConfigService;
import cz.kpsys.portaro.commons.convert.StringToUuidConverter;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.databasestructure.FileDb;
import cz.kpsys.portaro.databasestructure.GeneralDbConstants.TASK_QUEUE;
import cz.kpsys.portaro.databasestructure.LoggingDb;
import cz.kpsys.portaro.databasestructure.MessageDb;
import cz.kpsys.portaro.databasestructure.RecordOperationDb;
import cz.kpsys.portaro.databasestructure.UserDb.UZIVATELE;
import cz.kpsys.portaro.file.directory.ParentableDirectory;
import cz.kpsys.portaro.record.file.DirectoryAndItsContentIdLoader;
import cz.kpsys.portaro.sql.generator.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;

import static cz.kpsys.portaro.commons.db.QueryUtils.AS;
import static cz.kpsys.portaro.commons.db.QueryUtils.TC;
import static cz.kpsys.portaro.databasestructure.RecordDb.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RecordHardDeleter {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull TransactionTemplate transactionTemplate;
    @NonNull QueryFactory qf;
    @NonNull Runnable saveTransactionAuthenticator;
    @NonNull DirectoryAndItsContentIdLoader directoryAndItsContentIdLoader;
    @NonNull AppserverConfigService appserverConfigService;

    boolean shouldDeleteFromKat13;

    public void delete(@NonNull List<UUID> recordIds, boolean withIndexDeletion, boolean withUserRecordDeletion) {
        appserverConfigService.disableIndexing();
        try {
            ListUtil.chunks(recordIds, 200)
                    .forEach(chunk -> deleteTransactional(chunk, withIndexDeletion, withUserRecordDeletion));
        } finally {
            try {
                appserverConfigService.enableIndexing();
            } catch (Exception enableIndexingException) {
                log.error("Exception while enabling indexing in appserver", enableIndexingException);
            }
        }
    }

    private void deleteTransactional(@NonNull List<UUID> recordIds, boolean withIndexDeletion, boolean withUserRecordDeletion) {
        transactionTemplate.executeWithoutResult(_ -> deleteImpl(recordIds, withIndexDeletion, withUserRecordDeletion));
    }

    private void deleteImpl(@NonNull List<UUID> recordIds, boolean withIndexDeletion, boolean withUserRecordDeletion) {
        List<Query> queries = new ArrayList<>();

        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, OBALKYKNIH.TABLE, OBALKYKNIH.RECORD_ID, recordIds));

        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, KAT1_1.TABLE, KAT1_1.RECORD_ID, recordIds));
        queries.add(SqlDataClearHelper.updateColumnLike(qf, KAT1_1.TABLE, KAT1_1.OBSAH, "^aLINK WAS HARD DELETED", KAT1_1.OBSAH, recordIds));

        fillDeleteFromKat1_3(queries, recordIds);

        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, KAT1_4.TABLE, KAT1_4.RECORD_ID, recordIds));
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, KAT1_7.KAT1_7, KAT1_7.SOURCE_RECORD_ID, recordIds));
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, KAT1_7.KAT1_7, KAT1_7.TARGET_RECORD_ID, recordIds));

        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, RECORD_FIELD.TABLE, RECORD_FIELD.RECORD_ID, recordIds));
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, RECORD_FIELD.TABLE, RECORD_FIELD.TARGET_RECORD_ID, recordIds));
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, RECORD_KEY.TABLE, RECORD_KEY.RECORD_ID, recordIds));

        fillDeleteFromHoldings(queries, recordIds);

        if (withUserRecordDeletion) {
            queries.add(SqlDataClearHelper.updateColumnEq(qf, UZIVATELE.TABLE, UZIVATELE.RECORD_ID, null, UZIVATELE.RECORD_ID, recordIds));
        }

        fillDeleteFromMessages(queries, recordIds);

        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, OPAC_RATING.OPAC_RATING, OPAC_RATING.RECORD_ID, recordIds)); // pocet zobrazeni zaznamu se inkrementuje asynchronne 10s po zobrazeni, takze to dame co nejbliz smazani samotneho recordu
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, RECORD.TABLE, RECORD.ID, recordIds));

        fillDeleteFromEvents(queries, recordIds);

        fillDeleteFromFulltext(queries, recordIds);

        if (withIndexDeletion) {
            queries.addAll(recordIds.stream().map(recordId -> scheduleRecordReindexQuery(qf, recordId)).toList());
        }

        saveTransactionAuthenticator.run();
        queries.forEach(query ->
                jdbcTemplate.update(query.getSql(), query.getParamMap())
        );

        log.info("Records %s were HARD deleted".formatted(recordIds));
    }

    private void fillDeleteFromMessages(List<Query> queries, @NonNull List<UUID> recordIds) {
        SelectQuery q1 = qf.newSelectQuery();
        q1.select(MessageDb.THREAD_PARTICIPANT.ID)
                .from(MessageDb.THREAD_PARTICIPANT.TABLE)
                .where().in(MessageDb.THREAD_PARTICIPANT.THREAD_ID, recordIds).or().in(MessageDb.THREAD_PARTICIPANT.PARTICIPANT_ID, recordIds);

        List<UUID> threadAndParticipantIds = jdbcTemplate.queryForList(q1.getSql(), q1.getParamMap(), UUID.class);

        SelectQuery q2 = qf.newSelectQuery();
        q2.select(MessageDb.MESSAGE.ID)
                .from(MessageDb.MESSAGE.TABLE)
                .where().in(MessageDb.MESSAGE.THREAD_RECORD_ID, threadAndParticipantIds);

        List<UUID> messageIds = jdbcTemplate.queryForList(q2.getSql(), q2.getParamMap(), UUID.class);

        SelectQuery q3 = qf.newSelectQuery();
        q3.select(MessageDb.MESSAGE.CREATION_EVENT_ID)
                .from(MessageDb.MESSAGE.TABLE)
                .where().in(MessageDb.MESSAGE.ID, messageIds);

        List<UUID> messageCreationEventIds = jdbcTemplate.queryForList(q3.getSql(), q3.getParamMap(), UUID.class);

        SelectQuery q4 = qf.newSelectQuery();
        q4.select(MessageDb.MESSAGE_SENDING.ID)
                .from(MessageDb.MESSAGE_SENDING.TABLE)
                .where().in(MessageDb.MESSAGE_SENDING.MESSAGE_ID, messageIds);

        List<UUID> messageSendingIds = jdbcTemplate.queryForList(q4.getSql(), q4.getParamMap(), UUID.class);

        SelectQuery q5 = qf.newSelectQuery();
        q5.select(MessageDb.MESSAGE_SENDING.SENDING_EVENT_ID)
                .from(MessageDb.MESSAGE_SENDING.TABLE)
                .where().in(MessageDb.MESSAGE_SENDING.MESSAGE_ID, messageIds);

        List<UUID> sendingEventIds = jdbcTemplate.queryForList(q5.getSql(), q5.getParamMap(), UUID.class);

        SelectQuery q6 = qf.newSelectQuery();
        q6.select(MessageDb.MESSAGE_SENDING_POST.PRINTING_EVENT_ID)
                .from(MessageDb.MESSAGE_SENDING_POST.TABLE)
                .where().in(MessageDb.MESSAGE_SENDING_POST.MESSAGE_SENDING_ID, messageIds);

        List<UUID> printingEventIds = jdbcTemplate.queryForList(q6.getSql(), q6.getParamMap(), UUID.class);

        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, MessageDb.MESSAGE_SENDING_SMS.TABLE, MessageDb.MESSAGE_SENDING_SMS.ID, messageSendingIds));
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, MessageDb.MESSAGE_SENDING_POST.TABLE, MessageDb.MESSAGE_SENDING_POST.ID, messageSendingIds));
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, MessageDb.MESSAGE_SENDING_INTERNAL.TABLE, MessageDb.MESSAGE_SENDING_INTERNAL.ID, messageSendingIds));
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, MessageDb.MESSAGE_SENDING_EMAIL_ADDRESS.TABLE, MessageDb.MESSAGE_SENDING_EMAIL_ADDRESS.ID, messageSendingIds));
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, MessageDb.MESSAGE_SENDING_EMAIL.TABLE, MessageDb.MESSAGE_SENDING_EMAIL.ID, messageSendingIds));
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, MessageDb.MESSAGE_SENDING.TABLE, MessageDb.MESSAGE_SENDING.ID, messageSendingIds));
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, MessageDb.THREAD_PARTICIPANT.TABLE, MessageDb.THREAD_PARTICIPANT.PARTICIPANT_ID, recordIds));
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, MessageDb.THREAD_PARTICIPANT.TABLE, MessageDb.THREAD_PARTICIPANT.THREAD_ID, recordIds));
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, MessageDb.MESSAGE.TABLE, MessageDb.MESSAGE.ID, messageIds));

        //EVENTS
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, LoggingDb.EVENT.TABLE, LoggingDb.EVENT.ID, printingEventIds));
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, LoggingDb.EVENT.TABLE, LoggingDb.EVENT.ID, sendingEventIds));
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, LoggingDb.EVENT.TABLE, LoggingDb.EVENT.ID, messageCreationEventIds));
    }

    private void fillDeleteFromEvents(List<Query> queries, @NonNull List<UUID> recordIds) {
        SelectQuery q1 = qf.newSelectQuery();
        q1.select(RecordOperationDb.RECORD_OPERATION.EVENT_ID)
                .from(RecordOperationDb.RECORD_OPERATION.TABLE)
                .where().in(RecordOperationDb.RECORD_OPERATION.RECORD_ID, recordIds);

        List<UUID> eventsRecordOperation = jdbcTemplate.queryForList(q1.getSql(), q1.getParamMap(), UUID.class);
        List<UUID> events = new ArrayList<>(eventsRecordOperation);

        SelectQuery q2 = qf.newSelectQuery();
        q2.select(RECORD.CREATION_EVENT_ID, RECORD.ACTIVATION_EVENT_ID, RECORD.DELETION_EVENT_ID)
                .from(RECORD.TABLE)
                .where().in(RECORD.ID, recordIds);
        List<Map<String, Object>> eventsRecord = jdbcTemplate.queryForList(q2.getSql(), q2.getParamMap());
        eventsRecord.stream()
                .flatMap(stringObjectMap -> stringObjectMap.values().stream())
                .filter(Objects::nonNull)
                .map(StringToUuidConverter::tryConvertFromObject)
                .forEach(events::add);

        SelectQuery q3 = qf.newSelectQuery();
        q3.select(RECORD_HOLDING.CREATION_EVENT_ID, RECORD_HOLDING.DISCARDION_EVENT_ID, RECORD_HOLDING.DELETION_EVENT_ID)
                .from(RECORD_HOLDING.TABLE)
                .where().in(RECORD_HOLDING.RECORD_ID, recordIds);
        List<Map<String, Object>> eventsRecordHolding = jdbcTemplate.queryForList(q3.getSql(), q3.getParamMap());
        eventsRecordHolding.stream()
                .flatMap(stringObjectMap -> stringObjectMap.values().stream())
                .filter(Objects::nonNull)
                .map(StringToUuidConverter::tryConvertFromObject)
                .forEach(events::add);

        queries.add(SqlDataClearHelper.deleteFromTableByListOfIds(qf, LoggingDb.EVENT.TABLE, LoggingDb.EVENT.ID, events));
    }

    private void fillDeleteFromHoldings(List<Query> queries, @NonNull List<UUID> recordIds) {
        SelectQuery sq = qf.newSelectQuery();
        sq.select(RECORD_HOLDING.ID)
                .from(RECORD_HOLDING.TABLE)
                .where().in(RECORD_HOLDING.RECORD_ID, recordIds);
        List<UUID> recordHoldingIds = jdbcTemplate.queryForList(sq.getSql(), sq.getParamMap(), UUID.class);

        queries.add(SqlDataClearHelper.deleteFromTableByListOfIds(qf, RECORD_HOLDING_SOURCE.TABLE, RECORD_HOLDING_SOURCE.RECORD_HOLDING_ID, recordHoldingIds));
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, RECORD_HOLDING.TABLE, RECORD_HOLDING.RECORD_ID, recordIds));
        queries.add(SqlDataClearHelper.deleteFromTableByColumnIn(qf, RecordOperationDb.RECORD_OPERATION.TABLE, RecordOperationDb.RECORD_OPERATION.RECORD_ID, recordIds));
    }

    private void fillDeleteFromKat1_3(List<Query> queries, @NonNull List<UUID> recordIds) {
        if (!shouldDeleteFromKat13) {
            return;
        }

        SelectQuery sq = qf.newSelectQuery();
        sq.select(KAT1_4.ID_ZAZ)
                .from(KAT1_4.TABLE)
                .where().in(KAT1_4.RECORD_ID, recordIds);
        List<Long> idZaznamu = jdbcTemplate.queryForList(sq.getSql(), sq.getParamMap(), Long.class);

        queries.add(SqlDataClearHelper.deleteFromTableByListOfIds(qf, KAT1_3.TABLE, KAT1_3.FK_ZAZ, idZaznamu));
    }

    private void fillDeleteFromFulltext(List<Query> queries, @NonNull List<UUID> recordIds) {
        List<Integer> dirs = new ArrayList<>();

        recordIds.forEach(recordId -> dirs.addAll(
                directoryAndItsContentIdLoader.getDirectoryIdAndContentIds(recordId)
                        .stream()
                        .map(ParentableDirectory::getId)
                        .toList()));

        queries.add(SqlDataClearHelper.deleteFromTableByListOfIds(qf, FileDb.FULLTEXT_SOUBORY.TABLE, FileDb.FULLTEXT_SOUBORY.FK_FULLTEXT_SKUPINY, dirs));
        queries.add(SqlDataClearHelper.deleteFromTableByListOfIds(qf, FileDb.FULLTEXT_SKUPINY.TABLE, FileDb.FULLTEXT_SKUPINY.ID_FULLTEXT_SKUPINY, dirs));
    }

    public static Query scheduleRecordReindexQuery(QueryFactory qf, @NonNull UUID recordId) {
        InsertQuery iq = qf.newInsertQuery();
        iq.insert(TASK_QUEUE.TABLE);
        iq.values()
                .add(TASK_QUEUE.RECORD_ID, recordId)
                .add(TASK_QUEUE.TYPE, TASK_QUEUE.TYPE_VALUES.TYP_REINDEX);
        return iq;
    }

    public static Query scheduleReindexAllRecordsQueryExcept(QueryFactory qf, @NonNull List<String> excludedRecordIds) {
        InsertSubselectQuery iq = qf.newInsertSubselectQuery();
        iq.insert(TASK_QUEUE.TABLE);
        iq.columns(TASK_QUEUE.RECORD_ID, TASK_QUEUE.TYPE);
        iq.select(TC(RECORD.TABLE, RECORD.ID), AS(TASK_QUEUE.TYPE_VALUES.TYP_REINDEX, TASK_QUEUE.TYPE))
                .from(RECORD.TABLE)
                .where().notIn(TC(RECORD.TABLE, RECORD.ID), excludedRecordIds);
        return iq;
    }

}
