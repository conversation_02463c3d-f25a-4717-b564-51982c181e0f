package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.record.detail.spec.RecordFieldTypeId;
import lombok.NonNull;

public record EmptyMatchFail(

        @NonNull Text text

) implements NonAbsentDataFail {

    public static @NonNull EmptyMatchFail of(RecordFieldTypeId srcRecordFieldType) {
        return new EmptyMatchFail(Texts.ofNative("No cases defined, or all cases failed and no default case provided (src " + srcRecordFieldType + ")"));
    }

    @Override
    public String toString() {
        return "EmptySwitchFail";
    }

}
