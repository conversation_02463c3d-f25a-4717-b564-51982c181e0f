package cz.kpsys.portaro.record.detail.spec;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;
import org.springframework.util.Assert;

import java.util.Comparator;
import java.util.Objects;

/// Napr. d164sa1b:d245#0.a
public record RecordMultifieldId(

        @NonNull
        RecordIndexedId recordIndexedId,

        @NonNull
        FieldTypeId fieldTypeId

) implements RecordDatableId {

    public static Comparator<RecordMultifieldId> NUMERICALLY_COMPATIBLE_SORTER = Comparator.comparing(RecordMultifieldId::recordIndexedId, RecordIndexedId.COMPARATOR)
            .thenComparing(RecordMultifieldId::fieldTypeId, FieldTypeId.NUMERICALLY_COMPATIBLE_SORTER);

    public static RecordMultifieldId of(@NonNull RecordIndexedId recordIndexedId, @NonNull FieldTypeId fieldTypeId) {
        return new RecordMultifieldId(recordIndexedId, fieldTypeId);
    }

    @Override
    public @NonNull RecordIdFondPair recordIdFondPair() {
        return recordIndexedId.recordIdFondPair();
    }

    @JsonIgnore
    public boolean hasParentFieldId() {
        return recordIndexedId instanceof RecordFieldId;
    }

    @JsonIgnore
    public boolean isRoot() {
        return recordIndexedId instanceof RecordRootId;
    }

    @NonNull
    public RecordFieldId toRecordFieldId(int index) {
        FieldId fieldId = recordIndexedId instanceof RecordFieldId recordFieldId
                ? recordFieldId.fieldId().sub(fieldTypeId.getCode(), index) // Napr. d164sa1b:d245#0.a
                : FieldId.top(fieldTypeId.getCode(), index); // Napr. d164sa1b:d245
        return RecordFieldId.of(recordIdFondPair(), fieldId);
    }

    @Override
    public @NonNull RecordFieldTypeId toRecordFieldTypeId() {
        return RecordFieldTypeId.of(recordIdFondPair(), fieldTypeId);
    }

    @NonNull
    public FieldId getParentFieldId() {
        return Objects.requireNonNull(recordIndexedId.fieldId());
    }

    @Override
    public @NonNull RecordFieldId existingParentFieldId() {
        Assert.isInstanceOf(RecordFieldId.class, recordIndexedId, () -> "Cannot get parent field id from root record id (this is " + this + ")");
        return (RecordFieldId) Objects.requireNonNull(recordIndexedId);
    }

    public @NonNull MultifieldId toMultifieldId() {
        return switch (recordIndexedId) {
            case RecordRootId _ -> MultifieldId.ofRoot(fieldTypeId);
            case RecordFieldId recordFieldId -> MultifieldId.ofSub(recordFieldId.fieldId(), fieldTypeId);
        };
    }

    @Override
    public @NonNull RecordSpec toRecordSpec() {
        return RecordSpec.ofRecordMultifield(this);
    }

    public int nestingLevel() {
        return fieldTypeId.getLevel();
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, RecordMultifieldId.class, RecordMultifieldId::recordIndexedId, RecordMultifieldId::fieldTypeId);
    }

    @Override
    public int hashCode() {
        int result = recordIndexedId.hashCode();
        result = 31 * result + fieldTypeId.hashCode();
        return result;
    }

    @Override
    public String toString() {
        return recordIdFondPair().id().abbrDelim() + (recordIndexedId.fieldId() == null ? "" : getParentFieldId().toString() + FieldTypeId.DELIMITER_CHAR) + fieldTypeId.getCode();
    }
}
