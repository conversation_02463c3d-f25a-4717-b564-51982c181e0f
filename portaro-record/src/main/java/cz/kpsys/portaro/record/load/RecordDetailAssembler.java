package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.detail.FondedIdentifiedFieldContainer;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RecordDetailAssembler {

    @NonNull RecordIdFondPair recordIdFondPair;
    @NonNull Multifields loadedRecord;

    public @NonNull FondedIdentifiedFieldContainer assemble() {
        FondedIdentifiedFieldContainer detail = new FondedIdentifiedFieldContainer(recordIdFondPair);
        assembleFields(loadedRecord, detail);
        return detail;
    }

    private void assembleFields(Multifields multifields, FieldContainer container) {
        for (Multifield multifield : multifields.values()) {
            for (FieldSlot fieldSlot : multifield) {
                Field<?> field = fieldSlot.field();
                assembleField(field, container);
                assembleFields(fieldSlot.multifields(), field);
            }
        }
    }

    private void assembleField(Field<?> field, FieldContainer container) {
        EditableFieldType<?> fieldType = field.getType();
        warnInvalidConfig(fieldType, field);
        field.clear(); // removes all subfields (will be re-added in assembleFields)
        container.add(field);
    }

    private void warnInvalidConfig(EditableFieldType<?> fieldType, Field<?> existingField) {
        if (!fieldType.isGroup() && !existingField.getFields().isEmpty()) {
            log.warn("Given field {} is has {} subfields but it has no subfields defined in FDEF/AUT (field {}, defined field type {})", existingField.fieldTypeId(), existingField.getFields().size(), existingField, fieldType);
        }
    }

}
