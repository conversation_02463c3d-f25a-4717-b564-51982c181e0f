package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.spec.RecordFieldTypeId;
import lombok.NonNull;

public record IncorrectTypeFail(

        @NonNull Text text,
        @NonNull Class<?> expectedType,
        @NonNull Class<?> actualType

) implements NonAbsentDataFail {

    public static @NonNull IncorrectTypeFail of(@NonNull Text text, RecordFieldTypeId srcRecordFieldType, Formula<?> operand, Class<?> expectedType, Class<?> actualType) {
        return new IncorrectTypeFail(
                text,
                expectedType,
                actualType
        );
    }

    public static @NonNull IncorrectTypeFail of(RecordFieldTypeId srcRecordFieldType, Formula<?> operand, Class<?> expectedType, Class<?> actualType) {
        return new IncorrectTypeFail(
                Texts.ofNative("Expected type " + expectedType.getSimpleName() + " but actual " + actualType.getSimpleName() + " in " + operand),
                expectedType,
                actualType
        );
    }

    @Override
    public String toString() {
        return "IncorrectType(" + actualType.getSimpleName() + ", expected " + expectedType.getSimpleName() + ")";
    }

}
