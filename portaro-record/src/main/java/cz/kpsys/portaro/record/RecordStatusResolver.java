package cz.kpsys.portaro.record;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.security.PermissionResult;
import cz.kpsys.portaro.security.SecurityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static cz.kpsys.portaro.record.Record.TYPE_AUTHORITY;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
@Slf4j
public class RecordStatusResolver {

    @NonNull ContextualProvider<Department, RecordStatus> publishingDocumentRecordStatusProvider;
    @NonNull ContextualProvider<Department, Optional<RecordStatus>> editLockThreshold;
    @NonNull SecurityManager securityManager;

    public boolean isLockedState(RecordStatus status, Department ctx) {
        Optional<RecordStatus> editLockThresholdOpt = editLockThreshold.getOn(ctx);
        if (editLockThresholdOpt.isEmpty()) {
            return false;
        }
        return status.isSameOrHigherThan(editLockThresholdOpt.get());
    }

    public List<RecordStatusTransition> nextStates(Record record, Department ctx, @NonNull UserAuthentication currentAuth) {
        RecordStatus currentStatus;
        if (record.isActive() || record.getStatus() == RecordStatus.DRAFT) {
            currentStatus = record.getStatus();
        } else {
            log.warn("Record {} is not active (has not activation event id), but record status is not {}", record, RecordStatus.DRAFT.getId());
            currentStatus = RecordStatus.DRAFT;
        }

        List<RecordStatus> nextStates = currentStatus.nextStates(TYPE_AUTHORITY.equals(record.getType()), publishingDocumentRecordStatusProvider.getOn(ctx));

        return nextStates.stream()
                .filter(nextStatus -> !Set.of(RecordStatus.SENT_TO_CASLIN, RecordStatus.ACCEPTED_BY_CASLIN).contains(nextStatus))
                .map(nextStatus -> {
                    // publish
                    if (currentStatus == RecordStatus.DRAFT) {
                        PermissionResult publishPermission = securityManager.resolve(RecordSecurityActions.RECORD_PUBLISH, currentAuth, ctx, record);
                        return new RecordStatusTransition(nextStatus, publishPermission);
                    }

                    // lock
                    if (!isLockedState(currentStatus, ctx) && isLockedState(nextStatus, ctx)) {
                        PermissionResult lockUnlockPermission = securityManager.resolve(RecordSecurityActions.RECORD_LOCK_OR_UNLOCK, currentAuth, ctx, record);
                        return new RecordStatusTransition(nextStatus, lockUnlockPermission);
                    }

                    // unlock
                    if (isLockedState(currentStatus, ctx) && !isLockedState(nextStatus, ctx)) {
                        PermissionResult lockUnlockPermission = securityManager.resolve(RecordSecurityActions.RECORD_LOCK_OR_UNLOCK, currentAuth, ctx, record);
                        return new RecordStatusTransition(nextStatus, lockUnlockPermission);
                    }

                    PermissionResult editFastPermission = securityManager.resolve(RecordSecurityActions.RECORD_EDIT_FAST, currentAuth, ctx, record); // Zjistovani jeho konkretnich prav je pomale (nacita se po jednom editLevel z record operation), takze tady budeme resit jen fast variantu
                    return new RecordStatusTransition(nextStatus, editFastPermission);
                })
                .toList();
    }
}
