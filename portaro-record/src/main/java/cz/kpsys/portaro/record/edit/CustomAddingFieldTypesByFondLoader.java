package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.FieldTypeLoader;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiFunction;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CustomAddingFieldTypesByFondLoader implements FieldTypesByFondLoader {

    @NonNull FieldTypesByFondLoader delegate;
    @NonNull FieldTypeLoader fieldTypeLoader;
    @NonNull Map<FieldTypeId, BiFunction<Fond, FieldType<?>, Optional<EditableFieldType<?>>>> customFieldTypeProviders;

    @Override
    public List<EditableFieldType<?>> getTopfieldTypesByFond(@NonNull Fond fond) {
        List<EditableFieldType<?>> fieldTypes = new ArrayList<>(delegate.getTopfieldTypesByFond(fond));

        for (FieldTypeId topfieldTypeId : customFieldTypeProviders.keySet()) {
            Optional<EditableFieldType<?>> editableFieldType = loadCustomFieldType(fond, topfieldTypeId);
            editableFieldType.ifPresent(fieldTypes::add);
        }

        return fieldTypes;
    }

    @Override
    public <RET extends EditableFieldType<?>> RET findTopfieldTypeByFondAndId(@NonNull Fond fond, @NonNull FieldTypeId topfieldTypeId, @NonNull WhenMissing<RET> whenMissing) {
        Assert.isTrue(topfieldTypeId.getLevel() == FieldTypeId.LEVEL_TOPFIELD, () -> "FieldTypeId " + topfieldTypeId + " is not topfield!");

        if (customFieldTypeProviders.containsKey(topfieldTypeId)) {
            Optional<EditableFieldType<?>> actual = loadCustomFieldType(fond, topfieldTypeId);
            if (actual.isPresent()) {
                return (RET) actual.get();
            }
            return whenMissing.onMissing(fond, topfieldTypeId);
        }

        return delegate.findTopfieldTypeByFondAndId(fond, topfieldTypeId, whenMissing);
    }

    private Optional<EditableFieldType<?>> loadCustomFieldType(@NonNull Fond fond, @NonNull FieldTypeId topfieldTypeId) {
        FieldType<?> fieldType = fieldTypeLoader.getTopfieldTypeById(topfieldTypeId);
        var provider = customFieldTypeProviders.get(topfieldTypeId);
        return provider.apply(fond, fieldType);
    }

}