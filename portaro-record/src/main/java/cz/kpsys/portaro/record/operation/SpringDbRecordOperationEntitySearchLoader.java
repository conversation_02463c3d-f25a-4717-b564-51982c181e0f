package cz.kpsys.portaro.record.operation;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.database.RangePagingResultSetExtractor;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.Optional;

import static cz.kpsys.portaro.commons.util.ListUtil.getListOfIds;
import static cz.kpsys.portaro.databasestructure.RecordOperationDb.RECORD_OPERATION.*;
import static cz.kpsys.portaro.search.CoreSearchParams.FROM_DATE;
import static cz.kpsys.portaro.search.CoreSearchParams.TO_DATE;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SpringDbRecordOperationEntitySearchLoader extends AbstractSpringDbSearchLoader<MapBackedParams, RecordOperationEntity, RangePaging> {

    @NonNull RowMapper<RecordOperationEntity> rowMapper;

    public SpringDbRecordOperationEntitySearchLoader(@NonNull NamedParameterJdbcOperations jdbcTemplate,
                                                     @NonNull QueryFactory queryFactory) {
        super(jdbcTemplate, queryFactory);
        this.rowMapper = new RecordOperationEntityRowMapper();
    }

    @Override
    protected void select(@NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        sq.select(
                ID,
                RECORD_ID,
                EVENT_ID,
                FK_UZIV,
                TYPE_ID,
                FOND_ID,
                DEPARTMENT_ID,
                CREATION_DATE
        );
    }


    @Override
    protected boolean fillQuery(boolean count, @NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        sq.from(TABLE);

        if (p.hasNotNull(RecordConstants.SearchParams.RECORD)) {
            if (!p.hasLength(RecordConstants.SearchParams.RECORD)) {
                return false;
            }
            sq.where().and().in(RECORD_ID, p.get(RecordConstants.SearchParams.RECORD));
        }

        if (p.hasNotNull(RecordConstants.SearchParams.OPERATION_TYPE)) {
            if (!p.hasLength(RecordConstants.SearchParams.OPERATION_TYPE)) {
                return false;
            }
            sq.where().and().in(TYPE_ID, getListOfIds(p.get(RecordConstants.SearchParams.OPERATION_TYPE)));
        }

        if (p.hasNotNull(RecordConstants.SearchParams.OPERATED_FOND)) {
            if (!p.hasLength(RecordConstants.SearchParams.OPERATED_FOND)) {
                return false;
            }
            sq.where().and().in(FOND_ID, getListOfIds(p.get(RecordConstants.SearchParams.OPERATED_FOND)));
        }

        if (p.hasNotNull(CoreSearchParams.INITIATOR)) {
            sq.where().and().eq(FK_UZIV, p.get(CoreSearchParams.INITIATOR).getId());
        }

        if (p.hasNotNull(CoreSearchParams.DEPARTMENT)) {
            if (!p.hasLength(CoreSearchParams.DEPARTMENT)) {
                return false;
            }
            sq.where().and().in(DEPARTMENT_ID, getListOfIds(p.get(CoreSearchParams.DEPARTMENT)));
        }

        if (p.hasNotNull(FROM_DATE)) {
            sq.where().and().gtEq(CREATION_DATE, p.get(FROM_DATE));
        }

        if (p.hasNotNull(TO_DATE)) {
            sq.where().and().ltEq(CREATION_DATE, p.get(TO_DATE));
        }

        return true;
    }

    @Override
    protected Optional<SortingItem> defaultOrCustomSorting(@Nullable SortingItem customSorting) {
        SortingItem sorting = ObjectUtil.firstNotNull(customSorting, SortingItem.ofDefaultMessage(RecordConstants.SearchParams.OPERATION_DATE.getName(), true));
        String column;
        if (sorting.getField().equals(RecordConstants.SearchParams.OPERATION_DATE.getName())) {
            column = CREATION_DATE;
        } else {
            throw new UnsupportedOperationException("Sorting by %s is not supported yet".formatted(sorting.getField()));
        }
        if (sorting.isAsc()) {
            return Optional.of(SortingItem.ofAsc(column));
        }
        return Optional.of(SortingItem.ofDesc(column));
    }

    @Override
    protected ResultSetExtractor<Chunk<RecordOperationEntity, RangePaging>> createResultSetExtractor(@NonNull SelectQuery sq, @NonNull MapBackedParams p, @NonNull RangePaging paging, @NonNull Sorting sorting) {
        return new RangePagingResultSetExtractor<>(rowMapper, paging);
    }

}
