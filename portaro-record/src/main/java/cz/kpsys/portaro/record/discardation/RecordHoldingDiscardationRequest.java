package cz.kpsys.portaro.record.discardation;

import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.record.holding.RecordHolding;
import jakarta.validation.constraints.NotNull;
import lombok.With;
import lombok.experimental.FieldNameConstants;

@Form(id = "recordHoldingDiscardation", title = "{commons.ReallyDiscard}")
@FormSubmit(path = "/api/record-holdings/discard")
@With
@FieldNameConstants
public record RecordHoldingDiscardationRequest(

        @NotNull
        RecordHolding recordHolding

) {}
