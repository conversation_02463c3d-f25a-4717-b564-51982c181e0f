package cz.kpsys.portaro.record.revision;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EntityToRecordRevisionConverter implements Converter<RecordRevisionEntity, RecordRevision> {

    @NonNull ByIdLoadable<Record, Integer> nonDetailedDocumentByKindedIdLoader;
    @NonNull ByIdLoadable<Record, Integer> nonDetailedAuthorityByKindedIdLoader;

    @Override
    public RecordRevision convert(@NonNull RecordRevisionEntity entity) {
        return new RecordRevision(
                getRecord(entity),
                entity.date(),
                convertContent(entity)
        );
    }

    private Record getRecord(RecordRevisionEntity entity) {
        String prefixedKindedId = entity.prefixedKindedId();
        prefixedKindedId = StringUtil.removePrefixOpt(prefixedKindedId, "M").orElse(prefixedKindedId);
        Optional<String> documentKindedId = StringUtil.removePrefixOpt(prefixedKindedId, "D");
        if (documentKindedId.isPresent()) {
            return nonDetailedDocumentByKindedIdLoader.getById(Integer.parseInt(documentKindedId.get()));
        }
        Optional<String> authorityKindedId = StringUtil.removePrefixOpt(prefixedKindedId, "A");
        if (authorityKindedId.isPresent()) {
            return nonDetailedAuthorityByKindedIdLoader.getById(Integer.parseInt(authorityKindedId.get()));
        }
        throw new IllegalStateException("Invalid record revision record id '%s'".formatted(entity.prefixedKindedId()));
    }

    private String convertContent(RecordRevisionEntity entity) {
        String content = entity.content().trim();
        if (content.contains("<collection>")) {
            content = content.substring(content.indexOf("<record"));
            content = StringUtil.removeSuffix(content, "</collection>");
        }
        return content;
    }

}
