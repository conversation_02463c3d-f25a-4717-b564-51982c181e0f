package cz.kpsys.portaro.record.prop;

import cz.kpsys.portaro.prop.ObjectProperties;
import cz.kpsys.portaro.prop.ObjectPropertiesGenerator;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldContainer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DetailDelegatingRecordPropertiesGenerator implements ObjectPropertiesGenerator<Record> {

    @NonNull DetailRecordPropertiesGenerator detailRecordPropertiesGenerator;

    @NonNull
    @Override
    public ObjectProperties generate(@NonNull Record record) {
        FieldContainer recordDetail = Objects.requireNonNull(record.getDetail(), () -> "Detail of record %s is null".formatted(record));
        return detailRecordPropertiesGenerator.generate(record.getFond(), recordDetail);
    }

}
