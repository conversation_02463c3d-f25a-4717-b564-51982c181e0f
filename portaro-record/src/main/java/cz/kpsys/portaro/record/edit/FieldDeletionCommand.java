package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.With;

@With(AccessLevel.PRIVATE)
public record FieldDeletionCommand(

        @NonNull
        RecordFieldId recordFieldId,

        boolean missingHierarchyIgnoring,

        boolean influencerFieldPreferring,

        boolean emptyHierarchyDeleting,

        @NonNull
        DeletionLevel level
) {

    public enum DeletionLevel {
        CLEAR,
        DELETE
    }

    public static FieldDeletionCommand of(@NonNull RecordFieldId recordFieldId) {
        return new FieldDeletionCommand(recordFieldId, false, false, false, DeletionLevel.DELETE);
    }

    public static FieldDeletionCommand of(@NonNull RecordIdFondPair recordIdFondPair, @NonNull FieldId fieldId) {
        return of(RecordFieldId.of(recordIdFondPair, fieldId));
    }

    public static FieldDeletionCommand of(@NonNull Record record, @NonNull FieldId fieldId) {
        return of(record.idFondPair(), fieldId);
    }

    public FieldDeletionCommand ignoreMissingHierarchy() {
        return withMissingHierarchyIgnoring(true);
    }

    public FieldDeletionCommand notIgnoreMissingHierarchy() {
        return withMissingHierarchyIgnoring(false);
    }

    public FieldDeletionCommand preferInfluencerFieldDeletion() {
        return withInfluencerFieldPreferring(true);
    }

    public FieldDeletionCommand notPreferInfluencerFieldDeletion() {
        return withInfluencerFieldPreferring(false);
    }

    public FieldDeletionCommand deleteAlsoEmptyHierarchy() {
        return withEmptyHierarchyDeleting(true);
    }

    public FieldDeletionCommand notDeleteAlsoEmptyHierarchy() {
        return withEmptyHierarchyDeleting(false);
    }

    public FieldDeletionCommand clearOnly() {
        return withLevel(DeletionLevel.CLEAR);
    }

    public @NonNull FieldId fieldId() {
        return recordFieldId.fieldId();
    }

}
