package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.object.BasicIdentified;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.*;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import lombok.NonNull;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Set;
import java.util.function.Function;

public record Constant<RES extends FieldValue<?>>(

        @NonNull Provider<RES> value,
        @NonNull ScalarDatatype datatype

) implements NoaryFunction<RES> {

    public static Constant<NumberFieldValue> ofZero() {
        return ofInteger(BigDecimal.ZERO);
    }

    public static Constant<NumberFieldValue> ofOne() {
        return ofInteger(BigDecimal.ONE);
    }

    public static Constant<NumberFieldValue> ofInteger(@NonNull BigDecimal value) {
        Assert.state(value.scale() == 0, () -> "Cannot create integer constant with no-integer value %s".formatted(value));
        NumberFieldValue holder = NumberFieldValue.of(value, Set.of());
        return new Constant<>(StaticProvider.of(holder), CoreConstants.Datatype.NUMBER);
    }

    public static Constant<NumberFieldValue> ofLong(@NonNull Provider<Long> value) {
        return new Constant<>(value.andThen(BigDecimal::valueOf).andThen(number -> NumberFieldValue.of(number, Set.of())), CoreConstants.Datatype.NUMBER);
    }

    public static Constant<NumberFieldValue> ofInteger(@NonNull Provider<Integer> value) {
        return new Constant<>(value.andThen(BigDecimal::valueOf).andThen(number -> NumberFieldValue.of(number, Set.of())), CoreConstants.Datatype.NUMBER);
    }

    public static Constant<LocalDateFieldValue> ofLocalDate(@NonNull Provider<LocalDate> value) {
        return new Constant<>(value.andThen(date -> LocalDateFieldValue.of(date, Set.of())), CoreConstants.Datatype.NUMBER);
    }

    public static Constant<NumberFieldValue> ofDecimal2(@NonNull BigDecimal value) {
        Assert.state(value.scale() <= 2, () -> "Cannot create decimal2 constant with no-decimal2 value %s".formatted(value));
        NumberFieldValue holder = NumberFieldValue.of(value, Set.of());
        return new Constant<>(StaticProvider.of(holder), CoreConstants.Datatype.NUMBER_DECIMAL_2);
    }

    public static Constant<StringFieldValue> ofString(@NonNull String value) {
        return new Constant<>(StaticProvider.of(StringFieldValue.of(value, Set.of())), CoreConstants.Datatype.TEXT);
    }

    public static Constant<StringFieldValue> ofString(@NonNull Provider<String> value) {
        return new Constant<>(() -> StringFieldValue.of(value.get(), Set.of()), CoreConstants.Datatype.TEXT);
    }

    public static Constant<AcceptableValueFieldValue<?>> ofAcceptable(@NonNull Provider<String> value) {
        return new Constant<>(() -> AcceptableValueFieldValue.ofGeneric(new BasicIdentified<>(value.get()), Set.of()), CoreConstants.Datatype.TEXT);
    }


    public static Constant<BooleanFieldValue> ofBoolean(@NonNull Boolean value) {
        return new Constant<>(() -> BooleanFieldValue.of(value, Set.of()), CoreConstants.Datatype.BOOLEAN);
    }

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<RES>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        return datatype;
    }

    public @NonNull FormulaEvaluation<RES> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {
        return FormulaEvaluation.success(value.get());
    }

    @Override
    public String toString() {
        return "Const(%s)".formatted(value);
    }
}
