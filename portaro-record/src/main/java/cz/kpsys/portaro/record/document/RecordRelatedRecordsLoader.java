package cz.kpsys.portaro.record.document;

import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordStatus;
import lombok.NonNull;

import java.util.Collection;
import java.util.List;
import java.util.UUID;

public interface RecordRelatedRecordsLoader {

    List<Record> getAll(
            @NonNull UUID recordId,
            @NonNull Collection<Integer> partsContainingFieldNumbers,
            @NonNull List<RecordStatus> forbiddenRecordStatuses,
            @NonNull List<UUID> forbiddenRecordIds,
            @NonNull Range range
    );
}
