package cz.kpsys.portaro.record.importer.marc;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.fond.Fond;

import java.util.UUID;

public interface RecordImporter<T> {

     Identified<UUID> importRecord(T recordData, Fond fond, Department currentDepartment, UserAuthentication currentAuth);

     Identified<UUID> updateRecord(T recordData, Record record, Department currentDepartment, UserAuthentication currentAuth);

}
