package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.spec.*;
import cz.kpsys.portaro.record.detail.value.FieldPayload;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class StaticBoundRecordFieldLoader implements RecordFieldLoader<Field<?>> {

    @NonNull RecordFieldLoader<Field<?>> delegate;
    @NonNull FieldTypesByFondLoader editableFieldTypesByFondLoader;
    @NonNull Map<FieldTypeId, Function<Set<RecordIdFondPair>, Map<RecordIdFondPair, ? extends ScalarFieldValue<?>>>> bindingLoaders;

    public List<Field<?>> getAll(@NonNull RecordSpecSet specs) {
        if (specs.isEmpty()) {
            return List.of();
        }

        RecordSpecSet delegatingSpecs = RecordSpecSet.ofEmptyModifiable();
        Map<FieldTypeId, Set<RecordIdFondPair>> fieldTypeToLoadingRecordsMap = new HashMap<>();

        for (RecordSpec recordSpec : specs) {
            if (recordSpec.recordMatcher() instanceof RecordIdentifierRecordMatcher identifierRecordMatcher) {
                if (recordSpec instanceof FieldedRecordSpec fieldedRecordSpec) {
                    for (FieldTypeId boundFieldTypeId : bindingLoaders.keySet()) {
                        boolean boundFieldWanted = fieldedRecordSpec.fieldsSpec().covers(FieldsSpec.ofFieldType(boundFieldTypeId));
                        if (boundFieldWanted) {
                            fieldTypeToLoadingRecordsMap.computeIfAbsent(boundFieldTypeId, _ -> new HashSet<>()).add(identifierRecordMatcher.recordIdFondPair());
                            recordSpec = fieldedRecordSpec.minus(FieldsSpec.ofFieldType(boundFieldTypeId));
                        }
                    }
                } else {
                    for (FieldTypeId boundFieldTypeId : bindingLoaders.keySet()) {
                        fieldTypeToLoadingRecordsMap
                                .computeIfAbsent(boundFieldTypeId, _ -> new HashSet<>())
                                .add(identifierRecordMatcher.recordIdFondPair());
                    }
                }
            }
            delegatingSpecs.add(recordSpec);
        }


        List<Field<?>> result = new ArrayList<>(delegate.getAll(delegatingSpecs));

        for (Map.Entry<FieldTypeId, Set<RecordIdFondPair>> fieldTypeToLoadingRecords : fieldTypeToLoadingRecordsMap.entrySet()) {
            FieldTypeId boundFieldTypeId = fieldTypeToLoadingRecords.getKey();
            Set<RecordIdFondPair> loadingRecords = fieldTypeToLoadingRecords.getValue();

            Map<RecordIdFondPair, ? extends ScalarFieldValue<?>> recordIdBindingMap = load(boundFieldTypeId, loadingRecords);

            result.addAll(createFields(boundFieldTypeId, loadingRecords, recordIdBindingMap));
        }

        return result;
    }

    private @NonNull Map<RecordIdFondPair, ? extends ScalarFieldValue<?>> load(@NonNull FieldTypeId boundFieldTypeId, @NonNull Set<RecordIdFondPair> loadingRecords) {
        Function<Set<RecordIdFondPair>, Map<RecordIdFondPair, ? extends ScalarFieldValue<?>>> loader = bindingLoaders.get(boundFieldTypeId);
        return loader.apply(loadingRecords);
    }

    private @NonNull List<Field<? extends ScalarFieldValue<?>>> createFields(@NonNull FieldTypeId boundFieldTypeId, @NonNull Set<RecordIdFondPair> loadingRecordIdFondPairs, @NonNull Map<RecordIdFondPair, ? extends ScalarFieldValue<?>> recordBindingMap) {
        List<Field<? extends ScalarFieldValue<?>>> result = new ArrayList<>();

        for (RecordIdFondPair loadingRecord : loadingRecordIdFondPairs) {
            var binding = recordBindingMap.get(loadingRecord);
            if (binding != null) {
                EditableFieldType<?> fieldType = editableFieldTypesByFondLoader.getTopfieldTypeByFondAndIdOrUnknown(loadingRecord.fond(), boundFieldTypeId);
                var topfield = fieldType.createField(
                        loadingRecord,
                        boundFieldTypeId.toFieldIdWithAllFirstIndices(),
                        UuidGenerator.forIdentifier(),
                        FieldPayload.of(binding)
                );
                result.add(topfield);
            }
        }

        return result;
    }

}
