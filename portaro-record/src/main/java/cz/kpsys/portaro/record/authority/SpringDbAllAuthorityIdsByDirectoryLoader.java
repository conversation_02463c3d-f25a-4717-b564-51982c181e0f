package cz.kpsys.portaro.record.authority;

import cz.kpsys.portaro.file.directory.AllIdsByDirectoryProvider;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.RecordDb.KATAUT_4.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbAllAuthorityIdsByDirectoryLoader implements AllIdsByDirectoryProvider {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;

    @Override
    public List<UUID> getAllByDirectories(@NonNull List<Integer> directoryIds) {
        if (directoryIds.isEmpty()) {
            return List.of();
        }

        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(RECORD_ID);
        sq.from(TABLE);
        sq.where().in(FK_FULLTEXT_SKUPINY, new ArrayList<>(directoryIds));
        return jdbcTemplate.queryForList(sq.getSql(), sq.getParamMap(), UUID.class);
    }

}
