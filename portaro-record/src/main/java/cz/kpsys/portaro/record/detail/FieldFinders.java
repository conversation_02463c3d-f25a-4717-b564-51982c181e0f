package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.NonNull;

import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Stream;

public class FieldFinders {

    public static <CONT extends FieldStreamSource<? super F>, F extends FieldStreamSource<?> & WithFieldType> FieldFinder<CONT, F> byDeepTypeId(@NonNull FieldTypeId typeId) {
        return (FieldFinder<CONT, F>) by(By.typeId(typeId));
    }

    public static <CONT extends FieldStreamSource<F>, F extends FieldStreamSource<?>> FieldFinder<CONT, F> by(Predicate<? super F> predicate) {
        return new FieldFinder<>() {
            @Override
            public Stream<F> findIn(CONT fieldContainer) {
                return ListUtil.nestedBrowseExt((Stream<F>) fieldContainer.streamFields(), streamChildrenFunction())
                        .filter(predicate);
            }

            @Override
            public String toString() {
                return "By{%s}".formatted(predicate);
            }
        };
    }

    private static <F extends FieldStreamSource<?>> @NonNull Function<F, Stream<? extends F>> streamChildrenFunction() {
        return f -> (Stream<F>) f.streamFields();
    }

}
