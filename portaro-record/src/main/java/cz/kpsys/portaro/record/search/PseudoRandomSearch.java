package cz.kpsys.portaro.record.search;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class PseudoRandomSearch<ITEM> extends AbstractStandardSearch<MapBackedParams, ITEM, RangePaging> implements Search<MapBackedParams, ITEM, RangePaging> {

    @NonNull AbstractStandardSearch<MapBackedParams, ITEM, RangePaging> service;

    @Override
    public InternalSearchResult<ITEM, MapBackedParams, RangePaging> loadResult(RangePaging paging, SortingItem sorting, MapBackedParams completeParams, Department ctx, CacheMode cacheMode) {
        int originalPageSize = paging.pageSize();
        final int MULTIPLIER = 4;

        var result = service.loadResult(paging.withPageSize(originalPageSize * MULTIPLIER), sorting, completeParams, ctx, cacheMode);

        var shuffledContent = randomizeOrder(result.content().getItems()).stream()
                .limit(paging.pageSize())
                .toList();

        return result.withContent(result.content().withItems(shuffledContent));
    }

    private List<ITEM> randomizeOrder(List<ITEM> content) {
        var shuffledContent = new ArrayList<>(content);
        Collections.shuffle(shuffledContent);
        return shuffledContent;
    }
}
