package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.record.LabeledRecordRef;
import cz.kpsys.portaro.record.detail.frontend.ErroredFieldResponse;
import lombok.Getter;
import lombok.NonNull;
import lombok.Value;
import lombok.With;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.UUID;

@Value
@With
public class ViewableField implements ViewableFieldable, ViewableFieldContainer, WithRepetition, Identified<UUID> {

    @Deprecated
    @JsonIgnore
    @NonNull
    UUID id;

    @NonNull
    String code;

    @JsonIgnore
    int repetition;

    @Deprecated
    @JsonIgnore
    @NonNull
    Integer fieldRepetition;

    @Deprecated
    @JsonIgnore
    @NonNull
    String typeId;

    @JsonIgnore
    @NonNull
    FieldTypeId fieldTypeId;

    @NonNull
    Text fieldTypeText;

    @Getter(onMethod_ = @JsonIgnore(false)) // jackson does not add @JsonIgnore(false) to getter when annotation is on field
    @NonNull
    List<ViewableField> fields;

    @NonNull Text text;

    @Nullable ErroredFieldResponse error;

    @Getter
    @Nullable
    LabeledRecordRef recordLink;

    @Nullable String raw;

    @JsonIgnore
    @NonNull
    FieldId fieldId;

    boolean url;

    @JsonIgnore
    boolean composite;

    @JsonIgnore
    @Override
    public @NonNull FieldTypeId fieldTypeId() {
        return fieldTypeId;
    }

    @JsonIgnore
    @Override
    public @NonNull FieldType<?> getType() {
        throw new IllegalStateException("This method is forbidden to use in ViewableField");
    }

    @JsonIgnore
    @Nullable
    public UUID getRecordId() {
        if (recordLink == null) {
            return null;
        }
        return recordLink.id();
    }

}
