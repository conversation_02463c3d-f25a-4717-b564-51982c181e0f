package cz.kpsys.portaro.record;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.*;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum RecordStatus implements LabeledIdentified<Integer> {

    NOT_DEFINED(0, Texts.ofMessageCodedOrNativeOrEmptyNative("document.status.0", "Nedefinován")),
    UNKNOWN_STATUS_1(1, Texts.ofMessageCodedOrNativeOrEmptyNative("document.status.1", "Chyba - Nepodporovaný status 1")),
    BACKWARD_CATALOGING(2, Texts.ofMessageCodedOrNativeOrEmptyNative("document.status.2", "Zpětná katalogizace")),
    STANDARD_DOCUMENT(3, Texts.ofMessageCodedOrNativeOrEmptyNative("document.status.3", "Standardní záznam")),
    FINISHED_CATALOGING(4, Texts.ofMessageCodedOrNativeOrEmptyNative("document.status.4", "Ukončená katalogizace")),
    SENT_TO_CASLIN(5, Texts.ofMessageCodedOrNativeOrEmptyNative("document.status.5", "Odesláno do CASLIN")),
    ACCEPTED_BY_CASLIN(6, Texts.ofMessageCodedOrNativeOrEmptyNative("document.status.6", "Akceptováno systémem CASLIN")),
    @Deprecated
    EXCLUDED(9, Texts.ofMessageCoded("record.status.Discarded")),

    NOT_NATIONAL_CREATED_BY_RECONS(10, Texts.ofMessageCodedOrNativeOrEmptyNative("authority.status.0", "Není národní aut., založena při reconsu")), //neni narodni autorita, zalozena pri recons
    PROPOSAL_FOR_FIX_IN_NATIONAL(11, Texts.ofMessageCodedOrNativeOrEmptyNative("authority.status.1", "Navržena na opravu do nár. autorit")), //autorita navrzena na opravu do narodnich autorit
    FIXED_IN_NATIONAL(12, Texts.ofMessageCodedOrNativeOrEmptyNative("authority.status.2", "Opravena v národních autoritách")), //opravena autorita v narodnich autoritach
    CUSTOM(13, Texts.ofMessageCodedOrNativeOrEmptyNative("authority.status.3", "Vlastní autorita")), //zalozena autorita vlastni
    NATIONAL(14, Texts.ofMessageCodedOrNativeOrEmptyNative("authority.status.4", "Národní autorita")), //jedná se o narodni autoritu
    CUSTOM_NATIONAL(15, Texts.ofMessageCodedOrNativeOrEmptyNative("authority.status.5", "Vlastní národní autorita")), //vlastni narodni autorita (definitivni)
    NATIONAL_ARTWORK(16, Texts.ofMessageCodedOrNativeOrEmptyNative("authority.status.6", "Národní autorita - dílo")),

    @Deprecated
    DRAFT(80, Texts.ofMessageCoded("record.status.Draft")),
    @Deprecated
    DELETED(99, Texts.ofMessageCoded("record.status.Deleted"));

    public static final String SCHEMA_EXAMPLE_ID = "4";

    public static final Set<RecordStatus> DEPRECATED_STATUSES = Set.of(EXCLUDED, DRAFT, DELETED);
    public static final Codebook<RecordStatus, Integer> CODEBOOK = new StaticCodebook<>(values());
    public static final Codebook<RecordStatus, Integer> NOT_DEPRECATED_CODEBOOK = new StaticCodebook<>(Arrays.stream(values()).filter(recordStatus -> !DEPRECATED_STATUSES.contains(recordStatus)).toArray(RecordStatus[]::new));

    public static final Codebook<RecordStatus, Integer> LEGACY_AUTHORITY_CODEBOOK = new StaticCodebook<>(Map.of(
            0, NOT_NATIONAL_CREATED_BY_RECONS,
            1, PROPOSAL_FOR_FIX_IN_NATIONAL,
            2, FIXED_IN_NATIONAL,
            3, CUSTOM,
            4, NATIONAL,
            5, CUSTOM_NATIONAL,
            6, NATIONAL_ARTWORK,
            9, EXCLUDED,
            80, DRAFT,
            99, DELETED
    ));

    @NonNull Integer id;
    @NonNull Text text;

    @JsonIgnore
    public boolean isLowerThan(RecordStatus other) {
        return !isSameOrHigherThan(other);
    }

    @JsonIgnore
    public boolean isSameOrHigherThan(RecordStatus other) {
        Integer thisLevel = this.getComparableLevel();
        Integer otherLevel = other.getComparableLevel();

        if (thisLevel == null || otherLevel == null) {
            return false;
        }
        return thisLevel >= otherLevel;
    }

    public boolean isHigherThan(RecordStatus other) {
        Integer thisLevel = this.getComparableLevel();
        Integer otherLevel = other.getComparableLevel();

        if (thisLevel == null || otherLevel == null) {
            return false;
        }
        return thisLevel > otherLevel;
    }

    @JsonIgnore
    public Integer getComparableLevel() {
        if (this.equals(DRAFT)) {
            return -1;
        }
        if (isAuthority()) {
            return toLegacyAuthorityStatusId();
        }
        if (isDocument()) {
            return this.getId();
        }
        return null;
    }

    @JsonIgnore
    private boolean isAuthority() {
        return switch (this) {
            case NOT_NATIONAL_CREATED_BY_RECONS, PROPOSAL_FOR_FIX_IN_NATIONAL, FIXED_IN_NATIONAL,
                 CUSTOM, NATIONAL, CUSTOM_NATIONAL, NATIONAL_ARTWORK -> true;
            default -> false;
        };
    }

    @JsonIgnore
    private boolean isDocument() {
        return !isAuthority();
    }

    @JsonIgnore
    public static RecordStatus fromLegacyAuthorityStatusId(Integer legacyAuthorityStatusId) {
        return Optional.ofNullable(LEGACY_AUTHORITY_CODEBOOK.getById(legacyAuthorityStatusId))
                .orElseThrow(() -> new ItemNotFoundException(RecordStatus.class, legacyAuthorityStatusId));
    }

    @JsonIgnore
    public Integer toLegacyAuthorityStatusId() {
        return switch (this) {
            case NOT_NATIONAL_CREATED_BY_RECONS -> 0;
            case PROPOSAL_FOR_FIX_IN_NATIONAL -> 1;
            case FIXED_IN_NATIONAL -> 2;
            case CUSTOM -> 3;
            case NATIONAL -> 4;
            case CUSTOM_NATIONAL -> 5;
            case NATIONAL_ARTWORK -> 6;
            case DRAFT -> 80;
            case DELETED -> 99;
            default -> throw new IllegalArgumentException(String.format("This enum value %s is not for authority", name()));
        };
    }

    @JsonIgnore
    public List<RecordStatus> nextStates(boolean authority, RecordStatus publishingDocumentStatus) {
        if (authority) {
            return switch (this) {
                case DRAFT -> List.of(NATIONAL);
                default -> List.of();
            };
        }
        return switch (this) {
            case DRAFT,
                 NOT_DEFINED,
                 UNKNOWN_STATUS_1 -> List.of(publishingDocumentStatus);
            case STANDARD_DOCUMENT -> List.of(FINISHED_CATALOGING);
            case FINISHED_CATALOGING -> List.of(STANDARD_DOCUMENT, SENT_TO_CASLIN);
            case SENT_TO_CASLIN -> List.of(ACCEPTED_BY_CASLIN);
            default -> List.of();
        };
    }
}
