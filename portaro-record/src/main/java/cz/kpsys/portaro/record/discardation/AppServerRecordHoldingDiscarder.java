package cz.kpsys.portaro.record.discardation;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.appserver.Appserver;
import cz.kpsys.portaro.appserver.XmlAppserverRequest;
import cz.kpsys.portaro.appserver.handler.ConcreteErrorHandler;
import cz.kpsys.portaro.appserver.handler.HandlerFindingOrChainingAppserverErrorHandler;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.appserver.mapping.NoOpAppserverResponseHandler;
import cz.kpsys.portaro.commons.cache.CacheDeletableById;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordStatus;
import cz.kpsys.portaro.record.deletion.RecordDeletionAppserverRequest;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Map;

import static cz.kpsys.portaro.record.deletion.AppserverRecordDeleter.APPSERVER_ERROR_HANDLER_MAP;
import static java.util.stream.Collectors.toUnmodifiableSet;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AppServerRecordHoldingDiscarder implements RecordDiscarder {

    public static final String PATH = Appserver.APIPATH_SMAZANI_ZAZNAMU;

    @NonNull Map<Integer, ConcreteErrorHandler<RecordDeletionAppserverRequest, ?>> errorHandlers = APPSERVER_ERROR_HANDLER_MAP;

    @NonNull MappingAppserverService mappingAppserver;
    @NonNull ObjectMapper xmlMapper;
    @NonNull List<CacheDeletableById> cacheDeletablesByRecordId;

    @Override
    public void discard(@NonNull RecordDiscardationCommand command) {
        if (command.record().isDeleted()) {
            throw new IllegalStateException("Can not discard already deleted record");
        }

        Record record = command.record();

        RecordDeletionAppserverRequest appserverRequest = new RecordDeletionAppserverRequest(
                record.getId(),
                command.holdingDepartments().stream().map(Identified::getId).collect(toUnmodifiableSet()),
                RecordStatus.EXCLUDED.getId(),
                false,
                false
        );
        XmlAppserverRequest request = XmlAppserverRequest.byPost(PATH, appserverRequest, xmlMapper);

        mappingAppserver.call(
                request,
                NoOpAppserverResponseHandler.create(),
                new HandlerFindingOrChainingAppserverErrorHandler<>(appserverRequest, errorHandlers)
        );

        //smazeme zaznam z cache
        cacheDeletablesByRecordId.forEach(cacheDeletableById -> cacheDeletableById.deleteFromCacheById(record.getId()));
    }
}
