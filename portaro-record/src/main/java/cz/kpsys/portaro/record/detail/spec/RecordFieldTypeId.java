package cz.kpsys.portaro.record.detail.spec;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;

public record RecordFieldTypeId(

        @NonNull
        RecordIdFondPair recordIdFondPair,

        @NonNull
        FieldTypeId fieldTypeId

) {

    public static RecordFieldTypeId of(@NonNull RecordIdFondPair recordIdFondPair, @NonNull FieldTypeId fieldTypeId) {
        return new RecordFieldTypeId(recordIdFondPair, fieldTypeId);
    }

    @JsonIgnore
    public boolean hasParent() {
        return fieldTypeId.hasParent();
    }

    @JsonIgnore
    public boolean isRoot() {
        return fieldTypeId.isRoot();
    }

    @JsonIgnore
    @NonNull
    public RecordFieldTypeId existingParent() {
        return of(recordIdFondPair, fieldTypeId.existingParent());
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, RecordFieldTypeId.class, RecordFieldTypeId::recordIdFondPair, RecordFieldTypeId::fieldTypeId);
    }

    @Override
    public int hashCode() {
        int result = recordIdFondPair.hashCode();
        result = 31 * result + fieldTypeId.hashCode();
        return result;
    }

    @Override
    public String toString() {
        return recordIdFondPair.id().abbrDelim() + fieldTypeId;
    }

}
