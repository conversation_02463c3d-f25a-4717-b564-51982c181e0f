package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.convert.StringToIntegerConverter;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.value.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cz.kpsys.portaro.record.detail.FieldStorageBehaviour.OriginStrategy.ALWAYS_SELF_ORIGIN;

@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordFieldsToFieldsConverter implements Function<List<RecordField>, List<? extends Field<?>>> {

    @Override
    public List<? extends Field<?>> apply(@NonNull List<RecordField> source) {
        return source.stream()
                .sorted(Comparator.comparing(RecordField::getFieldId, FieldId.NUMERICALLY_COMPATIBLE_SORTER).thenComparing(RecordField::getCurrentLevelFieldIndex))
                .map(this::toPrefabField)
                .toList();
    }

    private @NonNull Field<?> toPrefabField(@NonNull RecordField recordField) {
        return new Field<>(
                recordField.getId(),
                recordField.getRecordFieldId(),
                recordField.getFieldType(),
                recordField.getRecordLink(),
                recordField.getTextSuffix(),
                getValue(recordField),
                getSubfields(recordField)
        );
    }

    private @Nullable ScalarFieldValue<?> getValue(@NonNull RecordField recordField) {
        if (recordField.getTextValue() == null) {
            return null;
        }

        FieldType<?> fieldType = recordField.getFieldType();

        Set<RecordIdFondPair> origins;
        if (fieldType.getFieldStorageBehaviour().originStrategy().equals(ALWAYS_SELF_ORIGIN)) {
            origins = Set.of(recordField.getRecordFieldId().recordIdFondPair());
        } else if (recordField.getOriginRecordId() != null) {
            origins = Set.of(recordField.getOriginRecordId());
        } else {
            log.warn("Given field (in record_field) {} has no origin defined, this is only warning for to be sure that we have origin where it should be. Remove this warning soon.", recordField.getRecordFieldId());
            origins = Set.of();
        }

        RecordFieldEntity sourceEntity = Objects.requireNonNull(recordField.getSourceEntity());

        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.DATE) && sourceEntity.dateValue() != null) {
            return LocalDateFieldValue.of(sourceEntity.dateValue(), origins);
        }

        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.DATETIME) && sourceEntity.datetimeValue() != null) {
            return InstantFieldValue.of(sourceEntity.datetimeValue(), origins);
        }

        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.DATE_RANGE) && sourceEntity.dateRangeValue() != null) {
            return DateRangeFieldValue.of(sourceEntity.dateRangeValue(), origins);
        }

        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.DATETIME_RANGE) && sourceEntity.datetimeRangeValue() != null) {
            return DatetimeRangeFieldValue.of(sourceEntity.datetimeRangeValue(), origins);
        }

        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.BOOLEAN) && sourceEntity.booleanValue() != null) {
            return BooleanFieldValue.of(sourceEntity.booleanValue(), origins);
        }

        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER) && sourceEntity.numericValue() != null) {
            return NumberFieldValue.of(sourceEntity.numericValue(), origins);
        }

        if ((fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER_DECIMAL_2) || fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER_DECIMAL_4)) && sourceEntity.numericValue() != null) {
            return NumberFieldValue.of(sourceEntity.numericValue(), origins);
        }

        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER_DECIMAL_6)) {
            throw new UnsupportedOperationException("Number with precision 6 is not supported");
        }

        if (fieldType.getCodebook().isPresent()) {
            if (!Identified.class.isAssignableFrom(fieldType.getDatatypeOrThrow().getJavaType())) {
                log.warn("Codebook is used for field {} but its datatype is not Identified", fieldType.getId());
            }
            if (fieldType.getDatatypeOrThrow().getJavaIdType() == null) {
                log.warn("Id type of codebook of field {} is null", fieldType.getId());
            }
            Class<?> javaIdType = ObjectUtil.firstNotNull(fieldType.getDatatypeOrThrow().getJavaIdType(), String.class);
            if (Integer.class.isAssignableFrom(javaIdType)) {
                // pokud je v record fieldu acceptable value s integerovym ideckem, hodnota nebude v numeric_value, ale normalne jako text, protoze u acceptable value vzdy ukladame jako string
                Codebook<? extends LabeledIdentified<Integer>, Integer> codebook = (Codebook<? extends LabeledIdentified<Integer>, Integer>) fieldType.getCodebook().get();
                Integer numericValue = StringToIntegerConverter.parseIfValidInteger(recordField.getTextValue())
                        .orElseThrow(() -> new IllegalArgumentException("Field type " + fieldType.getId() + " is defined as acceptable value with numeric id but has invalid integer value '" + recordField.getTextValue() + "'"));
                LabeledIdentified<?> acceptableValue = codebook.getById(numericValue);
                return AcceptableValueFieldValue.ofGeneric(acceptableValue, origins);
            }
            if (String.class.isAssignableFrom(javaIdType)) {
                Codebook<? extends LabeledIdentified<String>, String> codebook = (Codebook<? extends LabeledIdentified<String>, String>) fieldType.getCodebook().get();
                LabeledIdentified<?> acceptableValue = codebook.getById(recordField.getTextValue());
                return AcceptableValueFieldValue.ofGeneric(acceptableValue, origins);
            }
            throw new UnsupportedOperationException("Codebook is used for field " + fieldType.getId() + " but its id datatype " + javaIdType + " is not supported");
        }

        return StringFieldValue.of(recordField.getTextValue(), origins);
    }

    private @NonNull List<Field<?>> getSubfields(@NonNull RecordField recordField) {
        return recordField.getSubfields().stream()
                .sorted(Comparator.comparing(RecordField::getCurrentLevelFieldIndex))
                .map(this::toPrefabField)
                .collect(Collectors.toList());
    }

}
