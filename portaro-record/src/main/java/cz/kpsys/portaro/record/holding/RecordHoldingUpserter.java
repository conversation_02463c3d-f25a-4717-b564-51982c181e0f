package cz.kpsys.portaro.record.holding;

import cz.kpsys.portaro.id.UuidGenerator;

public interface RecordHoldingUpserter {

    RecordHoldingUpsertResult upsert(RecordHoldingUpsertCommand command);

    class Testing implements RecordHoldingUpserter {

        @Override
        public RecordHoldingUpsertResult upsert(RecordHoldingUpsertCommand command) {
            RecordHolding savedHolding = new RecordHolding(
                    UuidGenerator.forIdentifier(),
                    command.record().getId(),
                    command.department(),
                    UuidGenerator.forIdentifier(),
                    command.discarded() ? UuidGenerator.forIdentifier() : null,
                    null
            );
            return new RecordHoldingUpsertResult(savedHolding);
        }
    }

}
