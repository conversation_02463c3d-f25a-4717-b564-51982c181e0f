package cz.kpsys.portaro.record.deletion;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RecordHoldingDeletionRequestDefaulter implements TypedAuthenticatedContextualObjectModifier<RecordHoldingDeletionRequest> {

    @Override
    public RecordHoldingDeletionRequest modify(RecordHoldingDeletionRequest formObject, Department ctx, UserAuthentication currentAuth) {
        if (formObject.acceptingExemplarsDeletion() == null) {
            formObject = formObject.withAcceptingExemplarsDeletion(true);
        }
        return formObject;
    }
}
