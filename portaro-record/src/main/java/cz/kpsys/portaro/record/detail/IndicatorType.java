package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.object.BasicNamedLabeledIdentified;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.FallbackingCodebook;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.value.AcceptableValueFieldValue;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class IndicatorType extends BasicNamedLabeledIdentified<FieldTypeId> {

    public static final String STANDARD_EMPTY_IND = "#";
    public static final String WIDE_SUPPORTED_EMPTY_IND = " ";
    public static final Set<String> EMPTY_INDICATORS = Set.of(STANDARD_EMPTY_IND, WIDE_SUPPORTED_EMPTY_IND);
    public static final String DEFAULT_NAME = "default";
    public static final String DEFAULT_IND_1_NAME = "Ind 1";
    public static final String DEFAULT_IND_2_NAME = "Ind 2";
    public static final String NUMERIC_TYPE_PLACEHOLDER = "N";

    @NonNull List<Indicator> acceptableValues;

    private IndicatorType(@NonNull FieldTypeId fieldTypeId, @NonNull String name, @NonNull List<Indicator> acceptableValues) {
        super(fieldTypeId, name);
        this.acceptableValues = acceptableValues;
    }

    public static IndicatorType ofDefaultName(@NonNull FieldTypeId fieldTypeId, @NonNull List<Indicator> acceptableValues) {
        return new IndicatorType(
                fieldTypeId,
                getName(fieldTypeId),
                acceptableValues
        );
    }

    public static @NonNull String getName(@NonNull FieldTypeId fieldTypeId) {
        return fieldTypeId.getCode().equals(FieldTypes.IND_1_FIELD_CODE) ? IndicatorType.DEFAULT_IND_1_NAME : IndicatorType.DEFAULT_IND_2_NAME;
    }

    public static @NonNull ScalarDatatype createIndicatorDatatype(FieldTypeId fieldTypeId) {
        return ScalarDatatype.scalarSplitted(CoreConstants.Datatype.DATATYPE_PREFIX_INDICATOR, fieldTypeId.toString(), String.class);
    }

    public static @NonNull ByIdLoadable<LabeledIdentified<String>, String> unknownIndicatorFactory(FieldTypeId fieldTypeId) {
        return id -> Indicator.createUnknown(fieldTypeId, id);
    }

    @JsonIgnore
    public @NonNull List<Indicator> getAcceptableValues() {
        return acceptableValues;
    }

    @JsonIgnore
    public boolean hasAnyMeaningfulValue() {
        return acceptableValues.stream().anyMatch(indicator -> !IndicatorType.STANDARD_EMPTY_IND.equals(indicator.getId()));
    }

    @JsonIgnore
    private @NonNull Indicator createEmpty() {
        return Indicator.createEmpty(getId());
    }

    public Codebook<Indicator, String> toFallbackingCodebook() {
        return FallbackingCodebook.notInFindById(new StaticCodebook<>(acceptableValues), _ -> createEmpty());
    }

    public static String toMarcXmlIndicatorValue(@Nullable Field<?> ind, boolean emptyIndicatorInStandardHashFormat) {
        String result = null;

        if (ind != null && ind.hasValueHolder()) {
            Assert.isInstanceOf(AcceptableValueFieldValue.class, ind.getExistingValueHolder(), () -> "Indicator %s must have value holder of %s type, but is %s".formatted(ind, AcceptableValueFieldValue.class.getSimpleName(), ind.getExistingValueHolder().getClass().getSimpleName()));
            AcceptableValueFieldValue<LabeledIdentified<String>> valueHolder = (AcceptableValueFieldValue<LabeledIdentified<String>>) ind.getExistingValueHolder();
            result = valueHolder.value().getId();
        }

        if (result == null || IndicatorType.EMPTY_INDICATORS.contains(result)) {
            // we are communicating with appserver with "#" empty indicator and with outer world with " " empty indicator
            result = emptyIndicatorInStandardHashFormat
                    ? IndicatorType.STANDARD_EMPTY_IND
                    : IndicatorType.WIDE_SUPPORTED_EMPTY_IND;
        }

        return result;
    }

    public static String fromMarcXmlIndicatorValue(@NonNull @NotEmpty String ind) {
        if (IndicatorType.EMPTY_INDICATORS.contains(ind)) {
            return IndicatorType.STANDARD_EMPTY_IND;
        }
        return ind;
    }

}
