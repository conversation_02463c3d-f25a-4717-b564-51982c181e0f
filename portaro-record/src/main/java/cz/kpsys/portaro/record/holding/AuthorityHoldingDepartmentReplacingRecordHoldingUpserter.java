package cz.kpsys.portaro.record.holding;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AuthorityHoldingDepartmentReplacingRecordHoldingUpserter implements RecordHoldingUpserter {

    @NonNull RecordHoldingUpserter delegate;
    @NonNull Provider<Department> replacementDepartmentProvider;

    @Override
    public RecordHoldingUpsertResult upsert(RecordHoldingUpsertCommand command) {
        if (command.record().getType().equals(Record.TYPE_AUTHORITY)) {
            command = command.withDepartment(replacementDepartmentProvider.get());
        }

        return delegate.upsert(command);
    }
}
