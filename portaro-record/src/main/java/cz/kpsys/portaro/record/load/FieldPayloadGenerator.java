package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.collection.ListCutter;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.RecordIdentifier;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.fn.*;
import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.spec.FieldedRecordSpec;
import cz.kpsys.portaro.record.detail.spec.RecordDatableId;
import cz.kpsys.portaro.record.detail.spec.RecordSpec;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.*;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldPayloadGenerator implements FormulaEvaluator {

    @NonNull LoadedRecords loadedRecords;
    @NonNull RecordSpecSet searchedRecordSpecs;
    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;

    public void resolveNodeLink(@NonNull ValuableFieldNode node) {
        // field link - collect all field dependencies, and if there is only one unique record link, use it
        if (node.hasRecordLink()) {
            return;
        }

        Set<LookupDefinition> fieldLookupDefs = node.lookups();
        List<RecordLinksLookup> linkedRecords = ListUtil.convertStrict(fieldLookupDefs, fieldLookupDef -> fieldLookupDef.linkFieldSpec().lookup(node, loadedRecords));
        if (!linkedRecords.isEmpty() && linkedRecords.stream().allMatch(RecordLinksLookup::isSuccess)) {
            Set<RecordIdFondPair> distinctLinkedRecords = linkedRecords.stream().map(RecordLinksLookup::getSingle).collect(Collectors.toSet());
            // if a field links to only one unique record and that record is NOT self record, create a link
            if (distinctLinkedRecords.size() == 1 && !distinctLinkedRecords.iterator().next().typeSafeEquals(node.id().recordIdFondPair())) {
                node.setGeneratedRecordLink(distinctLinkedRecords.iterator().next());
            }
        }
    }

    public @NonNull FormulaResult resolveNodeValue(@NonNull ValuableFieldNode node) {
        if (node.hasScalarValue()) {
            return node.existingScalarValue();
        }

        if (node.hasVectorValue()) {
            return node.existingVectorValue();
        }

        if (node.isNonAbsentingError()) {
            return node.getExistingError();
        }

        Optional<Formula<? extends FieldValue<?>>> formula = node.formula();
        if (formula.isPresent()) {
            FormulaEvaluation<?> formulaResult = resolveFormulaValue(node, formula.get());
            if (formulaResult.isSuccess()) {
                switch (formulaResult.existingSuccess()) {
                    case ScalarFieldValue<?> scalar -> node.setGeneratedScalarValue(scalar);
                    case VectorFieldValue<?, ?> vector -> node.setGeneratedVectorValue(vector);
                }
            } else {
                node.setGeneratedValueError(formulaResult.existingFail());
            }
            return formulaResult.result();
        }

        AbsentValueFail absentValueResult = AbsentValueFail.of(node.id());
        //node.setGeneratedValueError(absentValueResult);
        return absentValueResult;
    }

    @Override
    public <T extends FieldValue<?>> @NonNull FormulaEvaluation<T> resolveFormulaValue(@NonNull ValuableFieldNode sourceNode, @NonNull Formula<T> formula) {
        return switch (formula) {
            case ResolvableFunction<T> fn -> fn.compute(sourceNode, this, searchedRecordSpecs);
            case Ref<T>(var pointer) -> getLinkedFieldValue(sourceNode, pointer);
            case BackRef<T>(var def) -> linkingRecordsFieldsLookup(sourceNode.id(), def);
        };
    }

    private <T extends FieldValue<?>> @NonNull List<FormulaEvaluation<T>> flat(List<? extends FormulaEvaluation<T>> formulaEvaluations) {
        List<FormulaEvaluation<T>> flattenList = new ArrayList<>(formulaEvaluations.size());
        for (FormulaEvaluation<T> operand : formulaEvaluations) {
            if (operand.isSuccess() && operand.existingSuccess() instanceof VectorFieldValue<?, ?> vectorSuccess) {
                List<? extends FormulaEvaluation<T>> vectorItemsEvaluations = ListUtil.convertStrict(vectorSuccess.values(), result -> (FormulaEvaluation<T>) FormulaEvaluation.success(result));
                flattenList.addAll(vectorItemsEvaluations);
            } else {
                flattenList.add(operand);
            }
        }
        return flattenList;
    }

    private <T extends FieldValue<?>> @NonNull FormulaEvaluation<T> getLinkedFieldValue(@NonNull ValuableFieldNode sourceNode, LookupDefinition sourceFieldLookupDef) {
        RecordLinksLookup recordLinksLookup = sourceFieldLookupDef.linkFieldSpec().lookup(sourceNode, loadedRecords); // get a record link (primary dep), not recursively!
        if (recordLinksLookup.isMissingLinkFields()) {
            return FormulaEvaluation.failure(AbsentLinkFail.of(recordLinksLookup.existingMissingLinkFieldIds()));
        }
        List<Multifield> allLinksLinkedFields = recordLinksLookup.recordLinks().stream()
                .map(recordLink -> {
                    FieldTypeId linkedFieldTypeId = recordEntryFieldTypeIdResolver.getLinkedFieldType(recordLink.fond(), sourceFieldLookupDef.linkedFieldSpec());
                    return loadedRecords.findMultifieldsByFieldTypeIdOrCreateStructure(recordLink, linkedFieldTypeId);
                })
                .flatMap(Collection::stream)
                .toList();
        if (allLinksLinkedFields.size() > 1) { // TODO: idealni by bylo jet pres vektory vzdy, ale nemame to otestovane
            return (FormulaEvaluation<T>) getVectorFormulaResult(allLinksLinkedFields);
        }
        Multifield linkedField = allLinksLinkedFields.getFirst();
        return (FormulaEvaluation<T>) FormulaEvaluation.result(resolveNodeValue(linkedField));
    }

    private <T extends FieldValue<?>> @NonNull FormulaEvaluation<T> linkingRecordsFieldsLookup(@NonNull RecordDatableId sourceDatableId, LinkingRecordsLookupDefinition sourceFieldLookupDef) {
        FieldTypeId linkingRecordsLinkFieldTypeId = sourceFieldLookupDef.linkingRecordsLinkFieldTypeId();
        RecordIdentifier linkingRecordsLink = sourceDatableId.recordIdFondPair().id();
        FieldTypeId linkingRecordsValueFieldTypeId = sourceFieldLookupDef.linkingRecordsValueFieldTypeId();

        FieldedRecordSpec linkingRecordsSpec = RecordSpec.ofLinkingRecordsFields(linkingRecordsLinkFieldTypeId, linkingRecordsLink, linkingRecordsValueFieldTypeId, sourceFieldLookupDef.linkingRecordsFondIds());
        if (!searchedRecordSpecs.covers(linkingRecordsSpec)) {
            return FormulaEvaluation.failure(AbsentLinkingFieldsFail.of(linkingRecordsLinkFieldTypeId, linkingRecordsLink, linkingRecordsValueFieldTypeId, sourceFieldLookupDef.linkingRecordsFondIds()));
        }

        List<Field<?>> linkingFields = loadedRecords.getFieldsWithTarget(linkingRecordsLink);

        List<Multifield> linkedFields = linkingFields.stream()
                .filter(field -> linkingRecordsSpec.recordMatcher().matches(field))
                .map(Field::getRecordIdFondPair)
                .map(linkingRecordIdFond -> loadedRecords.findMultifieldsByFieldTypeIdOrCreateStructure(linkingRecordIdFond, linkingRecordsValueFieldTypeId))
                .flatMap(Collection::stream)
                .toList();

        return (FormulaEvaluation<T>) getVectorFormulaResult(linkedFields);
    }

    private @NonNull FormulaEvaluation<?> getVectorFormulaResult(List<Multifield> linkedFields) {
        List<FormulaResult> results = ListUtil.convertStrict(linkedFields, this::resolveNodeValue);

        ListCutter<FormulaResult> resultsCutter = ListCutter.ofCopyOf(results);

        // fail fast on non-absent errors
        List<FailedResult> nonAbsentFails = resultsCutter.cutCastableAnd(FailedResult.class, failedResult -> !(failedResult instanceof AbsentDataFail));
        if (!nonAbsentFails.isEmpty()) {
            return FormulaEvaluation.failure(MultiNonAbsentFailFail.of(nonAbsentFails));
        }

        List<AbsentDataFail> absentFails = resultsCutter.cutCastable(AbsentDataFail.class);
        if (!absentFails.isEmpty()) {
            return FormulaEvaluation.failure(MultiAbsentDataFail.of(absentFails));
        }

        List<ScalarFieldValue<?>> linkingRecordsValues = resultsCutter.cutCastable(FieldValue.class).stream()
                .flatMap(fieldValue -> switch (fieldValue) {
                    case ScalarFieldValue<?> scalar -> Stream.of(scalar);
                    case VectorFieldValue<?, ?> vector -> vector.values().stream();
                })
                .toList();

        resultsCutter.assertEmpty(); // radeji overime, ze jsme pokryli vsechny resulty

        return FormulaEvaluation.success(VectorFieldValue.ofUnknownTypes(linkingRecordsValues));
    }

}
