package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import jakarta.annotation.Nonnull;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Delegate;
import lombok.experimental.FieldDefaults;

import java.util.Optional;
import java.util.function.Function;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RecordFieldFinder<T extends ScalarFieldValue<?>, RET> implements FieldFinder<FieldContainer, Field<T>> {

    public static <T extends ScalarFieldValue<?>, RET> RecordFieldFinder<T, RET> byDeepTypeId(
            @NonNull FieldTypeId typeId,
            @NonNull Function<? super Field<T>, ? extends Optional<RET>> extractor) {

        return new RecordFieldFinder<>(FieldFinders.byDeepTypeId(typeId), extractor);
    }

    @Delegate
    @Nonnull FieldFinder<FieldContainer, Field<T>> delegate;

    @NonNull Function<? super Field<T>, ? extends Optional<RET>> extractor;


    public @NonNull Optional<RET> findFirstIn(Record record) {
        return findFirstIn(record.getDetail(), extractor);
    }

    public @NonNull RET getFirstIn(Record record) {
        return getFirstIn(record.getDetail(), extractor);
    }

}
