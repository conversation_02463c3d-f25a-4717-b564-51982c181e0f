package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.commons.cache.CacheCleaner;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.LoadedFile;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.HashMap;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public abstract class CachingByFondCoverLoader implements CoverLoader, CacheCleaner {

    @NonNull CoverFactory coverFactory;
    @NonNull Map<Fond, byte[]> customUniversalCoversDataForFonds = new HashMap<>();


    @Override
    public LoadedFile getCover(Record record, Department currentDepartment) {
        synchronized (customUniversalCoversDataForFonds) {
            if (!customUniversalCoversDataForFonds.containsKey(record.getFond())) { //pokud mapa jeste neobsahuje dany fond, zjisti
                byte[] bytes = loadCoverDataForFondAndDepartmentOrNull(record.getFond(), currentDepartment); //bytes can be null
                customUniversalCoversDataForFonds.put(record.getFond(), bytes);
            }
        }
        
        byte[] coverDataByFond = customUniversalCoversDataForFonds.get(record.getFond());
        if (coverDataByFond != null) { //pokud existuje obalka podle fondu, vratime
            return coverFactory.createUniversalCover(record, coverDataByFond);
        }
        
        return null;
    }



    @Override
    public void clearCache() {
        customUniversalCoversDataForFonds.clear();
    }

    
    protected abstract byte[] loadCoverDataForFondAndDepartmentOrNull(Fond f, Department currentDepartment);

}
