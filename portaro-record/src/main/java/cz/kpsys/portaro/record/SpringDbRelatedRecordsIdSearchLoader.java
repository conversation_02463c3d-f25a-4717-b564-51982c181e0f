package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.database.SelectedColumnRowMapper;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.search.AbstractSingleColumnSpringDbSearchLoader;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.Objects;
import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.RecordDb.KAT1_7.*;

public class SpringDbRelatedRecordsIdSearchLoader extends AbstractSingleColumnSpringDbSearchLoader<MapBackedParams, UUID, RangePaging> {

    public SpringDbRelatedRecordsIdSearchLoader(@NonNull NamedParameterJdbcOperations jdbcTemplate, @NonNull QueryFactory queryFactory) {
        super(jdbcTemplate, queryFactory, KAT1_7, SOURCE_RECORD_ID, new SelectedColumnRowMapper<>(UUID.class, SOURCE_RECORD_ID));
    }

    @Override
    protected boolean fillQuery(boolean count, @NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        sq.from(KAT1_7);

        if (p.hasNotNull(RecordConstants.SearchParams.RECORD_RELATED_RECORD)) {
            RecordIdFondPair recordHeader = p.get(RecordConstants.SearchParams.RECORD_RELATED_RECORD);
            sq.where().eq(TARGET_RECORD_ID, recordHeader.id().id());
        }

        if (p.hasNotNull(RecordConstants.SearchParams.FIELD_TYPE_ID)) {
            var fieldId = p.get(RecordConstants.SearchParams.FIELD_TYPE_ID);
            if (fieldId.getLevel() != FieldTypeId.LEVEL_SUBFIELD) {
                throw new UnsupportedOperationException("We only support search by FieldTypeId with LEVEL_SUBFIELD");
            }
            sq.where().and().eq(CIS_POL, Objects.requireNonNull(fieldId.getParent()).getCode());
        }

        if (p.hasNotNull(RecordConstants.SearchParams.RECORD_RELATED_RECORD_FOND)) {
            if (!p.hasLength(RecordConstants.SearchParams.RECORD_RELATED_RECORD_FOND)) {
                return false;
            }
            sq.where().and().in(CIS_FOND, ListUtil.getListOfIds(p.get(RecordConstants.SearchParams.RECORD_RELATED_RECORD_FOND)));
        }

        return true;
    }
}
