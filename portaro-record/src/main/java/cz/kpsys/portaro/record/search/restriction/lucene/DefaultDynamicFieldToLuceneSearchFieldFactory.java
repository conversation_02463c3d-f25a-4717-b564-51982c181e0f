package cz.kpsys.portaro.record.search.restriction.lucene;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.search.restriction.FieldTypedSearchFieldParsing;
import cz.kpsys.portaro.search.restriction.serialize.lucene.DynamicFieldToLuceneSearchFieldFactory;
import cz.kpsys.portaro.search.restriction.serialize.lucene.LuceneConstants;
import cz.kpsys.portaro.search.restriction.serialize.lucene.LuceneQueryField;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

import static cz.kpsys.portaro.record.detail.FieldTypeId.DELIMITER;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DefaultDynamicFieldToLuceneSearchFieldFactory implements DynamicFieldToLuceneSearchFieldFactory {

    @NonNull ByIdLoadable<FieldType<?>, FieldTypeId> fieldTypeLoader;

    @Override
    public boolean supports(@NonNull String searchFieldName) {
        return FieldTypedSearchFieldParsing.hasDynamicFieldPrefix(searchFieldName);
    }

    @Override
    public List<LuceneQueryField> getSingleByExpandedSearchKey(@NonNull String prefixedSuffixedSearchFieldName) {
        FieldTypedSearchFieldParsing parsing = FieldTypedSearchFieldParsing.parsePrefixedSuffixed(prefixedSuffixedSearchFieldName);
        String luceneSearchFieldName = toLuceneSearchFieldName(parsing);
        ScalarDatatype datatype = getDatatype(parsing);
        return List.of(LuceneQueryField.create(luceneSearchFieldName, datatype));
    }

    private ScalarDatatype getDatatype(FieldTypedSearchFieldParsing parsing) {
        if (parsing.subject() == FieldTypedSearchFieldParsing.Subject.LINK || parsing.subject() == FieldTypedSearchFieldParsing.Subject.ORIGIN) {
            return CoreConstants.Datatype.UUID;
        }
        FieldType<?> fieldType = fieldTypeLoader.getById(parsing.fieldTypeId());
        return fieldType.getDatatypeOrThrow();
    }

    public static @NonNull String toLuceneSearchFieldName(FieldTypedSearchFieldParsing parsing) {
        String suffix = switch (parsing.subject()) {
            case VALUE -> LuceneConstants.LUCENE_FIELDTYPED_SEARCH_FIELD_VALUE_SUFFIX;
            case LINK -> LuceneConstants.LUCENE_FIELDTYPED_SEARCH_FIELD_LINK_SUFFIX;
            case ORIGIN -> LuceneConstants.LUCENE_FIELDTYPED_SEARCH_FIELD_ORIGIN_SUFFIX;
            case SORT -> LuceneConstants.LUCENE_FIELDTYPED_SEARCH_FIELD_SORT_SUFFIX;
        };
        return LuceneConstants.FIELD_SEARCH_PREFIX +
               parsing.fieldTypeId().value().replace(DELIMITER, LuceneConstants.LUCENE_FIELDTYPED_SEARCH_FIELD_FIELDTYPE_DELIMITER) +
               suffix;
    }
}
