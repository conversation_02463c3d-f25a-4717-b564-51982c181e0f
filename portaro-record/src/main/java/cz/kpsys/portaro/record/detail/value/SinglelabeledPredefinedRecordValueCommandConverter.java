package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SinglelabeledPredefinedRecordValueCommandConverter implements TypedFieldValueConverter<SinglelabeledPredefinedRecordValueCommand, FieldPayload<StringFieldValue>, StringFieldValue> {

    @NonNull
    @Override
    public FieldPayload<StringFieldValue> convert(@NonNull SinglelabeledPredefinedRecordValueCommand command, @NonNull FieldType<?> fieldType, @NonNull RecordFieldId fieldId) {
        StringFieldValue valueHolder = command.label() == null ? null : StringFieldValue.of(
                command.label(),
                getText(command),
                Set.of(command.originId())
        );
        return FieldPayload.of(valueHolder, command.recordLink());
    }

    private Text getText(SinglelabeledPredefinedRecordValueCommand input) {
        return StringUtil.hasTrimmedLength(input.label()) ? Texts.ofNative(input.label()) : Texts.ofEmpty();
    }

    @Override
    public String toString() {
        return getClass().getSimpleName();
    }
}
