package cz.kpsys.portaro.record.deletion;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Deleter;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Set;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordDeleterDelegatingRecordHoldingDeleter implements Deleter<RecordHoldingDeletionCommand> {

    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull RecordDeleter recordDeleter;

    @Override
    public void delete(RecordHoldingDeletionCommand command) {
        Record record = recordLoader.getById(command.recordHolding().getRecordId());
        RecordDeletionCommand deletionCommand = new RecordDeletionCommand(
                record,
                Set.of(command.recordHolding().getDepartment()),
                command.withExemplarsDeletion() ? Set.of(command.recordHolding().getDepartment()) : Set.of(),
                command.currentDepartment(),
                command.currentAuth(),
                command.withToDeletableExemplarStatusChange(),
                command.withMvsCancelling(),
                command.withCaslinCancelling()
        );
        recordDeleter.delete(deletionCommand);
    }
}
