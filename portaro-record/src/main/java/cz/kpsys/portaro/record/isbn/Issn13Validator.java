package cz.kpsys.portaro.record.isbn;

import cz.kpsys.portaro.commons.util.StringUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Issn13Validator implements IsbnValidator {

    /**
     * Issn in bar code
     */
    public static String REGEX = "^977\\d{4}\\d{3}00\\d$";

    @Override
    public boolean isValid(@NonNull String issn) {
        return StringUtil.hasTrimmedLength(issn) && issn.matches(REGEX);
    }
}
