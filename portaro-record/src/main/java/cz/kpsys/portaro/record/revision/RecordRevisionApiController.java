package cz.kpsys.portaro.record.revision;

import cz.kpsys.portaro.commons.object.LabeledIdRecord;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
@RequestMapping("/api/record-revisions")
@ResponseBody
public class RecordRevisionApiController extends GenericApiController {

    @NonNull RecordRevisionLoader recordRevisionLoader;

    @GetMapping
    public List<RecordRevisionResponse> query(@RequestParam("record") Record record) {
        return ListUtil.convert(recordRevisionLoader.getAllByRecord(record), source -> new RecordRevisionResponse(LabeledIdRecord.of(source.record()), source.date(), source.content()));
    }

}
