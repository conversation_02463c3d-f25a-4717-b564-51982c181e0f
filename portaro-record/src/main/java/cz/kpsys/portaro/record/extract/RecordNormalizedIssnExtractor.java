package cz.kpsys.portaro.record.extract;

import cz.kpsys.portaro.commons.object.LabeledValue;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.export.RecordViewFunctions;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordNormalizedIssnExtractor implements RecordValueExtractor {

    @Override
    public Stream<String> extractValue(Record record) {
        return RecordViewFunctions.normalizedIssns(record.getDetail())
                .stream()
                .map(LabeledValue::value);
    }
}
