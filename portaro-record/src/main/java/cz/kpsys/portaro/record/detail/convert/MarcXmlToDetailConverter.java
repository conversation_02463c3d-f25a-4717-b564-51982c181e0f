package cz.kpsys.portaro.record.detail.convert;

import cz.kpsys.portaro.record.detail.IdentifiedFieldContainer;
import cz.kpsys.portaro.record.detail.appservermarc.RecordAppserverMarcResponse;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.NonNull;
import org.jdom2.Document;

import java.util.List;
import java.util.function.Function;

public interface MarcXmlToDetailConverter {

    IdentifiedFieldContainer convertSingle(@NonNull String xmlString) throws RecordDetailFromMarcXmlConstructionException;

    List<IdentifiedFieldContainer> convertMultipleRecords(@NonNull Function<RecordAppserverMarcResponse, Fond> recordFondResolver, @NonNull Document xmlDocument) throws RecordDetailFromMarcXmlConstructionException;

}
