package cz.kpsys.portaro.record.detail.constraints;

import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.NonNull;

public record LinkingRecordsAsRelatedRecordsConstraintDef(

        @NonNull Fond searchedRecordsRootFond,
        @NonNull FieldTypeId searchedRecordsLinkFieldTypeId,
        @NonNull MatchingRecordDef matchingRecordDef,
        @NonNull FieldTypeId linkingRecordsLink,
        @NonNull OnMissing onMissing

) implements RecordConstraintDef {

    public static LinkingRecordsAsRelatedRecordsConstraintDef throwWhenMissing(@NonNull Fond searchedRecordsRootFond,
                                                                               @NonNull FieldTypeId searchedRecordsLinkFieldTypeId,
                                                                               @NonNull MatchingRecordDef matchingRecordDef,
                                                                               @NonNull FieldTypeId linkingRecordsLink) {
        return new LinkingRecordsAsRelatedRecordsConstraintDef(searchedRecordsRootFond, searchedRecordsLinkFieldTypeId, matchingRecordDef, linkingRecordsLink, OnMissing.THROW);
    }

    @Override
    public String toString() {
        return "{@" + searchedRecordsRootFond.getId() + ":" + searchedRecordsLinkFieldTypeId + ":link = " + matchingRecordDef + "}:" + linkingRecordsLink + ":link";
    }

}
