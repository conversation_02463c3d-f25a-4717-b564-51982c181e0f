package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.StringFieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import lombok.NonNull;

import java.util.function.Function;

import static cz.kpsys.portaro.CoreConstants.Datatype.TEXT;

public record Replace(

        @NonNull
        Formula<StringFieldValue> first,

        @NonNull
        String replacementFormat,

        @NonNull
        Formula<StringFieldValue> second

) implements BinaryFunction<StringFieldValue> {

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<StringFieldValue>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        return TEXT;
    }

    @Override
    public @NonNull FormulaEvaluation<StringFieldValue> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {
        var inputEval = evaluator.resolveFormulaValue(sourceNode, first).ensureOf(StringFieldValue.class, sourceNode, first);
        var replacementEval = evaluator.resolveFormulaValue(sourceNode, second).ensureOf(StringFieldValue.class, sourceNode, second);

        FormulaEvaluations<StringFieldValue> operandEvals = FormulaEvaluations.extract(inputEval, replacementEval);
        if (operandEvals.hasFail()) {
            return operandEvals.toMostCriticalMultiFailEval();
        }

        var input = inputEval.existingSuccess();
        var replacement = replacementEval.existingSuccess();

        return compute(input, replacement);
    }

    public @NonNull FormulaEvaluation<StringFieldValue> compute(@NonNull StringFieldValue input, @NonNull StringFieldValue replacement) {
        if (!input.value().contains(replacementFormat)) {
            return FormulaEvaluation.success(input);
        }

        var mergedOrigins = ListUtil.unionSet(input.origins(), replacement.origins());
        String replaced = input.value().replace(replacementFormat, replacement.value());
        return FormulaEvaluation.success(StringFieldValue.of(replaced, mergedOrigins));
    }
}
