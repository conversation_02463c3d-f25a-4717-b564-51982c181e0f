package cz.kpsys.portaro.record.deletion;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.holding.RecordHolding;
import lombok.NonNull;

public record RecordHoldingDeletionCommand(
        @NonNull RecordHolding recordHolding,
        @NonNull Department currentDepartment,
        @NonNull UserAuthentication currentAuth,
        @NonNull Boolean withExemplarsDeletion,
        @NonNull Boolean withToDeletableExemplarStatusChange,
        @NonNull Boolean withMvsCancelling,
        @NonNull Boolean withCaslinCancelling
) {}
