package cz.kpsys.portaro.record.merge;

import cz.kpsys.portaro.formannotation.annotations.valueeditor.bool.BooleanEditor;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.validation.AfterIntegrityValidationViolation;
import cz.kpsys.portaro.record.Record;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.With;

import java.util.List;

@Form(id = "recordMerge", title = "{commons.SelectEllipsis}")
@FormSubmit(path = "/api/records/merge")
@AfterIntegrityValidationViolation(bean = "recordMergeRequestDefaulter")
@With
public record RecordMergeRequest(

    @NotNull
    Record target,

    @NotEmpty
    List<Record> sources,

    @FormPropertyLabel("{recordMergeRequest.MergeDetail}")
    @BooleanEditor
    @NotNull
    Boolean mergeDetail

) {}
