package cz.kpsys.portaro.record.holding;

import cz.kpsys.portaro.commons.object.Identified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.RecordDb.RECORD_HOLDING_SOURCE.*;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class RecordHoldingSourceEntity implements Identified<UUID> {

    @Id
    @Column(name = ID)
    @EqualsAndHashCode.Include
    @NonNull
    UUID id;

    @Column(name = RECORD_HOLDING_ID)
    @NonNull
    UUID recordHoldingId;

    @Column(name = TYPE_ID)
    @NonNull
    Integer typeId;

    @Column(name = CREATION_EVENT_ID)
    @NonNull
    UUID creationEventId;

}
