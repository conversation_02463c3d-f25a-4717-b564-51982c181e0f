package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.sql.generator.DeleteQuery;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.Date;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbUnfoundCoverDeleter implements UnfoundCoverDeleter {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;

    @Override
    public void deleteAll() {
        deleteOlderThan(null);
    }
    
    @Override
    public void deleteOlderThan(Date maxDate) {
        delete(maxDate, null);
    }

    @Override
    public void deleteConnectionProblemedOlderThan(Date maxDate) {
        delete(maxDate, CoverSearchException.CONNECTION_PROBLEM);
    }
    
    public void delete(Date maxDate, String reason) {
        DeleteQuery dq = queryFactory.newDeleteQuery();
        dq.delete(SpringDbCoversToSearchLoader.UnfoundCovers.OPAC_NENALEZENE_OBALKY);
        if (maxDate != null) {
            dq.where().and().lt(SpringDbCoversToSearchLoader.UnfoundCovers.CAS_HLEDANI, maxDate);
        }
        if (reason != null) {
            dq.where().and().eq(SpringDbCoversToSearchLoader.UnfoundCovers.DUVOD, reason);
        }
        jdbcTemplate.update(dq.getSql(), dq.getParamMap());
    }
    
}
