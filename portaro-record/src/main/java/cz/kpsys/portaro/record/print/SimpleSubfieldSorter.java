package cz.kpsys.portaro.record.print;

import cz.kpsys.portaro.commons.util.ComparatorForExplicitIdSorting;
import cz.kpsys.portaro.record.detail.By;
import cz.kpsys.portaro.record.detail.ViewableField;
import org.springframework.lang.Nullable;

import java.util.List;

public class SimpleSubfieldSorter {

    /**
     * @param subfieldCodes can be null, then it returns all autonomous subfields (= except indicators)
     */
    public List<? extends ViewableField> getAndSort(ViewableField df, @Nullable List<String> subfieldCodes) {
        if (subfieldCodes == null) {
            return df.streamFields(By.autonomous()).toList();
        }
        return df.streamFields(By.anyCode(subfieldCodes))
                .sorted(new ComparatorForExplicitIdSorting<>(subfieldCodes, ViewableField::getCode))
                .toList();
    }

}
