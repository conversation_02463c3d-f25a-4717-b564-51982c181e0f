package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.util.StringUtil;
import lombok.NonNull;

import java.util.List;

public record MultiNonAbsentFailFail(

        @NonNull
        Text text,

        @NonNull
        List<? extends FailedResult> fails

) implements NonAbsentDataFail {

    public static @NonNull MultiNonAbsentFailFail of(@NonNull List<? extends FailedResult> fails) {
        return new MultiNonAbsentFailFail(MultiText.ofTexts(fails).withCommaSpaceDelimiter(), fails);
    }

    @Override
    public String toString() {
        return "Fails[" + StringUtil.listToString(fails, ",") + ']';
    }
}
