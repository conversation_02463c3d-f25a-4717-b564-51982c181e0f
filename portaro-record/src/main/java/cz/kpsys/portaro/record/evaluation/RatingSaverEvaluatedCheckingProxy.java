package cz.kpsys.portaro.record.evaluation;

import cz.kpsys.portaro.record.list.RecordIdStorage;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RatingSaverEvaluatedCheckingProxy implements RatingSaver {

    @NonNull RatingSaver delegate;
    @NonNull RecordIdStorage byUserEvaluatedDocuments;

    @Override
    public void saveRating(@NonNull UUID recordId, double value) {
        if (isDocumentRated(recordId)) {
            throw new AlreadyEvaluatedException();
        }
        delegate.saveRating(recordId, value);
        addRatedDocument(recordId);
    }

    private boolean isDocumentRated(UUID recordId) {
        return byUserEvaluatedDocuments.contains(recordId);
    }

    private void addRatedDocument(UUID recordId) {
        byUserEvaluatedDocuments.add(recordId);
    }
}
