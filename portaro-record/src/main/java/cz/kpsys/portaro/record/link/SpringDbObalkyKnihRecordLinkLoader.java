package cz.kpsys.portaro.record.link;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.web.Link;
import cz.kpsys.portaro.commons.web.StaticLink;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import static cz.kpsys.portaro.databasestructure.RecordDb.OBALKYKNIH.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbObalkyKnihRecordLinkLoader implements RecordLinkLoader, RowMapper<Link> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;

    @Override
    public List<? extends Link> getByRecord(@NonNull Record record) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(TABLE);
        sq.where()
                .eq(RECORD_ID, record.getId())
                .and()
                .isNotNull(URL_BACKLINK);
        return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
    }

    @Override
    public Link mapRow(ResultSet rs, int rowNum) throws SQLException {
        String backlinkUrl = rs.getString(URL_BACKLINK);
        return new StaticLink(backlinkUrl, Texts.ofNative("obalkyknih.cz"));
    }
}
