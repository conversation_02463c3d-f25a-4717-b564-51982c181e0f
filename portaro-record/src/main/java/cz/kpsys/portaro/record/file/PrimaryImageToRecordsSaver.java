package cz.kpsys.portaro.record.file;

import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.databasestructure.RecordDb;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.UpdateQuery;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.List;


/**
 * @deprecated Use {@code record.setCover(file);} followed by {@code recordSaver.save(record);} instead
 * to save the image.
 */
@AllArgsConstructor
@FieldDefaults(makeFinal = true, level = lombok.AccessLevel.PRIVATE)
@Deprecated
public class PrimaryImageToRecordsSaver implements Saver<PrimaryImageToRecordsSaveCommand, List<Record>> {

    @NonNull
    NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull
    QueryFactory queryFactory;

    @Override
    public @NonNull List<Record> save(@NonNull PrimaryImageToRecordsSaveCommand command) {
        if (command.records().isEmpty()) {
            return command.records();
        }
        command.records().forEach(record -> {
            if (record.getType().equals(Record.TYPE_DOCUMENT)) {
                UpdateQuery uqDocument = queryFactory.newUpdateQuery();
                uqDocument.update(RecordDb.KAT1_4.TABLE);
                uqDocument.where().eq(RecordDb.KAT1_4.RECORD_ID, record.getId());
                uqDocument.set().add(RecordDb.KAT1_4.FK_FULLTEXT_IMAGE, command.image().getId());
                notAutoCommittingJdbcTemplate.update(uqDocument.getSql(), uqDocument.getParamMap());
                return;
            }
            UpdateQuery uqAuthority = queryFactory.newUpdateQuery();
            uqAuthority.update(RecordDb.KATAUT_4.TABLE);
            uqAuthority.where().eq(RecordDb.KATAUT_4.RECORD_ID, record.getId());
            uqAuthority.set().add(RecordDb.KATAUT_4.FK_FULLTEXT_IMAGE, command.image().getId());
            notAutoCommittingJdbcTemplate.update(uqAuthority.getSql(), uqAuthority.getParamMap());
        });
        return command.records();
    }
}
