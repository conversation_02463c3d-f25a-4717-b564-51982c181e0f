package cz.kpsys.portaro.record.document;

import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.database.SelectedColumnRowMapper;
import cz.kpsys.portaro.databasestructure.RecordDb.RECORD;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordStatus;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.Collection;
import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.RecordDb.KAT1_7;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbRecordPartsLoader implements RecordRelatedRecordsLoader {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull AllByIdsLoadable<Record, UUID> detailedRecordLoader;

    /**
     * Popis dotazu:
     * pomoci documentId najdeme v kat1_7 prislusnou autoritu. Pak pomoci teto autority z KAT1_7 najdeme seznam casti
     * <br/>
     * <br/>
     * select kat1_4.id_zaz from kat1_7 k1 join kat1_7 k2 on (k1.fk_aut = k2.fk_aut) where k2.fk_zaz=<i>documentId</i> and k2.cis_fond &#062; 30 and k1.cis_fond &#062; 30;
     */
    @Override
    public List<Record> getAll(@NonNull UUID recordId,
                               @NonNull Collection<Integer> partsContainingFieldNumbers,
                               @NonNull List<RecordStatus> forbiddenRecordStatuses,
                               @NonNull List<UUID> forbiddenRecordIds,
                               @NonNull Range range) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.selectDistinct(TC("k1", KAT1_7.SOURCE_RECORD_ID), TC(RECORD.TABLE, RECORD.SORTING_KEY));
        sq.from(AS(KAT1_7.KAT1_7, "k1"));
        sq.joins().add(AS(KAT1_7.KAT1_7, "k2"), COLSEQ(TC("k1", KAT1_7.TARGET_RECORD_ID), TC("k2", KAT1_7.TARGET_RECORD_ID)));
        sq.joins().add(RECORD.TABLE, COLSEQ(TC("k1", KAT1_7.SOURCE_RECORD_ID), TC(RECORD.TABLE, RECORD.ID)));
        sq.where()
                .eq(TC("k2", KAT1_7.SOURCE_RECORD_ID), recordId)
                .and()
                .gt(TC("k2", KAT1_7.CIS_FOND), 30) //obe vazby jsou oproti getAllArticles() autoritni
                .and()
                .gt(TC("k1", KAT1_7.CIS_FOND), 30)
                .and()
                .in(TC("k1", KAT1_7.CIS_POL), partsContainingFieldNumbers);
        if (ListUtil.hasLength(forbiddenRecordIds)) {
            sq.where().and().notIn(TC(RECORD.TABLE, RECORD.ID), forbiddenRecordIds);
        }
        if (ListUtil.hasLength(forbiddenRecordStatuses)) {
            sq.where().and().notIn(TC(RECORD.TABLE, RECORD.RECORD_STATUS_ID), ListUtil.getListOfIds(forbiddenRecordStatuses));
        }
        sq.orderBy().addAsc(TC(RECORD.TABLE, RECORD.SORTING_KEY));

        sq.setRange(range);

        List<UUID> ids = jdbcTemplate.query(sq.getSql(), sq.getParamMap(), new SelectedColumnRowMapper<>(UUID.class, KAT1_7.SOURCE_RECORD_ID));
        return detailedRecordLoader.getAllByIds(ids);
    }
}
