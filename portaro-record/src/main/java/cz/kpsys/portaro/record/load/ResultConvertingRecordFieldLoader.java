package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ResultConvertingRecordFieldLoader<INTERMEDIATE, TARGET> implements RecordFieldLoader<TARGET> {

    @NonNull RecordFieldLoader<INTERMEDIATE> pureLoader;
    @NonNull Function<List<INTERMEDIATE>, List<? extends TARGET>> postConverter;
    boolean conversionCountChecking;

    public ResultConvertingRecordFieldLoader(@NonNull RecordFieldLoader<INTERMEDIATE> pureLoader, @NonNull Function<List<INTERMEDIATE>, List<? extends TARGET>> postConverter) {
        this(pureLoader, postConverter, true);
    }

    public List<TARGET> getAll(@NonNull RecordSpecSet specs) {
        List<INTERMEDIATE> originalContent = pureLoader.getAll(specs);
        List<? extends TARGET> convertedContent = Objects.requireNonNull(postConverter.apply(originalContent));

        if (conversionCountChecking) {
            Assert.state(originalContent.size() == convertedContent.size(), () -> "Size of converted search items (%s) is not same as original (%s)".formatted(convertedContent.size(), originalContent.size()));
        }

        return (List<TARGET>) convertedContent;
    }

}
