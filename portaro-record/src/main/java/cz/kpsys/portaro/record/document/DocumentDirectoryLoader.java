package cz.kpsys.portaro.record.document;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.file.directory.AllIdsByDirectoryProvider;
import cz.kpsys.portaro.file.directory.Directory;
import cz.kpsys.portaro.file.directory.DirectoryChainLoader;
import cz.kpsys.portaro.file.directory.ParentableDirectory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DocumentDirectoryLoader {

    @NonNull DirectoryChainLoader directoryChainLoader;
    @NonNull ByIdLoadable<ParentableDirectory, Integer> directoryLoader;
    @NonNull AllIdsByDirectoryProvider allDocumentIdsByDirectoryLoader;

    public List<UUID> getDocumentIdsByDirectoryId(Integer directoryId) {
        return getDocumentIdsByThisAndParentDirectoriesChain(directoryLoader.getById(directoryId));
    }

    public List<UUID> getDocumentIdsByThisAndParentDirectoriesChain(Directory directory) {
        List<ParentableDirectory> directoryChain = directoryChainLoader.getThisAndParentsChain(directory);
        return allDocumentIdsByDirectoryLoader.getAllByDirectories(ListUtil.getListOfIds(directoryChain));
    }
}
