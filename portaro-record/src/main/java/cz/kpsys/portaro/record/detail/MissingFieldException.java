package cz.kpsys.portaro.record.detail;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class MissingFieldException extends RuntimeException {

    public MissingFieldException(@NonNull Object by, @NonNull Object fieldContainer) {
        super("Field by %s not exists in %s".formatted(by, fieldContainer));
    }
}
