package cz.kpsys.portaro.record.creation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.edit.RecordEditation;
import lombok.NonNull;

import java.util.List;

public interface RecordEditationCreator<E> {

    RecordEditation ofNewRecord(@NonNull E externalRecord, @NonNull List<Department> holdingDepartments, @NonNull Department ctx, @NonNull UserAuthentication currentAuth);

    RecordEditation ofExistingRecord(@NonNull E externalRecord, @NonNull List<Department> holdingDepartments, @NonNull Department ctx, @NonNull UserAuthentication currentAuth);

}
