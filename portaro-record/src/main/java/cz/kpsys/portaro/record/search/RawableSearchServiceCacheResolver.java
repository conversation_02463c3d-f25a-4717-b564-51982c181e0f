package cz.kpsys.portaro.record.search;

import cz.kpsys.portaro.search.CacheMode;
import cz.kpsys.portaro.search.CachingSearchService;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.interceptor.CacheOperationInvocationContext;
import org.springframework.cache.interceptor.CacheResolver;
import org.springframework.cache.support.NoOpCache;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RawableSearchServiceCacheResolver implements CacheResolver {

    public static final String NOOP_CACHE_NAME = "none";

    @NonNull CacheManager cacheManager;

    @Override
    public Collection<? extends Cache> resolveCaches(CacheOperationInvocationContext<?> context) {
        CacheMode cacheMode = Arrays.stream(context.getArgs())
                .filter(o -> o instanceof CacheMode)
                .map(item -> (CacheMode) item)
                .findFirst()
                .orElseThrow();
        return switch (cacheMode) {
            case NONE -> List.of(new NoOpCache(NOOP_CACHE_NAME));
            case LONGTERM -> List.of(cacheManager.getCache(CachingSearchService.LONG_TERM_CACHE_NAME));
        };
    }
}
