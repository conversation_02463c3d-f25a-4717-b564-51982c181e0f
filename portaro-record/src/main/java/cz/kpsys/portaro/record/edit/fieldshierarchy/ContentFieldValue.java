package cz.kpsys.portaro.record.edit.fieldshierarchy;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import cz.kpsys.portaro.commons.object.ValuableRecord;
import lombok.NonNull;
import lombok.experimental.FieldNameConstants;

@JsonDeserialize(as = ContentFieldValue.class)
@FieldNameConstants
public record ContentFieldValue<E>(@NonNull E value) implements SetFieldValueRequest, ValuableRecord<E> {}
