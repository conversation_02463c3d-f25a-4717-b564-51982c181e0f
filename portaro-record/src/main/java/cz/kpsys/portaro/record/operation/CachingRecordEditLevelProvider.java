package cz.kpsys.portaro.record.operation;

import cz.kpsys.portaro.commons.cache.CacheDeletableById;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.role.editor.EditLevel;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CachingRecordEditLevelProvider implements RecordEditLevelProvider, CacheDeletableById {

    public static final String CACHE_NAME = "recordEditLevel";

    @NonNull RecordEditLevelProvider delegate;

    @Cacheable(value = CACHE_NAME, key = "#record.id", sync = true)
    @Override
    public EditLevel getRecordEditLevel(Record record) {
        return delegate.getRecordEditLevel(record);
    }

    @CacheEvict(value = CACHE_NAME)
    @Override
    public void deleteFromCacheById(Object id) {
    }
}
