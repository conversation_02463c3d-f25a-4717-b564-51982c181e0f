package cz.kpsys.portaro.record.fond;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.NamedLabeledIdentifiedRecord;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import jakarta.annotation.Nullable;
import lombok.NonNull;

public record RichFondResponse(

        @NonNull
        Integer id,

        @NonNull
        String name,

        @NonNull
        Text text,

        Integer order,

        @Nullable
        Integer parentId,

        @Nullable
        String binding,

        boolean ofAuthority,

        boolean enabled,

        boolean editable,

        boolean periodical,

        boolean recordable,

        @Nullable
        String entryFieldTypeConfigId,

        Integer z39Typ,

        @Nullable
        String documentCategories,

        @Nullable
        String documentTypes,

        boolean withExemplars,

        @Nullable
        String recordType,

        @Nullable
        String bibliographicLevel,

        boolean forSourceDocument,

        boolean threadable

) implements NamedLabeledIdentifiedRecord<Integer> {

    public static RichFondResponse mapFromFond(@NonNull Fond fond) {
        return new RichFondResponse(
                fond.getId(),
                fond.getName(),
                fond.getText(),
                fond.getOrder(),
                fond.getParentId(),
                fond.getBinding(),
                fond.isOfAuthority(),
                fond.isEnabled(),
                fond.isEditable(),
                fond.isPeriodical(),
                fond.isRecordable(),
                ObjectUtil.elvis(fond.getEntryFieldTypeConfigId(), FieldTypeId::value),
                fond.getZ39Typ(),
                fond.getDocumentCategories(),
                fond.getDocumentTypes(),
                fond.isWithExemplars(),
                fond.getRecordType(),
                fond.getBibliographicLevel(),
                fond.isForSourceDocument(),
                fond.getFondType() != FondType.THREAD
        );
    }
}