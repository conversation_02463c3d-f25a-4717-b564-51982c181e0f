package cz.kpsys.portaro.record.merge;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.appserver.Appserver;
import cz.kpsys.portaro.appserver.XmlAppserverRequest;
import cz.kpsys.portaro.appserver.handler.ConcreteErrorHandler;
import cz.kpsys.portaro.appserver.handler.HandlerFindingOrChainingAppserverErrorHandler;
import cz.kpsys.portaro.appserver.handler.ThrowingErrorHandler;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.appserver.mapping.NoOpAppserverResponseHandler;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class AppserverAuthorityMerger implements AuthorityMerger {

    private static final String PATH = Appserver.APIPATH_AUTHORITY_MERGE;

    @NonNull ObjectMapper xmlMapper;
    @NonNull MappingAppserverService mappingAppserver;
    @NonNull Map<Integer, ConcreteErrorHandler<AuthorityMergeAppserverRequest, ?>> errorHandlers = Map.of(
            1, new ThrowingErrorHandler<>("record.merge.CannotMergeDueToSeeAlsoFields")
    );

    @Override
    public void merge(@NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth, @NonNull List<Record> sources, @NonNull Record target) {
        AuthorityMergeAppserverRequest req = new AuthorityMergeAppserverRequest(
                ListUtil.convert(sources, Record::getKindedId),
                target.getKindedId()
        );
        XmlAppserverRequest request = XmlAppserverRequest.byPost(PATH, req, xmlMapper);

        mappingAppserver.call(request, NoOpAppserverResponseHandler.create(), new HandlerFindingOrChainingAppserverErrorHandler<>(req, errorHandlers));
    }
}
