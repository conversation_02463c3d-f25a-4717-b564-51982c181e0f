package cz.kpsys.portaro.record;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.NonNull;

import java.util.UUID;

@JacksonXmlRootElement(localName = "record")
public record RecordSaveAppserverResponse(

        @JacksonXmlProperty(localName = RECORD_ID)
        @NonNull
        UUID recordId,

        @JacksonXmlProperty(localName = ID)
        @NonNull
        Integer recordKindedId,

        @JacksonXmlProperty(localName = FOND_ID)
        @NonNull
        Integer fondId,

        @JacksonXmlProperty(localName = FULLTEXT_SKUPINA_ID)
        @NonNull
        Integer directoryId

) {

    public static final String RECORD_ENVELOPE = "record";
    public static final String RECORD_ID = "record_id";
    public static final String ID = "ID";
    public static final String FOND_ID = "FOND";
    public static final String FULLTEXT_SKUPINA_ID = "FULLTEXT_SKUPINA";

}
