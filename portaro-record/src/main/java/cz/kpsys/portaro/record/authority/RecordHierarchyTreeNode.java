package cz.kpsys.portaro.record.authority;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.HierarchyTreeNode;
import lombok.NonNull;
import lombok.Value;

import java.util.UUID;

@Value
public class RecordHierarchyTreeNode implements HierarchyTreeNode<UUID> {

    @NonNull UUID id;
    @NonNull Text text;
    @NonNull RecordHierarchyTreeNodeType type;
    @NonNull Integer level;

    @Override
    public boolean isCurrent() {
        return type == RecordHierarchyTreeNodeType.CURRENT;
    }

    public boolean isSee() {
        return type == RecordHierarchyTreeNodeType.SEE;
    }

    public boolean isSeeAlso() {
        return type == RecordHierarchyTreeNodeType.SEE_ALSO;
    }

    public boolean isParent() {
        return type == RecordHierarchyTreeNodeType.PARENT;
    }

    public boolean isChild() {
        return type == RecordHierarchyTreeNodeType.CHILD;
    }

    public enum RecordHierarchyTreeNodeType {
        CURRENT,
        PARENT,
        CHILD,
        SEE,
        SEE_ALSO
    }

}
