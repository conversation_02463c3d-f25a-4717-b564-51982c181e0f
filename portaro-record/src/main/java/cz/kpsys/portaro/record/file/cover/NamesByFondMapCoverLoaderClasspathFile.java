package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.web.PlaceholderTemplate;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.util.Assert;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class NamesByFondMapCoverLoaderClasspathFile extends CachingByFondCoverLoader {
    
    @NonNull PlaceholderTemplate wilcardedCoverFilePath;
    @NonNull Provider<@NonNull Map<Integer, String>> fondToNameMapProvider;
    @NonNull String defaultCoverName;

    /**
     *
     * @param wilcardedCoverFilePath cesta k souborum, ktera obsahuje {name}, coz je nazev dane obalky.
     * @param fondToNameMapProvider Mapa nazvu obalek podle fondu.
     * @param defaultCoverName nazev defaultni obalky, pokud se podle fondu nenajde.
     */
    public NamesByFondMapCoverLoaderClasspathFile(@NonNull CoverFactory coverFactory,
                                                  @NonNull String wilcardedCoverFilePath,
                                                  @NonNull Provider<@NonNull Map<Integer, String>> fondToNameMapProvider,
                                                  @NonNull String defaultCoverName) {
        super(coverFactory);
        this.wilcardedCoverFilePath = new PlaceholderTemplate(wilcardedCoverFilePath);
        this.fondToNameMapProvider = fondToNameMapProvider;
        this.defaultCoverName = defaultCoverName;
    }

    protected byte[] loadCoverDataForFondAndDepartmentOrNull(Fond f, Department currentDepartment) {
        Resource coverResource = getCoverResourceByFond(f);
        if (coverResource.exists()) {

            log.debug("Loading common cover for fond {} from {}", f.getId(), coverResource);

            try (InputStream is = coverResource.getInputStream()) {
                return IOUtils.toByteArray(is);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }
    
    protected Resource getCoverResourceByFond(Fond f) {
        int fondId = f.getId();
        String coverName = fondToNameMapProvider.get().get(fondId);
        
        if (StringUtil.isNullOrEmpty(coverName)) { //pokud fond nema prirazenou obalku, pouzijeme vychozi
            coverName = defaultCoverName;
        }
        
        ClassPathResource resource = new ClassPathResource(
                wilcardedCoverFilePath
                    .withParameter("name", coverName)
                    .build());
        Assert.isTrue(resource.exists(), () -> "Cover resource " + resource + " not exists");
        return resource;
    }
}
