package cz.kpsys.portaro.record.document;

import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordWellKnownFields;

import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

public class ByField007DocumentContentTypeResolver implements Function<Record, Optional<ContentType>> {


    @Override
    public Optional<ContentType> apply(Record d) {
        var field7 = d.getDetail().getFirstFieldByTypeId(RecordWellKnownFields.DocumentPhysicalDescription.TYPE_ID);

        if (field7.isEmpty() || field7.get().isEmpty()) {
            return Optional.empty();
        }

        if (!field7.get().getFields().isEmpty()) { // pravdepodobne se jedna o jiny nez knihovnicky format
            return Optional.empty();
        }

        String physicalDescription = Objects.requireNonNull(field7.get().getRaw());
        if (physicalDescription.isEmpty()) {
            return Optional.empty();
        }

        if (physicalDescription.charAt(0) == 's') {
            return Optional.of(ContentType.AUDIO);
        }
        if (physicalDescription.charAt(0) == 'a') {
            return Optional.of(ContentType.MAP);
        }
        if (physicalDescription.charAt(0) == 'm') {
            return Optional.of(ContentType.MOVIE);
        }
        if (physicalDescription.charAt(0) == 't') {
            //return ContentType.BOOK;
            //nic - t je obecny text, coz nechavame na rozhodnuti podle fondu
        }
        if (physicalDescription.charAt(0) == 'v') {
            return Optional.of(ContentType.VIDEO);
        }

        return Optional.empty();
    }

}
