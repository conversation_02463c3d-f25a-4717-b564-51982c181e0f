package cz.kpsys.portaro.record.importer.marc;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.bool.StringToBooleanConverter;
import cz.kpsys.portaro.commons.date.*;
import cz.kpsys.portaro.commons.object.BasicIdentified;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.DataAccessException;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.marcxml.MarcConstants;
import cz.kpsys.portaro.marcxml.model.*;
import cz.kpsys.portaro.number.StringToBigDecimalConverter;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.detail.value.*;
import cz.kpsys.portaro.record.edit.FieldEditationCommand;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordFieldEditor;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;

import static cz.kpsys.portaro.CoreConstants.Datatype.*;
import static cz.kpsys.portaro.record.detail.FieldTypes.AUTHORITY_LEADER_FIELD_TYPE_ID;
import static cz.kpsys.portaro.record.detail.FieldTypes.DOCUMENT_LEADER_FIELD_TYPE_ID;
import static cz.kpsys.portaro.record.edit.EmptyFieldCreation.toEnd;

@RequiredArgsConstructor
@Slf4j
public class RecordMarcDtoImporter implements RecordImporter<RecordMarcDto> {

    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordFieldEditor recordFieldEditor;
    @NonNull ByIdLoadable<Fond, Integer> fondLoader;
    @NonNull StringToLocalDateConverter marcXmlSubfieldStringToLocalDateConverter = StringToLocalDateConverter.withoutIsoFallback(MarcConstants.MARCXML_LOCAL_DATE_FORMATTER);
    @NonNull StringToInstantConverter marcXmlSubfieldStringToInstantConverter;
    @NonNull StringToDateRangeConverter marcXmlSubfieldStringToDateRangeConverter;
    @NonNull StringToDatetimeRangeConverter marcXmlSubfieldStringToDatetimeRangeConverter;
    @NonNull StringToBigDecimalConverter marcXmlSubfieldStringToNumberConverter = StringToBigDecimalConverter.INSTANCE;
    @NonNull Converter<@NonNull String, @NonNull Boolean> booleanConverter = StringToBooleanConverter.notAllowingNullSource();

    @Override
    public Identified<UUID> importRecord(RecordMarcDto recordData, Fond fond, Department ctx, UserAuthentication currentAuth) {
        RecordEditation recordEditation = recordEditationFactory
                .on(ctx)
                .ofNew(fond)
                .build(currentAuth);
        importFieldsToRecord(recordData, recordEditation, ctx, currentAuth);

        recordEditation.publish(ctx, currentAuth);
        return new BasicIdentified<>(recordEditation.getRecord().getId());
    }

    @Override
    public Identified<UUID> updateRecord(RecordMarcDto recordData, Record record, Department ctx, UserAuthentication currentAuth) {
        RecordEditation recordEditation = recordEditationFactory
                .on(ctx)
                .ofExisting(record)
                .build(currentAuth);
        recordEditation.clear();
        importFieldsToRecord(recordData, recordEditation, ctx, currentAuth);

        recordEditation.saveIfModified(ctx, currentAuth);
        return new BasicIdentified<>(recordEditation.getRecord().getId());
    }

    private void importFieldsToRecord(RecordMarcDto record, RecordEditation recordEditation, Department ctx, UserAuthentication currentAuth) {
        FieldTypeId leaderFieldTypeId = recordEditation.isAuthority() ? AUTHORITY_LEADER_FIELD_TYPE_ID : DOCUMENT_LEADER_FIELD_TYPE_ID;
        createAndSetValueForTopfield(recordEditation, leaderFieldTypeId, new StringValueCommand(record.leader(), ctx, currentAuth));

        for (ControlfieldMarcDto controlfield : record.controlfields()) {
            FieldTypeId typeId = FieldTypeId.recordField(recordEditation.isAuthority(), FieldHelper.fromNumber3Char(controlfield.tag())); // TODO: get fieldTypeId from field type hierarchy (from fieldTypeLoader)
            createAndSetValueForTopfield(recordEditation, typeId, new StringValueCommand(controlfield.value(), ctx, currentAuth));
        }

        for (DatafieldMarcDto datafield : record.datafields()) {
            FieldTypeId typeId = FieldTypeId.recordField(recordEditation.isAuthority(), FieldHelper.fromNumber3Char(datafield.tag())); // TODO: get fieldTypeId from field type hierarchy (from fieldTypeLoader)
            Field<?> editedTopfield = createTopfield(recordEditation, typeId);

            if (!IndicatorType.EMPTY_INDICATORS.contains(datafield.ind1())) {
                createAndSetValueForSubfield(recordEditation, editedTopfield, editedTopfield.getType().getFieldTypeId().sub(FieldTypes.IND_1_FIELD_CODE), new StringValueCommand(IndicatorType.fromMarcXmlIndicatorValue(datafield.ind1()), ctx, currentAuth));
            }
            if (!IndicatorType.EMPTY_INDICATORS.contains(datafield.ind2())) {
                createAndSetValueForSubfield(recordEditation, editedTopfield, editedTopfield.getType().getFieldTypeId().sub(FieldTypes.IND_2_FIELD_CODE), new StringValueCommand(IndicatorType.fromMarcXmlIndicatorValue(datafield.ind2()), ctx, currentAuth));
            }

            List<MarcDtoSubfieldGroup> subfieldGroups = groupComplexSubfields(editedTopfield.getType(), datafield);
            for (MarcDtoSubfieldGroup subfieldGroup : subfieldGroups) {
                if (subfieldGroup.complex()) {
                    createAndSetValueCompositeSubfield(recordEditation, subfieldGroup, editedTopfield.getType(), editedTopfield);
                } else {
                    createAndSetValueSubfield(recordEditation, subfieldGroup.getSingleSubfield(), editedTopfield.getType(), editedTopfield, ctx, currentAuth);
                }
            }
        }
    }

    private void createAndSetValueSubfield(RecordEditation recordEditation, SubfieldMarcDto marcSubfield, FieldType<?> parentDatafieldType, Field<?> parentDatafield, Department ctx, UserAuthentication currentAuth) {
        if (marcSubfield.value() == null) {
            return;
        }
        try {
            FieldType<?> subfieldType = parentDatafieldType.getSubfieldTypeOrParentVirtualGroupTypeFor(marcSubfield.code());
            FieldValueCommand valueCommand = getCommandForMarcSubfield(marcSubfield, subfieldType, ctx, currentAuth);
            createAndSetValueForSubfield(recordEditation, parentDatafield, subfieldType.getFieldTypeId(), valueCommand);
        } catch (Exception e) {
            throw new DataAccessException(String.format("Exception while mapping subfield %s of parent %s", marcSubfield, parentDatafield), marcSubfield.toString(), e);
        }
    }

    private FieldValueCommand getCommandForMarcSubfield(SubfieldMarcDto marcSubfield, FieldType<?> fieldType, Department ctx, UserAuthentication currentAuth) {
        String content = Objects.requireNonNull(marcSubfield.value());

        if (marcSubfield instanceof VerbisSubfieldMarcDto verbisSubfieldMarcDto && verbisSubfieldMarcDto.recordId() != null) { // element obsahuje id -> jedna se o odkaz na autoritu
            RecordIdFondPair recordLink = RecordIdFondPair.of(verbisSubfieldMarcDto.recordId(), fondLoader.getById(verbisSubfieldMarcDto.existingFondId()));
            RecordIdFondPair origin = RecordIdFondPair.of(verbisSubfieldMarcDto.originId(), fondLoader.getById(verbisSubfieldMarcDto.existingOriginFondId()));
            return new SinglelabeledPredefinedRecordValueCommand(recordLink, origin, content);
        }

        try {
            if (fieldType.getDatatypeOrThrow().equals(DATE)) {
                LocalDate value = marcXmlSubfieldStringToLocalDateConverter.convert(content);
                return new DateValueCommand(value, ctx, currentAuth);
            }
            if (fieldType.getDatatypeOrThrow().equals(DATETIME)) {
                Instant value = marcXmlSubfieldStringToInstantConverter.convert(content);
                return new InstantValueCommand(value, ctx, currentAuth);
            }
            if (fieldType.getDatatypeOrThrow().equals(BOOLEAN)) {
                Boolean value = Objects.requireNonNull(booleanConverter.convert(content));
                return new BooleanValueCommand(value, ctx, currentAuth);
            }
            if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER) || fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER_DECIMAL_2) || fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER_DECIMAL_4)) {
                BigDecimal value = marcXmlSubfieldStringToNumberConverter.convert(content);
                return new NumberValueCommand(value, ctx, currentAuth);
            }
            if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.DATE_RANGE)) {
                DateRange value = marcXmlSubfieldStringToDateRangeConverter.convert(content);
                return new DateRangeValueCommand(value, ctx, currentAuth);
            }
            if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.DATETIME_RANGE)) {
                DatetimeRange value = marcXmlSubfieldStringToDatetimeRangeConverter.convert(content);
                return new DatetimeRangeValueCommand(value, ctx, currentAuth);
            }
            if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER_DECIMAL_6)) {
                throw new UnsupportedOperationException("Number with precision 6 is not supported");
            }
        } catch (Exception e) {
            //napr. pri importu nemuseji typy souhlasit
            log.warn("Incorrect format of subfield value (subfield {}, value {})", marcSubfield, content, e);
        }
        return new StringValueCommand(content, ctx, currentAuth);
    }


    private void createAndSetValueCompositeSubfield(RecordEditation recordEditation, MarcDtoSubfieldGroup subfieldGroup, FieldType<?> parentDatafieldType, Field<?> parentDatafield) {
        try {
            StrictVerbisSubfieldMarcDto firstMarcSubfield = subfieldGroup.subfields().getFirst();
            FieldType<?> virtualSubfieldType = parentDatafieldType.getSubfieldTypeOrParentVirtualGroupTypeFor(firstMarcSubfield.code());
            Assert.state(virtualSubfieldType.isVirtualGroup(), "We are in 'createAndSetValueCompositeSubfield' but parentDatafieldType %s did not return a virtual-group type for code %s".formatted(parentDatafieldType, firstMarcSubfield.code()));

            List<FieldLabel> fieldLabels = ListUtil.convert(subfieldGroup.subfields(), marcSubfieldResponse -> {
                FieldType<?> subfieldType = virtualSubfieldType.getSubfieldTypeOrParentVirtualGroupTypeFor(marcSubfieldResponse.code());
                return new FieldLabel(subfieldType.getFieldTypeId(), marcSubfieldResponse.value());
            });

            FieldValueCommand valueCommand = new MultilabeledPredefinedMissingRecordValueCommand(fieldLabels);
            if (firstMarcSubfield.recordId() != null) {
                RecordIdFondPair recordLink = RecordIdFondPair.of(firstMarcSubfield.recordId(), fondLoader.getById(firstMarcSubfield.existingFondId()));
                RecordIdFondPair origin = RecordIdFondPair.of(firstMarcSubfield.originId(), fondLoader.getById(firstMarcSubfield.existingOriginFondId()));
                valueCommand = new MultilabeledPredefinedRecordValueCommand(recordLink, origin, fieldLabels);
            }
            createAndSetValueForSubfield(recordEditation, parentDatafield, virtualSubfieldType.getFieldTypeId(), valueCommand);
        } catch (Exception e) {
            throw new DataAccessException(String.format("Exception while mapping complex subfield group of parent %s", parentDatafield), subfieldGroup.toString(), e);
        }
    }

    private List<MarcDtoSubfieldGroup> groupComplexSubfields(FieldType<?> parentDatafieldType, DatafieldMarcDto marcDatafield) {
        List<StrictVerbisSubfieldMarcDto> strictSubfields = marcDatafield.subfields().stream()
                .filter(marcSubfield -> marcSubfield.value() != null)
                .map(SubfieldMarcDto::toStrictVerbis)
                .toList();
        List<MarcDtoSubfieldGroup> subfieldGroups = new ArrayList<>();
        for (StrictVerbisSubfieldMarcDto strictSubfield : strictSubfields) {
            FieldType<?> subfieldTypeForCode = parentDatafieldType.getSubfieldTypeOrParentVirtualGroupTypeFor(strictSubfield.code());
            if (subfieldTypeForCode.isVirtualGroup()) {
                Optional<MarcDtoSubfieldGroup> existingGroup = subfieldGroups.stream()
                        .filter(MarcDtoSubfieldGroup::complex)
                        .findFirst();
                if (existingGroup.isPresent()) {
                    existingGroup.get().add(strictSubfield);
                } else {
                    subfieldGroups.add(MarcDtoSubfieldGroup.complex(strictSubfield));
                }
            } else {
                subfieldGroups.add(MarcDtoSubfieldGroup.notComplex(strictSubfield));
            }
        }
        return subfieldGroups;
    }

    private Field<?> createTopfield(RecordEditation recordEditation, FieldTypeId typeId) {
        return recordEditation.createField(toEnd(typeId));
    }

    private void createAndSetValueForTopfield(RecordEditation editation, FieldTypeId typeId, FieldValueCommand valueCommand) {
        Field<?> topfield = createTopfield(editation, typeId);

        FieldEditationCommand command = FieldEditationCommand.of(editation.getRecord(), topfield.getFieldId(), valueCommand)
                .notCreateMissingHierarchy();
        recordFieldEditor.editField(editation, command);
    }

    private void createAndSetValueForSubfield(RecordEditation editation, Field<?> topField, FieldTypeId subfieldTypeId, FieldValueCommand valueCommand) {
        Field<?> subfield = topField.createField(toEnd(subfieldTypeId));

        FieldEditationCommand command = FieldEditationCommand.of(editation.getRecord(), subfield.getFieldId(), valueCommand)
                .notCreateMissingHierarchy();
        recordFieldEditor.editField(editation, command);
    }

}
