package cz.kpsys.portaro.record;

import com.fasterxml.jackson.annotation.JsonProperty;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.LabeledReference;
import cz.kpsys.portaro.record.fond.SimpleFondResponse;
import cz.kpsys.portaro.search.BasicMapSearchParams;
import lombok.NonNull;

import java.util.UUID;

public record LabeledRecordRef(

        @NonNull
        UUID id,

        @NonNull
        SimpleFondResponse fond,

        @NonNull
        Text text

) implements LabeledReference<UUID> {

    public static LabeledRecordRef ofRecordLink(@NonNull RecordIdFondPair link, @NonNull Text text) {
        return new LabeledRecordRef(link.id().id(), SimpleFondResponse.mapFromFond(link.fond()), text);
    }

    @JsonProperty
    @Override
    public @NonNull String kind() {
        return BasicMapSearchParams.KIND_RECORD;
    }
}
