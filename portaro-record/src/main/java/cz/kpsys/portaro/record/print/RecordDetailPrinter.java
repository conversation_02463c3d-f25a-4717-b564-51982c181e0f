package cz.kpsys.portaro.record.print;

import cz.kpsys.portaro.record.ViewableRecord;
import cz.kpsys.portaro.record.query.ValFieldGroup;
import lombok.NonNull;
import org.springframework.lang.Nullable;

public interface RecordDetailPrinter {

    String field(@NonNull ViewableRecord record, @NonNull String fieldCode, @Nullable String subfieldsCodes, @Nullable String prefix, @Nullable String suffix, @Nullable String fieldDelimiter, @Nullable String subfieldDelimiter);

    String group(@NonNull ValFieldGroup group);

    String group(@NonNull ValFieldGroup group, String prefix, String suffix, String fieldDelimiter, String subfieldDelimiter);
    
}
