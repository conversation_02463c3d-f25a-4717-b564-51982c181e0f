package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MultilabeledPredefinedRecordValueCommandConverter implements TypedFieldValueConverter<MultilabeledPredefinedRecordValueCommand, FieldPayload<StringFieldValue>, StringFieldValue> {

    @NonNull
    @Override
    public FieldPayload<StringFieldValue> convert(@NonNull MultilabeledPredefinedRecordValueCommand command, @NonNull FieldType<?> fieldType, @NonNull RecordFieldId fieldId) {
        StringFieldValue valueHolder = StringFieldValue.of(
                getLabel(command),
                getText(command),
                Set.of(command.originId())
        );
        return FieldPayload.of(valueHolder, command.recordLink());
    }

    private String getLabel(@NonNull MultilabeledPredefinedRecordValueCommand command) {
        if (command.fieldLabels().isEmpty()) {
            throw new IllegalStateException("Trying to set multilabeled predefined record value command but with no subfield labels");
        }
        return command.fieldLabels().stream()
                .map(FieldLabel::value)
                .collect(Collectors.joining(" "));
    }

    private Text getText(@NonNull MultilabeledPredefinedRecordValueCommand command) {
        if (command.fieldLabels().isEmpty()) {
            return Texts.ofEmpty();
        }
        List<Text> texts = command.fieldLabels().stream()
                .map(FieldLabel::value)
                .filter(StringUtil::hasLength)
                .map(Texts::ofNative)
                .toList();
        return MultiText.ofTexts(texts).withSpaceDelimiter();
    }

    @Override
    public String toString() {
        return getClass().getSimpleName();
    }

}
