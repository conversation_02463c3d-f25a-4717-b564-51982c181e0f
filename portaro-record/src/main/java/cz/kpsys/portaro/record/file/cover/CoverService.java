package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.file.FileUtils;
import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.io.FileStreamConsumer;
import cz.kpsys.portaro.commons.object.repo.Deleter;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.LoadedFile;
import cz.kpsys.portaro.file.LoadedIdentifiedFile;
import cz.kpsys.portaro.file.security.FileSecurityActions;
import cz.kpsys.portaro.id.IDdFile;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.file.RecordWithAttachmentSaveCommand;
import cz.kpsys.portaro.security.SecurityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CoverService implements Serializable {

    @NonNull
    FileDataStreamer fileThumbnailDataStreamer;
    @NonNull
    CoverLoader fallbackCoverLoader;
    @NonNull
    CoverLoader allWebCoverLoader;
    @NonNull
    Saver<RecordWithAttachmentSaveCommand, ?> recordWithAttachmentSaver;
    @NonNull
    Deleter<IDdFile> identifiedFileDeleter;
    @NonNull
    SecurityManager securityManager;
    @NonNull
    TransactionTemplate transactionTemplate;
    @NonNull
    ContextualProvider<Department, @NonNull List<UUID>> forbiddenRecordIDsProvider;
    @NonNull
    ContextualProvider<Department, @NonNull List<Integer>> forbiddenRecordStatusIDsProvider;


    public void loadCover(Record recordHeader, Department ctx, FileStreamConsumer streamConsumer, boolean withoutFallback) {
        if (recordHeader.getCover() != null) {
            fileThumbnailDataStreamer.streamData(recordHeader.getCover().getId(), null, streamConsumer);
            return;
        }

        if (withoutFallback) {
            throw new ItemNotFoundException(LoadedFile.class, "cover of record " + recordHeader.getId());
        }

        LoadedFile uc = fallbackCoverLoader.getCover(recordHeader, ctx);
        if (uc instanceof LoadedIdentifiedFile loadedIdentifiedFile) {
            FileStreamConsumer.streamBytes(streamConsumer, uc.getFilename(), FileUtils.getBytesOfInputStream(loadedIdentifiedFile.getInputStream()));
        }
    }


    public void searchCover(Record record, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        securityManager.throwIfCannot(FileSecurityActions.FILES_MANAGE, currentAuth, ctx);
        assertCanSearchRecordCover(record, ctx);

        LoadedFile cover = allWebCoverLoader.getCover(record, ctx);
        if (cover != null) { //pokud nalezneme, najdeme a evt. smazeme aktualni.
            log.info("Successfully loaded cover of {} from {}, saving it.", record, cover.getSource());
            saveCover(record, cover, ctx, currentAuth);
        }
    }


    public void saveCover(@NonNull Record record, LoadedFile cover, @NonNull Department ctx,
                          @NonNull UserAuthentication currentAuth) {
        securityManager.throwIfCannot(FileSecurityActions.FILES_MANAGE, currentAuth, ctx);

        transactionTemplate.executeWithoutResult(_ ->
                recordWithAttachmentSaver.save(RecordWithAttachmentSaveCommand.ofLoadedCover(record, cover, currentAuth, ctx))
        );
    }


    public void deletePrimaryCover(@NonNull Record record, @NonNull Department ctx,
                                   @NonNull UserAuthentication currentAuth) {
        securityManager.throwIfCannot(FileSecurityActions.FILES_MANAGE, currentAuth, ctx);

        if (record.getCover() != null) {
            transactionTemplate.executeWithoutResult(_ -> identifiedFileDeleter.delete(record.getCover()));
        }
    }


    public void setPrimaryCover(@NonNull Record record, @NonNull IdentifiedFile coverFile,
                                @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        securityManager.throwIfCannot(FileSecurityActions.FILES_MANAGE, currentAuth, ctx);

        transactionTemplate.executeWithoutResult(_ ->
                recordWithAttachmentSaver.save(RecordWithAttachmentSaveCommand.ofCover(record, coverFile, currentAuth, ctx))
        );
    }

    private void assertCanSearchRecordCover(Record record, @NonNull Department ctx) {
        if (forbiddenRecordStatusIDsProvider.getOn(ctx).contains(record.getStatus().getId())) {
            throw new ForbiddenRecordStatusException(record);
        }
        if (forbiddenRecordIDsProvider.getOn(ctx).contains(record.getId())) {
            throw new ForbiddenRecordException(record);
        }
    }

}
