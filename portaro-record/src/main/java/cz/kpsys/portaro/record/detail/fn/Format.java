package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.detail.value.StringFieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import lombok.NonNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cz.kpsys.portaro.CoreConstants.Datatype.TEXT;

public record Format<T extends ScalarFieldValue<?>>(

        @NonNull
        String format,

        @NonNull
        List<Formula<T>> operands

) implements NaryFunction<StringFieldValue, T> {

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<StringFieldValue>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        return TEXT;
    }

    @Override
    public @NonNull FormulaEvaluation<StringFieldValue> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {
        List<T> values = new ArrayList<>();
        for (Formula<T> operand : operands) {
            FormulaEvaluation<T> resolvedOperandValue = evaluator.resolveFormulaValue(sourceNode, operand);
            if (resolvedOperandValue.isSuccess()) {
                values.add(resolvedOperandValue.existingSuccess());
            }
            if (resolvedOperandValue.isFailure()) {
                return resolvedOperandValue.castedFailed();
            }
        }

        return compute(values);
    }

    public @NonNull FormulaEvaluation<StringFieldValue> compute(@NonNull List<T> operandValue) {
        String formatted = String.format(format(), operandValue.stream().map(ScalarFieldValue::value).toArray());
        Set<RecordIdFondPair> origins = operandValue.stream().flatMap(t -> t.origins().stream()).collect(Collectors.toSet());
        return FormulaEvaluation.success(StringFieldValue.of(formatted, origins));
    }
}
