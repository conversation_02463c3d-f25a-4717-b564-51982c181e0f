package cz.kpsys.portaro.record.document;

/**
 * <PERSON><PERSON><PERSON> ulozeni obsahu - fyzicka forma (knizka, CD-ROM) nebo WEB
 * Created by <PERSON> on 16.10.2016.
 */
public enum ContentStorageWay {

    PHYSICAL(null), PAPER(PHYSICAL), DATA_BEARER(PHYSICAL), CD(DATA_BEARER), DVD(DATA_BEARER), FLASH_DISK(DATA_BEARER),
    WEB(null);

    private ContentStorageWay superType;

    ContentStorageWay(ContentStorageWay superType) {
        this.superType = superType;
    }

    /**
     * <PERSON><PERSON><PERSON>, zda je dany typ tim typem nebo jednim ze supertypu.
     * @param type
     * @return
     */
    public boolean is(ContentStorageWay type) {
        if (this == type) {
            return true;
        }
        if (superType == null) {
            return false;
        }
        return superType.is(type);
    }
}
