package cz.kpsys.portaro.record.holding;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import lombok.NonNull;
import lombok.With;

@With
public record RecordHoldingUpsertCommand(

        @NonNull
        Record record,

        @NonNull
        Department department,

        @NonNull
        Boolean discarded,

        @NonNull
        Department ctx,

        @NonNull
        UserAuthentication currentAuth

) {}
