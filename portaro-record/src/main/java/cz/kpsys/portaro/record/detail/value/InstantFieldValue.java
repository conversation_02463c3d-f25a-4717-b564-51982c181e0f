package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.record.RecordIdFondPair;
import lombok.NonNull;

import java.time.Instant;
import java.util.Set;

public record InstantFieldValue(

    @NonNull
    Instant value,

    @NonNull
    String label,

    @NonNull
    Text text,

    @NonNull
    Set<RecordIdFondPair> origins

) implements ScalarFieldValue<Instant> {

    public static @NonNull InstantFieldValue of(@NonNull Instant date, @NonNull Set<RecordIdFondPair> origins) {
        return new InstantFieldValue(
                date,
                InstantTypedValueConverter.formatLabel(date),
                Texts.ofDateWithTime(date),
                origins
        );
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        return o instanceof InstantFieldValue that && value.equals(that.value);
    }

    @Override
    public int hashCode() {
        return value().hashCode();
    }

}
