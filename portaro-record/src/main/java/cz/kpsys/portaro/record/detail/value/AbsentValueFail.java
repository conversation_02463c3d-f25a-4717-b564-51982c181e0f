package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.record.detail.spec.RecordDatableId;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import lombok.NonNull;

public record AbsentValueFail(

        @NonNull Text text,
        @NonNull RecordDatableId missingValueFieldId

) implements AbsentDataFail {

    public static @NonNull AbsentValueFail of(RecordDatableId recordDatableId) {
        return new AbsentValueFail(
                Texts.ofNative("Missing value " + recordDatableId),
                recordDatableId
        );
    }

    @Override
    public @NonNull RecordSpecSet toSpec() {
        return RecordSpecSet.of(missingValueFieldId.toRecordSpec());
    }

    @Override
    public String toString() {
        return "AbsentValue[" + missingValueFieldId + ']';
    }

}
