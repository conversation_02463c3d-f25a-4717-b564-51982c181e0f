package cz.kpsys.portaro.record.search;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.convert.ConversionException;
import cz.kpsys.portaro.commons.date.InstantToIsoStringConverter;
import cz.kpsys.portaro.commons.date.LocalDateToIsoStringConverter;
import cz.kpsys.portaro.commons.date.StringToInstantConverter;
import cz.kpsys.portaro.commons.date.StringToLocalDateConverter;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Labeled;
import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.datatype.DatatypableStringConverter;
import cz.kpsys.portaro.datatype.Datatype;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.marcxml.MarcConstants;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.lucene.Facet;
import cz.kpsys.portaro.search.lucene.FacetKey;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SearchResponseToSearchResultConverter {

    @NonNull DatatypableStringConverter datatypableStringConverter;
    @NonNull StringToLocalDateConverter marcXmlSubfieldStringToLocalDateConverter = StringToLocalDateConverter.withoutIsoFallback(MarcConstants.MARCXML_LOCAL_DATE_FORMATTER);
    @NonNull StringToInstantConverter marcSubfieldStringToInstantConverter;
    @NonNull LocalDateToIsoStringConverter localDateToIsoStringConverter = new LocalDateToIsoStringConverter();
    @NonNull InstantToIsoStringConverter instantToIsoStringConverter = new InstantToIsoStringConverter();

    public InternalSearchResult<String, MapBackedParams, RangePaging> mapSearchResult(AppserverSearchResponse response,
                                                                                      String usedRawQuery,
                                                                                      Range range) {
        List<String> content = Objects.requireNonNull(response.content());

        List<Facet> notEmptyFacets = response.facets().stream()
                .filter(facetDto -> !facetDto.keys().isEmpty())
                .map(this::mapFacet)
                .toList();
        return new InternalSearchResult<>(Chunk.of(content, Paging.ofRange(range).incrementPage()), response.totalElements(), Paging.ofRange(range), notEmptyFacets, usedRawQuery);
    }

    private Facet mapFacet(AppserverSearchResponse.FacetDto facetDto) {
        List<FacetKey> facetKeys = ListUtil.convertStrict(facetDto.keys(), facetKeyDto -> mapFacetKey(facetKeyDto, getDatatype(facetDto)));
        return new Facet(facetDto.id(), getDatatype(facetDto), facetKeys);
    }

    private static @NonNull ScalarDatatype getDatatype(AppserverSearchResponse.FacetDto facetDto) {
        return switch (facetDto.datatype()) {
            case "ROK" -> CoreConstants.Datatype.YEAR;
            case String datatypeName -> Datatype.scalar(datatypeName);
        };
    }

    private FacetKey mapFacetKey(AppserverSearchResponse.FacetKeyDto facetKeyDto, ScalarDatatype facetDatatype) {
        String keyValue = facetKeyDto.value();

        Text text = Texts.ofNative(keyValue);
        if (facetDatatype.equals(CoreConstants.Datatype.DATE)) {
            keyValue = localDateToIsoStringConverter.convert(marcXmlSubfieldStringToLocalDateConverter.convert(keyValue));
            text = Texts.ofNative(keyValue);
        } else if (facetDatatype.equals(CoreConstants.Datatype.DATETIME)) {
            keyValue = instantToIsoStringConverter.convert(marcSubfieldStringToInstantConverter.convert(keyValue));
            text = Texts.ofNative(keyValue);
        } else {
            try {
                Object keyRichValue = datatypableStringConverter.convertFromSimpleTypePreservingStructure(keyValue, facetDatatype);
                if (keyRichValue instanceof Labeled labeledRichValue) {
                    text = labeledRichValue.getText();
                }
            } catch (Exception e) {
                throw new ConversionException("Cannot convert facet key '%s' to datatype %s".formatted(keyValue, facetDatatype), e);
            }
        }

        return new FacetKey(keyValue, keyValue, text, facetKeyDto.count());
    }
}
