package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.TimeMeter;
import cz.kpsys.portaro.databasestructure.RecordDb.FUN_DEPENDENT_RECORDS;
import cz.kpsys.portaro.databasestructure.RecordDb.RECORD;
import cz.kpsys.portaro.databasestructure.RecordDb.RECORD_FIELD;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import cz.kpsys.portaro.sql.generator.UpdateQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.time.Duration;
import java.util.List;
import java.util.UUID;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class DependentRecordFieldDbProcInvalidator {

    private static final int BATCH_SIZE = 50;

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory qf;

    /// Get IDs of records which depend on data from the root record.
    /// @param recordId root record from which start search
    /// @return list of dependent records' IDs on the root one. It does NOT include the ID of the root record.
    public @NonNull List<UUID> getDependentRecords(@NonNull UUID recordId) {
        SelectQuery sq = qf.newSelectQuery();
        sq.select(FUN_DEPENDENT_RECORDS.RETURN_COLUMN);
        sq.fromFunction(FUN_DEPENDENT_RECORDS.NAME, recordId, false);
        return TimeMeter.measureAndLog(() -> jdbcTemplate.queryForList(sq.getSql(), sq.getParamMap(), UUID.class), log, () -> "Load dependent records of %s".formatted(recordId), Duration.ofMillis(500), Duration.ofSeconds(1));
    }

    /// Get IDs of records which depend on data from the root record.
    /// @param recordId root record from which start search
    /// @return list of dependent records' IDs on the root one. It returns also the ID of the root record.
    public @NonNull List<UUID> getDependentRecordsWithSelf(@NonNull UUID recordId) {
        SelectQuery sq = qf.newSelectQuery();
        sq.select(FUN_DEPENDENT_RECORDS.RETURN_COLUMN);
        sq.fromFunction(FUN_DEPENDENT_RECORDS.NAME, recordId, true);
        return TimeMeter.measureAndLog(() -> jdbcTemplate.queryForList(sq.getSql(), sq.getParamMap(), UUID.class), log, () -> "Load dependent records of %s".formatted(recordId), Duration.ofMillis(500), Duration.ofSeconds(1));
    }

    public void invalidate(@NonNull List<@NonNull UUID> recordIds) {
        if (log.isEnabledForLevel(Level.TRACE)) {
            log.trace("Invalidating {} records: {}", recordIds.size(), recordIds);
        } else {
            log.debug("Invalidating {} records", recordIds.size());
        }
        TimeMeter.measureAndLog(() -> ListUtil.chunks(recordIds, BATCH_SIZE).forEach(this::doInvalidate), log, () -> "Invalidation of records", Duration.ofMillis(500), Duration.ofSeconds(3));
    }

    private void doInvalidate(List<UUID> ids) {
        invalidateRecords(ids);
        invalidateDependentRecordFields(ids);
    }

    private void invalidateRecords(List<UUID> ids) {
        UpdateQuery uq = qf.newUpdateQuery();
        uq.update(RECORD.TABLE);
        uq.set().add(RECORD.FIELDS_FULLY_CACHED, false);
        uq.where()
                .in(RECORD.ID, ids)
                .and()
                .eq(RECORD.FIELDS_FULLY_CACHED, true);
        jdbcTemplate.update(uq.getSql(), uq.getParamMap());
    }

    private void invalidateDependentRecordFields(List<UUID> ids) {
        UpdateQuery uq = qf.newUpdateQuery();
        uq.update(RECORD_FIELD.TABLE);
        uq.set()
                .add(RECORD_FIELD.CACHE_VALID, false)
                .add(RECORD_FIELD.TEXT_VALUE, null)
                .add(RECORD_FIELD.LARGE_TEXT_VALUE, null)
                .add(RECORD_FIELD.NUMERIC_VALUE, null)
                .add(RECORD_FIELD.DATE_VALUE, null)
                .add(RECORD_FIELD.DATETIME_VALUE, null)
                .add(RECORD_FIELD.DATERANGE_VALUE, null)
                .add(RECORD_FIELD.DATETIMERANGE_VALUE, null)
                .add(RECORD_FIELD.BOOLEAN_VALUE, null)
                .add(RECORD_FIELD.PROBLEM, null);
        uq.where()
                .in(RECORD_FIELD.TARGET_RECORD_ID, ids)
                .and()
                .eq(RECORD_FIELD.CACHE_VALID, true)
                .and()
                .isNotNull(RECORD_FIELD.TEXT_VALUE)
                .and()
                .isNotNull(RECORD_FIELD.TARGET_RECORD_ID);
        jdbcTemplate.update(uq.getSql(), uq.getParamMap());
    }

}
