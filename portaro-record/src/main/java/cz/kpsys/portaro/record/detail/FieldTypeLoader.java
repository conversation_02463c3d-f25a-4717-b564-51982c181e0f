package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import lombok.NonNull;

public interface FieldTypeLoader extends AllValuesProvider<FieldType<?>>, ByIdLoadable<FieldType<?>, FieldTypeId> {

    FieldType<?> getSubfieldTypeById(FieldTypeId id);

    FieldType<?> getTopfieldTypeById(FieldTypeId id);

    @Override
    default FieldType<?> getById(@NonNull FieldTypeId id) {
        if (id.getLevel() == FieldTypeId.LEVEL_SUBSUBFIELD) {
            FieldType<?> subfieldType = getSubfieldTypeById(id.getParent());
            return subfieldType.getSubfieldTypeFor(id);
        }
        if (id.getLevel() == FieldTypeId.LEVEL_SUBFIELD) {
            return getSubfieldTypeById(id);
        }
        if (id.getLevel() == FieldTypeId.LEVEL_TOPFIELD) {
            return getTopfieldTypeById(id);
        }
        throw new IllegalArgumentException("Unknown field type id level: %s (type id %s)".formatted(id.getLevel(), id));
    }
}
