package cz.kpsys.portaro.record.detail.spec;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.RecordIdentifier;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.util.Assert;

import java.util.Comparator;
import java.util.Set;

import static cz.kpsys.portaro.record.detail.spec.RecordSpecCollectors.mergingFieldsSpecs;

public sealed interface RecordSpec permits FieldedRecordSpec, WholeRecordSpec {

    Comparator<RecordSpec> COMPARATOR = Comparator.comparing(RecordSpec::recordMatcher, RecordMatcher.COMPARATOR);

    static @NonNull GenericRecordSpec ofGeneric(@NonNull RecordIdFondPair recordIdFondPair, FieldsSpec fieldsSpec) {
        return new GenericRecordSpec(RecordIdentifierRecordMatcher.of(recordIdFondPair), fieldsSpec);
    }

    static @NonNull GenericRecordSpec ofSingleFielded(@NonNull RecordIdFondPair recordIdFondPair, @NonNull FieldSpec fieldSpec) {
        return new GenericRecordSpec(RecordIdentifierRecordMatcher.of(recordIdFondPair), FieldsSpec.of(fieldSpec));
    }

    static @NonNull GenericRecordSpec ofEmptyFielded(RecordMatcher recordMatcher) {
        return new GenericRecordSpec(recordMatcher, FieldsSpec.ofEmpty());
    }

    private static FieldedRecordSpec createUnioned(@NonNull RecordMatcher recordMatcher, @NonNull Set<FieldSpec> fields1, @NonNull Set<FieldSpec> fields2) {
        return new GenericRecordSpec(recordMatcher, FieldsSpec.of(ListUtil.unionSet(fields1, fields2)));
    }

    static WholeRecordSpec ofWholeRecord(@NonNull RecordIdFondPair recordIdFondPair) {
        return WholeRecordSpec.of(recordIdFondPair);
    }

    static GenericRecordSpec ofRecordAndFieldTypes(@NonNull RecordIdFondPair recordIdFondPair, @NonNull Set<FieldTypeId> fieldTypeId) {
        FieldsSpec fieldsSpec = fieldTypeId.stream()
                .map(FieldsSpec::ofFieldType)
                .collect(mergingFieldsSpecs());
        return ofGeneric(recordIdFondPair, fieldsSpec);
    }

    static FieldedRecordSpec ofRecordAndFieldType(@NonNull RecordIdFondPair recordIdFondPair, @NonNull FieldTypeId fieldTypeId) {
        return ofGeneric(recordIdFondPair, FieldsSpec.ofFieldType(fieldTypeId));
    }

    static FieldedRecordSpec ofLinkingRecordsFields(@NonNull FieldTypeId linkingRecordsLinkFieldTypeId, @NonNull RecordIdentifier linkingRecordsLink, @NonNull FieldTypeId linkingRecordsValueFieldTypeId, @Nullable Set<Integer> linkingRecordsFondIds) {
        return new GenericRecordSpec(
                LinkingRecordRecordMatcher.of(linkingRecordsLinkFieldTypeId, linkingRecordsLink, linkingRecordsFondIds),
                FieldsSpec.of(FieldSpec.ofFieldType(linkingRecordsValueFieldTypeId), FieldSpec.ofFieldType(linkingRecordsLinkFieldTypeId)) // docasne pridame k nacteni i pole obsahujici link na aktualni record (linkingRecordsLinkFieldTypeId), abychom mohli po nacteni najit hledana pole
        );
    }

    static RecordFieldId ofRecordAndField(@NonNull RecordIdFondPair recordIdFondPair, @NonNull FieldId specificFieldId) {
        return RecordFieldId.of(recordIdFondPair, specificFieldId);
    }

    static FieldedRecordSpec ofRecordMultifield(@NonNull RecordMultifieldId recordMultifieldId) {
        return ofSingleFielded(recordMultifieldId.recordIdFondPair(), FieldSpec.ofMultifield(recordMultifieldId.toMultifieldId()));
    }

    @NonNull
    RecordMatcher recordMatcher();

    @NonNull
    FieldsSpec fieldsSpec();

    @NonNull
    FieldedRecordSpec withFieldsSpec(@NonNull FieldsSpec fieldsSpec);

    boolean isEmpty();

    default boolean isOfSameRecordMatcher(@NonNull RecordSpec other) {
        if (this.equals(other)) {
            return true;
        }
        return recordMatcher().equals(other.recordMatcher());
    }

    default boolean covers(@NonNull RecordSpec other) {
        if (!isOfSameRecordMatcher(other)) {
            return false;
        }
        if (!(this instanceof FieldedRecordSpec thisFieldedRecordSpec)) {
            return true;
        }
        if (!(other instanceof FieldedRecordSpec otherFieldedRecordSpec)) {
            return false;
        }
        return thisFieldedRecordSpec.fieldsSpec().covers(otherFieldedRecordSpec.fieldsSpec());
    }

    /// Slouci dva spec do jednoho tak, aby vysledek plne pokryval oba.
    /// - pokud jeden z nich vubec nespecifikuje fieldy, vrati se primo ten.
    /// - jinak se slouci seznam fieldu
    default RecordSpec union(@NonNull RecordSpec other) {
        Assert.state(isOfSameRecordMatcher(other), "Cannot combine recordSpec with another one, which is not of same record");
        if (covers(other)) {
            return this;
        }
        if (other.covers(this)) {
            return other;
        }
        // ve chvili, kdy vime, ze se jedna o ten samy record a navzajem se nepokryvaji, je jasne, ze museji byt fieldable
        Set<FieldSpec> thisFields = ((FieldedRecordSpec) this).existingFields();
        Set<FieldSpec> otherFields = ((FieldedRecordSpec) other).existingFields();
        return createUnioned(recordMatcher(), thisFields, otherFields);
    }

}
