package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.BooleanFieldValue;
import cz.kpsys.portaro.record.detail.value.FalseCaseEvaluation;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import lombok.NonNull;

import java.util.function.Function;


public record WhenCase<T extends ScalarFieldValue<?>>(
        @NonNull Formula<BooleanFieldValue> first,
        @NonNull Formula<T> second
) implements BinaryFunction<T>, Case<T> {

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<T>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        return operandResultDatatypeResolver.apply(second);
    }

    @Override
    public @NonNull FormulaEvaluation<T> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {
        var condEval = evaluator.resolveFormulaValue(sourceNode, first());
        if (condEval.isFailure()) {
            return condEval.castedFailed();
        }
        var condVal = condEval.existingSuccess();

        if (condVal.value()) {
            return evaluator.resolveFormulaValue(sourceNode, second());
        }

        return FormulaEvaluation.failure(FalseCaseEvaluation.of());
    }

}
