package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import lombok.NonNull;

import java.util.List;
import java.util.UUID;

public interface RichRecordLoader extends IdAndIdsLoadable<Record, UUID> {

    Record getDetailed(Record basic);

    List<Record> getDocumentsByKindedIds(@NonNull List<Integer> kindedIds);

    List<Record> getDetailedDocumentsByKindedIds(@NonNull List<Integer> kindedIds);

    List<Record> getAuthoritiesByKindedIds(@NonNull List<Integer> kindedIds);

    List<Record> getDetailedAuthoritiesByKindedIds(@NonNull List<Integer> kindedIds);
    
    Record getById(UUID id, boolean withDetail);
    
    List<Record> getAllByIds(List<UUID> ids, boolean withDetail);

    /**
     * Nacte dokumenty bez detailu
     */
    List<Record> getAllByIds(@NonNull List<UUID> ids);

}
