package cz.kpsys.portaro.record.detail.value;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.object.LabeledRecord;
import cz.kpsys.portaro.commons.object.ValuableRecord;
import cz.kpsys.portaro.record.RecordIdFondPair;
import lombok.NonNull;

import java.util.List;
import java.util.Set;

public sealed interface ScalarFieldValue<VALUE> extends FieldValue<VALUE>, LabeledRecord, ValuableRecord<VALUE> permits AcceptableValueFieldValue, BooleanFieldValue, DatetimeRangeFieldValue, InstantFieldValue, LocalDateFieldValue, DateRangeFieldValue, NumberFieldValue, StringFieldValue {

    @NonNull
    String label();

    @NonNull
    Set<RecordIdFondPair> origins();

    @JsonIgnore
    default VectorFieldValue<ScalarFieldValue<VALUE>, VALUE> asVector() {
        return new VectorFieldValue<>(List.of(this), text());
    }

}
