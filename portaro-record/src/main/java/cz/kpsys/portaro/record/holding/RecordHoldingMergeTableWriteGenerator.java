package cz.kpsys.portaro.record.holding;

import cz.kpsys.portaro.appserver.dml.TableWrite;
import cz.kpsys.portaro.databasestructure.RecordDb;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.merge.ItemMerge;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordHoldingMergeTableWriteGenerator {

    @NonNull RecordHoldingLoader recordHoldingLoader;
    @NonNull RecordHoldingSourceLoader recordHoldingSourceLoader;

    public List<TableWrite> generateWrites(Record sourceRecord, Record targetRecord) {
        List<RecordHolding> sourceRecordHoldings = recordHoldingLoader.getAllByRecordId(sourceRecord.getId());
        List<RecordHolding> targetRecordHoldings = recordHoldingLoader.getAllByRecordId(targetRecord.getId());

        // nejdrive zjistime, ktere holdingy jsou v obou zaznamech stejne a u nich presuneme
        List<ItemMerge<RecordHolding>> sourceToTargetHoldingMerges = getSourceHoldingMerges(sourceRecordHoldings, targetRecordHoldings);
        return sourceToTargetHoldingMerges.stream()
                .flatMap(merge -> {

                    if (merge.getWay() == ItemMerge.ItemMergingWay.MOVE) {
                        return getMoveHoldingWrites(merge.getItem(), targetRecord);
                    }

                    if (merge.getWay() == ItemMerge.ItemMergingWay.REPLACE) {
                        RecordHolding replacementHolding = Objects.requireNonNull(merge.getReplacement());

                        List<RecordHoldingSource> sourceHoldingSources = recordHoldingSourceLoader.getAllByRecordHoldingId(merge.getItem().getId());
                        List<RecordHoldingSource> targetHoldingSources = recordHoldingSourceLoader.getAllByRecordHoldingId(replacementHolding.getId());

                        Stream<TableWrite> holdingSourceWrites = getSourceHoldingSourceMerges(sourceHoldingSources, targetHoldingSources).stream()
                                .flatMap(holdingSourceMerge -> {
                                    if (holdingSourceMerge.getWay() == ItemMerge.ItemMergingWay.MOVE) {
                                        return getMoveHoldingSourceWrites(holdingSourceMerge.getItem(), replacementHolding);
                                    }
                                    if (holdingSourceMerge.getWay() == ItemMerge.ItemMergingWay.REPLACE) {
                                        return getDeleteHoldingSourceWrites(holdingSourceMerge.getItem());
                                    }
                                    throw new IllegalStateException();
                                });

                        Stream<TableWrite> holdingWrites = getDeleteHoldingWrites(merge.getItem());

                        return Stream.concat(holdingSourceWrites, holdingWrites);
                    }

                    throw new IllegalStateException();
                })
                .collect(Collectors.toList());
    }

    private Stream<TableWrite> getMoveHoldingWrites(@NonNull RecordHolding recordHolding, @NonNull Record targetRecord) {
        return Stream.of(
                TableWrite.createUpdate(RecordDb.RECORD_HOLDING.TABLE)
                        .addWhereCol(RecordDb.RECORD_HOLDING.ID, recordHolding.getId())
                        .addCol(RecordDb.RECORD_HOLDING.RECORD_ID, targetRecord.getId())
        );
    }

    private Stream<TableWrite> getDeleteHoldingWrites(@NonNull RecordHolding recordHolding) {
        return Stream.of(
                TableWrite.createDelete(RecordDb.RECORD_HOLDING_SOURCE.TABLE)
                        .addWhereCol(RecordDb.RECORD_HOLDING_SOURCE.RECORD_HOLDING_ID, recordHolding.getId()),
                TableWrite.createDelete(RecordDb.RECORD_HOLDING.TABLE)
                        .addWhereCol(RecordDb.RECORD_HOLDING.ID, recordHolding.getId())
        );
    }

    private Stream<TableWrite> getMoveHoldingSourceWrites(@NonNull RecordHoldingSource holdingSource, @NonNull RecordHolding targetRecordHolding) {
        return Stream.of(
                TableWrite.createUpdate(RecordDb.RECORD_HOLDING_SOURCE.TABLE)
                        .addWhereCol(RecordDb.RECORD_HOLDING_SOURCE.ID, holdingSource.getId())
                        .addCol(RecordDb.RECORD_HOLDING_SOURCE.RECORD_HOLDING_ID, targetRecordHolding.getId())
        );
    }

    private Stream<TableWrite> getDeleteHoldingSourceWrites(@NonNull RecordHoldingSource holdingSource) {
        return Stream.of(
                TableWrite.createDelete(RecordDb.RECORD_HOLDING_SOURCE.TABLE)
                        .addWhereCol(RecordDb.RECORD_HOLDING_SOURCE.RECORD_HOLDING_ID, holdingSource.getId())
        );
    }

    private List<ItemMerge<RecordHolding>> getSourceHoldingMerges(List<RecordHolding> sourceHoldings, List<RecordHolding> targetHoldings) {
        List<ItemMerge<RecordHolding>> merges = new ArrayList<>();
        for (RecordHolding sourceRecordHolding : sourceHoldings) {
            targetHoldings.stream()
                    .filter(sourceRecordHolding::isOfSameDepartment)
                    .findFirst()
                    .ifPresentOrElse(
                            targetHolding -> merges.add(ItemMerge.byReplace(sourceRecordHolding, targetHolding)),
                            () -> merges.add(ItemMerge.byInsert(sourceRecordHolding))
                    );
        }
        return merges;
    }

    private List<ItemMerge<RecordHoldingSource>> getSourceHoldingSourceMerges(List<RecordHoldingSource> sourceHoldingSources, List<RecordHoldingSource> targetHoldingSources) {
        List<ItemMerge<RecordHoldingSource>> merges = new ArrayList<>();
        for (RecordHoldingSource sourceHoldingSource : sourceHoldingSources) {
            targetHoldingSources.stream()
                    .filter(sourceHoldingSource::isOfSameType)
                    .findFirst()
                    .ifPresentOrElse(
                            targetHoldingSource -> merges.add(ItemMerge.byReplace(sourceHoldingSource, targetHoldingSource)),
                            () -> merges.add(ItemMerge.byInsert(sourceHoldingSource))
                    );
        }
        return merges;
    }
}
