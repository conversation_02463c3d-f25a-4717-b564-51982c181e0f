package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.collection.ListCutter;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.DataAccessException;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.NumberUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.datatype.Datatype;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.hierarchy.InheritanceLoader;
import cz.kpsys.portaro.object.AcceptableValueGroupItem;
import cz.kpsys.portaro.record.detail.data.AuthorityFieldTypeEntity;
import cz.kpsys.portaro.record.detail.data.FieldTypeEntity;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.fn.Formulas;
import cz.kpsys.portaro.record.detail.link.def.*;
import cz.kpsys.portaro.record.detail.source.FieldSource;
import cz.kpsys.portaro.record.fond.Fond;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.dao.EmptyResultDataAccessException;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cz.kpsys.portaro.CoreConstants.Datatype.*;
import static cz.kpsys.portaro.record.detail.fn.Formulas.*;
import static cz.kpsys.portaro.record.detail.link.LookupDefinition.ofSelfRecordNativeName;
import static java.util.Objects.requireNonNull;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class FieldTypeEntityToPrefabFieldTypeConverter implements Converter<List<? extends FieldTypeEntity>, List<PrefabFieldType>> {

    @NonNull ByIdLoadable<Fond, Integer> fondLoader;
    @NonNull InheritanceLoader<Fond> enabledFondInheritanceLoader;
    @NonNull ByIdLoadable<TransferType, Integer> transferTypeLoader = TransferType.CODEBOOK;
    @NonNull Provider<@NonNull TransferType> defaultTransferTypeProvider = TransferType.DEFAULT_PROVIDER;
    @NonNull Provider<@NonNull String> rootSerialCodeProvider;
    @NonNull ContextualProvider<@NonNull String, @NonNull Long> recordSequenceProvider;

    @Override
    public List<PrefabFieldType> convert(@NonNull List<? extends FieldTypeEntity> entities) {

        List<PrefabFieldType> nativeFieldTypes = ListUtil.convertStrict(entities, this::convertWithExceptionNesting);

        return enhanceNativeTypesWithGeneratedVirtualGroupTypes(nativeFieldTypes);
    }

    private @NonNull List<PrefabFieldType> enhanceNativeTypesWithGeneratedVirtualGroupTypes(List<PrefabFieldType> nativeFieldTypes) {
        List<PrefabFieldType> result = new ArrayList<>(nativeFieldTypes.size() * 2);
        ListCutter<PrefabFieldType> subfieldTypesCutter = ListCutter.ofCopyOf(nativeFieldTypes);

        while (subfieldTypesCutter.hasRemainingItems()) {
            PrefabFieldType prefabFieldType = subfieldTypesCutter.cutFirst();

            if (prefabFieldType.shouldHaveVirtualParent()) {
                FieldTypeId parentVirtualFieldTypeId = prefabFieldType.id().withCode(FieldTypes.VIRTUAL_GROUP_FIELD_CODE);

                List<PrefabFieldType> otherGroupedSiblings = subfieldTypesCutter.cut(prefabFieldType::shouldBeInSameVirtualGroup);
                List<PrefabFieldType> allGroupedSiblings = ListUtil.createNewListPrepending(prefabFieldType, otherGroupedSiblings);

                List<PrefabFieldType> modifiedGroupedSiblings = ListUtil.convertStrict(allGroupedSiblings, groupedSibling -> groupedSibling.withModifiedParentId(parentVirtualFieldTypeId));
                result.add(createParentVirtualFieldType(parentVirtualFieldTypeId, modifiedGroupedSiblings));
                result.addAll(modifiedGroupedSiblings);

            } else {
                result.add(prefabFieldType);
            }
        }

        return result;
    }

    private PrefabFieldType createParentVirtualFieldType(FieldTypeId parentVirtualFieldTypeId, @NotEmpty List<PrefabFieldType> subfieldTypes) {
        String joinedNativeName = subfieldTypes.stream().map(PrefabFieldType::nativeName).collect(Collectors.joining(", "));

        TransferType bestTransferType = Stream.of(TransferType.ADD, TransferType.OVERWRITE, TransferType.REWRITE_WHEN_CREATING, TransferType.DELETE_WHEN_EMPTY, TransferType.NO_TRANSFER)
                .filter(transferType -> subfieldTypes.stream().anyMatch(subfieldType -> subfieldType.transferType() == transferType))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("This should not happen - any transfer type was not found"));

        Fond firstSubfieldLinkFond = Objects.requireNonNull(subfieldTypes.getFirst().linkRootFondConstraint(), () -> "Cannot create parent virtual field type, because subfield type " + subfieldTypes.getFirst() + " does not have link root fond.");
        LinkDef linkDef = new NativeNameLinkDef(); // TODO: asi by bylo lepsi udelat link ne na entry native subfield, ale proste na nazev

        List<FieldTypeId> subfieldsTargetExportFieldTypeIds = subfieldTypes.stream()
                .map(PrefabFieldType::fieldExportSetting)
                .filter(FieldExportSetting::isEnabled)
                .map(FieldExportSetting::getTargetFieldTypeId)
                .toList();
        FieldExportSetting exportSetting = FieldExportSetting.toSubfields(subfieldsTargetExportFieldTypeIds);

        return new PrefabFieldType(
                parentVirtualFieldTypeId,
                null,
                joinedNativeName,
                true,
                false,
                true,
                linkDef,
                FieldSource.common(),
                bestTransferType,
                firstSubfieldLinkFond,
                PrefabFieldType.NO_EXPLICIT_VALUE_DATATYPE,
                exportSetting,
                PrefabFieldType.NO_PIC,
                FdefGeneration.PHYSICAL_LINK_NO_VALUE
        );
    }

    private PrefabFieldType convertWithExceptionNesting(@NonNull FieldTypeEntity entity) {
        try {
            return convert(entity);
        } catch (Exception e) {
            throw new DataAccessException("Cannot load %s: %s".formatted(entity.getFieldTypeId(), e.getMessage()), entity.getFieldTypeId(), e);
        }
    }

    private PrefabFieldType convert(@NonNull FieldTypeEntity entity) {
        boolean inAuthorityRecord = entity instanceof AuthorityFieldTypeEntity;

        @NonNull String exportFieldNumber = String.valueOf(requireNonNull(entity.getExportFieldNumber(), "Exported field number (CIS_TAG) is null"));
        @NullableNotBlank String exportSubfieldCode = StringUtil.notBlankTrimmedString(entity.getExportSubfieldCode());
        @NonNull FieldTypeId typeId = FieldTypeId.parse(entity.getFieldTypeId());
        @NonNull FieldRecordLinkType recordLinkType = FieldRecordLinkType.CODEBOOK.getById(entity.getAuthorityType());
        @NullableNotBlank String pic = StringUtil.notBlankTrimmedString(entity.getPic());
        @NullableNotBlank String phraseGroupId = StringUtil.notBlankTrimmedString(entity.getPhraseGroupId());
        @NonNull FdefGeneration fdefGeneration = FdefGeneration.CODEBOOK.getById(entity.getGeneration());
        Fond linkedRecordFond = NumberUtil.emptyIfNullOrZero(entity.getLinkedRecordFond()).map(fondLoader::getById).orElse(null);

        Formula<?> formula = null;
        FieldTypeId specificLinkFieldTypeId = null;
        FieldTypeId linkedRecordFieldTypeId = null;
        boolean parentOfAnyGenerated = false;


        if (rootSerialCodeProvider.get().equals("100007000310") || rootSerialCodeProvider.get().equals("100007001019") || rootSerialCodeProvider.get().equals("100007001020") || rootSerialCodeProvider.get().equals("100007001021") || rootSerialCodeProvider.get().equals("100007001022")) { // sutor prod + sutor test + sutor import
            if (typeId.value().equals("d10001")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d10001.a")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                formula = sum(backRef(FieldTypeId.parse("d2025.main"), FieldTypeId.parse("d2070.m")));
            }
        }

        /// //////////////////////////////////////////////////////////////////////////////////
        /// SYSTIS + DEPRACATED SUTORY
        /// //////////////////////////////////////////////////////////////////////////////////

        if (rootSerialCodeProvider.get().equals("100007001022")) {


            /// RÁMEC - KDYŽ NEMÁM NADŘAZENOU POLOŽKU TAK JSEM RÁMEC JÁ, POKUD MÁM PŘEBÍRÁM RÁMEC Z NADŘAZENÉ POLOŽKY
            if (typeId.value().equals("d1001")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1001.a")) {
                recordLinkType = FieldRecordLinkType.FORMULA;

                formula = coalesce(
                        ref("d1002.main", "d1001.a"),
                        ref(ofSelfRecordNativeName())
                );
            }

            /// ZHOTOVITEL NADŘAZENÉ POLOŽKY - BERU FORCED ZHOTOVITELE ZE ZAKÁZEK NEBO BERU ZHOTOVITELE Z NADŘAZENÉ POLOŽKY
            if (typeId.value().equals("d1003")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1003.a")) {
                recordLinkType = FieldRecordLinkType.FORMULA;

                formula = coalesce(
                        localRef("d1012.a"),
                        ref("d1002.main", "d1003.a")
                );
            }

            /// ZHOTOVITEL NADŘAZENÉ POLOŽKY - BERU FORCED ZHOTOVITELE ZE ZAKÁZEK NEBO BERU ZHOTOVITELE Z NADŘAZENÉ POLOŽKY
            if (typeId.value().equals("d1003")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1003.a")) {
                recordLinkType = FieldRecordLinkType.FORMULA;

                formula = coalesce(
                        localRef("d1012.a"),
                        ref("d1002.main", "d1003.a")
                );
            }

            /// VÝPOČET NABÍDNUTÉ CENY
            if (typeId.value().equals("d1644")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1644.c")) { // nabídnutá cena
                recordLinkType = FieldRecordLinkType.FORMULA;

                var nabidnutaPrace = round2(orZero(multiply(
                        ref("d1620.main", "d2221.c"),
                        localRef("d1617.m"), // pocet hodin vykazanych
                        localRef("d1612.m") // nabidnute mnozstvi
                )));

                var nabidnutyMaterialNeboSubdodavka = round2(orZero(multiply(
                        localRef("d1615.c"), // jednotková cena
                        localRef("d1612.m") // nabidnute mnozstvi
                )));

                formula = round2(sum(nabidnutaPrace, nabidnutyMaterialNeboSubdodavka));
            }

            /// VÝPOČET VYKAZOVANÝCH POLOŽEK A JEJICH PŘEDPOKLÁDANÉ NÁKLADOVÉ CENY
            if (typeId.value().equals("d1650")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1650.c")) { // celkova nakladova cena
                recordLinkType = FieldRecordLinkType.FORMULA;

                var ocekavanyNakladPrace = round2(orZero(multiply(
                        ref("d1610.main", "d2221.c"),
                        localRef("d1619.m"), // pocet hodin vykazanych
                        localRef("d1611.m") // mnozstvi
                )));

                var celkovaNakladSubdodavka = round2(orZero(multiply(
                        localRef("d1614.c"), // nakladova cena subdodavky
                        localRef("d1611.m") // mnozstvi
                )));

                formula = round2(sum(
                        ocekavanyNakladPrace,
                        celkovaNakladSubdodavka,
                        backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1650.c"), enabledFondInheritanceLoader.getThisAndChildren(fondLoader.getById(76)))
                ));
            }

            /// VÝPOČET OBCHODNÍCH POLOŽEK A JEJICH PŘEDPOKLÁDANÉ PRODEJNÍ CENY
            if (typeId.value().equals("d1651")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1651.c")) { // celkova prodejni cena
                recordLinkType = FieldRecordLinkType.FORMULA;

                var ocekavanyProdejPrace = round2(orZero(multiply(
                        ref("d1620.main", "d2221.c"),
                        localRef("d1619.m"), // pocet hodin vykazanych
                        localRef("d1611.m") // mnozstvi
                )));

                var celkovaProdejSubdodavka = round2(orZero(multiply(
                        localRef("d1615.c"), // nakladova cena subdodavky
                        localRef("d1611.m") // mnozstvi
                )));

                formula = round2(sum(
                        ocekavanyProdejPrace,
                        celkovaProdejSubdodavka,
                        backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1651.c"), enabledFondInheritanceLoader.getThisAndChildren(fondLoader.getById(76)))
                ));
            }

            /// AGREGACNI FUNKCE
            /// Celkový náklad podřazených výkazů + Celkový náklad podřazených položek
            if (typeId.value().equals("d1641")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1641.c")) {
                recordLinkType = FieldRecordLinkType.FORMULA;

                formula = round2(sum(
                        backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d2071.c"), enabledFondInheritanceLoader.getThisAndChildren(fondLoader.getById(27))),  // 27 je fond "Vykazované náklady"
                        backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1641.c"), enabledFondInheritanceLoader.getThisAndChildren(fondLoader.getById(75)))   // 76 je fond "Položky vlastní práce"*/
                ));
            }

            /// AGREGACNI FUNKCE
            /// Celkový prodej v podřazených výkazů + Celkový prodej podřazených položek
            if (typeId.value().equals("d1642")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1642.c")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                formula = round2(sum(
                        backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d2081.c"), enabledFondInheritanceLoader.getThisAndChildren(fondLoader.getById(27))),  // 27 je fond "Vykazované náklady"
                        backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1642.c"), enabledFondInheritanceLoader.getThisAndChildren(fondLoader.getById(75)))   // 76 je fond "Položky vlastní práce"
                ));
            }

            /// ///////////////////////////////////////////
            /// AGREGACNI FUNKCE ZAKÁZKY
            if (typeId.value().equals("d1150")) {
                parentOfAnyGenerated = true;
            }
            /// Celková nabídnutá cena zakázky
            if (typeId.value().equals("d1150.c")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                formula = round2(sum(backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1644.c"))));
            }
            /// Celkové očekávaný náklad na zakázku
            if (typeId.value().equals("d1150.e")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                formula = round2(sum(backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1650.c"))));
            }
            /// Celkové očekávaný prodej na zakázku
            if (typeId.value().equals("d1150.f")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                formula = round2(sum(backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1651.c"))));
            }
            /// Celkové náklady na zakázku
            if (typeId.value().equals("d1150.g")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                formula = round2(sum(backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1641.c"))));
            }
            /// Celkové prodej na zakázku
            if (typeId.value().equals("d1150.h")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                formula = round2(sum(backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1642.c"))));
            }
            /// ////////////////////////////////////////


            /// PLNĚNÍ POLOŽKY
            if (typeId.value().equals("d1659")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1659.s")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                formula = round2(orZero(divide4(
                        localRef("d1642.c"), // Celkový prodej
                        localRef("d1651.c") // Očekávaný prodej
                )));
            }

            /// NAMAPOVANI FONDU Z KATALOGU CINNOSTI DO CENIKU
            if (typeId.value().equals("d2205")) {
                parentOfAnyGenerated = true;
            }
            // LINK FOND
            // Preneseni informace o FONDU z Katalogu cinnosti profesí a strojů
            if (typeId.value().equals("d2205.t")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d2200.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("fond");
            }


//            /// NAMAPOVANI CENIKU (KATALOGU CINNOSTI) DO MODELAČNÍHO CENÍKU OBCHODNÍCH POLOŽEK NÁKLADU
//            if (typeId.value().equals("d1610")) {
//                parentOfAnyGenerated = true;
//            }
//            if (typeId.value().equals("d1610.t")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d1610.main");
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
//            }
//
//            /// NAMAPOVANI CENIKU (KATALOGU CINNOSTI)  DO MODELAČNÍHO CENÍKU OBCHODNÍCH POLOŽEK PRODEJE
//            if (typeId.value().equals("d1620")) {
//                parentOfAnyGenerated = true;
//            }
//            if (typeId.value().equals("d1620.t")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d1620.main");
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
//            }
//
//            /// NAMAPOVANI CENIKU (KATALOGU CINNOSTI) DO NÁKLADOVÝ CENÍKU OSOBY
//            if (typeId.value().equals("d2054")) {
//                parentOfAnyGenerated = true;
//            }
//            if (typeId.value().equals("d2054.t")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2054.main");
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
//            }
//
//            /// NAMAPOVANI CENIKU (KATALOGU CINNOSTI) DO NÁKLADOVÝ CENÍKU STROJE
//            if (typeId.value().equals("d2040")) {
//                parentOfAnyGenerated = true;
//            }
//            if (typeId.value().equals("d2040.t")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2040.main");
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
//            }
//
//            /// NAMAPOVANI CENIKU (KATALOGU CINNOSTI) DO PRODEJNÍHO CENÍKU OSOBY
//            if (typeId.value().equals("d2064")) {
//                parentOfAnyGenerated = true;
//            }
//            if (typeId.value().equals("d2064.t")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2064.main");
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
//            }
//
//            /// NAMAPOVANI CENIKU (KATALOGU CINNOSTI) DO PRODEJNÍHO CENÍKU STROJE
//            if (typeId.value().equals("d2045")) {
//                parentOfAnyGenerated = true;
//            }
//            if (typeId.value().equals("d2045.t")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2045.main");
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
//            }

            /// INLINE FUNKCE
            /// VYPOCET nakladu a jejich vykazane nakladove ceny
            if (typeId.value().equals("d2071")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d2071.c")) { // Vykazana cena
                recordLinkType = FieldRecordLinkType.FORMULA;
                specificLinkFieldTypeId = null;

                /// Stroj
                var mnozstviStrojNaklad = round2(orZero(multiply(
                        ref("d2064.main", "d2221.c"),
                        localRef("d2070.o") // pocet jednotek vykazanych stroje
                )));

                /// Pracovní den
                var mnozstviOsobaNaklad = round2(orZero(multiply(
                        ref("d2054.main", "d2221.c"),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNaklad = round2(orZero(multiply(
                        ref("d2054.main", "d2221.d"),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));

                /// Víkend
                var mnozstviOsobaNakladVikend = round2(orZero(multiply(
                        ref("d2054.main", "d2221.e"),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNakladVikend = round2(orZero(multiply(
                        ref("d2054.main", "d2221.e"),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));

                /// Svátek
                var mnozstviOsobaNakladSvatek = round2(orZero(multiply(
                        ref("d2054.main", "d2221.f"),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNakladSvatek = round2(orZero(multiply(
                        ref("d2054.main", "d2221.f"),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));

                // TODO tady potrebuju aby conditional nevracel chybu, kdyz na polozku navazu dalsi polozku tak tak nema datum
                formula = sum(mnozstviOsobaNaklad, prescasOsobaNaklad, mnozstviStrojNaklad);
//                formula = conditional(ref("d2030.d", "d2116.o"),
//                        sum(mnozstviOsobaNakladSvatek, prescasOsobaNakladSvatek, mnozstviStrojNaklad),
//                        conditional(ref("d2030.d", "d2115.o"),
//                                sum(mnozstviOsobaNakladVikend, prescasOsobaNakladVikend, mnozstviStrojNaklad),
//                                sum(mnozstviOsobaNaklad, prescasOsobaNaklad, mnozstviStrojNaklad)));
            }

            /// VYPOCET nakladu a jejich vykazane nakladove ceny
            if (typeId.value().equals("d2081")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d2081.c")) { // Vykazana cena
                recordLinkType = FieldRecordLinkType.FORMULA;
                specificLinkFieldTypeId = null;

                /// Stroj
                var mnozstviStrojProdej = round2(orZero(multiply(
                        ref("d2045.main", "d2221.c"),
                        localRef("d2080.o") // pocet jednotek uctovanych stroje
                )));

                /// Pracovní den
                var mnozstviOsobaProdej = round2(orZero(multiply(
                        ref("d2040.main", "d2221.c"),
                        localRef("d2080.m") // pocet hodin uctovanych
                )));
                var prescasOsobaProdej = round2(orZero(multiply(
                        ref("d2040.main", "d2221.d"),
                        localRef("d2080.n") // pocet hodin uctovanych prescasovych
                )));

                /// Víkend
                var mnozstviOsobaProdejVikend = round2(orZero(multiply(
                        ref("d2040.main", "d2221.e"),
                        localRef("d2080.m") // pocet hodin vykazanych
                )));
                var prescasOsobaProdejVikend = round2(orZero(multiply(
                        ref("d2040.main", "d2221.e"),
                        localRef("d2080.n") // pocet hodin vykazanych prescasovych
                )));

                /// Svátek
                var mnozstviOsobaProdejSvatek = round2(orZero(multiply(
                        ref("d2040.main", "d2221.f"),
                        localRef("d2080.m") // pocet hodin vykazanych
                )));
                var prescasOsobaProdejSvatek = round2(orZero(multiply(
                        ref("d2040.main", "d2221.f"),
                        localRef("d2080.n") // pocet hodin vykazanych prescasovych
                )));

                // TODO tady potrebuju aby conditional nevracel chybu, kdyz na polozku navazu dalsi polozku tak tak nema datum
                formula = sum(mnozstviOsobaProdej, prescasOsobaProdej, mnozstviStrojProdej);
//                formula = conditional(ref("d2030.d", "d2116.o"),
//                        sum(mnozstviOsobaProdejSvatek, prescasOsobaProdejSvatek, mnozstviStrojProdej),
//                        conditional(ref("d2030.d", "d2115.o"),
//                                sum(mnozstviOsobaProdejVikend, prescasOsobaProdejVikend, mnozstviStrojProdej),
//                                sum(mnozstviOsobaProdej, prescasOsobaProdej, mnozstviStrojProdej)));
            }

            /// VYKAZ PRACE
            /// LINK DIVIZE PRACOVNIKA DLE Z JEHO MZDOVE POLOZKY
            if (typeId.value().equals("d2037")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d2037.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d2051.a");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1851.a");
            }

            /// PERSONALISTIKA
            /// LINKOVÁNÍ Z PRACOVNÍHO POMĚRU DO MZDY
            if (typeId.value().equals("d1881")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1881.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d1880.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1820.a");
            }
            if (typeId.value().equals("d1881.e")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d1880.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1820.e");
            }
            if (typeId.value().equals("d1882")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1882.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d1880.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1850.a");
            }

            /// LINKOVÁNÍ ZE MZDY DO MĚSÍČNÍ DOCHÁZKY PRACOVNÍKŮ
            if (typeId.value().equals("d8981")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d8981.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d8980.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1881.a");
            }
            if (typeId.value().equals("d8981.e")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d8980.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1881.e");
            }
            if (typeId.value().equals("d8982")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d8982.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d8980.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1882.a");
            }
            if (typeId.value().equals("d8925")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d8925.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d8980.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1825.a");
            }
            if (typeId.value().equals("d8955")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d8955.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d8980.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1855.main.a");
            }
            if (typeId.value().equals("d8955.b")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d8980.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1855.main.b");
            }

            if (typeId.value().equals("d8951")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d8951.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d8980.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1851.a");
            }
        }

        /// //////////////////////////////////////////////////////////////////////////////////
        /// //////////////////////////////////////////////////////////////////////////////////
        /// //////////////////////////////////////////////////////////////////////////////////
        /// SUTOR OSTRY VZORCE NA NOVE FDEDY
        /// //////////////////////////////////////////////////////////////////////////////////
        /// //////////////////////////////////////////////////////////////////////////////////
        /// //////////////////////////////////////////////////////////////////////////////////

        /// LINK PRACOVNÍK, PRACOVNÍ POMĚR ZE MZDY
        if (rootSerialCodeProvider.get().equals("100007000310") || rootSerialCodeProvider.get().equals("100007001020") || rootSerialCodeProvider.get().equals("100007001021")) {
            if (typeId.value().equals("d2050")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d2050.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d2051.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1880.main.a");
            }
            if (typeId.value().equals("d2050.b")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d2051.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1880.main.b");
            }
        }

        if (rootSerialCodeProvider.get().equals("100007000310") || rootSerialCodeProvider.get().equals("100007001019")  || rootSerialCodeProvider.get().equals("100007001020") || rootSerialCodeProvider.get().equals("100007001021")) { //PROD + TEST

            ///  GENEROVÁNÍ ČÍSLA ZAKÁZKY - samostatné formule jsou u fondovým
            if (typeId.value().equals("d245")) {
                parentOfAnyGenerated = true;
            }

            if (typeId.value().equals("d1815")) {
                parentOfAnyGenerated = true;
            }

            if (typeId.value().equals("d1815.a")) {
                recordLinkType = FieldRecordLinkType.FORMULA;

                formula = match(
                        whenCase(equal(localRef("d1820.e"), constAcceptable("3")), constLong(RecordSequenceValueProvider.of(recordSequenceProvider, "osobni_cislo_hpp"))), // "HPP Neurčitá"
                        whenCase(equal(localRef("d1820.e"), constAcceptable("4")), constLong(RecordSequenceValueProvider.of(recordSequenceProvider, "osobni_cislo_hpp"))), // "HPP Určitá"
                        whenCase(equal(localRef("d1820.e"), constAcceptable("5")), constLong(RecordSequenceValueProvider.of(recordSequenceProvider, "osobni_cislo_zivnost"))), // "Živnost"
                        whenCase(equal(localRef("d1820.e"), constAcceptable("6")), constLong(RecordSequenceValueProvider.of(recordSequenceProvider, "osobni_cislo_subdodavatele"))), // "subdodavatel"
                        whenCase(equal(localRef("d1820.e"), constAcceptable("10")), constLong(RecordSequenceValueProvider.of(recordSequenceProvider, "osobni_cislo_praxe"))), // "Praxe"
                        whenCase(equal(localRef("d1820.e"), constAcceptable("8")), constLong(RecordSequenceValueProvider.of(recordSequenceProvider, "osobni_cislo_dpp"))), // "DPP"
                        whenCase(equal(localRef("d1820.e"), constAcceptable("9")), constLong(RecordSequenceValueProvider.of(recordSequenceProvider, "osobni_cislo_dpc"))), // "DPČ"
                        whenCase(equal(localRef("d1820.e"), constAcceptable("7")), constLong(RecordSequenceValueProvider.of(recordSequenceProvider, "osobni_cislo_zarazka"))), // "Tma" TODO asi Zarážkový pracovníci
                        defaultCase(constLong(RecordSequenceValueProvider.of(recordSequenceProvider, "osobni_cislo")))
                );
            }


            /// RÁMEC - KDYŽ NEMÁM NADŘAZENOU POLOŽKU TAK JSEM RÁMEC JÁ, POKUD MÁM PŘEBÍRÁM RÁMEC Z NADŘAZENÉ POLOŽKY
            if (typeId.value().equals("d1001")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1001.a")) {
                recordLinkType = FieldRecordLinkType.FORMULA;

                formula = coalesce(
                        ref("d1002.main", "d1001.a"),
                        ref(ofSelfRecordNativeName())
                );
            }

            /// ZHOTOVITEL NADŘAZENÉ POLOŽKY - BERU FORCED ZHOTOVITELE ZE ZAKÁZEK NEBO BERU ZHOTOVITELE Z NADŘAZENÉ POLOŽKY
            if (typeId.value().equals("d1003")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1003.a")) {
                recordLinkType = FieldRecordLinkType.FORMULA;

                formula = coalesce(
                        localRef("d1012.a"),
                        ref("d1002.main", "d1003.a")
                );
            }

            /// ZHOTOVITEL NADŘAZENÉ POLOŽKY - BERU FORCED ZHOTOVITELE ZE ZAKÁZEK NEBO BERU ZHOTOVITELE Z NADŘAZENÉ POLOŽKY
            if (typeId.value().equals("d1003")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1003.a")) {
                recordLinkType = FieldRecordLinkType.FORMULA;

                formula = coalesce(
                        localRef("d1012.a"),
                        ref("d1002.main", "d1003.a")
                );
            }

            /// KATALOGOVA CINNOST NADŘAZENÉ POLOŽKY - BERU FORCED KATALOGOVA CINNOST ZE ZAKÁZEK NEBO BERU KATALOGOVA CINNOST Z NADŘAZENÉ POLOŽKY
            if (typeId.value().equals("d1607")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1607.a")) {
                recordLinkType = FieldRecordLinkType.FORMULA;

                formula = coalesce(
                        localRef("d1609.a"),
                        ref("d1002.main", "d1607.a")
                );
            }

            /// /////////////////////////////////////////////////////////////////////////////////////////////////////////////
            /// KALKULACE ZAKÁZKY
            /// /////////////////////////////////////////////////////////////////////////////////////////////////////////////

            /// VÝPOČET KALKULOVANÉ CENY
            if (typeId.value().equals("d1644")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1644.c")) { // kalkulovaná cena
                recordLinkType = FieldRecordLinkType.FORMULA;

                var nabidnutaPrace = round2(orZero(multiply(
                        ref("d1608.main", "d2221.c"),
                        localRef("d1617.m"), // pocet hodin vykazanych
                        localRef("d1612.m") // kalkulované mnozstvi
                )));

                var nabidnutyMaterialNeboSubdodavka = round2(orZero(multiply(
                        localRef("d1615.c"), // jednotková cena
                        localRef("d1612.m") // kalkulované mnozstvi
                )));

                formula = round2(sum(nabidnutaPrace, nabidnutyMaterialNeboSubdodavka));
            }

            /// PLÁNOVÁNÍ NÁKLAD
            /// VÝPOČET VYKAZOVANÝCH POLOŽEK A JEJICH PŘEDPOKLÁDANÉ NÁKLADOVÉ CENY
            if (typeId.value().equals("d1656")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1656.c")) { // celkova nakladova cena
                recordLinkType = FieldRecordLinkType.FORMULA;

                formula = round2(sum(
                        orZero(localRef("d1657.c")),
                        backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1656.c"), enabledFondInheritanceLoader.getThisAndChildren(fondLoader.getById(78)))
                ));
            }

            /// PLÁNOVÁNÍ NÁKLAD
            /// VÝPOČET VYKAZOVANÝCH POLOŽEK A JEJICH PŘEDPOKLÁDANÉ NÁKLADOVÉ CENY
            if (typeId.value().equals("d1650")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1650.c")) { // celkova nakladova cena
                recordLinkType = FieldRecordLinkType.FORMULA;

                var ocekavanyNakladPrace = round2(orZero(multiply(
                        ref("d1610.main", "d2221.c"),
                        localRef("d1619.m"), // pocet hodin vykazanych
                        localRef("d1611.m") // mnozstvi
                )));

                var celkovaNakladSubdodavka = round2(orZero(multiply(
                        localRef("d1614.c"), // nakladova cena subdodavky
                        localRef("d1611.m") // mnozstvi
                )));

                formula = round2(sum(
                        ocekavanyNakladPrace,
                        celkovaNakladSubdodavka,
                        backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1650.c"), enabledFondInheritanceLoader.getThisAndChildren(fondLoader.getById(76)))
                ));
            }

            /// PLÁNOVÁNÍ PRODEJ
            /// VÝPOČET PLÁNOVANÝCH POLOŽEK A JEJICH PŘEDPOKLÁDANÉ PRODEJNÍ CENY
            if (typeId.value().equals("d1651")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1651.c")) { // PLÁNOVANÁ prodejni cena
                recordLinkType = FieldRecordLinkType.FORMULA;

                var ocekavanyProdejPrace = round2(orZero(multiply(
                        ref("d1620.main", "d2221.c"),
                        localRef("d1619.m"), // plánovaný počet hodin
                        localRef("d1611.m") // mnozstvi
                )));

                var celkovaProdejSubdodavka = round2(orZero(multiply(
                        localRef("d1615.c"), // plánovaná cena subdodávky
                        localRef("d1611.m") // mnozstvi
                )));

                formula = round2(sum(
                        ocekavanyProdejPrace,
                        celkovaProdejSubdodavka,
                        backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1651.c"), enabledFondInheritanceLoader.getThisAndChildren(fondLoader.getById(76)))
                ));
            }

            /// SLEDOVÁNÍ REALIZACE NÁKLAD
            /// Celkový náklad podřazených výkazů + Celkový náklad podřazených položek
            if (typeId.value().equals("d1641")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1641.c")) {
                recordLinkType = FieldRecordLinkType.FORMULA;

                formula = round2(sum(
                        backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d2071.c"), enabledFondInheritanceLoader.getThisAndChildren(fondLoader.getById(27))),  // 27 je fond "Vykazované náklady"
                        backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1641.c"), enabledFondInheritanceLoader.getThisAndChildren(fondLoader.getById(75)))   // 75 je fond "Položky vlastní práce"*/
                ));
            }

            /// SLEDOVÁNÍ REALIZACE PRODEJ
            /// Celkový prodej v podřazených výkazů + Celkový prodej podřazených položek
            if (typeId.value().equals("d1642")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1642.c")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                formula = round2(sum(
                        backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d2081.c"), enabledFondInheritanceLoader.getThisAndChildren(fondLoader.getById(27))),  // 27 je fond "Vykazované náklady"
                        backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1642.c"), enabledFondInheritanceLoader.getThisAndChildren(fondLoader.getById(75)))   // 75 je fond "Položky vlastní práce"
                ));
            }

            /// ///////////////////////////////////////////
            /// AGREGACNI FUNKCE ZAKÁZKY
            if (typeId.value().equals("d1150")) {
                parentOfAnyGenerated = true;
            }
            /// KALKULACE zakázky
            if (typeId.value().equals("d1150.c")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                formula = round2(sum(backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1644.c"))));
            }
            /// SOUČET BUDGETŮ REALIZACE
            if (typeId.value().equals("d1150.d")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                formula = round2(sum(backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1656.c"))));
            }
            /// PLÁNOVANÝ náklad na zakázku
            if (typeId.value().equals("d1150.e")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                formula = round2(sum(backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1650.c"))));
            }
            /// PLÁNOVANÝ prodej na zakázku
            if (typeId.value().equals("d1150.f")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                formula = round2(sum(backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1651.c"))));
            }
            /// Celkové náklady na zakázku
            if (typeId.value().equals("d1150.g")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                formula = round2(sum(backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1641.c"))));
            }
            /// Celkové prodej na zakázku
            if (typeId.value().equals("d1150.h")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                formula = round2(sum(backRef(FieldTypeId.parse("d1002.main"), FieldTypeId.parse("d1642.c"))));
            }
            /// ////////////////////////////////////////


            /// PLNĚNÍ POLOŽKY
            if (typeId.value().equals("d1659")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1659.s")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                var plneniFond76 = round2(divide4(
                        localRef("d1642.c"), // Celkový prodej
                        localRef("d1651.c") // Očekávaný prodej
                ));

                var plneniFond77a78 = round2(divide4(
                        localRef("d1642.c"), // Celkový prodej
                        localRef("d1657.c")  // Budget
                ));

                formula = coalesce(
                        plneniFond76,
                        plneniFond77a78
                );
            }

            /// NAMAPOVANI FONDU Z KATALOGU CINNOSTI DO CENIKU
            if (typeId.value().equals("d2205")) {
                parentOfAnyGenerated = true;
            }
            // LINK FOND
            // Preneseni informace o FONDU z Katalogu cinnosti profesí a strojů
            if (typeId.value().equals("d2205.t")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d2200.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("fond");
            }

            /// TOTO ASI BUDE TŘEBA AŽ ZAČNEM ŘEŠIT EXTRA ČINNOSTI
//            /// NAMAPOVANI CENIKU (KATALOGU CINNOSTI) DO NÁKLADOVÝ CENÍKU OSOBY
//            if (typeId.value().equals("d2054")) {
//                parentOfAnyGenerated = true;
//            }
//            if (typeId.value().equals("d2054.t")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2054.main");
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
//            }
//
//            /// NAMAPOVANI CENIKU (KATALOGU CINNOSTI) DO NÁKLADOVÝ CENÍKU STROJE
//            if (typeId.value().equals("d2040")) {
//                parentOfAnyGenerated = true;
//            }
//            if (typeId.value().equals("d2040.t")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2040.main");
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
//            }
//
//            /// NAMAPOVANI CENIKU (KATALOGU CINNOSTI) DO PRODEJNÍHO CENÍKU OSOBY
//            if (typeId.value().equals("d2064")) {
//                parentOfAnyGenerated = true;
//            }
//            if (typeId.value().equals("d2064.t")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2064.main");
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
//            }
//
//            /// NAMAPOVANI CENIKU (KATALOGU CINNOSTI) DO PRODEJNÍHO CENÍKU STROJE
//            if (typeId.value().equals("d2045")) {
//                parentOfAnyGenerated = true;
//            }
//            if (typeId.value().equals("d2045.t")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2045.main");
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
//            }

            /// INLINE FUNKCE
            /// VYPOCET nakladu a jejich vykazane nakladove ceny
            if (typeId.value().equals("d2071")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d2071.c")) { // Vykazana cena
                recordLinkType = FieldRecordLinkType.FORMULA;
                specificLinkFieldTypeId = null;

                /// Stroj
                var mnozstviStrojNaklad = round2(orZero(multiply(
                        ref("d2064.main", "d2221.c"),
                        localRef("d2070.o") // pocet jednotek vykazanych stroje
                )));

                /// Pracovní den
                var mnozstviOsobaNaklad = round2(orZero(multiply(
                        ref("d2054.main", "d2221.c"),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNaklad = round2(orZero(multiply(
                        ref("d2054.main", "d2221.d"),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));

                /// Víkend
                var mnozstviOsobaNakladVikend = round2(orZero(multiply(
                        ref("d2054.main", "d2221.e"),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNakladVikend = round2(orZero(multiply(
                        ref("d2054.main", "d2221.e"),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));

                /// Svátek
                var mnozstviOsobaNakladSvatek = round2(orZero(multiply(
                        ref("d2054.main", "d2221.f"),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNakladSvatek = round2(orZero(multiply(
                        ref("d2054.main", "d2221.f"),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));

                // TODO tady potrebuju aby conditional nevracel chybu, kdyz na polozku navazu dalsi polozku tak tak nema datum
                formula = sum(mnozstviOsobaNaklad, prescasOsobaNaklad, mnozstviStrojNaklad);
//                formula = conditional(ref("d2030.d", "d2116.o"),
//                        sum(mnozstviOsobaNakladSvatek, prescasOsobaNakladSvatek, mnozstviStrojNaklad),
//                        conditional(ref("d2030.d", "d2115.o"),
//                                sum(mnozstviOsobaNakladVikend, prescasOsobaNakladVikend, mnozstviStrojNaklad),
//                                sum(mnozstviOsobaNaklad, prescasOsobaNaklad, mnozstviStrojNaklad)));
            }

            /// VYPOCET nakladu a jejich vykazane nakladove ceny
            if (typeId.value().equals("d2081")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d2081.c")) { // Vykazana cena
                recordLinkType = FieldRecordLinkType.FORMULA;
                specificLinkFieldTypeId = null;

                /// Stroj
                var mnozstviStrojProdej = round2(orZero(multiply(
                        ref("d2045.main", "d2221.c"),
                        localRef("d2080.o") // pocet jednotek uctovanych stroje
                )));

                /// Pracovní den
                var mnozstviOsobaProdej = round2(orZero(multiply(
                        ref("d2040.main", "d2221.c"),
                        localRef("d2080.m") // pocet hodin uctovanych
                )));
                var prescasOsobaProdej = round2(orZero(multiply(
                        ref("d2040.main", "d2221.d"),
                        localRef("d2080.n") // pocet hodin uctovanych prescasovych
                )));

                /// Víkend
                var mnozstviOsobaProdejVikend = round2(orZero(multiply(
                        ref("d2040.main", "d2221.e"),
                        localRef("d2080.m") // pocet hodin vykazanych
                )));
                var prescasOsobaProdejVikend = round2(orZero(multiply(
                        ref("d2040.main", "d2221.e"),
                        localRef("d2080.n") // pocet hodin vykazanych prescasovych
                )));

                /// Svátek
                var mnozstviOsobaProdejSvatek = round2(orZero(multiply(
                        ref("d2040.main", "d2221.f"),
                        localRef("d2080.m") // pocet hodin vykazanych
                )));
                var prescasOsobaProdejSvatek = round2(orZero(multiply(
                        ref("d2040.main", "d2221.f"),
                        localRef("d2080.n") // pocet hodin vykazanych prescasovych
                )));

                // TODO tady potrebuju aby conditional nevracel chybu, kdyz na polozku navazu dalsi polozku tak tak nema datum
                formula = sum(mnozstviOsobaProdej, prescasOsobaProdej, mnozstviStrojProdej);
//                formula = conditional(ref("d2030.d", "d2116.o"),
//                        sum(mnozstviOsobaProdejSvatek, prescasOsobaProdejSvatek, mnozstviStrojProdej),
//                        conditional(ref("d2030.d", "d2115.o"),
//                                sum(mnozstviOsobaProdejVikend, prescasOsobaProdejVikend, mnozstviStrojProdej),
//                                sum(mnozstviOsobaProdej, prescasOsobaProdej, mnozstviStrojProdej)));
            }

            /// VYKAZ PRACE
            /// LINK DIVIZE PRACOVNIKA DLE Z JEHO MZDOVE POLOZKY
            if (typeId.value().equals("d2037")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d2037.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d2051.a");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1883.a");
            }

            /// ////////////////////////////////////////////////////////////
            /// PERSONALISTIKA
            /// ////////////////////////////////////////////////////////////

            /// LINKOVÁNÍ ICO FIRMY DO PRACOVNÍHO POMĚRU
            if (typeId.value().equals("d1850")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1850.i")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d1850.a");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1210.v");
            }

            /// LINKOVÁNÍ DIC FIRMY DO PRACOVNÍHO POMĚRU
            if (typeId.value().equals("d1850")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1850.j")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d1850.a");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1210.w");
            }

            /// LINKOVÁNÍ VEDOUCÍHO DIVIZE
            if (typeId.value().equals("d1852")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1852.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d1851.a");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1230.main.a");
            }
            if (typeId.value().equals("d1852.b")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d1851.a");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1230.main.b");
            }

            /// LINKOVÁNÍ Z PRACOVNÍHO POMĚRU DO MZDY
            /// OSOBNÍ ČÍSLO
            if (typeId.value().equals("d1881")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1881.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d1880.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1815.a");
            }
            if (typeId.value().equals("d1881.e")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d1880.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1820.e");
            }
            /// ZAMĚSTNAVATEL
            if (typeId.value().equals("d1882")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1882.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d1880.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1850.a");
            }
            /// DIVIZE
            if (typeId.value().equals("d1883")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1883.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d1880.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1851.a");
            }
            /// VEDOUCÍ
            if (typeId.value().equals("d1884")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1884.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d1880.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1852.a");
            }
            if (typeId.value().equals("d1884.b")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d1880.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1852.b");
            }
            /// PRACOVNÍ POZICE
            if (typeId.value().equals("d1885")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1885.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d1880.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1825.a");
            }
            /// ÚVAZEK
            if (typeId.value().equals("d1886")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d1886.m")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d1880.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1840.m");
            }

            /// LINKOVÁNÍ ZE MZDY DO MĚSÍČNÍ DOCHÁZKY PRACOVNÍKŮ
            /// OSOBNÍ ČÍSLO
            if (typeId.value().equals("d8981")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d8981.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d8980.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1881.a");
            }
            if (typeId.value().equals("d8981.e")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d8980.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1881.e");
            }
            /// ZAMĚSTNAVATEL
            if (typeId.value().equals("d8982")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d8982.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d8980.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1882.a");
            }
            /// DIVIZE
            if (typeId.value().equals("d8983")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d8983.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d8980.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1883.a");
            }
            /// VEDOUCÍ
            if (typeId.value().equals("d8984")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d8984.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d8980.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1884.a");
            }
            if (typeId.value().equals("d8984.b")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d8980.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1884.b");
            }
            /// PRACOVNÍ POZICE
            if (typeId.value().equals("d8985")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d8985.a")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d8980.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1885.a");
            }
            /// ÚVAZEK
            if (typeId.value().equals("d8986")) {
                parentOfAnyGenerated = true;
            }
            if (typeId.value().equals("d8986.m")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d8980.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d1886.m");
            }

        }

        LinkDef linkDef = getLinkDef(
                recordLinkType,
                ObjectUtil.elvis(formula, Formulas::cache),
                specificLinkFieldTypeId,
                linkedRecordFieldTypeId,
                StringUtil.notBlankTrimmedString(entity.getLinkedRecordEntryFieldSubfieldCode())
        );

        return new PrefabFieldType(
                typeId,
                typeId,
                ObjectUtil.firstNotNull(StringUtil.notBlankTrimmedString(entity.getName()), typeId.toString()),
                false,
                !entity.getNotRepeatable(),
                true,
                linkDef,
                FieldSource.common(),
                getTransferType(entity),
                linkedRecordFond,
                getExplicitDatatype(typeId, recordLinkType, pic, phraseGroupId),
                typeId.isRoot()
                        ? getFieldExportSetting(inAuthorityRecord, exportFieldNumber)
                        : getSubfieldExportSetting(inAuthorityRecord, exportFieldNumber, exportSubfieldCode, typeId),
                pic,
                getFieldValueGeneration(parentOfAnyGenerated, recordLinkType, fdefGeneration, typeId)
        );
    }

    private @NonNull FdefGeneration getFieldValueGeneration(boolean parentOfGenerated, FieldRecordLinkType recordLinkType, FdefGeneration fdefGeneration, FieldTypeId typeId) {
        // Ve standardních databázích se GENERATED_PHYSICAL nikde nenastavovalo takže pokud to je nastavené víme že jsme to tak chtěli a není potřeba kontrolovat.
        if (fdefGeneration.equals(FdefGeneration.GENERATED_PHYSICAL)) {
            return fdefGeneration;
        }

        if (parentOfGenerated) {
            return weakAssertThat(fdefGeneration, FdefGeneration.GENERATED_FIELD_PARENT, typeId);
        }
        return switch (recordLinkType) {
            case NO_LINK -> weakAssertThat(fdefGeneration, FdefGeneration.PHYSICAL, typeId);
            case THIS_FIELD_ENTRYFIELD_LINK -> weakAssertThat(fdefGeneration, FdefGeneration.PHYSICAL_LINK_GENERATED_VALUE, typeId);
            case LOCAL_RECORD, PARENT_VIRTUAL_GROUP_ENTRYFIELD_LINK, SPECIFIC_FIELD_LINK, FORMULA -> weakAssertThat(fdefGeneration, FdefGeneration.GENERATED, typeId);
        };
    }

    private FdefGeneration weakAssertThat(FdefGeneration configuredValue, FdefGeneration returnValue, FieldTypeId typeId) {
        if (!returnValue.equals(configuredValue)) {
            log.warn("{} wants {} but {} is configured!", typeId, returnValue, configuredValue);
        }
        return returnValue;
    }

    private @NonNull LinkDef getLinkDef(@NonNull FieldRecordLinkType recordLinkType,
                                        @Nullable Formula<?> formula,
                                        @Nullable FieldTypeId specificLinkFieldTypeId,
                                        @Nullable FieldTypeId linkedRecordFieldTypeId,
                                        @Nullable String linkedRecordEntryFieldSubfieldCode) {
        return switch (recordLinkType) {
            case NO_LINK -> new NoLinkDef();
            case FORMULA -> new FormulaDef<>(requireNonNull(formula, () -> "Record link type defined as %s but formula is null".formatted(recordLinkType)));
            case LOCAL_RECORD -> new SelfRecordDef(requireNonNull(linkedRecordFieldTypeId));
            case SPECIFIC_FIELD_LINK -> new SpecificFieldLinkDef(requireNonNull(specificLinkFieldTypeId), requireNonNull(linkedRecordFieldTypeId));
            case PARENT_VIRTUAL_GROUP_ENTRYFIELD_LINK ->
                    new ParentVirtualGroupToCustomEntrySubfieldLinkDef(requireNonNull(linkedRecordEntryFieldSubfieldCode, "Record link type defined as complex-relation but linked record entry field subfield code is null"));
            case THIS_FIELD_ENTRYFIELD_LINK -> new SimpleEntryCustomSubfieldLinkDef(requireNonNull(linkedRecordEntryFieldSubfieldCode, "Record link type defined as simple-relation but linked record entry field subfield code is null"));
        };
    }

    private FieldExportSetting getFieldExportSetting(boolean inAuthorityRecord, @NonNull String exportFieldNumber) {
        if (exportFieldNumber.equals(PrefabFieldType.TARGET_EXPORT_FIELD_NOT_TO_EXPORT)) {
            return FieldExportSetting.disabled();
        }
        return FieldExportSetting.toField(FieldTypeId.recordField(inAuthorityRecord, exportFieldNumber));
    }

    private FieldExportSetting getSubfieldExportSetting(boolean inAuthorityRecord, @NonNull String exportFieldNumber, @Nullable String exportSubfieldCode, @NonNull FieldTypeId subfieldTypeId) {
        String resolvedCode = ObjectUtil.firstNotNull(exportSubfieldCode, subfieldTypeId.getCode());
        if (exportFieldNumber.equals(PrefabFieldType.TARGET_EXPORT_FIELD_NOT_TO_EXPORT) || resolvedCode.equals(PrefabFieldType.TARGET_EXPORT_SUBFIELD_NOT_TO_EXPORT)) {
            return FieldExportSetting.disabled();
        }
        return FieldExportSetting.toSubfield(FieldTypeId.recordField(inAuthorityRecord, exportFieldNumber).sub(resolvedCode));
    }

    private TransferType getTransferType(FieldTypeEntity dto) {
        try {
            return transferTypeLoader.getById(dto.getTransferTypeId());
        } catch (EmptyResultDataAccessException | ItemNotFoundException e) {
            return defaultTransferTypeProvider.get();
        }
    }

    private @Nullable ScalarDatatype getExplicitDatatype(@NonNull FieldTypeId typeId, @NonNull FieldRecordLinkType recordLinkType, @NullableNotBlank String pic, @NullableNotBlank String phraseGroupId) {
        if (pic != null && phraseGroupId != null) {
            log.warn("Invalid fdef[aut] configuration for field {}: both pic ('{}') and fk_frazeskup ('{}') filled", typeId, pic, phraseGroupId);
        }

        if (recordLinkType != FieldRecordLinkType.NO_LINK) {
            if (pic != null) {
                log.warn("Invalid fdef[aut] configuration for field {}: pic filled ('{}') when auttyp is {} ({})", typeId, pic, recordLinkType.getId(), recordLinkType.name());
            }
            if (phraseGroupId != null) {
                log.warn("Invalid fdef[aut] configuration for field {}: fk_frazeskup filled ('{}') when auttyp is {} ({})", typeId, phraseGroupId, recordLinkType.getId(), recordLinkType.name());
            }
            return null;
        }

        if (pic != null) {
            if (pic.startsWith(DATATYPE_PREFIX_VAL)) {
                return Datatype.scalar(pic, AcceptableValueGroupItem.class, String.class);
            }
            return Datatype.scalar(pic);
        }
        if (phraseGroupId != null) {
            return Datatype.scalar(DATATYPE_PREFIX_PHRASE + phraseGroupId, String.class);
        }
        return TEXT;
    }

}
