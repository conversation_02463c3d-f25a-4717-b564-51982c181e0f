package cz.kpsys.portaro.record.detail.data;

import jakarta.persistence.Column;

import java.io.Serializable;
import java.util.Objects;

import static cz.kpsys.portaro.databasestructure.RecordDb.STYLY.*;

public class ViewableFieldId implements Serializable {

    @Column(name = ID_STYL)
    private Integer styleId;

    @Column(name = CIS_POL)
    private Integer fieldNumber;

    @Column(name = PODPOLE)
    private String subfieldCode;

    public ViewableFieldId() {
    }

    public ViewableFieldId(Integer styleId, Integer fieldNumber, String subfieldCode) {
        this.styleId = styleId;
        this.fieldNumber = fieldNumber;
        this.subfieldCode = subfieldCode;
    }

    public Integer getStyleId() {
        return styleId;
    }

    public Integer getFieldNumber() {
        return fieldNumber;
    }

    public String getSubfieldCode() {
        return subfieldCode;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ViewableFieldId that)) {
            return false;
        }
        return Objects.equals(getStyleId(), that.getStyleId()) &&
               Objects.equals(getFieldNumber(), that.getFieldNumber()) &&
               Objects.equals(getSubfieldCode(), that.getSubfieldCode());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getStyleId(), getFieldNumber(), getSubfieldCode());
    }
}
