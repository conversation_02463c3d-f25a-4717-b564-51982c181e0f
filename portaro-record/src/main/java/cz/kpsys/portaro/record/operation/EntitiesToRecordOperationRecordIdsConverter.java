package cz.kpsys.portaro.record.operation;

import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EntitiesToRecordOperationRecordIdsConverter implements Converter<List<RecordOperationEntity>, List<? extends UUID>> {

    @Override
    public List<UUID> convert(@NonNull List<RecordOperationEntity> entities) {
        return ListUtil.convert(entities, RecordOperationEntity::getRecordId);
    }

}
