package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;

import java.util.Collection;

public class FieldTypeNotDefinedException extends ItemNotFoundException {

    public FieldTypeNotDefinedException(FieldTypeListable<?> parent, FieldTypeId desiredFieldTypeId, Collection<FieldTypeId> availableSubfieldTypeIds) {
        super(FieldType.class.getSimpleName(),
                desiredFieldTypeId,
                "Field type %s is not defined in %s %s, check FDEF/FDEFAUT. Available field types are %s".formatted(desiredFieldTypeId, parent.getClass().getSimpleName(), parent, availableSubfieldTypeIds),
                Texts.ofNative("Field type %s is not defined in %s %s, check FDEF/FDEFAUT. Available field types are %s".formatted(desiredFieldTypeId, parent.getClass().getSimpleName(), parent, availableSubfieldTypeIds))
        );
    }

    public FieldTypeNotDefinedException(FieldTypeListable<?> parent, String desiredSubfieldCode, Collection<FieldTypeId> availableSubfieldTypeIds) {
        super(FieldType.class.getSimpleName(),
                "Field type with code %s".formatted(desiredSubfieldCode),
                "Field type with code %s is not defined in %s %s, check FDEF/FDEFAUT. Available field types are %s".formatted(desiredSubfieldCode, parent.getClass().getSimpleName(), parent, availableSubfieldTypeIds),
                Texts.ofNative("Field type with code %s is not defined in %s %s, check FDEF/FDEFAUT. Available field types are %s".formatted(desiredSubfieldCode, parent.getClass().getSimpleName(), parent, availableSubfieldTypeIds))
        );
    }

}
