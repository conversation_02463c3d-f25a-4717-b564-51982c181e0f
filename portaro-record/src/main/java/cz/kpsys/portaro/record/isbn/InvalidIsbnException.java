package cz.kpsys.portaro.record.isbn;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.commons.object.SeveritedException;

public class InvalidIsbnException extends RuntimeException implements UserFriendlyException, SeveritedException {

    public InvalidIsbnException(String isbn) {
        super("Invalid Isbn: %s".formatted(isbn));
    }

    @Override
    public Text getText() {
        return Texts.ofNative("Invalid ISBN");
    }

    @Override
    public int getSeverity() {
        return SEVERITY_WARNING;
    }

}
