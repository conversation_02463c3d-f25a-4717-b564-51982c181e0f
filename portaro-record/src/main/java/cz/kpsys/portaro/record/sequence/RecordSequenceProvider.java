package cz.kpsys.portaro.record.sequence;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.transaction.support.TransactionTemplate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordSequenceProvider implements ContextualProvider<String, Long> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull TransactionTemplate readwriteTransactiontemplate;

    @Override
    public Long getOn(String name) throws ItemNotFoundException {
        return readwriteTransactiontemplate.execute(_ -> {
            String sql = """
                    update record_sequence
                    set current_value = current_value + 1
                    where name = :name
                    RETURNING current_value
                    """;

            MapSqlParameterSource params = new MapSqlParameterSource("name", name);

            Long newValue = jdbcTemplate.queryForObject(sql, params, Long.class);

            if (newValue == null) {
                throw new IllegalArgumentException("RecordSequence with name=" + name + " does not exist");
            }

            return newValue;
        });
    }
}