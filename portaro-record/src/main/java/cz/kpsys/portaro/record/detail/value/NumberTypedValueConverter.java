package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class NumberTypedValueConverter implements TypedFieldValueConverter<NumberValueCommand, FieldPayload<NumberFieldValue>, NumberFieldValue> {

    @NonNull
    @Override
    public FieldPayload<NumberFieldValue> convert(@NonNull NumberValueCommand command, @NonNull FieldType<?> fieldType, @NonNull RecordFieldId fieldId) {
        return FieldPayload.of(NumberFieldValue.of(command.value(), Set.of(fieldId.recordIdFondPair())));
    }

    @Override
    public String toString() {
        return getClass().getSimpleName();
    }
}
