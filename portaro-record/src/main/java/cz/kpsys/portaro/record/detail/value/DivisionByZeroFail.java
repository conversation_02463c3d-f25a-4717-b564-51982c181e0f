package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import lombok.NonNull;

public record DivisionByZeroFail(

        @NonNull Text text

) implements ArithmeticFail {

    public static @NonNull FailedResult of() {
        return new DivisionByZeroFail(Texts.ofNative("Division by zero"));
    }

    @Override
    public String toString() {
        return "DivisionByZero";
    }

}
