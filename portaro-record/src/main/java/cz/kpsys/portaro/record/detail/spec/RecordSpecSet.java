package cz.kpsys.portaro.record.detail.spec;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public record RecordSpecSet(

        @NonNull Map<RecordMatcher, @NotEmpty RecordSpec> specsMap

) implements Iterable<RecordSpec> {

    public static final RecordSpecSet EMPTY_UNMODIFIABLE = new RecordSpecSet(Map.of());

    private static RecordSpecSet of(@NonNull Collection<? extends RecordSpec> specs) {
        Map<RecordMatcher, RecordSpec> map = specs.stream()
                .filter(recordSpec -> !recordSpec.isEmpty())
                .collect(Collectors.toMap(RecordSpec::recordMatcher, item -> item));
        return new RecordSpecSet(map);
    }

    public static RecordSpecSet ofCopyOf(@NonNull Collection<? extends RecordSpec> specs) {
        return of(specs);
    }

    public static RecordSpecSet ofCopyOf(@NonNull RecordSpecSet specs) {
        return new RecordSpecSet(new HashMap<>(specs.specsMap));
    }

    public static RecordSpecSet of(@NonNull RecordSpec spec) {
        if (spec.isEmpty()) {
            return ofEmptyUnmodifiable();
        }
        return of(Set.of(spec));
    }

    public static RecordSpecSet ofEmptyModifiable() {
        return new RecordSpecSet(new HashMap<>());
    }

    public static RecordSpecSet ofEmptyUnmodifiable() {
        return EMPTY_UNMODIFIABLE;
    }

    public Set<RecordSpec> specs() {
        return new HashSet<>(specsMap.values());
    }

    public void addAll(Collection<RecordSpec> specs) {
        for (RecordSpec spec : specs) {
            add(spec);
        }
    }

    public void addAll(RecordSpecSet specs) {
        for (RecordSpec spec : specs) {
            add(spec);
        }
    }

    public RecordSpecSet add(RecordSpec spec) {
        if (spec.isEmpty()) {
            return this;
        }
        specsMap.merge(spec.recordMatcher(), spec, RecordSpec::union);
        return this;
    }

    public boolean isEmpty() {
        return specsMap.isEmpty();
    }

    public int size() {
        return specsMap.size();
    }

    public Stream<RecordSpec> stream() {
        return specsMap.values().stream();
    }

    @Override
    public @NonNull Iterator<RecordSpec> iterator() {
        return specsMap.values().iterator();
    }

    public boolean covers(RecordSpecSet referentialSpecs) {
        return referentialSpecs.stream().allMatch(this::covers);
    }

    public boolean covers(RecordSpec referentialSpec) {
        if (isEmpty()) { // small optimalization
            return false;
        }

        // in past, we had "stream().anyMatch(localSpec -> localSpec.covers(referentialSpec));" implementation, but now we want to be quick so use a map, but is it really correct?
        RecordSpec specWithSameMatcher = specsMap.get(referentialSpec.recordMatcher());
        if (specWithSameMatcher == null) {
            return false;
        }

        return specWithSameMatcher.covers(referentialSpec); // hypoteticky bychom mohli pouzit rovnou specWithSameMatcher.fieldsSpec().covers(referentialSpec.fieldsSpec()) ??
    }

    public RecordSpecSet minus(RecordSpecSet referentialSpec) {
        if (isEmpty()) {
            return this;
        }
        if (referentialSpec.isEmpty()) {
            return this;
        }
        Map<RecordMatcher, RecordSpec> result = new HashMap<>();
        for (var entry : specsMap.entrySet()) {
            RecordSpec recordSpec = referentialSpec.specsMap().get(entry.getKey());
            if (recordSpec == null || !recordSpec.covers(entry.getValue())) {
                result.put(entry.getKey(), entry.getValue());
            }
        }
        return new RecordSpecSet(result);
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, RecordSpecSet.class, RecordSpecSet::specs);
    }

    @Override
    public int hashCode() {
        return specs().hashCode();
    }

    @Override
    public String toString() {
        return "RecordSpecs[%s]".formatted(isEmpty() ? "NONE" : stream().sorted(RecordSpec.COMPARATOR).map(Object::toString).collect(Collectors.joining(",")));
    }

}
