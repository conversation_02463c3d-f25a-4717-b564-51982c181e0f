package cz.kpsys.portaro.record.fond;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FondRelationsMapProvider {

    @NonNull Codebook<Fond, Integer> fondLoader;
    @NonNull Codebook<FondInclusion, UUID> fondInclusionLoader;

    public @NonNull Provider<Map<Integer, Set<Integer>>> relationsMapWhereKeyParentFondId() {
        return () -> {
            List<Fond> fondsRelations = fondLoader.getAll();

            Map<Integer, Set<Integer>> relationsMap = new HashMap<>();
            for (Fond fond : fondsRelations) {
                if (fond.getParentId() != null) {
                    relationsMap.computeIfAbsent(fond.getParentId(), _ -> new HashSet<>()).add(fond.getId());
                }
            }
            return relationsMap;
        };
    }

    public @NonNull Provider<Map<Integer, Set<Integer>>> relationsMapWhereKeyFondId() {
        return () -> {
            List<Fond> fondRelations = fondLoader.getAll();

            Map<Integer, Set<Integer>> relationsMap = new HashMap<>();
            for (Fond fond : fondRelations) {
                if (fond.getParentId() != null) {
                    relationsMap.computeIfAbsent(fond.getId(), _ -> new HashSet<>()).add(fond.getParentId());
                }
            }
            return relationsMap;
        };
    }

    public @NonNull Provider<Map<Integer, Set<Integer>>> relationsMapWithInclusionsWhereParentOrFondIdKey() {
        return () -> {
            List<FondInclusion> inclusions = fondInclusionLoader.getAll();

            Map<Integer, Set<Integer>> relationsMap = relationsMapWhereKeyParentFondId().get();

            for (FondInclusion relation : inclusions) {
                relationsMap.computeIfAbsent(relation.fondId(), _ -> new HashSet<>()).add(relation.includedFondId());
            }
            return relationsMap;
        };
    }

    public @NonNull Provider<Map<Integer, Set<Integer>>> relationsMapWithInclusionsWhereIncludedFondIdKey() {
        return () -> {
            List<FondInclusion> inclusions = fondInclusionLoader.getAll();

            Map<Integer, Set<Integer>> relationsMap = relationsMapWhereKeyFondId().get();

            for (FondInclusion relation : inclusions) {
                relationsMap.computeIfAbsent(relation.includedFondId(), _ -> new HashSet<>()).add(relation.fondId());
            }
            return relationsMap;
        };
    }
}
