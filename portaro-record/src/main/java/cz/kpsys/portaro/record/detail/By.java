package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.object.Contentable;
import cz.kpsys.portaro.commons.object.Identified;
import jakarta.validation.constraints.NotBlank;
import lombok.NonNull;

import java.util.Collection;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Predicate;

public class By {

    public static <E extends Identified<UUID>> Predicate<E> id(@NonNull UUID id) {
        return e -> e.getId().equals(id);
    }

    public static <E extends WithCode> FieldCodeFilter<E> code(@NonNull String code) {
        return new FieldCodeFilter<E>(code);
    }

    public static <E extends WithCode> FieldCodeFilter<E> anyCode(@NonNull Collection<String> codes) {
        return new FieldCodeFilter<E>(codes);
    }

    public static <E extends WithCode> FieldCodeFilter<E> anyCode(@NonNull String... codes) {
        return new FieldCodeFilter<E>(Set.of(codes));
    }

    public static <E extends WithRepetition> FieldRepetitionFilter<E> repetition(int repetition) {
        return new FieldRepetitionFilter<>(repetition);
    }

    public static <E extends WithFieldId> Predicate<E> fieldId(@NonNull FieldId fieldId) {
        return field -> field.getFieldId().equals(fieldId);
    }

    public static <E extends WithFieldId> Predicate<E> parentFieldId(@NonNull FieldId parentFieldId) {
        return field -> field.getFieldId().hasParent() && field.getFieldId().existingParent().equals(parentFieldId);
    }

    public static @NonNull Predicate<WithCode> autonomous() {
        return anyCode(FieldTypes.INDICATORS_FIELD_CODES).negate();
    }

    public static @NonNull <E extends FieldLike & WithFieldType> Predicate<E> siblingOfType(@NonNull FieldId fieldId, @NonNull FieldTypeId typeId) {
        return By.<E>typeId(typeId).and(By.siblingOf(fieldId));
    }

    public static @NonNull <E extends FieldLike & WithFieldType> Predicate<E> siblingOfCode(@NonNull FieldId fieldId, @NonNull @NotBlank String codeToSearchIn) {
        return By.<E>code(codeToSearchIn).and(By.siblingOf(fieldId));
    }

    public static <E extends FieldLike> @NonNull Predicate<E> siblingOf(@NonNull FieldId fieldId) {
        return By.parentFieldId(fieldId.existingParent());
    }

    public static <E extends WithCode & WithRepetition> Predicate<E> codeAndRepetition(@NonNull String code, int repetition) {
        FieldCodeFilter<E> codeFilter = code(code);
        return codeFilter.and(repetition(repetition));
    }

    public static <F extends WithFieldType> Predicate<F> typeId(@NonNull FieldTypeId typeId) {
        return new Predicate<F>() {
            @Override
            public boolean test(F field) {
                return field.getType().getFieldTypeId().equals(typeId);
            }

            @Override
            public String toString() {
                return "fieldTypeId=" + typeId;
            }
        };
    }

    public static <F extends WithFieldType> Predicate<F> type(@NonNull FieldType<?> type) {
        return field -> field.getType().equals(type);
    }

    public static <FIELD_TYPE extends FieldType<?>> Predicate<FIELD_TYPE> type(@NonNull FieldTypeId typeId) {
        return fieldType -> fieldType.getFieldTypeId().equals(typeId);
    }

    public static <E extends WithFieldType & WithRepetition> Predicate<E> typeAndRepetition(@NonNull FieldTypeId typeId, int repetition) {
        Predicate<E> typeIdFilter = By.typeId(typeId);
        return typeIdFilter.and(repetition(repetition));
    }

    public static <E extends Field<?>> Predicate<E> controlfield() {
        return new ValuedAndNonsubfieldedFieldFilter<E>();
    }

    public static <E extends Field<?>> Predicate<E> datafield() {
        return new SubfieldedFieldFilter<E>();
    }

    public static <E extends Contentable> Predicate<E> notEmpty() {
        return e -> !e.isEmpty();
    }

    public static <E extends Field<?>> Predicate<E> havingValue(String rawValue) {
        return e -> e.getRaw() != null && e.getRaw().equals(rawValue);
    }

    public static Predicate<Field<?>> havingSubfieldWithValue(@NonNull FieldTypeId typeId, @NonNull String rawValue) {
        return field -> {
            Predicate<Field<?>> byType = By.typeId(typeId);
            Predicate<Field<?>> byHavingValue = By.havingValue(rawValue);
            Optional<?> firstField = field.getFirstField(byType.and(byHavingValue));
            return firstField.isPresent();
        };
    }
}
