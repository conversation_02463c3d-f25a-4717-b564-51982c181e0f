package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.NonNull;
import lombok.With;

import java.util.Comparator;
import java.util.UUID;

@With
public record RecordIdFondPair(

        @NonNull
        RecordIdentifier id,

        @NonNull
        Fond fond

) implements IdentifiedRecord<RecordIdentifier>, Comparable<RecordIdFondPair> {

    public static final Comparator<RecordIdFondPair> COMPARATOR = Comparator.comparing(RecordIdFondPair::id);

    public static RecordIdFondPair of(RecordIdentifier id, Fond fond) {
        return new RecordIdFondPair(id, fond);
    }

    public static RecordIdFondPair of(UUID id, Fond fond) {
        return of(RecordIdentifier.of(id), fond);
    }

    public static RecordIdFondPair of(String id, Fond fond) {
        return of(UUID.fromString(id), fond);
    }

    @Override
    public boolean equals(Object obj) {
        return ObjectUtil.equalsIdentified(this, obj, RecordIdFondPair.class);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }

    public boolean typeSafeEquals(@NonNull RecordIdFondPair other) {
        return id.equals(other.id) && this.fond.equals(other.fond);
    }

    @Override
    public int compareTo(@NonNull RecordIdFondPair o) {
        return COMPARATOR.compare(this, o);
    }

    @Override
    public String toString() {
        return "RecordIdFondPair[" + id + ", " + fond + '}';
    }
}
