package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ReconsCache {

    @NonNull Map<UUID, RecordIdFondPair> map = new HashMap<>();

    public static ReconsCache empty() {
        return new ReconsCache();
    }

    public @NonNull RecordIdFondPair get(@NonNull RecordIdentifier recordIdentifier) {
        return Objects.requireNonNull(map.get(recordIdentifier.id()), () -> "No record-fond-pair for id %s in cache (%s items in cache: %s)".formatted(recordIdentifier, map.size(), map.keySet()));
    }

    public @NonNull RecordIdFondPair get(@NonNull UUID rid) {
        return Objects.requireNonNull(map.get(rid), () -> "No record-fond-pair for id %s in cache (%s items in cache: %s)".formatted(rid, map.size(), map.keySet()));
    }

    private void putAllFondIdPairs(@NonNull Collection<RecordIdFondPair> recordIdFondPairs) {
        recordIdFondPairs.forEach(pair -> {
            map.put(pair.id().id(), pair);

            // tohle budeme moct odstranit, az prestaneme pouzivat kat1_1, ve ktere mohou zaznamy odkazovat na originalni zaznam, ktery byl ale zmerovany do jineho. V record_field uz takove radky nejsou, vsechny vzdy odkazuji na aktivni zaznamy
            if (pair.id().masterId() != null) {
                map.put(pair.id().masterId(), pair);
            }
        });
    }

    public void loadAbsents(@NonNull List<UUID> recordIds, AllByIdsLoadable<RecordIdFondPair, UUID> recordFondPairsLoader) {
        List<UUID> absentIds = recordIds.stream()
                .filter(recordId -> !map.containsKey(recordId))
                .distinct()
                .toList();
        List<RecordIdFondPair> loadedAbsents = recordFondPairsLoader.getAllByIds(absentIds);
        logNotAllFondsLoaded(loadedAbsents, absentIds);
        putAllFondIdPairs(loadedAbsents);
    }

    private static void logNotAllFondsLoaded(List<RecordIdFondPair> loadedAbsents, List<UUID> absentIds) {
        if (loadedAbsents.size() != absentIds.size()) {
            List<UUID> missingAbsentIds = absentIds.stream().filter(missingId -> loadedAbsents.stream().noneMatch(recordIdFondPair -> recordIdFondPair.id().streamIds().anyMatch(missingId::equals))).toList();
            log.error("Not loaded all absent record id-fond pairs: {} is not in DB but is needed", missingAbsentIds);
        }
    }

}
