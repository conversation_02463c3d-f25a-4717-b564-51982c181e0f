package cz.kpsys.portaro.record.operation;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.BasicUser;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.Value;
import lombok.With;
import org.springframework.lang.Nullable;

import java.time.Instant;
import java.util.UUID;

@Value
@With
@EqualsAndHashCode(of = "id")
public class RecordOperation implements Identified<UUID> {

    @NonNull
    UUID id;

    @Nullable
    UUID eventId;

    @NonNull
    Record record;

    @NonNull
    Department department;

    @NonNull
    BasicUser user;

    @NonNull
    RecordOperationType recordOperationType;

    @NonNull
    Instant date;

}
