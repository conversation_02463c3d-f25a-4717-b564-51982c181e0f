package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.record.RecordIdentifier;
import cz.kpsys.portaro.record.detail.IndicatorType;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.RecordDb.KATX_1_COMBINED.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class LegacyRecordFieldEntityRowMapper implements RowMapper<LegacyRecordFieldEntity> {

    @Override
    public LegacyRecordFieldEntity mapRow(@NonNull ResultSet rs, int rowNum) throws SQLException {
        Integer id = DbUtils.getIntegerNotNull(rs, ID);
        UUID recordId = DbUtils.uuidNotNull(rs, RECORD_ID);
        String fieldNumberPrefix = DbUtils.getStringNotNull(rs, FIELD_NUMBER_PREFIX);
        Integer fieldNumber = DbUtils.getIntegerNotNull(rs, CIS_POL);
        Integer fieldIndex = DbUtils.getIntegerNotNull(rs, PORADI_POLE);
        String indicators = rs.getString(INDIK);
        String content = rs.getString(OBSAH);
        Instant lastUpdateDate = DbUtils.instantNotNull(rs, DATCAS);
        Integer lastUpdateUserId = DbUtils.getIntegerNotNull(rs, FK_UZIV);

        String ind1 = getInd(indicators, 0);
        String ind2 = getInd(indicators, 1);

        return new LegacyRecordFieldEntity(
                id,
                RecordIdentifier.of(recordId),
                fieldNumberPrefix,
                fieldNumber,
                fieldIndex,
                ind1,
                ind2,
                content,
                lastUpdateDate,
                lastUpdateUserId
        );
    }

    private static String getInd(String indicators, int index) {
        String valueInDb = String.valueOf(indicators.charAt(index));
        return IndicatorType.EMPTY_INDICATORS.contains(valueInDb) ? null : valueInDb;
    }

}
