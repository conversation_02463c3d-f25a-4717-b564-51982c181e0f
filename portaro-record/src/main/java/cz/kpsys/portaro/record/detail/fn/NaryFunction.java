package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.value.FieldValue;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public interface NaryFunction<RES extends FieldValue<?>, IN extends FieldValue<?>> extends ResolvableFunction<RES> {

    @NonNull
    @NotEmpty
    List<? extends Formula<IN>> operands();

    @NonNull
    @NotEmpty
    default Set<LookupDefinition> dependencies() {
        return operands().stream()
                .map(Formula::dependencies)
                .flatMap(Set::stream)
                .collect(Collectors.toUnmodifiableSet());
    }

    @Override
    @NonNull
    default Stream<? extends Formula<?>> subformulas() {
        return operands().stream()
                .flatMap(op -> Stream.concat(Stream.of(op), op.subformulas()));
    }
}
