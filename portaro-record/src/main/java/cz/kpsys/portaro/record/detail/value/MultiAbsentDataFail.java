package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import lombok.NonNull;

import java.util.List;

import static cz.kpsys.portaro.record.detail.spec.RecordSpecCollectors.mergingRecordSpecSets;

public record MultiAbsentDataFail(

        @NonNull
        Text text,

        @NonNull
        List<? extends AbsentDataFail> fails

) implements AbsentDataFail {

    public static @NonNull MultiAbsentDataFail of(@NonNull List<? extends AbsentDataFail> fails) {
        return new MultiAbsentDataFail(MultiText.ofTexts(fails).withCommaSpaceDelimiter(), fails);
    }

    @Override
    public @NonNull RecordSpecSet toSpec() {
        return fails.stream()
                .map(AbsentDataFail::toSpec)
                .collect(mergingRecordSpecSets());
    }

    @Override
    public String toString() {
        return "Fails[" + StringUtil.listToString(fails, ",") + ']';
    }
}
