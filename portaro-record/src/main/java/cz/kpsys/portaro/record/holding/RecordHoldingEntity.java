package cz.kpsys.portaro.record.holding;

import cz.kpsys.portaro.commons.object.Identified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.RecordDb.RECORD_HOLDING.*;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString
public class RecordHoldingEntity implements Identified<UUID> {

    @Id
    @Column(name = ID)
    @EqualsAndHashCode.Include
    @NonNull
    UUID id;

    @Column(name = RECORD_ID)
    @NonNull
    UUID recordId;

    @Column(name = DEPARTMENT_ID)
    @NonNull
    Integer departmentId;

    @Column(name = CREATION_EVENT_ID)
    @NonNull
    UUID creationEventId;

    @Column(name = DISCARDION_EVENT_ID)
    @Nullable
    UUID discardionEventId;

    @Column(name = DELETION_EVENT_ID)
    @Nullable
    UUID deletionEventId;

}
