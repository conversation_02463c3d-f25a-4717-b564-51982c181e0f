package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.RecordIdFondPair;
import lombok.experimental.NonFinal;
import org.jspecify.annotations.Nullable;

import java.util.Objects;

public record FieldPayload<VH extends ScalarFieldValue<?>>(

        @Nullable
        @NonFinal
        VH valueHolder,

        @Nullable
        @NonFinal
        RecordIdFondPair recordLink

) {

    public static <VH extends ScalarFieldValue<?>> FieldPayload<VH> of(@Nullable VH valueHolder) {
        return new FieldPayload<>(valueHolder, null);
    }

    public static <VH extends ScalarFieldValue<?>> FieldPayload<VH> of(@Nullable VH valueHolder, @Nullable RecordIdFondPair recordLink) {
        return new FieldPayload<>(valueHolder, recordLink);
    }

    public static <VH extends ScalarFieldValue<?>> FieldPayload<VH> ofMissingLink(@Nullable VH valueHolder) {
        return new FieldPayload<>(valueHolder, null);
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, FieldPayload.class, FieldPayload::recordLink, FieldPayload::valueHolder);
    }

    @Override
    public int hashCode() {
        int result = Objects.hashCode(recordLink());
        result = 31 * result + Objects.hashCode(valueHolder());
        return result;
    }
}
