package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class NumericAcceptableValueTypedValueConverter implements TypedFieldValueConverter<NumberValueCommand, FieldPayload<AcceptableValueFieldValue<LabeledIdentified<Integer>>>, AcceptableValueFieldValue<LabeledIdentified<Integer>>> {

    @NonNull ByIdLoadable<? extends LabeledIdentified<Integer>, Integer> acceptableValuesLoader;

    @NonNull
    @Override
    public FieldPayload<AcceptableValueFieldValue<LabeledIdentified<Integer>>> convert(@NonNull NumberValueCommand command, @NonNull FieldType<?> fieldType, @NonNull RecordFieldId fieldId) {
        var acceptableValue = getAcceptableValue(command.value());
        return FieldPayload.of(AcceptableValueFieldValue.ofGeneric(acceptableValue, Set.of(fieldId.recordIdFondPair())));
    }

    private LabeledIdentified<Integer> getAcceptableValue(BigDecimal acceptableValueId) {
        int acceptableValueIntId = acceptableValueId.intValueExact();
        return acceptableValuesLoader.getById(acceptableValueIntId);
    }

    @Override
    public String toString() {
        return getClass().getSimpleName();
    }
}
