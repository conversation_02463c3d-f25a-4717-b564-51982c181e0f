package cz.kpsys.portaro.record.prop;


import cz.kpsys.portaro.commons.object.Labeled;
import cz.kpsys.portaro.commons.object.LabeledRefOrName;
import cz.kpsys.portaro.commons.object.LabeledValue;
import cz.kpsys.portaro.prop.ObjectPropertyKey;

import java.util.List;

public class RecordPropertyKeys {

    public static final ObjectPropertyKey<String> NAME = ObjectPropertyKey.ofScalar("name", String.class);

    public static final ObjectPropertyKey<List<String>> ALTERNATIVE_NAMES = ObjectPropertyKey.ofList("alternativeNames", String.class);

    /**
     * Normalized ISBNs (without dashes)
     */
    public static final ObjectPropertyKey<List<LabeledValue<String>>> ISBNS = ObjectPropertyKey.ofListOfValues("isbns", String.class);

    /**
     * Normalized ISSNs (without dashes)
     */
    public static final ObjectPropertyKey<List<LabeledValue<String>>> ISSNS = ObjectPropertyKey.ofListOfValues("issns", String.class);

    public static final ObjectPropertyKey<List<LabeledRefOrName>> PUBLISHERS = ObjectPropertyKey.ofList("publishers", LabeledRefOrName.class);

    public static final ObjectPropertyKey<LabeledValue<Integer>> PUBLICATION_START_YEAR = ObjectPropertyKey.ofScalarValue("publicationStartYear", Integer.class);

    public static final ObjectPropertyKey<List<Labeled>> SUBTITLE = ObjectPropertyKey.ofList("subtitle", Labeled.class);

    public static final ObjectPropertyKey<Labeled> RESPONSIBILITY_STATEMENT = ObjectPropertyKey.ofScalar("responsibilityStatement", Labeled.class);

    public static final ObjectPropertyKey<List<LabeledRefOrName>> PRIMARY_AUTHORS = ObjectPropertyKey.ofList("primaryAuthors", LabeledRefOrName.class);

    public static final ObjectPropertyKey<List<LabeledRefOrName>> SECONDARY_AUTHORS = ObjectPropertyKey.ofList("secondaryAuthors", LabeledRefOrName.class);

    public static final ObjectPropertyKey<String> PRIMARY_CPK_ID = ObjectPropertyKey.ofScalar("primaryCpkId", String.class);
}
