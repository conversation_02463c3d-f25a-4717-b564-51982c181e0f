package cz.kpsys.portaro.record.evaluation;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.commons.object.SeveritedException;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AlreadyEvaluatedException extends RuntimeException implements SeveritedException, UserFriendlyException {

    @Getter
    int severity = SeveritedException.SEVERITY_WARNING;

    @Getter
    @NonNull
    Text text = Texts.ofMessageCoded("rating.YouAlreadyEvaluated");

    public AlreadyEvaluatedException() {
        super("User already evaluated record");
    }
}
