package cz.kpsys.portaro.record.merge;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DefaultRecordMerger implements RecordMerger {

    @NonNull RecordDetailMerger recordDetailMerger;
    @NonNull DocumentMerger documentMerger;
    @NonNull AuthorityMerger authorityMerger;
    @NonNull IdAndIdsLoadable<Record, UUID> detailedRecordLoader;
    @NonNull ContextualFunction<Record, Department, Boolean> departmentedRecordCanHaveExemplarsPredicate;
    @NonNull ContextualFunction<Record, Department, Boolean> subtreeDepartmentedRecordHasExemplarsPredicate;

    @Override
    public void merge(@NonNull RecordMergeCommand command) {
        Record targetRecord = detailedRecordLoader.getById(command.targetRecord().getId());
        List<Record> sourceRecords = detailedRecordLoader.getAllByIds(Record.getListOfUuids(command.sourceRecords()));
        Assert.notEmpty(sourceRecords, "Can not merge records: source records must not be empty");

        validateAllOfSameKind(targetRecord, sourceRecords);

        for (Record sourceRecord : sourceRecords) {
            mergeSingleRecord(command, targetRecord, sourceRecord, command.currentDepartment(), command.currentAuth());
        }
    }

    private void mergeSingleRecord(@NonNull RecordMergeCommand command, @NonNull Record targetRecord, @NonNull Record sourceRecord, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        boolean fromExternal = sourceRecord.isExternal();
        boolean toAuthority = targetRecord.getType().equals(Record.TYPE_AUTHORITY);
        boolean toDocument = targetRecord.getType().equals(Record.TYPE_DOCUMENT);

        if (!fromExternal && toAuthority) {
            // internal authorities are merging via AS
            authorityMerger.merge(ctx, currentAuth, List.of(sourceRecord), targetRecord);
            return;
        }

        validateExemplars(command);

        if (command.mergeDetail()) {
            recordDetailMerger.merge(targetRecord, sourceRecord, ctx, currentAuth);
        }

        if (!fromExternal && toDocument) {
            documentMerger.merge(targetRecord, List.of(sourceRecord), ctx, currentAuth);
        }
    }

    private void validateAllOfSameKind(Record targetRecord, List<Record> sourceRecords) {
        if (!sourceRecords.stream().allMatch(record -> targetRecord.getType().equals(record.getType()))) {
            throw new IllegalArgumentException("Cannot merge across documents and authorities");
        }
    }

    private void validateExemplars(RecordMergeCommand command) {
        if (hasAnySourceRecordsAnyExemplar(command) && !canTargetRecordHaveExemplars(command)) {
            throw new IllegalArgumentException("Source record contains exemplars, but target record cannot have exemplars.");
        }
    }

    private boolean hasAnySourceRecordsAnyExemplar(RecordMergeCommand command) {
        return command.sourceRecords().stream().anyMatch(record -> subtreeDepartmentedRecordHasExemplarsPredicate.getOn(record, command.currentDepartment()));
    }

    private boolean canTargetRecordHaveExemplars(RecordMergeCommand command) {
        return departmentedRecordCanHaveExemplarsPredicate.getOn(command.targetRecord(), command.currentDepartment());
    }
}
