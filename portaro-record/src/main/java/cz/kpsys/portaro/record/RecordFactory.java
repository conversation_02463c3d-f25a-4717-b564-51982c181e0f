package cz.kpsys.portaro.record;

import cz.kpsys.portaro.prop.ObjectProperties;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.detail.SimpleFieldContainer;
import cz.kpsys.portaro.record.edit.RecordValidator;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.prop.DetailRecordPropertiesGenerator;
import cz.kpsys.portaro.record.prop.RecordPropertiesGenerators;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.UUID;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class RecordFactory {

    @NonNull DetailRecordPropertiesGenerator detailRecordPropertiesGenerator;
    @NonNull RecordValidator recordValidator;

    public static Record testingMonography(@NonNull UUID id, @NonNull Integer kindedId) {
        return testingMonography(id, kindedId, id.toString());
    }

    public static Record testingMonography(@NonNull UUID id, @NonNull Integer kindedId, @NonNull String name) {
        return testingMonography(id, kindedId, name, new SimpleFieldContainer());
    }

    public static Record testingMonography(@NonNull UUID id, @NonNull Integer kindedId, @NonNull String name, @NonNull FieldContainer fieldContainer) {
        RecordIdFondPair recordIdFondPair = RecordIdFondPair.of(id, Fond.testingMonography());
        Record d = new Record(recordIdFondPair, kindedId, null, false, name, fieldContainer, RecordPropertiesGenerators.createForDocumentByExplicit(name, null, null, null, null));
        d.setStatus(RecordStatus.FINISHED_CATALOGING);
        return d;
    }

    public Record create(@NonNull RecordIdFondPair recordIdFondPair, int kindedId, @NonNull Fond fond, boolean external, @NonNull FieldContainer detail) {
        ObjectProperties properties = detailRecordPropertiesGenerator.generate(fond, detail);
        Record record;
        if (fond.isOfAuthority()) {
            record = Record.createAuthority(recordIdFondPair, kindedId,  null, external, null, detail, properties);
        } else {
            record = Record.createDocument(recordIdFondPair, kindedId, null, external, null, detail, properties);
        }

        var validation = recordValidator.validate(record.getFond(), record.getDetail());
        record.setValid(validation.isOk());

        return record;
    }

}
