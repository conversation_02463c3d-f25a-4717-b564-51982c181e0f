package cz.kpsys.portaro.record.detail.spec;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collector;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PACKAGE)
public class RecordSpecSetsMergingCollector implements Collector<RecordSpecSet, RecordSpecSet, RecordSpecSet> {

    @Override
    public Supplier<RecordSpecSet> supplier() {
        return RecordSpecSet::ofEmptyModifiable;
    }

    @Override
    public BiConsumer<RecordSpecSet, RecordSpecSet> accumulator() {
        return RecordSpecSet::addAll;
    }

    @Override
    public BinaryOperator<RecordSpecSet> combiner() {
        return (recordSpecs1, recordSpecs2) -> {
            recordSpecs1.addAll(recordSpecs2);
            return recordSpecs1;
        };
    }

    @Override
    public Function<RecordSpecSet, RecordSpecSet> finisher() {
        return Function.identity();
    }

    @Override
    public Set<Characteristics> characteristics() {
        return Set.of(Characteristics.UNORDERED, Characteristics.IDENTITY_FINISH);
    }
}
