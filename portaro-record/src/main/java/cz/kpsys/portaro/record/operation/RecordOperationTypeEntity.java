package cz.kpsys.portaro.record.operation;

import cz.kpsys.portaro.commons.object.BasicNamedIdentified;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import static cz.kpsys.portaro.databasestructure.RecordOperationDb.DEF_VYKONY.*;

@Entity
@Table(name = TABLE)
@AttributeOverrides({
        @AttributeOverride(name = "id", column = @Column(name = ID_VYKON)),
        @AttributeOverride(name = "name", column = @Column(name = POPIS))
})
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Getter
public class RecordOperationTypeEntity extends BasicNamedIdentified<Integer> {

    public RecordOperationTypeEntity(Integer id, String name) {
        super(id, name);
    }
}
