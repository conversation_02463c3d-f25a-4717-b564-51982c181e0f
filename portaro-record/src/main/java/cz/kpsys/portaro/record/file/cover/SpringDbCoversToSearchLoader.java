package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.commons.convert.StringToUuidConverter;
import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.RecordDb.RECORD.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SpringDbCoversToSearchLoader implements CoversToSearchLoader {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull AllByIdsLoadable<Record, UUID> detailedRecordLoader;
    @NonNull List<Integer> forbiddenRecordStatuses;
    @NonNull StringToUuidConverter stringToUuidConverter = StringToUuidConverter.INSTANCE;

    @Override
    public List<Record> getAll(String service, Range range) {
        SelectQuery sq = buildQuery(service, range);

        List<UUID> recordIds = ListUtil.convert(jdbcTemplate.queryForList(sq.getSql(), sq.getParamMap(), String.class), stringToUuidConverter);

        log.debug("Found {} records (their ids) by service {}.", recordIds.size(), service);

        List<Record> records = detailedRecordLoader.getAllByIds(recordIds);

        log.debug("Loaded {} records by {} ids.", records.size(), recordIds.size());

        return records;
    }


    private SelectQuery buildQuery(String service, Range range) {
        SelectQuery subsq = queryFactory.newSelectQuery();
        subsq.select(
                TC("r", ID),
                AS(TC("ono", UnfoundCovers.RECORD_ID), "ono_rec")
        );
        subsq.from(AS(TABLE, "r"));
        subsq.joins().addLeft(AS(UnfoundCovers.OPAC_NENALEZENE_OBALKY, "ono"), COLSEQ("r", ID, "ono", UnfoundCovers.RECORD_ID) + AND + TC("ono", UnfoundCovers.SLUZBA) + EQ + "'" + service + "'");

        subsq.where().and().isNull(TC("r", PRIMARY_FILE_ID));

        if (!forbiddenRecordStatuses.isEmpty()) {
            subsq.where().and().notIn(TC("r", RECORD_STATUS_ID), forbiddenRecordStatuses);
        }

        subsq.orderBy().addAsc(queryFactory.getDbSpecifics().getRandomForSorting());


        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(TC("subquery", ID));
        sq.fromSubquery(subsq.getRevealedParamsQuery(), "subquery");
        sq.where().isNull("ono_rec");
        sq.setRange(range);
        return sq;
    }

    public static class UnfoundCovers {
        public static final String OPAC_NENALEZENE_OBALKY = "opac_nenalezene_obalky";
        public static final String SLUZBA = "sluzba";
        public static final String DUVOD = "duvod";
        public static final String CAS_HLEDANI = "cas_hledani";
        public static final String RECORD_ID = "record_id";
    }
}
