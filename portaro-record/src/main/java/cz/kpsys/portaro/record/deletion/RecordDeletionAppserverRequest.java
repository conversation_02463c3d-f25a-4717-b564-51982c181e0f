package cz.kpsys.portaro.record.deletion;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.NonNull;

import java.util.Set;
import java.util.UUID;

@JacksonXmlRootElement(localName = "smaz_zazn")
public record RecordDeletionAppserverRequest(

        @JacksonXmlProperty(localName = "RECORD_ID")
        @NonNull
        UUID recordId,

        @JacksonXmlProperty(localName = "DEPARTMENT_ID")
        @JacksonXmlElementWrapper(useWrapping = false)
        @NonNull
        Set<Integer> holdingDepartmentIds,

        @JacksonXmlProperty(localName = "STATUS")
        @NonNull
        Integer statusId,

        @JacksonXmlProperty(localName = "ZRUSIT_MVS")
        boolean mvsDeletion,

        @JacksonXmlProperty(localName = "ZRUSIT_CASLIN")
        boolean caslinDeletion

) {}
