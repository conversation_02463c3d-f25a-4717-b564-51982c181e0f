package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.fond.InclusionLoader;
import cz.kpsys.portaro.record.sec.CurrentAuthFondsLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordEditationAvailableFondsResolver {

    @NonNull CurrentAuthFondsLoader currentAuthEditableFondsLoader;
    @NonNull InclusionLoader<Fond> enabledFondInheritanceLoader;

    public @NonNull List<Fond> getFondsByRootFond(@Nullable Fond rootFond, @Nullable Boolean isForAuthority, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        List<Fond> fonds = currentAuthEditableFondsLoader.getAllByAuth(currentAuth, ctx);

        fonds = new ArrayList<>(Fond.filterRecordable(fonds));

        if (isForAuthority != null) {
            List<Fond> subkindedFonds = isForAuthority ? Fond.filterAuthorityFonds(fonds) : Fond.filterDocumentFonds(fonds);
            fonds.retainAll(subkindedFonds);
        }

        if (rootFond != null) {
            List<Fond> rootFondSubTree = enabledFondInheritanceLoader.getThisAndIncludedAndChildren(rootFond);
            fonds.retainAll(rootFondSubTree);
        }

        return fonds;
    }
}
