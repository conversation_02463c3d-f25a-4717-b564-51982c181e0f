package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.commons.date.DatetimeRange;
import cz.kpsys.portaro.commons.object.Provider;
import lombok.NonNull;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collection;
import java.util.List;
import java.util.UUID;

public interface RecordDayIdLoader {

    List<UUID> load(@NonNull Instant from, @NonNull Instant exclusiveTo);

    List<UUID> load(@NonNull Collection<LocalDate> dates);

    default List<UUID> load(@NonNull LocalDate from, @NonNull LocalDate exclusiveTo, @NonNull Provider<ZoneId> timeZoneProvider) {
        var zone = timeZoneProvider.get();
        return load(from.atStartOfDay(zone).toInstant(), exclusiveTo.atStartOfDay(zone).toInstant());
    }

    default List<UUID> load(@NonNull DatetimeRange range) {
        return load(range.fromDate(), range.toDate());
    }

}
