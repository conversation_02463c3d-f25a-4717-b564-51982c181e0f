package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.LabeledValuesGroup;
import cz.kpsys.portaro.commons.object.repo.AllValuesProvidedCodebook;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.FallbackingCodebook;
import cz.kpsys.portaro.datatype.DatatypedAcceptableValuesRegistry;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.object.AcceptableValueGroupItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

import static cz.kpsys.portaro.CoreConstants.Datatype.DATATYPE_PREFIX_EDV;
import static cz.kpsys.portaro.CoreConstants.Datatype.DATATYPE_PREFIX_VAL;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldAcceptableValuesResolver {

    @NonNull DatatypedAcceptableValuesRegistry registry;
    @NonNull ByIdLoadable<LabeledValuesGroup<LabeledIdentified<String>, String>, Object> defValAcceptableValuesGroupLoader;

    public Optional<Codebook<? extends LabeledIdentified<?>, ?>> resolve(@NonNull ScalarDatatype scalarDatatype) {
        Optional<Codebook<? extends LabeledIdentified<?>, ?>> codebook = registry.getCodebook(scalarDatatype);
        if (codebook.isPresent()) {
            return codebook;
        }

        Optional<AllValuesProvider<? extends LabeledIdentified<?>>> provider = registry.getProvider(scalarDatatype);
        if (provider.isPresent()) {
            return Optional.of(AllValuesProvidedCodebook.ofCustomId(provider.get(), Identified::getId));
        }

        Optional<String> groupId = scalarDatatype.unprefixedName(DATATYPE_PREFIX_VAL).or(() -> scalarDatatype.unprefixedName(DATATYPE_PREFIX_EDV));
        if (groupId.isPresent()) {
            LabeledValuesGroup<LabeledIdentified<String>, String> group = defValAcceptableValuesGroupLoader.getById(groupId.get());
            FallbackingCodebook<LabeledIdentified<String>, String> groupWithUnknownValueFallback = FallbackingCodebook.notInFindById(group, id -> AcceptableValueGroupItem.createUnknown(id, groupId.get()));
            return Optional.of(groupWithUnknownValueFallback);
        }
        return Optional.empty();
    }

}
