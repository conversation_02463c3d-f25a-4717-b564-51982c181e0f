package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Stream;

public interface ViewableFieldContainer {

    @JsonIgnore
    @NonNull
    default Stream<? extends ViewableField> streamFields() {
        return getFields().stream();
    }

    @JsonIgnore
    default List<? extends ViewableField> getFields() {
        return streamFields().toList();
    }

    default List<? extends ViewableField> getFields(@NonNull Predicate<? super ViewableField> matcher) {
        return streamFields(matcher).toList();
    }

    default @NonNull Stream<? extends ViewableField> streamFields(@NonNull Predicate<? super ViewableField> matcher) {
        return streamFields().filter(matcher);
    }

    default Optional<? extends ViewableField> getFirstField(@NonNull Predicate<? super ViewableField> matcher) {
        return streamFields()
                .filter(matcher)
                .findFirst();
    }

    default Optional<? extends ViewableField> getFirstFieldByCode(@NonNull String code) {
        return getFirstField(By.code(code));
    }

    default Optional<? extends ViewableField> getFirstField(Predicate<? super ViewableField> matcher1, Predicate<? super ViewableField> matcher2) {
        return streamFields(matcher1)
                .flatMap(field -> field.streamFields(matcher2))
                .findFirst();
    }

    default List<? extends ViewableField> getFields(Predicate<? super ViewableField> matcher1, Predicate<? super ViewableField> matcher2) {
        return streamFields(matcher1)
                .flatMap(field -> field.streamFields(matcher2))
                .toList();
    }

}
