package cz.kpsys.portaro.record.document;

import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.record.Record;
import lombok.NonNull;

import java.util.List;
import java.util.UUID;

public interface DocumentLoader extends IdAndIdsLoadable<Record, UUID> {

    Record getDetailed(Record basic);

    /**
     * Nacte dokumenty bez detailu
     */
    List<Record> getAllByIds(@NonNull List<UUID> ids);

}
