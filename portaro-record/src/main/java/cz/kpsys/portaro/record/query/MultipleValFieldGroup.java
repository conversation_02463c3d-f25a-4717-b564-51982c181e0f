package cz.kpsys.portaro.record.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.detail.SimpleViewableFieldContainer;
import cz.kpsys.portaro.record.detail.ViewableField;
import cz.kpsys.portaro.record.detail.ViewableFieldContainer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class MultipleValFieldGroup extends ValFieldGroup {

    @NonNull List<SingleValFieldGroup> singles;

    public MultipleValFieldGroup(@NonNull ViewableFieldContainer originalDetail) {
        this(originalDetail, new ArrayList<>());
    }

    public MultipleValFieldGroup(@NonNull ViewableFieldContainer originalDetail, @NonNull List<ViewableField> fields) {
        super(originalDetail);
        this.singles = ListUtil.convertStrict(fields, singleField -> new SingleValFieldGroup(originalDetail, singleField));
    }

    public static MultipleValFieldGroup empty() {
        return new MultipleValFieldGroup(new SimpleViewableFieldContainer());
    }
    
    public static ValFieldGroup create(@NonNull ViewableFieldContainer originalDetail, List<? extends ValFieldGroup> fields) {
        MultipleValFieldGroup newGroup = new MultipleValFieldGroup(originalDetail);

        for (ValFieldGroup group : fields) {
            if (group instanceof SingleValFieldGroup) {
                newGroup.singles.add((SingleValFieldGroup) group);
            } else if (group instanceof MultipleValFieldGroup) {
                newGroup.singles.addAll(group.getAll());
            } else {
                throw new IllegalArgumentException("Unsupported ValFieldGroup type %s".formatted(group));
            }
        }

        return newGroup;
    }

    
    @Override
    public List<SingleValFieldGroup> getAll() {
        return singles;
    }
    

    public List<Integer> getRepetitions() {
        return ListUtil.convert(singles, SingleValFieldGroup::getRepetition);
    }

    @JsonIgnore
    public UUID getRecordId() {
        if (singles.isEmpty()) {
            return null;
        }
        if (singles.size() == 1) {
            return singles.getFirst().getRecordId();
        }
        return null;
    }
}
