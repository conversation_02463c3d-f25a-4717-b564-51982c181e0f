package cz.kpsys.portaro.record.detail.spec;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import cz.kpsys.portaro.commons.convert.StringToUuidConverter;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;

import java.util.UUID;

@JsonSerialize(using = RecordIdFieldTypeIdJsonSerializer.class)
public record RecordIdFieldTypeId(

        @NonNull
        UUID recordId,

        @NonNull
        FieldTypeId fieldTypeId

) {

    public static final String RECORD_ID_FIELD_TYPE_ID_DELIM = ":";

    public static RecordIdFieldTypeId of(@NonNull UUID recordId, @NonNull FieldTypeId fieldTypeId) {
        return new RecordIdFieldTypeId(recordId, fieldTypeId);
    }

    public static RecordIdFieldTypeId parse(@NonNull String value) {
        if (!value.contains(RECORD_ID_FIELD_TYPE_ID_DELIM)) {
            throw new IllegalArgumentException("Invalid format. Expected {recordId}:{fieldTypeId}, got: " + value);
        }
        String[] parts = value.split(RECORD_ID_FIELD_TYPE_ID_DELIM, 2);
        if (parts.length != 2) {
            throw new IllegalArgumentException("Invalid format. Expected {recordId}:{fieldTypeId}, got: " + value);
        }
        UUID recordId = StringToUuidConverter.fromString(parts[0]);
        FieldTypeId fieldTypeId = FieldTypeId.parse(parts[1]);
        return new RecordIdFieldTypeId(recordId, fieldTypeId);
    }

    public String value() {
        return recordId + RECORD_ID_FIELD_TYPE_ID_DELIM + fieldTypeId.value();
    }

    @Override
    public String toString() {
        return UuidGenerator.abbr6(recordId) + RECORD_ID_FIELD_TYPE_ID_DELIM + fieldTypeId;
    }

}
