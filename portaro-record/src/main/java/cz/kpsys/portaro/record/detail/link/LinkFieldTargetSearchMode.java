package cz.kpsys.portaro.record.detail.link;

public enum LinkFieldTargetSearchMode {

    /// Link is directly in this field (simple authority or `main` field of complex authority)
    SELF_LINK,
    /// Link is in parent virtual field (this is typically true for subfields of complex authorities)
    VIRTUAL_GROUP_FIELD_LINK,
    /// Field with link is explicitly defined
    SPECIFIC_FIELD_LINK,
    /// Link is current record
    SELF_RECORD

}
