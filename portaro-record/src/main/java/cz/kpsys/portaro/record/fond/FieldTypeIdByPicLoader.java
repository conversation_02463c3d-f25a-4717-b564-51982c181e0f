package cz.kpsys.portaro.record.fond;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldTypeIdByPicLoader {

    @NonNull FieldTypesByFondLoader fieldTypesByFondLoader;

    @Nullable
    public FieldTypeId loadFieldTypeIdByPic(Fond fond, @NonNull String pic) {
        List<EditableFieldType<?>> allFieldTypes = fieldTypesByFondLoader.getTopfieldTypesByFond(fond);

        return allFieldTypes.stream()
                .map(fieldType -> findByPicRecursive(fieldType, pic))
                .flatMap(Optional::stream)
                .findFirst()
                .orElse(null);
    }

    private Optional<FieldTypeId> findByPicRecursive(EditableFieldType<?> fieldType, String pic) {
        if (ObjectUtil.nullSafeEquals(fieldType.pic(), pic)) {
            return Optional.of(fieldType.getFieldTypeId());
        }

        for (EditableFieldType<?> subfieldType : fieldType.getSubfieldTypes()) {
            Optional<FieldTypeId> found = findByPicRecursive(subfieldType, pic);
            if (found.isPresent()) {
                return found;
            }
        }

        return Optional.empty();
    }
}
