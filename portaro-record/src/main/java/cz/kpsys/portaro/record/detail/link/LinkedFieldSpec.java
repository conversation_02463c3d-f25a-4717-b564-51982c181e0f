package cz.kpsys.portaro.record.detail.link;

import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;

public sealed interface LinkedFieldSpec permits CustomEntrySubfieldLinkedFieldSpec, NativeEntrySubfieldLinkedFieldSpec, SpecificLinkedFieldSpec {

    @NonNull
    static LinkedFieldSpec ofSpecific(@NonNull FieldTypeId fieldTypeId) {
        return new SpecificLinkedFieldSpec(fieldTypeId);
    }

    @NonNull
    static LinkedFieldSpec ofCustomEntrySubfield(@NonNull String entryCustomSubfieldCode) {
        return new CustomEntrySubfieldLinkedFieldSpec(entryCustomSubfieldCode);
    }

    @NonNull
    static LinkedFieldSpec ofNativeName() {
        return new NativeEntrySubfieldLinkedFieldSpec();
    }

    @NonNull
    static LinkedFieldSpec ofNativeEntrySubfield() {
        return new NativeEntrySubfieldLinkedFieldSpec();
    }

}
