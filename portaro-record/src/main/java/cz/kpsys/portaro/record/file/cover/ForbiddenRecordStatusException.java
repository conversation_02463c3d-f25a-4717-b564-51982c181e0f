package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.commons.object.SeveritedException;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ForbiddenRecordStatusException extends RuntimeException implements UserFriendlyException, SeveritedException {

    @NonNull Record record;

    public ForbiddenRecordStatusException(@NonNull Record record) {
        super("Record " + record + " has forbidden status " + record.getStatus());
        this.record = record;
    }

    @Override
    public Text getText() {
        return MultiText.ofTexts("{}: {}", Texts.ofNative("Záznam má zakázaný status"), record.getStatus().getText());
    }

    @Override
    public int getSeverity() {
        return SEVERITY_WARNING;
    }

}
