package cz.kpsys.portaro.record.fond;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.DefaultProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
@Getter
public enum FondType implements Identified<String> {

    STANDARD(Constants.STANDARD),
    AUTHORITY(Constants.AUTHORITY),
    DOCUMENT(Constants.DOCUMENT),
    ACQUISITION(Constants.ACQUISITION),
    ELOAN(Constants.ELOAN),
    ILL(Constants.ILL),
    SUPPLIER(Constants.SUPPLIER),
    USER_PERSON(Constants.USER_PERSON),
    DAY(Constants.DAY),
    THREAD(Constants.THREAD),
    COMPANY(Constants.COMPANY),
    PROPERTY(Constants.PROPERTY),
    PROPERTY_GROUP(Constants.PROPERTY_GROUP),
    DEPARTMENT(Constants.DEPARTMENT);

    public static final Codebook<FondType, String> CODEBOOK = new StaticCodebook<>(values());
    public static final Provider<@NonNull FondType> DEFAULT_PROVIDER = DefaultProvider.byId(FondType.CODEBOOK, FondType.STANDARD.getId());

    @NonNull String id;

    public boolean matches(@NonNull Fond fond) {
        return fond.getFondType() == this;
    }

    public static class Constants {
        public static final String SUPPLIER = "supplier";
        public static final String USER_PERSON = "user_person";
        public static final String ELOAN = "eloan";
        public static final String ILL = "ill";
        public static final String ACQUISITION = "acquisition";
        public static final String THREAD = "thread";
        public static final String DOCUMENT = "document";
        public static final String AUTHORITY = "authority";
        public static final String STANDARD = "standard";
        public static final String DAY = "day";
        public static final String DEPARTMENT = "department";
        public static final String COMPANY = "company";
        public static final String PROPERTY = "property";
        public static final String PROPERTY_GROUP = "property_group";
    }
}
