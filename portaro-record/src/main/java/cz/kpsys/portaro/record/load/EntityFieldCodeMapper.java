package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.FieldTypes;
import cz.kpsys.portaro.record.detail.spec.MultifieldId;
import lombok.NonNull;

public class EntityFieldCodeMapper {

    public static final String DB_SUBFIELD_TYPE_ID_SEPARATOR = ".";
    public static final String MAIN_DB_CODE = "$";
    public static final String IND_1_DB_CODE = "i1";
    public static final String IND_2_DB_CODE = "i2";

    public static String dbCodeToCode(@NonNull String dbSubfieldCode) {
        return switch (dbSubfieldCode) {
            case MAIN_DB_CODE -> FieldTypes.VIRTUAL_GROUP_FIELD_CODE;
            case IND_1_DB_CODE -> FieldTypes.IND_1_FIELD_CODE;
            case IND_2_DB_CODE -> FieldTypes.IND_2_FIELD_CODE;
            default -> dbSubfieldCode;
        };
    }

    public static String codeToDbCode(@NullableNotBlank String fieldCode) {
        return switch (fieldCode) {
            case FieldTypes.VIRTUAL_GROUP_FIELD_CODE -> MAIN_DB_CODE;
            case FieldTypes.IND_1_FIELD_CODE -> IND_1_DB_CODE;
            case FieldTypes.IND_2_FIELD_CODE -> IND_2_DB_CODE;
            default -> fieldCode;
        };
    }

    public static String fieldIdToDbFieldId(@NonNull FieldId fieldId) {
        return fieldId
                .remapCodes(EntityFieldCodeMapper::codeToDbCode)
                .value();
    }

    public static String fieldTypeIdToDbFieldTypeId(@NonNull FieldTypeId fieldTypeId) {
        return fieldTypeId
                .remapCodes(EntityFieldCodeMapper::codeToDbCode)
                .value();
    }

    public static String fieldGroupIdToDbFieldGroupId(@NonNull MultifieldId multifieldId) {
        if (multifieldId.hasParentFieldId()) {
            return fieldIdToDbFieldId(multifieldId.existingParentFieldId()) + DB_SUBFIELD_TYPE_ID_SEPARATOR + codeToDbCode(multifieldId.fieldTypeId().getCode());
        }
        return fieldTypeIdToDbFieldTypeId(multifieldId.fieldTypeId());
    }

}
