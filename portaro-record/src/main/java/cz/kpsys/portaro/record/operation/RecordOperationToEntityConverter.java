package cz.kpsys.portaro.record.operation;

import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

public class RecordOperationToEntityConverter implements Converter<RecordOperation, RecordOperationEntity> {

    @Override
    public RecordOperationEntity convert(@NonNull RecordOperation ro) {
        return new RecordOperationEntity(
                ro.getId(),
                ro.getRecord().getId(),
                ro.getEventId(),
                ro.getRecord().getFond().getId(),
                ro.getDepartment().getId(),
                ro.getUser().getId(),
                ro.getRecordOperationType().getId(),
                ro.getDate()
        );
    }

}
