package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import lombok.NonNull;

public interface FieldValueConverter {

    <PAYLOAD extends FieldPayload<VH>, VH extends ScalarFieldValue<?>> PAYLOAD convert(@NonNull FieldValueCommand command, @NonNull FieldType<?> fieldType, @NonNull RecordFieldId fieldId, @NonNull Class<PAYLOAD> desiredType);
}
