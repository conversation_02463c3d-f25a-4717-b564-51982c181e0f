package cz.kpsys.portaro.record.matcher;

import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.directory.AllIdsByDirectoryProvider;
import cz.kpsys.portaro.file.directory.Directory;
import cz.kpsys.portaro.file.directory.DirectoryChainLoader;
import cz.kpsys.portaro.matcher.AbstractMatcher;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.By;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.UUID;

/**
 * Filtr, ktery se diva na dane podpole zaznamu, zda sedi s danym stringem.
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class FileRecordSubfieldValueMatcher<E extends IdentifiedFile> extends AbstractMatcher<E> {

    @NonNull DirectoryChainLoader directoryChainLoader;
    @NonNull AllIdsByDirectoryProvider allRecordIdsByDirectoryProvider;
    @NonNull AllByIdsLoadable<Record, UUID> detailedRecordLoader;
    @NonNull FieldTypeId subfieldTypeId;
    @NonNull String matchValue;

    public FileRecordSubfieldValueMatcher(@NonNull DirectoryChainLoader directoryChainLoader,
                                          @NonNull AllIdsByDirectoryProvider allRecordIdsByDirectoryProvider,
                                          @NonNull AllByIdsLoadable<Record, UUID> detailedRecordLoader,
                                          @NonNull FieldTypeId subfieldTypeId,
                                          @NonNull String matchValue) {
        super(String.format("File record subfield %s must be %s", subfieldTypeId, matchValue));
        this.directoryChainLoader = directoryChainLoader;
        this.allRecordIdsByDirectoryProvider = allRecordIdsByDirectoryProvider;
        this.detailedRecordLoader = detailedRecordLoader;
        this.subfieldTypeId = subfieldTypeId;
        this.matchValue = matchValue;
    }

    public String getMatchValue() {
        return matchValue;
    }

    public FieldTypeId getSubfieldTypeId() {
        return subfieldTypeId;
    }

    public Object getValue() {
        return subfieldTypeId + "=" + matchValue;
    }

    @Override
    public boolean matches(E item) {
        List<? extends Directory> directoryChain = directoryChainLoader.getThisAndParentsChain(item.getDirectory());
        List<UUID> recordIdsOfThisDirChain = allRecordIdsByDirectoryProvider.getAllByDirectories(ListUtil.getListOfIds(directoryChain));
        List<Record> records = detailedRecordLoader.getAllByIds(recordIdsOfThisDirChain);
        return records.stream()
                .flatMap(record -> record.streamFields(By.typeId(subfieldTypeId.existingParent())))
                .flatMap(topfield -> topfield.streamFields(By.typeId(subfieldTypeId)))
                .anyMatch(subfield -> subfield.getRaw().equals(matchValue));
    }
    
}
