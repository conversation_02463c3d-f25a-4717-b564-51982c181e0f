package cz.kpsys.portaro.record.holding;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.NonNull;

import java.util.UUID;

@JacksonXmlRootElement(localName = "record_adoption")
public record RecordHoldingUpsertAppserverRequest(

        @JacksonXmlProperty(localName = "record_id")
        @NonNull
        UUID recordId,

        @JacksonXmlProperty(localName = "department_id")
        @NonNull
        Long departmentId,

        @JacksonXmlProperty(localName = "discarded")
        @NonNull
        Boolean discarded

) {}
