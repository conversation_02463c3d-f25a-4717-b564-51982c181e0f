package cz.kpsys.portaro.record;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.edit.RecordEditationEvent;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.util.List;

public record RecordSaveCommand(

    boolean creation,

    @NonNull
    Record record,

    @NonNull
    Department department,

    @NonNull
    Department ctx,

    @NonNull
    UserAuthentication currentAuth,

    @NonNull
    List<RecordEditationEvent> unsavedModifications,

    boolean machineEditation,

    @Nullable
    Fond sourceRecordAuthorityFond

) {

    public static RecordSaveCommand createStandard(@NonNull Record record,
                                                   @NonNull Department department,
                                                   @NonNull Department ctx,
                                                   @NonNull UserAuthentication currentAuth,
                                                   @NonNull List<RecordEditationEvent> unsavedModifications,
                                                   boolean machineEditation) {
        return new RecordSaveCommand(!DataUtils.isPersistedId(record.getKindedId()), record, department, ctx, currentAuth, unsavedModifications, machineEditation, null);
    }

    public static RecordSaveCommand createSourceDocument(@NonNull Record record,
                                                         @NonNull Department department,
                                                         @NonNull Department ctx,
                                                         @NonNull UserAuthentication currentAuth,
                                                         @NonNull List<RecordEditationEvent> unsavedModifications,
                                                         boolean machineEditation,
                                                         @NonNull Fond sourceRecordAuthorityFond) {
        Assert.state(sourceRecordAuthorityFond.isOfAuthority() && sourceRecordAuthorityFond.isForSourceDocument(), "Cannot create source document with sourceRecordAuthorityFond which is not 'forSourceDocument'");
        return new RecordSaveCommand(!DataUtils.isPersistedId(record.getKindedId()), record, department, ctx, currentAuth, unsavedModifications, machineEditation, sourceRecordAuthorityFond);
    }

    public boolean forSourceRecord() {
        return sourceRecordAuthorityFond != null;
    }
}
