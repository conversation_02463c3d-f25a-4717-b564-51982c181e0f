package cz.kpsys.portaro.record.document;

import cz.kpsys.portaro.commons.web.Link;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.view.DownloadLinkContextualResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CompositeDownloadLinkContextualResolver<CTX> implements DownloadLinkContextualResolver<CTX> {

    @NonNull List<DownloadLinkContextualResolver<CTX>> contextualResolvers;

    @Override
    public List<Link> resolveLinks(Record record, CTX ctx) {
        return contextualResolvers.stream()
                .flatMap(resolver -> resolver.resolveLinks(record, ctx).stream())
                .toList();
    }
}
