package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.spec.RecordDatableId;
import cz.kpsys.portaro.record.detail.value.AbsentDataFail;
import cz.kpsys.portaro.record.detail.value.FailedResult;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.detail.value.VectorFieldValue;
import lombok.NonNull;

import java.util.Optional;
import java.util.Set;

public interface ValuableFieldNode {

    @NonNull RecordIdFondPair record();

    @NonNull RecordDatableId id();

    @NonNull
    Optional<Formula<?>> formula();

    @NonNull
    Set<LookupDefinition> lookups();

    boolean hasRecordLink();

    void setGeneratedRecordLink(@NonNull RecordIdFondPair recordLink);


    boolean hasVectorValue();

    @NonNull
    VectorFieldValue<?, ?> existingVectorValue();

    void setGeneratedVectorValue(@NonNull VectorFieldValue<?, ?> value);


    boolean hasScalarValue();

    @NonNull
    ScalarFieldValue<?> existingScalarValue();

    void setGeneratedScalarValue(@NonNull ScalarFieldValue<?> value);


    boolean hasError();

    @NonNull FailedResult getExistingError();

    default boolean isNonAbsentingError() {
        return hasError() && !(getExistingError() instanceof AbsentDataFail);
    }

    void setGeneratedValueError(@NonNull FailedResult failedResult);

}
