package cz.kpsys.portaro.record.search.restriction;

import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.field.StaticSearchFields;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.RawRestriction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import cz.kpsys.portaro.search.restriction.modifier.RestrictionModifier;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import static cz.kpsys.portaro.record.RecordConstants.SearchParams.CNA;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AuthorityFieldsSearchParamsRestrictionModifier implements RestrictionModifier<MapBackedParams> {

    @NonNull ContextualFunction<@NonNull String, @NonNull Department, @NonNull String> authorityOnlyGlobalSearchQToRawQueryConverter;

    @Override
    public Conjunction<SearchField> modify(Conjunction<SearchField> authorityConjunction, MapBackedParams p, Department ctx) {
        authorityConjunction.addIfHas(p, CNA, val -> new Term<>(StaticSearchFields.CNA, new Eq(val)));

        if (p.hasNotNull(CoreSearchParams.Q) && !p.hasNotNull(CoreSearchParams.FIELD)) {
            String rawQuery = authorityOnlyGlobalSearchQToRawQueryConverter.getOn(p.get(CoreSearchParams.Q), ctx);
            authorityConjunction.add(new RawRestriction<>(rawQuery));
        }

        return authorityConjunction;
    }
}
