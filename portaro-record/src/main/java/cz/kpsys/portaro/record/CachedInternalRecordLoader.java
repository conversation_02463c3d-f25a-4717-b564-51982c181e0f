package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.cache.DynamicCache;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CachedInternalRecordLoader implements InternalRecordLoader {

    @NonNull DynamicCache<Record> cache;
    @NonNull InternalRecordLoader delegate;

    @Override
    public Record getById(@NonNull UUID id) throws ItemNotFoundException {
        Optional<Record> cached = cache.findById(id);
        Record record = cached.orElseGet(() -> delegate.getById(id));
        cache.add(id, record);
        return record;
    }

    @Override
    public List<Record> getDocumentsByKindedIds(@NonNull List<Integer> kindedIds) {
        return delegate.getDocumentsByKindedIds(kindedIds);
    }

    @Override
    public List<Record> getAuthoritiesByKindedIds(@NonNull List<Integer> kindedIds) {
        return delegate.getAuthoritiesByKindedIds(kindedIds);
    }

    @Override
    public List<Record> getAllByIds(@NonNull List<UUID> ids) {
        List<UUID> idsToLoad = new ArrayList<>(ids.size());
        List<Record> result = new ArrayList<>(ids.size());

        for (UUID id : ids) {
            Optional<Record> cached = cache.findById(id);
            if (cached.isPresent()) {
                result.add(cached.get());
            } else {
                idsToLoad.add(id);
            }
        }

        if (log.isInfoEnabled()) {
            log.info("In cache is {} records, will load {} records from DB.", result.size(), idsToLoad.size());
        }

        //zaznamy, ktere nemam v cachi nactu z db

        if (!idsToLoad.isEmpty()) {
            List<Record> databased = delegate.getAllByIds(idsToLoad);

            //prave nactene dokumenty hodime do cache
            databased.forEach(databasedRecord -> cache.add(databasedRecord.getId(), databasedRecord));

            //spojime cached a databased dokumenty
            result.addAll(databased);
        }
        return result;
    }
}
