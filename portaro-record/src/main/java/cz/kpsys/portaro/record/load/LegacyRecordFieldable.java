package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.commons.object.NonNullOrderedRecord;
import cz.kpsys.portaro.commons.object.ValuableRecord;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.RecordIdentifier;
import jakarta.validation.constraints.Min;
import lombok.NonNull;

import java.util.Collection;
import java.util.stream.Stream;

public interface LegacyRecordFieldable<V extends LegacyRecordFieldValue> extends ValuableRecord<@NonNull V>, NonNullOrderedRecord {

    static Stream<LegacyRecordFieldable<?>> nestedBrowse(Collection<? extends LegacyRecordFieldable<?>> primaryTopfields) {
        return ListUtil.nestedBrowseExt(primaryTopfields.stream(), topfield -> {
            if (topfield.value() instanceof LegacyRecordFieldSubfieldsValue subfieldsValue) {
                return subfieldsValue.subfields().stream();
            }
            return Stream.empty();
        });
    }

    @NonNull
    RecordIdentifier recordIdentifier();

    @Min(0)
    @NonNull
    Integer sameCodeIndex();

    @NonNull
    V value();

    @Min(0)
    @NonNull
    @Override
    default Integer order() {
        return sameCodeIndex();
    }

}
