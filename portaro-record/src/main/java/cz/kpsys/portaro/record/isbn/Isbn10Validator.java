package cz.kpsys.portaro.record.isbn;

import cz.kpsys.portaro.commons.util.StringUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Isbn10Validator implements IsbnValidator {

    public static final int WITH_CHECKSUM_LENGTH = 10;
    public static final int WITHOUT_CHECKSUM_LENGTH = WITH_CHECKSUM_LENGTH - 1;

    @Override
    public boolean isValid(@NonNull String isbn) {
        if (StringUtil.isNullOrEmpty(isbn)) {
            return false;
        }

        isbn = IsbnValidator.normalize(isbn);

        if (!isValidRegexp(isbn)) {
            return false;
        }

        int pocetZnaku = isbn.length();
        if (pocetZnaku != WITH_CHECKSUM_LENGTH) {
            return false;
        }

        String withoutChecksum = isbn.substring(0, WITHOUT_CHECKSUM_LENGTH);
        char computedChecksum = computeIsbn10CheckDigit(withoutChecksum);
        char actualChecksum = isbn.charAt(WITHOUT_CHECKSUM_LENGTH);
        return computedChecksum == actualChecksum;
    }

    static boolean isValidRegexp(String isbn) {
        return isbn.matches(IsbnValidator.REGEX_10_OR_13_WITHOUT_MINUSES);
    }

    public static char computeIsbn10CheckDigit(@NonNull String nineCharsIsbn) {
        if (nineCharsIsbn.length() != WITHOUT_CHECKSUM_LENGTH) {
            throw new IllegalStateException("Given value '%s' is not an isbn/issn 10 without check digit char".formatted(nineCharsIsbn));
        }

        int n = Integer.parseInt(nineCharsIsbn);
        int sum = 0;
        for (int i = 2; i <= WITHOUT_CHECKSUM_LENGTH + 1; i++) {
            int digit = n % 10; // rightmost digit
            sum = sum + i * digit;
            n = n / 10;
        }
        if (sum % 11 == 1) {
            return 'X';
        }
        if (sum % 11 == 0) {
            return '0';
        }
        return String.valueOf(11 - (sum % 11)).charAt(0);
    }
}
