package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;

@Slf4j
public class FieldSorter {

    /**
     * Reorders fields by STYLY
     */
    public void reorderFieldByCodeRule(Field<?> field, @NonNull FieldContainer parentContainer, List<? extends WithCode> definedFieldTypes) {
        List<String> sortRule = ListUtil.convert(definedFieldTypes, WithCode::getCode);
        FieldType<?> subfieldType = field.getType();
        Optional<Integer> subfieldTypeOrderOpt = getSubfieldTypeOrder(subfieldType, sortRule);
        if (subfieldTypeOrderOpt.isEmpty()) {
            return;
        }

        int thisSubfieldTypeOrder = subfieldTypeOrderOpt.orElseThrow(); //poradi typu tohoto podpole

        //najdeme podpole, ktere musi byt tesne pred hledanym a pokud existuje (existuje vzdy, kdyz neni hledane podpole jedinym podpolem v poli nebo hledane nema byt prvni), posunume hledane za nej
        Optional<Field<?>> justPreviousSubfieldOpt = parentContainer.streamFields()
                .filter(sf -> !sf.equals(field))
                .filter(sf -> {
                    //chceme jen ta podpole, jejichz typy maji poradi mensi nebo rovno aktualnimu typu
                    Optional<Integer> currentSubfieldTypeOrderOpt = getSubfieldTypeOrder(sf.getType(), sortRule);
                    //noinspection OptionalIsPresent
                    if (currentSubfieldTypeOrderOpt.isEmpty()) { //...a pokud nemame definici nektereho z existujicich podpoli ve fdefu, hazime ho na konec
                        return false;
                    }
                    return thisSubfieldTypeOrder >= currentSubfieldTypeOrderOpt.get();
                })
                .max((sf1, sf2) -> {
                    //seradime podpole podle typu podpoli a opakovani podpoli
                    int sf1TypeOrder = getSubfieldTypeOrder(sf1.getType(), sortRule).orElseThrow();
                    int sf2TypeOrder = getSubfieldTypeOrder(sf2.getType(), sortRule).orElseThrow();
                    int compareTypeOrders = Integer.compare(sf1TypeOrder, sf2TypeOrder);
                    if (compareTypeOrders != 0) {
                        return compareTypeOrders;
                    }
                    return Integer.compare(sf1.getRepetition(), sf2.getRepetition()); //jsou-li typy stejne, radime podle opakovani podpole
                });

        Supplier<Integer> desiredIndex = () -> justPreviousSubfieldOpt.map(justPreviousSubfield -> parentContainer.getFields().indexOf(justPreviousSubfield) + 1).orElse(0); //nejedna se vzdy presne o index - muze byt i cislo proste o 1 vyssi nez je index predchoziho podpole
        Supplier<Integer> currentIndex = () -> parentContainer.getFields().indexOf(field);
        while (!currentIndex.get().equals(desiredIndex.get())) {
            moveField(field, currentIndex.get() < desiredIndex.get(), parentContainer);
        }
    }


    private Optional<Integer> getSubfieldTypeOrder(WithCode subfieldType, List<String> sortRuleCodes) throws FieldTypeNotDefinedException {
        String code = subfieldType.getCode();
        int i = sortRuleCodes.indexOf(code);
        if (i == -1) {
            return Optional.empty();
        }
        return Optional.of(i);
    }


    private void moveField(Field<?> subfield, boolean down, FieldContainer fieldList) {
        List<Field<?>> allFields = fieldList.getFields();

        //najdeme index fieldu v allFields
        int movedTopfield1Idx = allFields.indexOf(subfield);
        if (movedTopfield1Idx == -1) { //dane podpole v poli neni, coz by ale nemelo nastat
            return;
        }

        //najdeme druhy field, se kterym se vymenime
        int movedTopfield2Idx = movedTopfield1Idx + (down ? 1 : -1);
        if (movedTopfield2Idx < 0 || movedTopfield2Idx > allFields.size() - 1) { //krajni podpole nemuzeme uz vice posunovat
            return;
        }

        //prohodime pole
        Field<?> movedTopfield2 = allFields.get(movedTopfield2Idx);
        fieldList.swapFields(subfield, movedTopfield2);
    }
}
