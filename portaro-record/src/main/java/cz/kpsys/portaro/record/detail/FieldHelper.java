package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.convert.StringToIntegerConverter;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.util.StringUtil;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

public class FieldHelper {

    public static String fieldCodeToMarc21Tag(@NonNull String prefixedCode) {
        Function<Integer, String> fn = fieldNumber -> FieldHelper.toNumber3Char(String.valueOf(fieldNumber));
        return fieldCodeTo(prefixedCode, fn)
                .orElseThrow(() -> new IllegalArgumentException("Invalid field code: %s".formatted(prefixedCode)));
    }

    public static Integer fieldCodeToNumber(@NonNull String prefixedCode) {
        return fieldCodeToNumberOpt(prefixedCode)
                .orElseThrow(() -> new IllegalArgumentException("Invalid field code: %s".formatted(prefixedCode)));
    }

    public static Optional<Integer> fieldCodeToNumberOpt(@NonNull String prefixedCode) {
        return fieldCodeTo(prefixedCode, Function.identity());
    }

    private static <E> Optional<E> fieldCodeTo(@NonNull String prefixedCode, Function<Integer, E> fn) {
        Optional<String> authorityCode = StringUtil.removePrefixOpt(prefixedCode, FieldTypeId.NEW_AUTHORITY_NUMBER_PREFIX);
        Optional<String> documentCode = StringUtil.removePrefixOpt(prefixedCode, FieldTypeId.NEW_DOCUMENT_NUMBER_PREFIX);
        Optional<String> restOfCode = authorityCode.or(() -> documentCode);
        if (restOfCode.isPresent()) {
            String code = restOfCode.get();
            if (StringToIntegerConverter.isValidInteger(code)) {
                Integer fieldNumber = StringToIntegerConverter.strict().convert(code);
                return Optional.of(fn.apply(fieldNumber));
            }
        }
        return Optional.empty();
    }

    public static String toNumber3Char(@NonNull String number) {
        return StringUtil.prependToSize(number, '0', 3);
    }

    public static String fromNumber3Char(@NonNull String number3Char) {
        Integer number = StringToIntegerConverter.strict().convert(number3Char);
        return String.valueOf(number);
    }

    /**
     * https://support.kpsys.cz/issues/22178
     * Pote, co se vsechna pouziti query("{cislo}") preklopi na query("{a|d}{cislo}"), muzeme vyhodit (nebo jeste treba pak jeste chvili i logovat)
     */
    public static String convertLegacyCodeToNewPrefixedCode(@NonNull String code, @Nullable Provider<Boolean> recordIsAuthorityResolver) {
        if (code.startsWith(FieldTypeId.NEW_DOCUMENT_NUMBER_PREFIX) || code.startsWith(FieldTypeId.NEW_AUTHORITY_NUMBER_PREFIX) || !StringToIntegerConverter.isValidInteger(code)) {
            return code;
        }
        if (recordIsAuthorityResolver != null && recordIsAuthorityResolver.get()) {
            return FieldTypeId.NEW_AUTHORITY_NUMBER_PREFIX + code;
        }
        return FieldTypeId.NEW_DOCUMENT_NUMBER_PREFIX + code;
    }

    public static boolean anyFieldContainsSameValue(List<Field<?>> fields, Field<?> referenceField) {
        return fields
                .stream()
                .anyMatch(field -> field.hasSameValue(referenceField));
    }
}
