package cz.kpsys.portaro.record.evaluation;

import cz.kpsys.portaro.database.DbUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

import static cz.kpsys.portaro.record.evaluation.SpringDbRatingLoaderAndSaver.*;

public class DocumentRatingRowMapper implements RowMapper<DocumentRating> {

    @Override
    public DocumentRating mapRow(ResultSet rs, int rowNum) throws SQLException {
        if (rs.getObject(ID_OPAC_HODNOCENI) == null) {
            return null;
        }
        return new DocumentRating(
                rs.getInt(ID_OPAC_HODNOCENI),
                DbUtils.uuidNotNull(rs, RECORD_ID),
                rs.getInt(POCET),
                rs.getDouble(HODNOTA),
                DbUtils.instantNotNull(rs, NAPOSLEDY));
    }

}
