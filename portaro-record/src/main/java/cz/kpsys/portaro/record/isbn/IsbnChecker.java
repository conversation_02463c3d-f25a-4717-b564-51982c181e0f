package cz.kpsys.portaro.record.isbn;

import lombok.NonNull;

public class IsbnChecker {

    private static final IsbnValidator ISBN13 = new Isbn13Validator();
    private static final IsbnValidator ISBN10 = new Isbn10Validator();
    private static final IsbnValidator ISSN8 = new Issn8Validator();
    private static final IsbnValidator ISSN13 = new Issn13Validator();

    public static void throwIfInvalid(@NonNull String source) throws InvalidIsbnException {
        if (!isValidIsbn(source)) {
            throw new InvalidIsbnException(source);
        }
    }

    public static boolean isValidIsbn(String isbn) {
        return isValidIsbn10(isbn) || isValidIsbn13(isbn);
    }

    public static boolean isValidIsbn10(String isbn) {
        return ISBN10.isValid(isbn);
    }

    public static boolean isValidIsbn13(String isbn) {
        return ISBN13.isValid(isbn);
    }

    public static boolean isValidIssn(String issn) {
        return isValidIssn8(issn) || isValidIssn13(issn);
    }

    public static boolean isValidIssn8(String issn) {
        return ISSN8.isValid(issn);
    }

    public static boolean isValidIssn13(String issn) {
        return ISSN13.isValid(issn);
    }

}
