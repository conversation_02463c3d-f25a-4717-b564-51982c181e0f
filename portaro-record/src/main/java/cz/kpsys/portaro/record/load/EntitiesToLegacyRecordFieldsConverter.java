package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.commons.convert.ConversionException;
import cz.kpsys.portaro.commons.convert.StringToUuidConverter;
import cz.kpsys.portaro.commons.util.RegExpUtils;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.record.RecordIdentifier;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.FieldTypes;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EntitiesToLegacyRecordFieldsConverter implements Function<List<LegacyRecordFieldEntity>, List<? extends LegacyRecordTopfield<?>>> {

    public static final String LEADER_FIELD_CODE_IN_DATABASE = "0";

    @Override
    public List<LegacyRecordTopfield<?>> apply(@NonNull List<LegacyRecordFieldEntity> entities) {
        Map<String, Integer> sameCodeIndexes = new HashMap<>(entities.size());
        return entities.stream()
                .sorted(Comparator.comparing(LegacyRecordFieldEntity::fieldNumberPrefix).thenComparing(LegacyRecordFieldEntity::fieldNumber).thenComparing(LegacyRecordFieldEntity::index))
                .map(entity -> mapTopfield(entity, sameCodeIndexes))
                .collect(Collectors.toList());
    }

    private @NonNull LegacyRecordTopfield<? extends LegacyRecordFieldValue> mapTopfield(LegacyRecordFieldEntity entity, Map<String, Integer> sameCodeIndexes) {
        try {
            String topfieldCode = createTopfieldCode(entity);
            Integer sameCodeIndex = sameCodeIndexes.compute(entity.recordIdentifier().id() + "-" + topfieldCode, (_, v) -> v == null ? FieldId.FIRST_FIELD_REPETITION : v + 1);

            return new LegacyRecordTopfield<>(
                    entity.id(),
                    entity.recordIdentifier(),
                    topfieldCode,
                    sameCodeIndex,
                    entity.ind1(),
                    entity.ind2(),
                    getValue(entity.recordIdentifier(), entity.content()),
                    entity.lastUpdateDate(),
                    entity.lastUpdateUserId()
            );
        } catch (Exception e) {
            throw new ConversionException("Cannot map topfield to prefab field (record %s, field %s, content %s): %s".formatted(entity.recordIdentifier().id(), entity.fieldNumber(), entity.content(), e.getMessage()), e);
        }
    }

    private static @NonNull String createTopfieldCode(LegacyRecordFieldEntity entity) {
        String code = String.valueOf(entity.fieldNumber());
        if (code.equals(LEADER_FIELD_CODE_IN_DATABASE)) {
            return entity.fieldNumberPrefix().equals(FieldTypeId.NEW_DOCUMENT_NUMBER_PREFIX)
                    ? FieldTypes.DOCUMENT_LEADER_FIELD_CODE
                    : FieldTypes.AUTHORITY_LEADER_FIELD_CODE;
        }
        return entity.fieldNumberPrefix() + code;
    }

    private @NonNull LegacyRecordFieldValue getValue(@NonNull RecordIdentifier recordIdentifier, @NonNull @NotBlank String content) {
        if (!content.startsWith(LegacyRecordFieldEntity.SUBFIELDS_DELIMITER_PREFIX)) {
            if (content.startsWith(LegacyRecordFieldEntity.SIMPLE_AUTHORITY_LINK_PREFIX)) {
                String value = StringUtil.removePrefix(content, LegacyRecordFieldEntity.SIMPLE_AUTHORITY_LINK_PREFIX);
                return mapRecordValue(value);
            }
            if (content.startsWith(LegacyRecordFieldEntity.COMPLEX_AUTHORITY_LINK_PREFIX)) {
                String value = StringUtil.removePrefix(content, LegacyRecordFieldEntity.COMPLEX_AUTHORITY_LINK_PREFIX);
                return mapRecordValue(value);
            }
            return mapSimpleValue(content);
        }

        String[] splitsWithFirstEmptyString = content.split(RegExpUtils.escape(LegacyRecordFieldEntity.SUBFIELDS_DELIMITER_PREFIX));
        List<String> splits = Arrays.stream(splitsWithFirstEmptyString).skip(1).toList(); // first item in split will be ""
        List<LegacyRecordSubfield<? extends LegacyRecordFieldValue>> subfields = new ArrayList<>();
        Map<String, Integer> subfieldOrderMap = new HashMap<>();
        for (String s : splits) { // s = "aAbc :", "bDef,", "cGhi"
            String code = s.substring(0, 1); // code = "a"
            String contentWithoutCode = s.substring(1); // contentWithoutCode = "Abc :"
            Integer sameCodeIndex = subfieldOrderMap.compute(code, (k, v) -> (v == null) ? 0 : v + 1);
            subfields.add(new LegacyRecordSubfield<>(recordIdentifier, code, sameCodeIndex, getValue(recordIdentifier, contentWithoutCode)));
        }
        return new LegacyRecordFieldSubfieldsValue(subfields);
    }

    private @NonNull LegacyRecordFieldRecordValue mapRecordValue(String value) {
        if (!isRecordUuidValue(value)) {
            throw new IllegalStateException("Old numeric record id is not permitted in fdef[aut]!");
        }
        String recordIdString = value.substring(0, UuidGenerator.UUID_LENGTH);
        String suffix = StringUtil.notEmptyString(value.substring(UuidGenerator.UUID_LENGTH));
        return new LegacyRecordFieldRecordValue(UUID.fromString(recordIdString), suffix);
    }

    private @NonNull LegacyRecordFieldSimpleValue mapSimpleValue(@NonNull String content) {
        return new LegacyRecordFieldSimpleValue(content);
    }

    private static boolean isRecordUuidValue(String value) {
        if (value.length() < UuidGenerator.UUID_LENGTH) {
            return false;
        }
        String maybeUuid = value.substring(0, UuidGenerator.UUID_LENGTH);
        return StringToUuidConverter.isUuid(maybeUuid);
    }

}
