package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class RecordLockedContextualPredicate implements ContextualFunction<Record, Department, @NonNull Boolean> {

    @NonNull RecordStatusResolver recordStatusResolver;

    @Override
    public @NonNull Boolean getOn(Record target, Department ctx) {
        return recordStatusResolver.isLockedState(target.getStatus(), ctx);
    }
}
