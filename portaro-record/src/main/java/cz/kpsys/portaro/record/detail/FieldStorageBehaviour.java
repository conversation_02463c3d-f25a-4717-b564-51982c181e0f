package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.record.RecordIdFondPair;
import lombok.NonNull;
import lombok.With;

import java.util.Optional;

/// Definition of field behaviour when loading or saving.
///
/// @param storeRecordField      when a row should be written to record_field
/// @param storeRecordFieldValue when should value be written to record_field
/// @param cacheValidity         how CACHE_VALID flag should be set
/// @param selfLink              whether targetRecordId should be set to itself (will be eligible for invalidation and removal when cache_valid=true)
/// @param originStrategy        how origin_id should behave
/// @param storeKatX_1           if a row should always be written to KatX_1
/// @param wholeFieldGenerated   indicates that data are completely generated (not linked or stored elsewhere, like BOUND)
///
/// WARNING! DO NOT REORDER FIELDS! OR CHECK DEFINITIONS AND REORDER THEM ALSO!
@With
public record FieldStorageBehaviour(

        @NonNull RecordFieldStorage storeRecordField,
        @NonNull RecordFieldValueStorage storeRecordFieldValue,
        @NonNull CacheValidity cacheValidity,
        boolean selfLink,
        @NonNull OriginStrategy originStrategy,
        boolean storeKatX_1,
        boolean wholeFieldGenerated

) {

    public enum RecordFieldStorage {

        /// Always store as row in record_field.
        ALWAYS,

        /// Never store row in record_field.
        NEVER,

        /// Store row only when caching is enabled.
        ONLY_CACHED;

        public boolean shouldStore(boolean isFieldCacheEnabled) {
            return switch (this) {
                case ALWAYS -> true;
                case NEVER -> false;
                case ONLY_CACHED -> isFieldCacheEnabled;
            };
        }
    }

    public enum RecordFieldValueStorage {

        /// Always store value in record_field.
        VALUE_ALWAYS,

        /// Never store value in record_field.
        VALUE_NEVER,

        /// Store value only when caching is enabled.
        VALUE_ONLY_CACHED
    }

    public enum CacheValidity {

        /// Always set cache_valid flag to `true`.
        ALWAYS_VALID,

        /// Set cache_valid flag to `true` only when caching is enabled.
        IF_CACHE_ENABLED,

        /// Always set cache_valid flag to `false`.
        NEVER_VALID;

        public boolean resolveValidity(boolean isFieldCacheEnabled) {
            return switch(this) {
                case ALWAYS_VALID -> true;
                case IF_CACHE_ENABLED -> isFieldCacheEnabled;
                case NEVER_VALID -> false;
            };
        }
    }

    public enum OriginStrategy {

        /// Origin should be always set to the containing record (self).
        ALWAYS_SELF_ORIGIN,

        /// If there is exactly one origin, keep it. If there is more than one, set origin to containing record (self).
        SELF_ORIGIN_FALLBACK_WHEN_MANY;

        /// if the field does not have a value, returns empty
        /// if the field has a value, returns the origin of the value
        /// if the field has multiple values, returns self-record
        public @NonNull Optional<RecordIdFondPair> resolveMaxOneOrigin(@NonNull Field<?> field) {
            if (!field.hasValueHolder()) {
                return Optional.empty();
            }
            return switch (this) {
                case ALWAYS_SELF_ORIGIN -> Optional.of(field.getRecordIdFondPair());
                case SELF_ORIGIN_FALLBACK_WHEN_MANY -> {
                    var valueHolder = field.getExistingValueHolder();
                    if (valueHolder.origins().size() > 1) {
                        yield Optional.of(field.getRecordIdFondPair());
                    }
                    yield valueHolder.origins().stream().findFirst();
                }
            };
        }
    }

}
