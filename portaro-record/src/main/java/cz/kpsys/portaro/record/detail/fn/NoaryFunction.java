package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.value.FieldValue;
import lombok.NonNull;

import java.util.Set;
import java.util.stream.Stream;

public interface NoaryFunction<RES extends FieldValue<?>> extends ResolvableFunction<RES> {

    @NonNull
    default Set<LookupDefinition> dependencies() {
        return Set.of();
    }

    @NonNull
    default Stream<? extends Formula<?>> subformulas() {
        return Stream.of();
    }
}
