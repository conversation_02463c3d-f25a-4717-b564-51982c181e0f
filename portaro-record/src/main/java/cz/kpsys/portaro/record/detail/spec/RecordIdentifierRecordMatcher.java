package cz.kpsys.portaro.record.detail.spec;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.Field;
import lombok.NonNull;

public record RecordIdentifierRecordMatcher(

        @NonNull
        RecordIdFondPair recordIdFondPair

) implements RecordMatcher {

    public static RecordIdentifierRecordMatcher of(@NonNull RecordIdFondPair recordIdFondPair) {
        return new RecordIdentifierRecordMatcher(recordIdFondPair);
    }

    @Override
    public boolean matches(@NonNull Field<?> field) {
        return field.getRecordIdFondPair().equals(recordIdFondPair);
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, RecordIdentifierRecordMatcher.class, RecordIdentifierRecordMatcher::recordIdFondPair);
    }

    @Override
    public int hashCode() {
        return recordIdFondPair.hashCode();
    }

    @Override
    public String toString() {
        return recordIdFondPair.id().abbr();
    }

}
