package cz.kpsys.portaro.record.edit.field008;

import lombok.NonNull;

import java.io.Serializable;
import java.util.List;


public record Field008Definitions(
        @NonNull List<Field008CodeEntity> codes,
        @NonNull List<Field008LabelEntity> labels,
        @NonNull List<Field008DocumentTypeEntity> documentTypes,
        @NonNull Field008PositionWithDictionaryValue placeOfPublication,
        @NonNull Field008PositionWithDictionaryValue language,
        @NonNull Field008Position publicationStatus,
        @NonNull Field008Position modifiedRecord,
        @NonNull Field008Position catalogingSource

) implements Serializable {}
