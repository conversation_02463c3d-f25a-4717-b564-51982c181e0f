package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.spec.RecordFieldTypeId;
import lombok.NonNull;

import java.util.Map;
import java.util.stream.Collectors;

public record EmptyCoalesceFail(

        @NonNull Text text

) implements NonAbsentDataFail {

    public static @NonNull EmptyCoalesceFail of(RecordFieldTypeId srcRecordFieldType, Map<Formula<?>, AbsentDataFail> failedOperandResults) {
        return new EmptyCoalesceFail(Texts.ofNative("No values of coalesce " + generateReport(failedOperandResults) + " (src " + srcRecordFieldType + ")"));
    }

    private static String generateReport(Map<Formula<?>, AbsentDataFail> failedOperandResults) {
        return failedOperandResults.entrySet().stream()
                .map(entry -> entry.getKey() + " = " + entry.getValue())
                .collect(Collectors.joining(", "));
    }

    @Override
    public String toString() {
        return "EmptyCoalesce";
    }

}
