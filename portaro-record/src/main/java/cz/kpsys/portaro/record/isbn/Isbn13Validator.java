package cz.kpsys.portaro.record.isbn;

import cz.kpsys.portaro.commons.util.StringUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Isbn13Validator implements IsbnValidator {

    public static final int WITH_CHECKSUM_LENGTH = 13;
    public static final int WITHOUT_CHECKSUM_LENGTH = WITH_CHECKSUM_LENGTH - 1;

    @Override
    public boolean isValid(@NonNull String isbn) {
        if (StringUtil.isNullOrEmpty(isbn)) {
            return false;
        }

        isbn = IsbnValidator.normalize(isbn);

        if (!isValidRegexp(isbn)) {
            return false;
        }

        int pocetZnaku = isbn.length();
        if (pocetZnaku != WITH_CHECKSUM_LENGTH) {
            return false;
        }

        String withoutChecksum = isbn.substring(0, WITHOUT_CHECKSUM_LENGTH);
        int computedChecksum = computeEan13Checksum(withoutChecksum);
        int actualChecksum = Character.getNumericValue(isbn.charAt(WITHOUT_CHECKSUM_LENGTH));
        return computedChecksum == actualChecksum;
    }

    static boolean isValidRegexp(String isbn) {
        return isbn.matches(IsbnValidator.REGEX_10_OR_13_WITHOUT_MINUSES);
    }

    public static int computeEan13Checksum(@NonNull String twelveCharsEan) {
        if (twelveCharsEan.length() != WITHOUT_CHECKSUM_LENGTH) {
            throw new IllegalStateException("Given value '%s' is not an isbn/issn 13 without check digit char".formatted(twelveCharsEan));
        }

        int tot = 0;
        for (int i = 0; i < twelveCharsEan.length(); i++) {
            int digit = Integer.parseInt(twelveCharsEan.substring(i, i + 1));
            tot += (i % 2 == 0) ? digit : digit * 3;
        }

        //checksum must be 0-9. If calculated as 10 then = 0
        int checksum = 10 - (tot % 10);
        if (checksum == 10) {
            checksum = 0;
        }

        return checksum;
    }
}
