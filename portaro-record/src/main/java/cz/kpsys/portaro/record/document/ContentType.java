package cz.kpsys.portaro.record.document;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

/**
 * Typ obsahu - text, audio, video,... Kazdy typ muze mit nadrazeny typ (music je zaroven obecne audio)
 * Created by <PERSON> on 16.10.2016.
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public enum ContentType {

    TEXT(null),
    BOOK(TEXT),
    PERIODICAL(TEXT),
    ARTICLE(TEXT),
    //vyzkumna zprava
    REPORT(TEXT),
    //recenze
    REVIEW(TEXT),

    MAP(null),

    IMAGE(null),

    AUDIO(null),
    MUSIC(AUDIO),
    SPEECH(AUDIO),

    VIDEO(null),
    MOVIE(VIDEO);

    @Nullable ContentType superType;

    /**
     * <PERSON>rat<PERSON>, zda je dany typ tim typem nebo jednim ze supertypu.
     * @param type
     * @return
     */
    public boolean is(ContentType type) {
        if (this == type) {
            return true;
        }
        if (superType == null) {
            return false;
        }
        return superType.is(type);
    }


    /**
     * Fallback metoda pro velocity
     * @param contentTypeName
     * @return
     */
    public boolean is(String contentTypeName) {
        return is(ContentType.valueOf(contentTypeName));
    }
}
