package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.Set;
import java.util.stream.Collectors;

public record LinkingRecordsLookupDefinition(

        @NonNull FieldTypeId linkingRecordsLinkFieldTypeId,
        @NonNull FieldTypeId linkingRecordsValueFieldTypeId,
        @Nullable Set<Integer> linkingRecordsFondIds

) {

    @Override
    public String toString() {
        String fondCondition = ListUtil.notNullStream(linkingRecordsFondIds).map(Object::toString).collect(Collectors.joining(",", "@", " "));
        return "{" + fondCondition + linkingRecordsLinkFieldTypeId + ":link = {thisField}:record}:" + linkingRecordsValueFieldTypeId;
    }
}
