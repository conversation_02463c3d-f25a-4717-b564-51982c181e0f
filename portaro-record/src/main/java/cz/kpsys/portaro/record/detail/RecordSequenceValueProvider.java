package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.Provider;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordSequenceValueProvider implements Provider<Long> {

    @NonNull ContextualProvider<@NonNull String, @NonNull Long> recordSequenceProvider;
    @NonNull String sequenceName;

    public static RecordSequenceValueProvider of(ContextualProvider<@NonNull String, @NonNull Long> recordSequenceProvider, String sequenceName) {
        return new RecordSequenceValueProvider(recordSequenceProvider, sequenceName);
    }

    @Override
    public Long get() {
        return recordSequenceProvider.getOn(sequenceName);
    }

    @Override
    public String toString() {
        return "recordSequence(" + sequenceName + ")";
    }
}