package cz.kpsys.portaro.record;

import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.text.TextEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.text.TextValueEditorType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.With;

@Form(id = "recordImport", title = "Import záznamu (přepsání z<PERSON>znamu jiným)")
@FormSubmit(path = "/api/records/import")
@With
public record RecordImportRequest(

        @NotNull
        Record record,

        @NotNull
        String format,

        @FormPropertyLabel("xml")
        @TextEditor(type = TextValueEditorType.TEXTAREA)
        @NotBlank
        @Size(min = 1, max = 10000)
        String content

) {}
