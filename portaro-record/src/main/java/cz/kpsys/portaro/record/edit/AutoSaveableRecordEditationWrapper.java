package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordStatus;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AutoSaveableRecordEditationWrapper implements AutoSaveableRecordEditation {

    @NonNull RecordEditation target;
    @NonNull RecordAutoSavingPolicy autoSavingPolicy;
    @NonNull Department savingDepartment;
    @NonNull UserAuthentication currentAuth; // TODO: This is evil. Fix by not persisting RecordEditation.

    public AutoSaveableRecordEditationWrapper(@NonNull RecordEditation target,
                                              @NonNull RecordAutoSavingPolicy autoSavingPolicy,
                                              @NonNull Department savingDepartment,
                                              @NonNull UserAuthentication currentAuth) {
        this.target = target;
        this.autoSavingPolicy = autoSavingPolicy;
        this.savingDepartment = savingDepartment;
        this.currentAuth = currentAuth;
        autoSaveWhenAllowed();
    }

    private void autoSaveWhenAllowed() {
        if (autoSavingPolicy == RecordAutoSavingPolicy.AFTER_EACH_CHANGE_ALWAYS || (autoSavingPolicy == RecordAutoSavingPolicy.AFTER_EACH_CHANGE_WHEN_DRAFT && isDraft())) {
            saveIfModified(savingDepartment, currentAuth);
        }
    }

    @Override
    public String getId() {
        return target.getId();
    }

    @Override
    public boolean isDraft() {
        return target.isDraft();
    }

    @Override
    public boolean isDeleted() {
        return target.isDeleted();
    }

    @Override
    public boolean isRevisionSaved() {
        return target.isRevisionSaved();
    }

    @Override
    public boolean isAuthority() {
        return target.isAuthority();
    }

    @NonNull
    @Override
    public Fond getFond() {
        return target.getFond();
    }

    @NonNull
    @Override
    public Record getRecord() {
        return target.getRecord();
    }

    @Override
    public @NonNull List<Field<?>> getFields() {
        return target.getFields();
    }

    @Override
    public @NonNull Stream<Field<?>> streamFields() {
        return target.streamFields();
    }

    @NonNull
    @Override
    public List<? extends EditableFieldType<?>> getSubfieldTypes() {
        return target.getSubfieldTypes();
    }

    @Override
    public Field<?> createField(@NonNull EmptyFieldCreation creation) {
        Field<?> topfield = target.createField(creation);
        autoSaveWhenAllowed();
        return topfield;
    }

    @Override
    public RecordEditation replaceAllFields(FieldContainer newFields) {
        target.replaceAllFields(newFields);
        autoSaveWhenAllowed();
        return this;
    }

    @Override
    public RecordEditation moveFields(FieldMovementCommand command) {
        target.moveFields(command);
        autoSaveWhenAllowed();
        return this;
    }

    @Override
    public RecordEditation changeFond(Fond fond) {
        target.changeFond(fond);
        autoSaveWhenAllowed();
        return this;
    }

    @Override
    public RecordEditation changeStatus(RecordStatus status, Department ctx) {
        target.changeStatus(status, ctx);
        autoSaveWhenAllowed();
        return this;
    }

    @Override
    public void copyTopfield(Field<?> targetField, Field<?> sourceTopfield) {
        target.copyTopfield(targetField, sourceTopfield);
    }

    @Override
    public RecordEditation publish(@NonNull Department ctx, @NonNull UserAuthentication currentAuth) throws IllegalStateException {
        target.publish(ctx, currentAuth);
        autoSaveWhenAllowed();
        return this;
    }

    @Override
    public RecordEditation saveIfModified(@NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        target.saveIfModified(ctx, currentAuth);
        return this;
    }

    @Override
    public void notifyChange(RecordEditationEvent event) {
        target.notifyChange(event);
        autoSaveWhenAllowed();
    }
}
