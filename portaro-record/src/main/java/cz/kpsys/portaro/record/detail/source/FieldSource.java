package cz.kpsys.portaro.record.detail.source;

import lombok.NonNull;

public sealed interface FieldSource permits BindingFieldSource, CommonFieldSource, FondFieldSource {

    static FieldSource common() {
        return new CommonFieldSource();
    }

    static FieldSource binding(@NonNull String bindingName) {
        return new BindingFieldSource(bindingName);
    }

    static FieldSource fond() {
        return new FondFieldSource();
    }
}
