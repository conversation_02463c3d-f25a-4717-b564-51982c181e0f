package cz.kpsys.portaro.record.search;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.isbn.IsbnChecker;
import cz.kpsys.portaro.search.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.function.Consumer;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ByIsbnSearchingDepartmentedRecordLoader implements ItemCustomizableContextualSearchFunction<String, Department, List<Record>> {

    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> detailedRecordSearchLoader;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull HierarchyLoadScope mode;
    boolean throwIfInvalid;

    @Override
    public List<Record> getOn(@NonNull String isbn, MapBackedParams customParams, Department ctx) {
        if (throwIfInvalid) {
            IsbnChecker.throwIfInvalid(isbn);
        }
        if (!IsbnChecker.isValidIsbn(isbn)) {
            return List.of();
        }

        Consumer<MapBackedParams> params = p -> {
            p.set(RecordConstants.SearchParams.ISBN, isbn);
            p.set(CoreSearchParams.DEPARTMENT, contextHierarchyLoader.getAllByScope(ctx, mode));
            p.setAll(customParams);
        };

        return detailedRecordSearchLoader.getContent(RangePaging.forAll(), params);
    }
}
