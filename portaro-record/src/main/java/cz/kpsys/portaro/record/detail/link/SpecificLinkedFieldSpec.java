package cz.kpsys.portaro.record.detail.link;

import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;

public record SpecificLinkedFieldSpec(

        @NonNull
        FieldTypeId targetFieldTypeId

) implements LinkedFieldSpec {

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof SpecificLinkedFieldSpec that)) {
            return false;
        }
        return targetFieldTypeId().equals(that.targetFieldTypeId());
    }

    @Override
    public int hashCode() {
        return targetFieldTypeId().hashCode();
    }

    @Override
    public String toString() {
        return targetFieldTypeId.toString();
    }
}
