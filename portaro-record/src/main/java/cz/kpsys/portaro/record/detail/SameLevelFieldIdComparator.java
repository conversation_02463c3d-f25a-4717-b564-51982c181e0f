package cz.kpsys.portaro.record.detail;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.Comparator;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SameLevelFieldIdComparator implements Comparator<FieldId> {

    @NonNull Comparator<FieldId> sameLevelComparator;

    public SameLevelFieldIdComparator(@NonNull Comparator<String> codeComparator) {
        this.sameLevelComparator = Comparator.comparing(FieldId::getCode, codeComparator).thenComparing(FieldId::getRepetition);
    }

    @Override
    public int compare(FieldId s1, FieldId s2) {
        // d245.a, d100.a
        if (s1.hasParent() && s2.hasParent()) {
            int parentLevelComparison = compare(s1.existingParent(), s2.existingParent());
            if (parentLevelComparison != 0) {
                return parentLevelComparison;
            }
            // d245.a, d245.b
            return sameLevelComparator.compare(s1, s2);
        }

        // d245.a, d100
        if (s1.hasParent()) {
            int parentComparison = compare(s1.existingParent(), s2);
            if (parentComparison != 0) {
                return parentComparison;
            }
            // d245.a, d245
            return 1;
        }

        // d245, d100.a
        if (s2.hasParent()) {
            int parentComparison = compare(s1, s2.existingParent());
            if (parentComparison != 0) {
                return parentComparison;
            }
            // d245, d245.a
            return -1;
        }

        // d245, d100
        return sameLevelComparator.compare(s1, s2);
    }

}
