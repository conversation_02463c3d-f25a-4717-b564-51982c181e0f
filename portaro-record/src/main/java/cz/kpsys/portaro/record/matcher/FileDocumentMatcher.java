package cz.kpsys.portaro.record.matcher;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.directory.AllIdsByDirectoryProvider;
import cz.kpsys.portaro.file.directory.Directory;
import cz.kpsys.portaro.file.directory.DirectoryChainLoader;
import cz.kpsys.portaro.matcher.AbstractMatcher;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class FileDocumentMatcher extends AbstractMatcher<IdentifiedFile> {

    @NonNull List<UUID> recordIds;
    @NonNull DatabaseCheckingFileAttacher databaseCheckingFileAttacher;


    public FileDocumentMatcher(@NonNull List<UUID> recordIds,
                               @NonNull AllIdsByDirectoryProvider allRecordIdsByDirectoryProvider,
                               @NonNull DirectoryChainLoader directoryChainLoader) {
        super("Document of attached file one of " + recordIds);
        this.recordIds = recordIds;
        this.databaseCheckingFileAttacher = new DatabaseCheckingFileAttacher(directoryChainLoader, allRecordIdsByDirectoryProvider);
    }


    public List<UUID> getRecordIds() {
        return recordIds;
    }


    public Object getValue() {
        return recordIds;
    }


    @Override
    public boolean matches(IdentifiedFile f) {
        return databaseCheckingFileAttacher.isFileOwnedByOneOfDocuments(f, recordIds);
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class DatabaseCheckingFileAttacher {

        @NonNull DirectoryChainLoader directoryChainLoader;
        @NonNull AllIdsByDirectoryProvider allRecordIdsByDirectoryProvider;

        public boolean isFileOwnedByOneOfDocuments(IdentifiedFile f, List<UUID> documentIds) {
            Directory directory = f.getDirectory();
            if (directory != null) {
                return isDocumentsInDirectory(directory, documentIds);
            }
            return false;
        }

        private boolean isDocumentsInDirectory(Directory directory, List<UUID> documentIds) {
            List<? extends Directory> directoryChain = directoryChainLoader.getThisAndParentsChain(directory);
            List<UUID> documentIdsOfThisDirChain = allRecordIdsByDirectoryProvider.getAllByDirectories(ListUtil.getListOfIds(directoryChain));
            return documentIdsOfThisDirChain.stream().anyMatch(documentIds::contains);
        }
    }
    
}
