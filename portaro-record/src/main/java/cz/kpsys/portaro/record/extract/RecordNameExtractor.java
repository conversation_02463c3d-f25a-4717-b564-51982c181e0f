package cz.kpsys.portaro.record.extract;

import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Objects;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordNameExtractor implements RecordValueExtractor {

    @Override
    public Stream<String> extractValue(Record record) {
        var name = StringUtil.notBlankTrimmedString(record.getName());
        if (Objects.isNull(name)) {
            return Stream.empty();
        }
        return Stream.of(name);
    }
}
