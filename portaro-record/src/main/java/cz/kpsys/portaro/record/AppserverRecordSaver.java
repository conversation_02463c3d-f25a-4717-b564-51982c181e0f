package cz.kpsys.portaro.record;

import cz.kpsys.portaro.appserver.Appserver;
import cz.kpsys.portaro.appserver.XmlAppserverRequest;
import cz.kpsys.portaro.appserver.mapping.AppserverResponseHandler;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.appserver.oxm.AppserverTag;
import cz.kpsys.portaro.commons.cache.CacheDeletableById;
import cz.kpsys.portaro.commons.convert.StringToIntegerConverter;
import cz.kpsys.portaro.commons.convert.StringToUuidConverter;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.commons.util.ComparatorForExplicitSorting;
import cz.kpsys.portaro.record.detail.convert.DetailToMarcXmlConverter;
import cz.kpsys.portaro.record.edit.RecordEditationEvent;
import cz.kpsys.portaro.record.operation.RecordOperationType;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jdom2.Document;
import org.jdom2.Element;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import static cz.kpsys.portaro.record.Record.TYPE_AUTHORITY;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class AppserverRecordSaver implements RecordSaver {

    public static final String PATH = Appserver.APIPATH_DETAIL_DOKUMENTU_ULOZENI;

    public static final List<RecordEditationEvent.Type> MODIFICATION_TYPES_PRIORITIES = List.of(
            RecordEditationEvent.Type.RECORD_PUBLICATION,
            RecordEditationEvent.Type.RECORD_DRAFT_CREATION,
            RecordEditationEvent.Type.RECORD_STATUS_CHANGE_TO_FINISHED_CATALOGING,
            RecordEditationEvent.Type.RECORD_STATUS_CHANGE_TO_UNFINISHED_CATALOGING,
            RecordEditationEvent.Type.RECORD_STATUS_CHANGE_TO_CASLIN_SENT,
            RecordEditationEvent.Type.RECORD_STATUS_CHANGE_TO_CASLIN_ACCEPTED,
            RecordEditationEvent.Type.RECORD_STATUS_CHANGE_TO_OTHER,
            RecordEditationEvent.Type.RECORD_FOND_CHANGE,
            RecordEditationEvent.Type.FIELD_VALUE_EDITATION,
            RecordEditationEvent.Type.FIELD_VALUE_SETTING,
            RecordEditationEvent.Type.FIELD_VALUE_DELETION,
            RecordEditationEvent.Type.FIELD_MOVEMENT,
            RecordEditationEvent.Type.FIELD_CREATION,
            RecordEditationEvent.Type.FIELD_REMOVAL
    );
    public static final Set<RecordEditationEvent.Type> DRAFT_IGNORED_MODIFICATION_TYPES = Set.of(
            RecordEditationEvent.Type.RECORD_FOND_CHANGE,
            RecordEditationEvent.Type.RECORD_STATUS_CHANGE_TO_FINISHED_CATALOGING,
            RecordEditationEvent.Type.RECORD_STATUS_CHANGE_TO_UNFINISHED_CATALOGING,
            RecordEditationEvent.Type.RECORD_STATUS_CHANGE_TO_CASLIN_SENT,
            RecordEditationEvent.Type.RECORD_STATUS_CHANGE_TO_CASLIN_ACCEPTED,
            RecordEditationEvent.Type.RECORD_STATUS_CHANGE_TO_OTHER,
            RecordEditationEvent.Type.FIELD_VALUE_EDITATION,
            RecordEditationEvent.Type.FIELD_VALUE_SETTING,
            RecordEditationEvent.Type.FIELD_VALUE_DELETION,
            RecordEditationEvent.Type.FIELD_CREATION,
            RecordEditationEvent.Type.FIELD_REMOVAL,
            RecordEditationEvent.Type.FIELD_MOVEMENT
    );

    @NonNull MappingAppserverService mappingAppserver;
    @NonNull DetailToMarcXmlConverter detailToMarcXmlConverter;
    @NonNull ByIdLoadable<Record, UUID> authorityLoader;
    @NonNull List<CacheDeletableById> cacheDeletablesByRecordId;
    @NonNull IdAndIdsLoadable<RecordEntity, UUID> recordEntityLoader;

    
    @Override
    public Record save(RecordSaveCommand cmd) {
        boolean isPublishing = RecordEditationEvent.containsAnyOfType(cmd.unsavedModifications(), RecordEditationEvent.Type.RECORD_PUBLICATION);
        XmlAppserverRequest request = XmlAppserverRequest.byPost(PATH)
                .withHeadlessBody(generateXml(cmd, isPublishing));

        if (cmd.forSourceRecord()) {
            SourceRecordSaveAppserverResponse response = mappingAppserver.call(request, new SourceRecordResponseHandler());
            clearCaches(cmd);
            Assert.state(cmd.record().getId().equals(response.sourceDocumentId()), "Source document record uuid sent to AS and received uuid does not match: sent=%s received=%s".formatted(cmd.record().getId(), response.sourceDocumentId()));
            return authorityLoader.getById(response.sourceAuthorityId());
        }

        RecordSaveAppserverResponse response = mappingAppserver.call(request, new ResponseHandler());

        clearCaches(cmd);

        Assert.state(cmd.record().getId().equals(response.recordId()), "Record uuid sent to AS and received uuid does not match: sent=%s received=%s".formatted(cmd.record().getId(), response.recordId()));
        cmd.record().setKindedId(response.recordKindedId());
        if (cmd.creation()) {
            cmd.record().setDirectoryId(response.directoryId());
        }

        // Aplikáč nevrací event id, tak si je donačteme
        RecordEntity loadedEntity = recordEntityLoader.getById(cmd.record().getId());

        return updateFromEntity(cmd.record(), loadedEntity);
    }

    private void clearCaches(RecordSaveCommand cmd) {
        for (CacheDeletableById cacheDeletableById : cacheDeletablesByRecordId) {
            cacheDeletableById.deleteFromCacheById(cmd.record().getId());
        }
    }


    private String generateXml(RecordSaveCommand cmd, boolean isPublishing) {
        StringBuilder s = new StringBuilder();
        for (Integer recordOperationTypeId : filterRecordOperationIds(cmd)) {
            s.append(new AppserverTag("vykon", recordOperationTypeId));
        }
        s.append(new AppserverTag("pujcovna", cmd.department()));
        s.append(detailToMarcXmlConverter.convert(
                cmd.record().getType().equals(TYPE_AUTHORITY),
                cmd.record().getDetail(),
                cmd.record().getFond(),
                cmd.creation() ? null : cmd.record().getKindedId(),
                cmd.record().getId(),
                cmd.record().isActive() || isPublishing,
                cmd.record().getStatus(),
                cmd.sourceRecordAuthorityFond()
        ));
        return s.toString();
    }

    // Donačtení dat Recordu přímo z databáze
    private Record updateFromEntity(Record updated, RecordEntity entity) {
        updated.setName(entity.getName());
        updated.setCreationEventId(entity.getCreationEventId());
        updated.setActivationEventId(entity.getActivationEventId());
        updated.setDeletionEventId(entity.getDeletionEventId());
        // FIXME: zatím existují Recordy, které mají DIRECTORY_ID = null. Až to bude opraveno, odkomentovat kód
        //updated.setDirectoryId(entity.getDirectoryId());
        //updated.setCover(ObjectUtil.elvis(entity.getPrimaryFileId(), StubFile::new));
        return updated;
    }

    @NonNull
    private Set<Integer> filterRecordOperationIds(@NonNull RecordSaveCommand cmd) {
        Set<RecordEditationEvent.Type> filteredPrioritizedEditationTypes = cmd.unsavedModifications().stream()
                .map(RecordEditationEvent::type)
                .filter(modificationType -> {
                    if (cmd.record().isActive()) {
                        return true;
                    }
                    return !DRAFT_IGNORED_MODIFICATION_TYPES.contains(modificationType);
                })
                .min(new ComparatorForExplicitSorting<>(MODIFICATION_TYPES_PRIORITIES))
                .map(Set::of)
                .orElse(Set.of());
        return filteredPrioritizedEditationTypes.stream()
                .map(modificationType -> switch (modificationType) {
                    case RECORD_DRAFT_CREATION -> RecordOperationType.ID_DRAFT_CREATION;
                    case RECORD_PUBLICATION -> cmd.machineEditation() ? RecordOperationType.ID_CREATION_BY_APP : RecordOperationType.ID_PUBLICATION_BY_WEB;
                    case RECORD_FOND_CHANGE -> RecordOperationType.ID_FOND_CHANGE;
                    case RECORD_STATUS_CHANGE_TO_UNFINISHED_CATALOGING -> RecordOperationType.ID_STATUS_CHANGE_TO_UNFINISHED_CATALOGING;
                    case RECORD_STATUS_CHANGE_TO_FINISHED_CATALOGING -> RecordOperationType.ID_STATUS_CHANGE_TO_FINISHED_CATALOGING;
                    case RECORD_STATUS_CHANGE_TO_CASLIN_SENT -> RecordOperationType.ID_STATUS_CHANGE_TO_CASLIN_SENT;
                    case RECORD_STATUS_CHANGE_TO_CASLIN_ACCEPTED -> RecordOperationType.ID_STATUS_CHANGE_TO_CASLIN_ACCEPTED;
                    case RECORD_LOCK,
                         RECORD_UNLOCK,
                         RECORD_STATUS_CHANGE_TO_OTHER -> RecordOperationType.ID_STATUS_CHANGE_TO_OTHER;
                    case FIELD_VALUE_EDITATION,
                         FIELD_VALUE_SETTING,
                         FIELD_VALUE_DELETION,
                         FIELD_CREATION,
                         FIELD_REMOVAL,
                         FIELD_MOVEMENT -> cmd.machineEditation() ? RecordOperationType.ID_EDITATION_BY_APP : RecordOperationType.ID_EDITATION;
                })
                .collect(Collectors.toUnmodifiableSet());
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class ResponseHandler implements AppserverResponseHandler<RecordSaveAppserverResponse> {

        @NonNull StringToUuidConverter stringToUuidConverter = StringToUuidConverter.INSTANCE;
        @NonNull StringToIntegerConverter stringToIntegerConverter = StringToIntegerConverter.strict();

        @Override
        public RecordSaveAppserverResponse mapResponse(Document xml) {
            Element recordElement = xml.getRootElement().getChild(RecordSaveAppserverResponse.RECORD_ENVELOPE);
            @NonNull UUID recordId = stringToUuidConverter.convert(recordElement.getChildTextTrim(RecordSaveAppserverResponse.RECORD_ID));
            Integer recordKindedId = stringToIntegerConverter.convert(recordElement.getChildTextTrim(RecordSaveAppserverResponse.ID));
            Integer fondId = stringToIntegerConverter.convert(recordElement.getChildTextTrim(RecordSaveAppserverResponse.FOND_ID));
            Integer directoryId = stringToIntegerConverter.convert(recordElement.getChildTextTrim(RecordSaveAppserverResponse.FULLTEXT_SKUPINA_ID));
            return new RecordSaveAppserverResponse(recordId, recordKindedId, fondId, directoryId);
        }
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class SourceRecordResponseHandler implements AppserverResponseHandler<SourceRecordSaveAppserverResponse> {

        @NonNull StringToUuidConverter stringToUuidConverter = StringToUuidConverter.INSTANCE;
        @NonNull StringToIntegerConverter stringToIntegerConverter = StringToIntegerConverter.strict();

        @Override
        public SourceRecordSaveAppserverResponse mapResponse(Document xml) {
            Element recordElement = xml.getRootElement().getChild(RecordSaveAppserverResponse.RECORD_ENVELOPE);
            @NonNull UUID sourceAuthorityId = stringToUuidConverter.convert(recordElement.getChildTextTrim(SourceRecordSaveAppserverResponse.SOURCE_RECORD_AUTHORITY_RECORD_ID));
            @NonNull UUID sourceDocumentId = stringToUuidConverter.convert(recordElement.getChildTextTrim(SourceRecordSaveAppserverResponse.SOURCE_RECORD_DOCUMENT_RECORD_ID));
            @NonNull Integer sourceAuthorityKindedId = stringToIntegerConverter.convert(recordElement.getChildTextTrim(SourceRecordSaveAppserverResponse.SOURCE_RECORD_AUTHORITY_ID));
            @NonNull Integer sourceAuthorityFondId = stringToIntegerConverter.convert(recordElement.getChildTextTrim(SourceRecordSaveAppserverResponse.SOURCE_RECORD_AUTHORITY_FOND_ID));
            @NonNull Integer sourceDocumentKindedId = stringToIntegerConverter.convert(recordElement.getChildTextTrim(SourceRecordSaveAppserverResponse.SOURCE_RECORD_DOCUMENT_ID));
            @NonNull Integer sourceDocumentFondId = stringToIntegerConverter.convert(recordElement.getChildTextTrim(SourceRecordSaveAppserverResponse.SOURCE_RECORD_DOCUMENT_FOND_ID));
            return new SourceRecordSaveAppserverResponse(
                    sourceAuthorityId,
                    sourceDocumentId,
                    sourceAuthorityKindedId,
                    sourceAuthorityFondId,
                    sourceDocumentKindedId,
                    sourceDocumentFondId
            );
        }
    }
}
