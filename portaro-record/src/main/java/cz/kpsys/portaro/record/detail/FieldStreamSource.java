package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Stream;

public interface FieldStreamSource<F> {

    @JsonIgnore
    List<Field<?>> getFields();

    @JsonIgnore
    @NonNull
    default Stream<Field<?>> streamFields() {
        return getFields().stream();
    }

    default List<Field<?>> getFields(@NonNull Predicate<? super Field<?>> matcher) {
        return streamFields(matcher).toList();
    }

    default @NonNull Stream<Field<?>> streamFields(@NonNull Predicate<? super Field<?>> matcher) {
        return streamFields().filter(matcher);
    }

    default Stream<Field<? extends ScalarFieldValue<?>>> deepStreamFields() {
        return streamFields()
                .flatMap(field -> Stream.concat(Stream.of(field), field.deepStreamFields()));
    }

    default Optional<Field<?>> getFirstField(@NonNull Predicate<? super Field<?>> matcher) {
        return streamFields()
                .filter(matcher)
                .findFirst();
    }

}
