package cz.kpsys.portaro.record.isbn;

import cz.kpsys.portaro.search.restriction.Disjunction;
import cz.kpsys.portaro.search.restriction.Restriction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import cz.kpsys.portaro.search.restriction.matcher.EqWords;
import cz.kpsys.portaro.search.restriction.matcher.StartsWithWords;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class IsbnDisjunctionAddingTermConverter<FIELD> implements Converter<Term<FIELD>, Restriction<? extends FIELD>> {

    @NonNull FIELD fieldToCreate;

    @Override
    public Restriction<? extends FIELD> convert(@NonNull Term<FIELD> term) {
        return new Disjunction<FIELD>(List.of(term, createIsbnEqTerm(term)));
    }

    private Term<FIELD> createIsbnEqTerm(@NonNull Term<FIELD> term) {
        String value = getValue(term);
        String coreValue = new Isbn(value).getCoreValue();
        return new Term<>(fieldToCreate, new Eq(coreValue));
    }

    private String getValue(@NonNull Term<FIELD> term) {
        if (term.matcher() instanceof StartsWithWords startsWithWords) {
            return startsWithWords.value();
        }
        if (term.matcher() instanceof EqWords eqWords) {
            return eqWords.value();
        }
        throw new IllegalStateException(String.format("Matcher of class %s (term %s) is invalid for conversion to isbn disjunction", term.matcher().getClass().getSimpleName(), term));
    }

}