package cz.kpsys.portaro.record.operation;

import cz.kpsys.portaro.database.DbUtils;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.RecordOperationDb.RECORD_OPERATION.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
class RecordOperationEntityRowMapper implements RowMapper<RecordOperationEntity> {

    @Override
    public RecordOperationEntity mapRow(@NonNull ResultSet rs, int rowNum) throws SQLException {
        UUID id = DbUtils.uuidNotNull(rs, ID);
        UUID recordId = DbUtils.uuidNotNull(rs, RECORD_ID);
        UUID eventId = DbUtils.uuidOrNull(rs, EVENT_ID);
        Integer fondId = DbUtils.getIntegerNotNull(rs, FOND_ID);
        Integer departmentId = DbUtils.getIntegerNotNull(rs, DEPARTMENT_ID);
        Integer userId = DbUtils.getIntegerNotNull(rs, FK_UZIV);
        Integer recordOperationTypeId = DbUtils.getIntegerNotNull(rs, TYPE_ID);
        Instant date = DbUtils.instantNotNull(rs, CREATION_DATE);
        return new RecordOperationEntity(id, recordId, eventId, fondId, departmentId, userId, recordOperationTypeId, date);
    }
}
