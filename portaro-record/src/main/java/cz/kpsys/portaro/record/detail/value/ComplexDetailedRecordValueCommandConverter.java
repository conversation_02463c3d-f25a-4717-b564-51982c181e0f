package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.fn.Ref;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.util.Optional;
import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ComplexDetailedRecordValueCommandConverter implements TypedFieldValueConverter<DetailedRecordValueCommand, FieldPayload<StringFieldValue>, StringFieldValue> {

    @NonNull RecordEntryFieldTypeIdResolver entryFieldTypeIdResolver;

    @Override
    public @NonNull FieldPayload<StringFieldValue> convert(@NonNull DetailedRecordValueCommand command, @NonNull FieldType<?> fieldType, @NonNull RecordFieldId fieldId) {
        Optional<ScalarFieldValue<?>> linkedValue = getLinkedRecordSubfieldValue(fieldType, command.record());
        StringFieldValue valueHolder = StringFieldValue.of(
                getLabel(linkedValue, command.record()),
                getText(linkedValue),
                getOrigins(linkedValue)
        );
        RecordIdFondPair recordLink = command.record().idFondPair();
        return FieldPayload.of(valueHolder, recordLink);
    }

    private @NonNull Text getText(Optional<ScalarFieldValue<?>> linkedValue) {
        return linkedValue
                .map(ScalarFieldValue::text)
                .orElseGet(Texts::ofEmpty);
    }

    private @NonNull String getLabel(Optional<ScalarFieldValue<?>> linkedValue, Record input) {
        return linkedValue
                .map(ScalarFieldValue::label)
                .filter(StringUtil::hasLength)
                .orElse(UuidGenerator.abbr6(input.getId()));
    }


    private @NonNull Set<RecordIdFondPair> getOrigins(Optional<ScalarFieldValue<?>> linkedValue) {
        return linkedValue
                .map(ScalarFieldValue::origins)
                .orElse(Set.of());
    }

    private Optional<ScalarFieldValue<?>> getLinkedRecordSubfieldValue(@NonNull FieldType<?> sourceFieldType, Record record) {
        FieldContainer detail = record.getDetail();
        if (detail == null) {
            return Optional.empty();
        }
        Optional<Formula<?>> formulaOpt = sourceFieldType.getFormula();
        if (formulaOpt.isEmpty()) {
            return Optional.empty();
        }
        Assert.isInstanceOf(Ref.class, formulaOpt.get(), () -> "Only %s formula type of field type %s is not supported when setting value via getLinkedRecordSubfieldValue".formatted(Ref.class.getSimpleName(), sourceFieldType));
        Ref<?> ref = (Ref<?>) formulaOpt.get();
        FieldTypeId linkedFieldTypeId = entryFieldTypeIdResolver.getLinkedFieldType(record.getFond(), ref.pointer().linkedFieldSpec());
        Optional<Field<?>> firstIn = FieldFinders.<FieldContainer, Field<?>>byDeepTypeId(linkedFieldTypeId).findFirstIn(detail);
        if (firstIn.isEmpty()) {
            return Optional.empty();
        }
        if (!firstIn.get().hasValueHolder()) {
            return Optional.empty();
        }
        return Optional.of(firstIn.get().getExistingValueHolder());
    }

}
