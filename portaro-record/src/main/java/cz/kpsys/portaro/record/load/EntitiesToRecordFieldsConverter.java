package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.collection.ListCutter;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.ReconsCache;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.RecordIdentifier;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import cz.kpsys.portaro.record.detail.spec.RecordMultifieldId;
import cz.kpsys.portaro.record.detail.spec.RecordRootId;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class EntitiesToRecordFieldsConverter implements Function<List<RecordFieldEntity>, List<? extends RecordField>> {

    @NonNull AllByIdsLoadable<RecordIdFondPair, UUID> recordFondPairsLoader;
    @NonNull FieldTypesByFondLoader editableFieldTypesByFondLoader;

    @Override
    public List<RecordField> apply(@NonNull List<RecordFieldEntity> recordFieldEntities) {
        Map<RecordMultifieldId, Integer> multifieldIndexes = new HashMap<>();

        // Fill the record-fond-pair cache
        ReconsCache reconsCache = ReconsCache.empty();
        List<UUID> partialRecordIds = collectAllNeededRecordIdsToLoad(recordFieldEntities);
        reconsCache.loadAbsents(partialRecordIds, recordFondPairsLoader);

        // build index
        FieldIndex fieldIndex = new FieldIndex();
        for (var recordFieldEntity: recordFieldEntities) {
            fieldIndex.createTopfieldFor(recordFieldEntity, reconsCache, multifieldIndexes);
        }

        // add subfields (or controlfield data) to topfields
        // for better performance, we first group entities by records
        Map<RecordIdentifier, List<RecordFieldEntity>> recordGroupedEntities = recordFieldEntities.stream().collect(Collectors.groupingBy(RecordFieldEntity::recordId));
        for (Map.Entry<RecordIdentifier, List<RecordFieldEntity>> recordIdentifierListEntry : recordGroupedEntities.entrySet()) {
            ListCutter<RecordFieldEntity> entityCutter = ListCutter.ofCopyOf(recordIdentifierListEntry.getValue());
            while (entityCutter.hasRemainingItems()) {
                RecordFieldEntity entity = entityCutter.cutFirst();

                RecordField topfield = fieldIndex.getExistingTopfieldFor(entity);

                if (entity.isControlfield()) {
                    fillTopfieldFromEntity(topfield, entity, reconsCache);
                } else {
                    RecordField subfield = createRecordSubfield(topfield, entity, entityCutter, multifieldIndexes, reconsCache);
                    topfield.getSubfields().add(subfield);
                }
            }
        }

        return fieldIndex.findTopfields();
    }

    private RecordField createRecordSubfield(RecordField parentField, RecordFieldEntity entity, ListCutter<RecordFieldEntity> entityCutter, Map<RecordMultifieldId, Integer> multifieldIndexes, ReconsCache reconsCache) {
        EditableFieldType<?> parentFieldType = parentField.getFieldType();

        String subfieldCode = entity.existingSubfieldCode();
        FieldTypeId fieldTypeId = parentFieldType.getFieldTypeId().sub(subfieldCode);
        EditableFieldType<?> fieldType = parentFieldType.getSubfieldTypeFor(fieldTypeId);
        RecordMultifieldId recordMultifieldId = RecordMultifieldId.of(parentField.getRecordFieldId(), fieldTypeId);
        Integer sameMultifieldIndex = multifieldIndexes.compute(recordMultifieldId, (_, v) -> v == null ? FieldId.FIRST_FIELD_REPETITION : v + 1);
        RecordFieldId recordFieldId = recordMultifieldId.toRecordFieldId(sameMultifieldIndex);

        RecordField subfield = createRecordSubfield(fieldType, recordFieldId, entity, reconsCache);

        if (fieldType.isVirtualGroup()) {
            List<? extends FieldType<?>> groupedFieldTypes = fieldType.getSubfieldTypes();
            List<FieldIndex.VirtualGroupMemberKey> groupedFieldTypeKeys = ListUtil.convertStrict(groupedFieldTypes, type -> FieldIndex.VirtualGroupMemberKey.create(type.getFieldTypeId()));
            List<RecordFieldEntity> subsubfields = entityCutter.cut(ent ->
                groupedFieldTypeKeys.stream().anyMatch(groupedFieldTypeKey -> groupedFieldTypeKey.matches(ent)));

            for (RecordFieldEntity subsubfieldEntity : subsubfields) {
                RecordField subsubfield = createRecordSubfield(subfield, subsubfieldEntity, entityCutter, multifieldIndexes, reconsCache);
                subfield.getSubfields().add(subsubfield);
                if (subsubfieldEntity.targetRecordId() != null && subsubfieldEntity.targetRecordId().equals(subsubfieldEntity.recordId())) {
                    // Subsubfields are only cached ones. So if target_record_id = record_id, relink to correct one!
                    subsubfield.setRecordLink(subfield.getRecordLink());
                }
            }
        }

        return subfield;
    }

    private static @NonNull List<UUID> collectAllNeededRecordIdsToLoad(@NonNull List<RecordFieldEntity> recordFieldEntities) {
        return recordFieldEntities.stream()
                .mapMulti((RecordFieldEntity entity, Consumer<RecordIdentifier> consumer) -> {
                    consumer.accept(entity.recordId());
                    if (entity.targetRecordId() != null) {
                        consumer.accept(entity.targetRecordId());
                    }
                    if (entity.originRecordId() != null) {
                        consumer.accept(entity.originRecordId());
                    }
                })
                .flatMap(RecordIdentifier::streamIds)
                .distinct()
                .toList();
    }

    private RecordField createRecordSubfield(EditableFieldType<?> fieldType, @NonNull RecordFieldId recordFieldId, RecordFieldEntity recordFieldEntity, ReconsCache reconsCache) {
        String textValue = recordFieldEntity.isStaleRecordLink() ? null : recordFieldEntity.textValue();
        return new RecordField(
                recordFieldEntity.id(),
                recordFieldId,
                fieldType,
                Objects.requireNonNull(recordFieldEntity.subfieldIndex()),
                recordFieldEntity.lastModificationDate(),
                new ArrayList<>(),
                textValue,
                ObjectUtil.elvis(recordFieldEntity.targetRecordId(), reconsCache::get),
                recordFieldEntity.textSuffix(),
                ObjectUtil.elvis(recordFieldEntity.originRecordId(), reconsCache::get),
                recordFieldEntity
        );
    }

    private void fillTopfieldFromEntity(RecordField topfield, RecordFieldEntity entity, ReconsCache reconsCache) {
        topfield.setTextValue(entity.textValue());
        topfield.setRecordLink(ObjectUtil.elvis(entity.targetRecordId(), reconsCache::get));
        topfield.setOriginRecordId(ObjectUtil.elvis(entity.originRecordId(), reconsCache::get));
        topfield.setSourceEntity(entity);
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    private class FieldIndex {

        @NonNull Map<RecordTopfieldIdKey, RecordField> topfieldsMap = new HashMap<>(); // pozor klic muze obsahovat jine opakovani, nez to realne, ktere pak recordFieldu priradime
        @NonNull List<RecordField> topfields = new ArrayList<>();

        public RecordField getExistingTopfieldFor(RecordFieldEntity entity) {
            return Objects.requireNonNull(topfieldsMap.get(RecordTopfieldIdKey.create(entity)), () -> String.format("Cannot find topfield for %s in indexMap", entity));
        }

        public void createTopfieldFor(RecordFieldEntity entity, @NonNull ReconsCache reconsCache, @NonNull Map<RecordMultifieldId, Integer> multifieldIndexes) {
            topfieldsMap.computeIfAbsent(RecordTopfieldIdKey.create(entity), _ -> {
                // Create a topfield stub, which will then be filled with another data / subfields
                RecordField newTopfield = createTopfield(entity, reconsCache, multifieldIndexes);
                topfields.add(newTopfield);
                return newTopfield;
            });
        }

        private @NonNull RecordField createTopfield(RecordFieldEntity entity, @NonNull ReconsCache reconsCache, @NonNull Map<RecordMultifieldId, Integer> multifieldIndexes) {
            FieldTypeId topfieldTypeId = FieldTypeId.top(entity.fieldCode());
            RecordIdFondPair recordIdFondPair = reconsCache.get(entity.recordId());
            EditableFieldType<?> topfieldType = editableFieldTypesByFondLoader.getTopfieldTypeByFondAndIdOrUnknown(recordIdFondPair.fond(), topfieldTypeId);
            RecordMultifieldId recordMultifieldId = RecordMultifieldId.of(RecordRootId.of(recordIdFondPair), topfieldType.getFieldTypeId());
            Integer sameMultifieldIndex = multifieldIndexes.compute(recordMultifieldId, (_, v) -> v == null ? FieldId.FIRST_FIELD_REPETITION : v + 1);
            RecordFieldId recordFieldId = recordMultifieldId.toRecordFieldId(sameMultifieldIndex);
            return new RecordField(
                    entity.id(),
                    recordFieldId,
                    topfieldType,
                    entity.fieldIndex(),
                    entity.lastModificationDate(),
                    new ArrayList<>(),
                    null,
                    null,
                    null,
                    null,
                    null
            );
        }

        public List<RecordField> findTopfields() {
            return topfields;
        }

        private record RecordTopfieldIdKey(
                @NonNull RecordIdentifier recordIdentifier,
                @NonNull FieldId fieldId
        ) {

            public static RecordTopfieldIdKey create(RecordFieldEntity recordFieldEntity) {
                return new RecordTopfieldIdKey(
                        recordFieldEntity.recordId(),
                        FieldId.top(recordFieldEntity.fieldCode(), recordFieldEntity.fieldIndex())
                );
            }

        }

        private record VirtualGroupMemberKey(
                @NonNull String topfieldCode,
                @NonNull String ssfCode
        ) {
            public static VirtualGroupMemberKey create(FieldTypeId fieldTypeId) {
                String topfieldCode = fieldTypeId.existingParent().existingParent().getCode();
                String ssfCode = fieldTypeId.getCode();
                return new VirtualGroupMemberKey(topfieldCode, ssfCode);
            }

            public boolean matches(@NonNull RecordFieldEntity entity) {
                if (entity.isControlfield()) {
                    return false;
                }
                return topfieldCode.equals(entity.fieldCode()) && ssfCode.equals(entity.subfieldCode());
            }
        }

    }

}