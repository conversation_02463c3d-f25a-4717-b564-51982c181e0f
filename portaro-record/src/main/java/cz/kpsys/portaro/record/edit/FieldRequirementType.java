package cz.kpsys.portaro.record.edit;

import com.fasterxml.jackson.annotation.JsonFormat;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum FieldRequirementType implements LabeledIdentified<Integer> {

    INHERIT(0, Texts.ofNative("zdědit")),
    REQUIRED(1, Texts.ofNative("povinné"));

    public static FieldRequirementType merge(FieldRequirementType f1, FieldRequirementType f2) {
        if (f1.equals(REQUIRED) || f2.equals(REQUIRED)) {
            return REQUIRED;
        }
        return INHERIT;
    }

    public static final Codebook<FieldRequirementType, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer id;
    @NonNull Text text;

}
