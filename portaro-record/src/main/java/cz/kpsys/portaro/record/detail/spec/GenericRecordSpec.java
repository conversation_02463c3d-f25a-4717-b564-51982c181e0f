package cz.kpsys.portaro.record.detail.spec;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import lombok.NonNull;
import lombok.With;

import java.util.Objects;
import java.util.Set;

@With
public record GenericRecordSpec(

        @NonNull
        RecordMatcher recordMatcher,

        @NonNull
        FieldsSpec fieldsSpec

) implements FieldedRecordSpec {

    @Override
    public @NonNull Set<FieldSpec> existingFields() {
        return fieldsSpec.existingSpecs();
    }

    @Override
    public boolean isEmpty() {
        return fieldsSpec.isEmpty();
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, FieldedRecordSpec.class, FieldedRecordSpec::recordMatcher, FieldedRecordSpec::fieldsSpec);
    }

    @Override
    public int hashCode() {
        return Objects.hash(recordMatcher, fieldsSpec);
    }

    @Override
    public String toString() {
        return recordMatcher + ":" + fieldsSpec;
    }

}
