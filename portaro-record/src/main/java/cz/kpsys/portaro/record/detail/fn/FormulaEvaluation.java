package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.detail.value.*;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.util.Assert;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public record FormulaEvaluation<SUCCESS extends FieldValue<?>>(

        @Nullable SUCCESS success,
        @Nullable FailedResult fail

) {

    public FormulaEvaluation {
        Assert.state((success != null && fail == null) || (success == null && fail != null), "Exactly one of result or fail must be set");
    }

    public static <T extends FieldValue<?>> FormulaEvaluation<T> success(T result) {
        return new FormulaEvaluation<>(result, null);
    }

    public static <T extends FieldValue<?>> FormulaEvaluation<T> failure(FailedResult error) {
        return new FormulaEvaluation<>(null, error);
    }

    public static FormulaEvaluation<?> result(FormulaResult result) {
        return switch (result) {
            case FailedResult failedResult -> failure(failedResult);
            case FieldValue<?> fieldValue -> success(fieldValue);
        };
    }

    public boolean isSuccess() {
        return success != null;
    }

    public boolean isFailure() {
        return fail != null;
    }

    public @NonNull SUCCESS existingSuccess() {
        Assert.state(isSuccess(), () -> "Cannot get result because evaluation is failure");
        return success;
    }

    public FailedResult existingFail() {
        Assert.state(isFailure(), () -> "Cannot get result because evaluation is success");
        return fail;
    }

    public <F extends FailedResult> F existingFailOf(Class<F> expectedType) {
        FailedResult failedResult = existingFail();
        return ObjectUtil.cast(failedResult, expectedType, () -> "Failed result is not of expected type: " + expectedType.getSimpleName() + " but " + failedResult.getClass().getSimpleName());
    }

    public FormulaResult result() {
        return ObjectUtil.firstNotNull(success, fail);
    }

    public <OTHER_TYPE extends FieldValue<?>> FormulaEvaluation<OTHER_TYPE> castedFailed() {
        return FormulaEvaluation.failure(existingFail());
    }

    public FormulaEvaluation<VectorFieldValue<?, ?>> asVector() {
        if (isFailure()) {
            return castedFailed();
        }
        SUCCESS scalarSuccess = existingSuccess();
        return switch (scalarSuccess) {
            case VectorFieldValue<?, ?> vectorFieldValue -> FormulaEvaluation.success(vectorFieldValue);
            case ScalarFieldValue<?> scalarFieldValue -> FormulaEvaluation.success(scalarFieldValue.asVector());
        };
    }

    public FormulaEvaluation<SUCCESS> ensureOf(@NonNull Class<SUCCESS> expectedType, @NonNull ValuableFieldNode sourceNode, @NonNull Formula<?> operand) {
        if (isFailure()) {
            return this;
        }
        SUCCESS success = existingSuccess();
        if (expectedType.isInstance(success)) {
            return this;
        }
        if (ScalarFieldValue.class.isAssignableFrom(expectedType) && success instanceof VectorFieldValue<?, ?>) {
            return FormulaEvaluation.failure(NotAScalarFail.of(sourceNode.id().toRecordFieldTypeId(), operand));
        }
        return FormulaEvaluation.failure(IncorrectTypeFail.of(sourceNode.id().toRecordFieldTypeId(), operand, expectedType, success.getClass()));
    }

    public <SCALAR_ITEM extends ScalarFieldValue<VALUE>, VALUE> FormulaEvaluation<VectorFieldValue<SCALAR_ITEM, VALUE>> ensureVectorOf(@NonNull Class<SCALAR_ITEM> expectedType, @NonNull ValuableFieldNode sourceNode, @NonNull Formula<?> operand) {
        if (isFailure()) {
            return castedFailed();
        }
        if (!(existingSuccess() instanceof VectorFieldValue<?, ?> vectorFieldValue)) {
            return FormulaEvaluation.failure(NotAVectorFail.of(sourceNode.id().toRecordFieldTypeId(), operand));
        }
        List<FieldValue<?>> values = (List<FieldValue<?>>) vectorFieldValue.values();

        List<IncorrectTypeFail> incorrectTypeFails = List.of();
        for (FieldValue<?> itemValue : values) {
            if (!expectedType.isInstance(itemValue)) {
                if (incorrectTypeFails.isEmpty()) {
                    incorrectTypeFails = new ArrayList<>(values.size());
                }
                incorrectTypeFails.add(IncorrectTypeFail.of(sourceNode.id().toRecordFieldTypeId(), operand, expectedType, itemValue.getClass()));
            }
        }

        return (FormulaEvaluation<VectorFieldValue<SCALAR_ITEM, VALUE>>) this;
    }

    public FormulaEvaluation<SUCCESS> ensureOf(@NonNull ParameterizedTypeReference<SUCCESS> expectedType, @NonNull ValuableFieldNode sourceNode) {
        if (isFailure()) {
            return castedFailed();
        }
        Type type = expectedType.getType();
        if (type instanceof ParameterizedType parameterizedType) {
            Type rawType = parameterizedType.getRawType();
            if (rawType instanceof Class<?> clazz && clazz.isInstance(existingSuccess())) {
                return this;
            }
        }
        throw new UnsupportedOperationException("Not yet implemented: ensureType " + expectedType.getType());
    }
}
