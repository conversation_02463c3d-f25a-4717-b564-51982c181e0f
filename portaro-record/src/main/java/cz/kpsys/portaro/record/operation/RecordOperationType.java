package cz.kpsys.portaro.record.operation;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.BasicNamedLabeledIdentified;

public class RecordOperationType extends BasicNamedLabeledIdentified<Integer> {

    public static final int ID_CREATION = 1;
    public static final int ID_EDITATION = 2;
    public static final int ID_DOCUMENT_DISCARDION = 3;
    public static final int ID_STATUS_CHANGE_TO_FINISHED_CATALOGING = 4;
    public static final int ID_BULK_EDITATION = 5;
    public static final int ID_MERGING = 6;
    public static final int ID_IMPORT = 7;
    public static final int ID_DELETION = 8;
    public static final int ID_AUTHORITY_CREATION_OF_DOCUMENT = 9;
    public static final int ID_RESTORATION = 10;
    public static final int ID_RESAVE = 11;
    public static final int ID_STATUS_CHANGE_TO_UNFINISHED_CATALOGING = 12;
    public static final int ID_STATUS_CHANGE_TO_CASLIN_SENT = 13;
    public static final int ID_STATUS_CHANGE_TO_CASLIN_ACCEPTED = 14;
    public static final int ID_PUBLICATION_BY_WEB = 15;
    public static final int ID_FOND_CHANGE = 16;
    public static final int ID_CREATION_BY_APP = 17;
    public static final int ID_EDITATION_BY_APP = 18;
    public static final int ID_UNBLOCK_EDITATION = 21;
    public static final int ID_AUTHORITY_SENT_TO_NATIONAL_AUTHORITIES = 23;
    public static final int ID_CREATION_BY_CI = 24;
    public static final int ID_EDITATION_BY_CI = 25;
    public static final int ID_DRAFT_CREATION = 26;
    public static final int ID_STATUS_CHANGE_TO_OTHER = 28;
    public static final int ID_RECORD_ADOPTION = 29;
    public static final int ID_EXEMPLAR_CREATION = 51;
    public static final int ID_EXEMPLAR_EDITATION = 52;
    public static final int ID_EXEMPLAR_DELETION = 53;
    public static final int ID_EXEMPLAR_DISCARDION = 54;
    public static final int ID_EXEMPLAR_DISCARDION_RESTORE = 55;

    public RecordOperationType(Integer id, String name) {
        super(id, name);
    }

    @Override
    public Text getText() {
        return Texts.ofColumnMessageCodedOrNative(getName(), "DEF_VYKONY", "POPIS", getId());
    }
}
