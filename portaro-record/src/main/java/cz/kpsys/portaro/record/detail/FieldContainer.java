package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.record.RecordIdFondPair;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Predicate;

public interface FieldContainer extends FieldStreamSource<Field<?>> {

    /**
     * Prida pole do seznamu kontrolnich nebo datovych poli (automaticky podle typu)
     */
    default @NonNull Field<?> add(Field<?> field) {
        List<Field<?>> fields = getFields();
        fields.add(field);
        recomputeIndexes();
        return field;
    }

    default boolean remove(Field<?> field) {
        List<Field<?>> fields = getFields();
        if (fields.remove(field)) {
            recomputeIndexes();
            return true;
        }
        return false;
    }

    default boolean removeAll(Collection<Field<?>> fieldsToRemove) {
        List<Field<?>> fields = getFields();
        if (fields.removeAll(fieldsToRemove)) {
            recomputeIndexes();
            return true;
        }
        return false;
    }

    default boolean clear() {
        List<Field<?>> fields = getFields();
        if (fields.isEmpty()) {
            return false;
        }
        fields.clear();
        return true;
    }

    default Optional<Field<?>> getFirstFieldByTypeId(@NonNull FieldTypeId typeId) {
        return getFirstField(By.typeId(typeId));
    }

    default Optional<Field<?>> getFirstField(Predicate<? super Field<?>> matcher1, Predicate<? super Field<?>> matcher2) {
        return streamFields(matcher1)
                .flatMap(field -> field.streamFields(matcher2))
                .findFirst();
    }

    // TODO: edge cases
    default Optional<Field<?>> getFirstFieldRecursive(@NonNull Predicate<? super Field<?>> matcher) {
        var matchInFields = streamFields()
                .filter(matcher)
                .findFirst();

        if (matchInFields.isPresent()) {
            return matchInFields;
        }

        // search in subfields
        return streamFields()
                .map(field -> field.getFirstFieldRecursive(matcher))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .findFirst();
    }

    default List<Field<?>> getFields(Predicate<? super Field<?>> matcher1, Predicate<? super Field<?>> matcher2) {
        return streamFields(matcher1)
                .flatMap(field -> field.streamFields(matcher2))
                .toList();
    }

    default void swapFields(Field<?> field1, Field<?> field2) {
        List<Field<?>> topFields = getFields();

        int field1Idx = topFields.indexOf(field1);
        int field2Idx = topFields.indexOf(field2);
        Assert.isTrue(field1Idx >= 0, () -> String.format("Field %s is not in this record", field1));
        Assert.isTrue(field2Idx >= 0, () -> String.format("Field %s is not in this record", field2));

        //prohodime pole v seznamu
        Collections.swap(topFields, field1Idx, field2Idx);
        recomputeIndexes();
    }

    @JsonIgnore
    default void recomputeIndexes() {
        List<Field<?>> fields = getFields();
        Map<String, Integer> sameCodeIndexes = new HashMap<>(fields.size());
        for (Field<?> subfield : fields) {
            Integer sameCodeIndex = sameCodeIndexes.compute(subfield.getCode(), (k, v) -> v == null ? FieldId.FIRST_FIELD_REPETITION : v + 1);
            subfield.setFieldId(subfield.getFieldId().withIndex(sameCodeIndex));
        }
    }

    @JsonIgnore
    FieldContainer copy(@NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId);

}
