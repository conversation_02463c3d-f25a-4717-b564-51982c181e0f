package cz.kpsys.portaro.record.holding;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EntityToRecordHoldingSourceConverter implements Converter<RecordHoldingSourceEntity, RecordHoldingSource> {

    @Override
    public RecordHoldingSource convert(RecordHoldingSourceEntity entity) {
        return new RecordHoldingSource(
                entity.getId(),
                entity.getRecordHoldingId(),
                RecordHoldingSourceType.CODEBOOK.getById(entity.getTypeId()),
                entity.getCreationEventId()
        );
    }

}
