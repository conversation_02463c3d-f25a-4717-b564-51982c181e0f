package cz.kpsys.portaro.record.fond;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.LabeledIdentifiedRecord;
import lombok.NonNull;

public record SimpleFondResponse(

        @NonNull
        Integer id,

        @NonNull
        Text text

) implements LabeledIdentifiedRecord<Integer> {

    public static SimpleFondResponse mapFromFond(@NonNull Fond fond) {
        return new SimpleFondResponse(fond.getId(), fond.getText());
    }
}
