package cz.kpsys.portaro.record.detail;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.util.Collection;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class FieldExportSetting {

    @Nullable
    FieldTypeId targetFieldTypeId;

    public static FieldExportSetting toField(FieldTypeId targetExportField) {
        return new FieldExportSetting(targetExportField);
    }

    public static FieldExportSetting toSubfield(FieldTypeId targetExportSubfield) {
        Assert.isTrue(targetExportSubfield.getLevel() == FieldTypeId.LEVEL_SUBFIELD, "targetExportSubfield %s must be subfield type, but level is %s".formatted(targetExportSubfield, targetExportSubfield.getLevel()));
        return new FieldExportSetting(targetExportSubfield);
    }

    public static FieldExportSetting toSubfields(@NonNull Collection<FieldTypeId> targetExportSubfieldIds) {
        if (targetExportSubfieldIds.isEmpty()) {
            return FieldExportSetting.disabled();
        }

        FieldTypeId topfieldTypeId = targetExportSubfieldIds.stream()
                .map(FieldTypeId::getParent)
                .reduce((parentType1, parentType2) -> {
                    if (parentType1.equals(parentType2)) {
                        return parentType1;
                    }
                    throw new IllegalStateException(String.format("Cannot composite field export settings of differrent subfield parents (%s and %s)", parentType1, parentType2));
                })
                .orElseThrow(() -> new IllegalStateException(String.format("%s must not be empty", targetExportSubfieldIds)));

        String codes = targetExportSubfieldIds.stream()
                .map(FieldTypeId::getCode)
                .reduce("", (s, s2) -> s + "+" + s2);

        return new FieldExportSetting(FieldTypeId.subfield(topfieldTypeId, codes));
    }

    public static FieldExportSetting disabled() {
        return new FieldExportSetting(null);
    }

    public boolean isEnabled() {
        return targetFieldTypeId != null;
    }

    @Nullable
    public FieldTypeId getTargetFieldTypeId() {
        return targetFieldTypeId;
    }

    @Override
    public String toString() {
        return isEnabled() ? ("exporting to " + targetFieldTypeId) : "not-exporting";
    }
}
