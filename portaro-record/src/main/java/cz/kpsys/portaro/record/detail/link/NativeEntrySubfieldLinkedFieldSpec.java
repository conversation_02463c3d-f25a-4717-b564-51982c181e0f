package cz.kpsys.portaro.record.detail.link;

public record NativeEntrySubfieldLinkedFieldSpec() implements LinkedFieldSpec {

    @Override
    public boolean equals(Object o) {
        return o instanceof NativeEntrySubfieldLinkedFieldSpec;
    }

    @Override
    public int hashCode() {
        return 864222887;
    }

    @Override
    public String toString() {
        return "{nativeEntrySubfield}";
    }
}
