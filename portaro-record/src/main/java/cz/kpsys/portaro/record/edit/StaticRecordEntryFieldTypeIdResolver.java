package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.HashMap;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class StaticRecordEntryFieldTypeIdResolver implements RecordEntryFieldTypeIdResolver {

    @NonNull Map<Fond, FieldTypeId> entryElements = new HashMap<>();

    public StaticRecordEntryFieldTypeIdResolver with(Fond fond, FieldTypeId entryFieldTypeId) {
        entryElements.put(fond, entryFieldTypeId);
        return this;
    }

    @Override
    public @NonNull FieldTypeId getEntryNativeSubfield(@NonNull Fond fond) {
        return entryElements.get(fond);
    }

    @Override
    public @NonNull FieldTypeId getEntryCustomSubfield(@NonNull Fond fond, @NonNull String subfieldCode) {
        return entryElements.get(fond).withCode(subfieldCode);
    }
}
