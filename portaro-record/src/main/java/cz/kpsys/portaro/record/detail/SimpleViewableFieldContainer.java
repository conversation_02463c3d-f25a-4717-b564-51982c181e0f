package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SimpleViewableFieldContainer implements ViewableFieldContainer {

    @JsonIgnore(false)
    @Getter
    @NonNull
    List<ViewableField> fields;

    public SimpleViewableFieldContainer() {
        this(new ArrayList<>());
    }

    public SimpleViewableFieldContainer(List<ViewableField> fields) {
        this.fields = new ArrayList<>(fields);
    }

    public static SimpleViewableFieldContainer emptyUnmodifiable() {
        return new SimpleViewableFieldContainer(List.of());
    }

    @Override
    public String toString() {
        return "ViewableFieldContainer{with %s fields}".formatted(fields.size());
    }

}
