package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.value.FieldValue;
import lombok.NonNull;

import java.util.Set;
import java.util.stream.Stream;

public record Ref<RES extends FieldValue<?>>(

        @NonNull LookupDefinition pointer

) implements Formula<RES> {

    @Override
    public @NonNull Set<LookupDefinition> dependencies() {
        return Set.of(pointer);
    }

    @Override
    public @NonNull Stream<Formula<RES>> subformulas() {
        return Stream.of();
    }

    @Override
    public String toString() {
        return pointer.toString();
    }
}
