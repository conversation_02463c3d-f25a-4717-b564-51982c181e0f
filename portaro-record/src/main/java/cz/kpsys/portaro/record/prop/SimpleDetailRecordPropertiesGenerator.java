package cz.kpsys.portaro.record.prop;

import cz.kpsys.portaro.prop.ObjectProperties;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.fond.FondTypeResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SimpleDetailRecordPropertiesGenerator implements DetailRecordPropertiesGenerator {

    @NonNull
    @Override
    public ObjectProperties generate(@NonNull Fond fond, @NonNull FieldContainer recordDetail) {
        if (FondTypeResolver.isDocumentFond(fond)) {
            return ObjectProperties.of(RecordPropertiesGenerators.createForDocumentByDetail(recordDetail));
        }
        if (FondTypeResolver.isAuthorityFond(fond)) {
            return ObjectProperties.of(RecordPropertiesGenerators.createForAuthorityByDetail(recordDetail));
        }
        return ObjectProperties.empty();
    }

}
