package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.date.DatetimeRange;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.record.RecordIdFondPair;
import lombok.NonNull;

import java.util.Set;

public record DatetimeRangeFieldValue(

    @NonNull
    DatetimeRange value,

    @NonNull
    String label,

    @NonNull
    Text text,

    @NonNull
    Set<RecordIdFondPair> origins

) implements ScalarFieldValue<DatetimeRange> {

    public static @NonNull DatetimeRangeFieldValue of(@NonNull DatetimeRange value, @NonNull Set<RecordIdFondPair> origins) {
        return new DatetimeRangeFieldValue(
                value,
                DatetimeRangeTypedValueConverter.formatLabel(value),
                Texts.ofDatetimeRange(value),
                origins
        );
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        return o instanceof DatetimeRangeFieldValue that && value.equals(that.value);
    }

    @Override
    public int hashCode() {
        return value().hashCode();
    }

}
