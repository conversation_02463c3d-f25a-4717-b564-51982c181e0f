package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.collection.ListCutter;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.TimeMeter;
import cz.kpsys.portaro.databasestructure.RecordDb.RECORD;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.spec.*;
import cz.kpsys.portaro.sql.generator.Brackets;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import cz.kpsys.portaro.sql.generator.Where;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.util.Assert;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.RecordDb.RECORD_FIELD.*;
import static cz.kpsys.portaro.record.load.EntityFieldCodeMapper.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class NewRecordFieldEntityLoader implements RecordFieldLoader<RecordFieldEntity> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull RowMapper<RecordFieldEntity> rowMapper;

    @Override
    public List<RecordFieldEntity> getAll(@NonNull RecordSpecSet specSet) {
        if (specSet.isEmpty()) {
            return List.of();
        }

        SelectQuery sq = queryFactory.newSelectQuery();
        List<SelectQuery> unions = buildBulkSpecQueries(specSet.specs());
        sq.fromUnionAll(unions, "unions", useCteQueries()); // particularQueriesInBrackets = true kvuli tomu, ze v jednotlivych query pouzivame "with". Ve firebirdu ale CTE nepouzivame a (asi) bez nej tam nesmi byt zavorky

        // ve vyslednych entitach mohou byt duplicity (kvuli rychlosti a moznosti paralelizace v PG pouzivame union all, bez distinct) takze pak musime deduplikovat
        List<RecordFieldEntity> duplicableEntities = TimeMeter.measureAndLog(() -> jdbcTemplate.query(sq.getSql(), sq.getParamMap(), rowMapper), log, () -> "Load record field entities (%s specs: %s)".formatted(specSet.size(), specSet), Duration.ofMillis(10), Duration.ofMillis(100));
        return duplicableEntities.stream()
                .distinct()
                .sorted(RecordFieldEntity.COMPARATOR)
                .toList();
    }

    private @NonNull SelectQuery createSelect() {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(
                TC(TABLE, ID),
                TC(TABLE, RECORD_ID),
                TC(TABLE, FIELD_CODE),
                TC(TABLE, FIELD_INDEX),
                TC(TABLE, SUBFIELD_CODE),
                TC(TABLE, SUBFIELD_INDEX),
                TC(TABLE, TARGET_RECORD_ID),
                TC(TABLE, ORIGIN_RECORD_ID),
                TC(TABLE, CACHE_VALID),
                TC(TABLE, TEXT_VALUE),
                TC(TABLE, TEXT_SUFFIX),
                TC(TABLE, LARGE_TEXT_VALUE),
                TC(TABLE, NUMERIC_VALUE),
                TC(TABLE, DATE_VALUE),
                TC(TABLE, DATETIME_VALUE),
                TC(TABLE, DATERANGE_VALUE),
                TC(TABLE, DATETIMERANGE_VALUE),
                TC(TABLE, BOOLEAN_VALUE),
                TC(TABLE, CURRENCY_VALUE),
                TC(TABLE, PROBLEM),
                TC(TABLE, LAST_MODIFICATION_DATE),
                TC(TABLE, LAST_PROCESS_DATE)
        );
        sq.from(TABLE);
        sq.where().and().brackets()
                .eq(TC(TABLE, CACHE_VALID), true) // Cache valid = always OK
                .or()
                .addStringCondition(COLSNEQ(TABLE, TARGET_RECORD_ID, TABLE, RECORD_ID));
        return sq;
    }

    private @NonNull List<SelectQuery> buildBulkSpecQueries(Collection<RecordSpec> recordSpecs) {
        ListCutter<RecordSpec> specsCutter = ListCutter.ofCopyOf(recordSpecs);

        List<SelectQuery> queries = new ArrayList<>();

        List<RecordSpec> identifierRecordSpecs = specsCutter.cut(fieldedRecordSpec -> fieldedRecordSpec.recordMatcher() instanceof RecordIdentifierRecordMatcher);
        List<IdentifierMatchableCondition> identifierMatchableConditions = mapToIdentifierMatchableCondition(identifierRecordSpecs);
        queries.addAll(buildIdentifierMatchableConditionQueries(identifierMatchableConditions));

        List<RecordSpec> linkingRecordSpecs = specsCutter.cut(fieldedRecordSpec -> fieldedRecordSpec.recordMatcher() instanceof LinkingRecordRecordMatcher);
        for (RecordSpec linkingRecordSpec : linkingRecordSpecs) {
            queries.add(buildLinkingFieldedSpecQuery(linkingRecordSpec));
        }

        specsCutter.assertEmpty();
        return queries;
    }

    private List<IdentifierMatchableCondition> mapToIdentifierMatchableCondition(List<RecordSpec> identifierRecordSpecs) {
        return identifierRecordSpecs.stream()
                .flatMap(recordSpec -> {
                    UUID recordId = ((RecordIdentifierRecordMatcher) recordSpec.recordMatcher()).recordIdFondPair().id().id();
                    return switch (recordSpec) {
                        case WholeRecordSpec _ -> Stream.of(new IdentifierMatchableCondition(recordId, null, null, null));
                        case FieldedRecordSpec fieldedRecordSpec -> fieldedRecordSpec.existingFields().stream().map(fieldSpec -> mapFieldSpecToIdentifierMatchableCondition(recordId, fieldSpec));
                    };
                })
                .toList();
    }

    private static @NonNull IdentifierMatchableCondition mapFieldSpecToIdentifierMatchableCondition(UUID recordId, FieldSpec fieldSpec) {
        FieldTypeId fieldTypeId = null;
        if (fieldSpec.isFieldTypeSpecified()) {
            Assert.state(fieldSpec.existingFieldType().getLevel() != FieldTypeId.LEVEL_SUBSUBFIELD, () -> "Log to tell you that level subsubfield of specific field type id is used! specificFieldTypeId=%s".formatted(fieldSpec.existingFieldType()));
            fieldTypeId = fieldSpec.existingFieldType();
        }

        FieldId fieldId = null;
        if (fieldSpec.isFieldIdSpecified()) {
            Assert.state(fieldSpec.existingFieldId().getLevel() != FieldId.LEVEL_SUBSUBFIELD, () -> "Log to tell you that level subsubfield of specific field id is used! specificFieldId=%s".formatted(fieldSpec.existingFieldId()));
            fieldId = fieldSpec.existingFieldId();
        }

        MultifieldId multifieldId = null;
        if (fieldSpec.isMultifieldIdSpecified()) {
            multifieldId = fieldSpec.existingMultifieldId();
        }

        if (fieldTypeId == null && fieldId == null && multifieldId == null) {
            throw new IllegalStateException("Nothing processed, because of empty fieldSpec " + fieldSpec);
        }

        return new IdentifierMatchableCondition(recordId, fieldId, fieldTypeId, multifieldId);
    }

    /// pouzivame takovato query
    ///  - with+unnest, abychom s vyhodouposlali array jako jediny parametr
    ///  - with+join (misto in), abychom vyuzili index u posgresu
    /// ```
    /// with wanted as (
    ///     select u.record_id, u.field_group_id
    ///     from unnest(
    ///             cast(array ['a1984aa6-57c0-4468-9296-892e2eb741d2','a1984aa6-57c0-4468-9296-892e2eb741d2'] as uuid[]),
    ///             array ['d1106#0.t','d245#0.a']
    ///     ) as u(record_id, field_group_id)
    /// )
    /// select record_field.*
    /// from record_field
    ///         join wanted on record_field.record_id = wanted.record_id and record_field.field_group_id = wanted.field_group_id
    /// where ((record_field.cache_valid = true) or (record_field.target_record_id <> record_field.record_id))
    /// ```
    private @NonNull List<SelectQuery> buildIdentifierMatchableConditionQueries(List<IdentifierMatchableCondition> identifierMatchableConditions) {
        ListCutter<IdentifierMatchableCondition> conditionsCutter = ListCutter.ofCopyOf(identifierMatchableConditions);

        List<SelectQuery> queries = new ArrayList<>(4);

        if (useCteQueries()) {
            List<IdentifierMatchableCondition> wholeRecordConditions = conditionsCutter.cut(IdentifierMatchableCondition::isWholeRecord);
            if (!wholeRecordConditions.isEmpty()) {
                List<UUID> recordIds = ListUtil.convertStrict(wholeRecordConditions, IdentifierMatchableCondition::recordId);
                SelectQuery sq = createSelect();
                sq.with("wanted", cteQuery -> {
                    cteQuery.select(AS(unnest(cast(cteQuery.nextParamPlaceholder(recordIds.toArray(new UUID[0])), "uuid[]")), "record_id"));
                });
                sq.joins().add("wanted", COLSEQ(TC(TABLE, RECORD_ID), TC("wanted", "record_id")));
                queries.add(sq);
            }

            List<IdentifierMatchableCondition> fieldIddConditions = conditionsCutter.cut(condition -> condition.fieldId != null);
            if (!fieldIddConditions.isEmpty()) {
                List<UUID> recordIds = ListUtil.convertStrict(fieldIddConditions, IdentifierMatchableCondition::recordId);
                List<String> fieldIds = ListUtil.convertStrict(fieldIddConditions, condition -> fieldIdToDbFieldId(condition.fieldId));
                SelectQuery sq = createSelect();
                sq.with("wanted", cteQuery -> {
                    cteQuery.select("u.record_id", "u.field_id");
                    cteQuery.from(AS(unnest(
                            cast(cteQuery.nextParamPlaceholder(recordIds.toArray(new UUID[0])), "uuid[]"),
                            cteQuery.nextParamPlaceholder(fieldIds.toArray(new String[0]))
                    ), "u(record_id, field_id)"));
                });
                sq.joins().add("wanted", and(
                        COLSEQ(TC(TABLE, RECORD_ID), TC("wanted", "record_id")),
                        COLSEQ(TC(TABLE, FIELD_ID), TC("wanted", "field_id"))
                ));
                queries.add(sq);
            }

            List<IdentifierMatchableCondition> fieldTypedConditions = conditionsCutter.cut(condition -> condition.fieldTypeId != null);
            if (!fieldTypedConditions.isEmpty()) {
                List<UUID> recordIds = ListUtil.convertStrict(fieldTypedConditions, IdentifierMatchableCondition::recordId);
                List<String> fieldTypeIds = ListUtil.convertStrict(fieldTypedConditions, condition -> fieldTypeIdToDbFieldTypeId(condition.fieldTypeId));
                SelectQuery sq = createSelect();
                sq.with("wanted", cteQuery -> {
                    cteQuery.select("u.record_id", "u.field_type_id");
                    cteQuery.from(AS(unnest(
                            cast(cteQuery.nextParamPlaceholder(recordIds.toArray(new UUID[0])), "uuid[]"),
                            cteQuery.nextParamPlaceholder(fieldTypeIds.toArray(new String[0]))
                    ), "u(record_id, field_type_id)"));
                });
                sq.joins().add("wanted", and(
                        COLSEQ(TC(TABLE, RECORD_ID), TC("wanted", "record_id")),
                        COLSEQ(TC(TABLE, FIELD_TYPE_ID), TC("wanted", "field_type_id"))
                ));
                queries.add(sq);
            }

            List<IdentifierMatchableCondition> fieldGroupedConditions = conditionsCutter.cut(condition -> condition.fieldGroupId != null);
            if (!fieldGroupedConditions.isEmpty()) {
                List<UUID> recordIds = ListUtil.convertStrict(fieldGroupedConditions, IdentifierMatchableCondition::recordId);
                List<String> fieldGroupIds = ListUtil.convertStrict(fieldGroupedConditions, condition -> fieldGroupIdToDbFieldGroupId(condition.fieldGroupId));
                SelectQuery sq = createSelect();
                sq.with("wanted", cteQuery -> {
                    cteQuery.select("u.record_id", "u.field_group_id");
                    cteQuery.from(AS(unnest(
                            cast(cteQuery.nextParamPlaceholder(recordIds.toArray(new UUID[0])), "uuid[]"),
                            cteQuery.nextParamPlaceholder(fieldGroupIds.toArray(new String[0]))
                    ), "u(record_id, field_group_id)"));
                });
                sq.joins().add("wanted", and(
                        COLSEQ(TC(TABLE, RECORD_ID), TC("wanted", "record_id")),
                        COLSEQ(TC(TABLE, FIELD_GROUP_ID), TC("wanted", "field_group_id"))
                ));
                queries.add(sq);
            }

        } else {
            List<IdentifierMatchableCondition> wholeRecordConditions = conditionsCutter.cut(IdentifierMatchableCondition::isWholeRecord);
            if (!wholeRecordConditions.isEmpty()) {
                List<UUID> recordIds = ListUtil.convertStrict(wholeRecordConditions, IdentifierMatchableCondition::recordId);
                SelectQuery sq = createSelect();
                sq.where().and().in(TC(TABLE, RECORD_ID), recordIds);
                queries.add(sq);
            }

            List<IdentifierMatchableCondition> fieldIddConditions = conditionsCutter.cut(condition -> condition.fieldId != null);
            if (!fieldIddConditions.isEmpty()) {
                SelectQuery sq = createSelect();
                Brackets brackets = sq.where().and().brackets();
                for (IdentifierMatchableCondition fieldIddCondition : fieldIddConditions) {
                    Brackets fieldCondition = brackets.or().brackets();
                    fillRecordId(fieldIddCondition.recordId, fieldCondition, TABLE);
                    fillFieldId(fieldIddCondition.fieldId, fieldCondition, TABLE);
                }
                queries.add(sq);
            }

            List<IdentifierMatchableCondition> fieldTypedConditions = conditionsCutter.cut(condition -> condition.fieldTypeId != null);
            if (!fieldTypedConditions.isEmpty()) {
                SelectQuery sq = createSelect();
                Brackets brackets = sq.where().and().brackets();
                for (IdentifierMatchableCondition fieldTypedCondition : fieldTypedConditions) {
                    Brackets fieldCondition = brackets.or().brackets();
                    fillRecordId(fieldTypedCondition.recordId, fieldCondition, TABLE);
                    fillFieldTypeId(fieldTypedCondition.fieldTypeId, fieldCondition, TABLE);
                }
                queries.add(sq);
            }

            List<IdentifierMatchableCondition> fieldGroupedConditions = conditionsCutter.cut(condition -> condition.fieldGroupId != null);
            if (!fieldGroupedConditions.isEmpty()) {
                SelectQuery sq = createSelect();
                Brackets brackets = sq.where().and().brackets();
                for (IdentifierMatchableCondition fieldGroupedCondition : fieldGroupedConditions) {
                    Brackets fieldCondition = brackets.or().brackets();
                    fillRecordId(fieldGroupedCondition.recordId, fieldCondition, TABLE);
                    fillFieldGroupId(fieldGroupedCondition.fieldGroupId, fieldCondition, TABLE);
                }
                queries.add(sq);
            }
        }

        conditionsCutter.assertEmpty();
        return queries;
    }

    private boolean useCteQueries() {
        return queryFactory.getDbSpecifics().supportsArrays() && queryFactory.getDbSpecifics().supportsUnnestFunction();
    }

    private @NonNull SelectQuery buildLinkingFieldedSpecQuery(RecordSpec linkingRecordSpecs) {
        SelectQuery sq = createSelect();

        fillRecordMatchingConditions(sq.where(), (LinkingRecordRecordMatcher) linkingRecordSpecs.recordMatcher());

        switch (linkingRecordSpecs) {
            case WholeRecordSpec _ -> {
                // no condition adding
            }
            case FieldedRecordSpec fieldedRecordSpec -> {
                Brackets subbrackets = sq.where().and().brackets();
                for (FieldSpec fieldSpec : fieldedRecordSpec.existingFields()) {
                    Brackets fieldCondition = subbrackets.or().brackets();
                    fillFieldSpecBrackets(fieldCondition, fieldSpec);
                }
            }
        }

        return sq;
    }

    private void fillRecordMatchingConditions(Brackets brackets, LinkingRecordRecordMatcher linkingRecordMatcher) {
        brackets.and().in(TC(TABLE, RECORD_ID), sq -> {
            String subqueryRecordFieldAlias = "subq_record_field";
            String subqueryRecordAlias = "subq_record";

            sq.select(RECORD_ID);
            sq.from(AS(TABLE, subqueryRecordFieldAlias));

            Where subqueryBrackets = sq.where();
            sq.joins().add(AS(RECORD.TABLE, subqueryRecordAlias), COLSEQ(TC(subqueryRecordAlias, RECORD.ID), TC(subqueryRecordFieldAlias, RECORD_ID)));
            subqueryBrackets
                    .and().eq(TC(subqueryRecordFieldAlias, TARGET_RECORD_ID), linkingRecordMatcher.linkingRecordsLink().id());

            if (linkingRecordMatcher.linkingRecordsFondIds() != null) {
                subqueryBrackets.and().in(TC(subqueryRecordAlias, RECORD.FOND_ID), linkingRecordMatcher.linkingRecordsFondIds());
            }

            fillFieldTypeId(linkingRecordMatcher.linkingRecordsLinkFieldTypeId(), subqueryBrackets, subqueryRecordFieldAlias);

            subqueryBrackets
                    .and().isNull(TC(subqueryRecordAlias, RECORD.DELETION_EVENT_ID))
                    .and().isNotNull(TC(subqueryRecordAlias, RECORD.ACTIVATION_EVENT_ID));
        });
    }

    private void fillFieldSpecBrackets(Brackets brackets, FieldSpec fieldSpec) {
        boolean atLeastOneProcessed = false;

        if (fieldSpec.isFieldIdSpecified()) {
            fillFieldId(fieldSpec.existingFieldId(), brackets, TABLE);
            atLeastOneProcessed = true;
        }
        if (fieldSpec.isFieldTypeSpecified()) {
            fillFieldTypeId(fieldSpec.existingFieldType(), brackets, TABLE);
            atLeastOneProcessed = true;
        }
        if (fieldSpec.isMultifieldIdSpecified()) {
            fillFieldGroupId(fieldSpec.existingMultifieldId(), brackets, TABLE);
            atLeastOneProcessed = true;
        }

        if (!atLeastOneProcessed) {
            throw new IllegalStateException("Nothing processed! Empty RecordFieldSpec!");
        }
    }

    private void fillRecordId(@NonNull UUID recordId, Brackets fieldBrackets, String tableAlias) {
        fieldBrackets.and().eq(TC(tableAlias, RECORD_ID), recordId);
    }

    private void fillFieldId(@NonNull FieldId fieldId, Brackets fieldBrackets, String tableAlias) {
        switch (fieldId.getLevel()) {
            case FieldTypeId.LEVEL_SUBSUBFIELD:
                // mozna se nepouziva
                log.warn("Log to tell you that level subsubfield of specific field id is used! fieldId={}", fieldId);
                fieldBrackets.and().eq(TC(tableAlias, FIELD_ID), fieldIdToDbFieldId(fieldId.existingParent()));
                break;
            default:
                fieldBrackets.and().eq(TC(tableAlias, FIELD_ID), fieldIdToDbFieldId(fieldId));
                break;
        }
    }

    private void fillFieldTypeId(@NonNull FieldTypeId fieldTypeId, Brackets fieldBrackets, String tableAlias) {
        switch (fieldTypeId.getLevel()) {
            case FieldTypeId.LEVEL_SUBSUBFIELD:
                // mozna se nepouziva
                log.warn("Log to tell you that level subsubfield of specific field type id is used! fieldTypeId={}", fieldTypeId);
                fieldBrackets.and().eq(TC(tableAlias, FIELD_TYPE_ID), fieldTypeIdToDbFieldTypeId(fieldTypeId.existingParent()));
                break;
            default:
                fieldBrackets.and().eq(TC(tableAlias, FIELD_TYPE_ID), fieldTypeIdToDbFieldTypeId(fieldTypeId));
                break;
        }
    }

    private void fillFieldGroupId(@NonNull MultifieldId multifieldId, Brackets fieldBrackets, String tableAlias) {
        fieldBrackets.and().eq(TC(tableAlias, FIELD_GROUP_ID), fieldGroupIdToDbFieldGroupId(multifieldId));
    }

    private record IdentifierMatchableCondition(
            @NonNull UUID recordId,
            @Nullable FieldId fieldId,
            @Nullable FieldTypeId fieldTypeId,
            @Nullable MultifieldId fieldGroupId
    ) {
        private IdentifierMatchableCondition {
            Assert.state((fieldId == null ? 0 : 1) + (fieldTypeId == null ? 0 : 1) + (fieldGroupId == null ? 0 : 1) <= 1, () -> "Max one of fieldId, fieldTypeId, multifieldId can be specified for now");
        }

        public boolean isWholeRecord() {
            return fieldId == null && fieldTypeId == null && fieldGroupId == null;
        }
    }

}
