package cz.kpsys.portaro.record.edit.authorityfield008;

import cz.kpsys.portaro.commons.util.ListUtil;

import java.util.List;


public class AuthorityField008EditFormData {
    private static final String DIRECT_OR_INDIRECT_GEOGRAPHIC_SUBDIVISION_LABEL = "06 Přímé nebo nepřímé geografické zpřesnění";
    private static final List<String> directOrIndirectGeographicSubdivision = List.of(
            "# = nepoužívá se geografické zpřesnění",
            "d = geografické zpřesnění - přímé",
            "i = geografické zpřesnění - nepřímé",
            "n = nelze použít",
            "| = kód se neuvádí"
    );

    private static final String ROMANIZATION_SCHEME_LABEL = "07 Transliterace";
    private static final List<String> romanizationScheme = List.of(
            "a = mezinárodní standard",
            "b = národní standard",
            "c = standard národní asociace knihoven",
            "d = standard národní knihovny nebo národní bibliografie",
            "e = lokální standard",
            "f = standard neznámého původu",
            "g = konvenční transliterace",
            "n = nelze použít",
            "| = kód se neuvádí"
    );

    private static final String LANGUAGE_OF_CATALOG_LABEL = "08 Jazyk katalogu";
    private static final List<String> languageOfCatalog = List.of(
            "# = není znám",
            "a = angličtina a francouzština",
            "e = pouze angličtina",
            "f = pouze francouzština",
            "| = kód se neuvádí"
    );

    private static final String KIND_OF_RECORD_LABEL = "09 Druh záznamu";
    private static final List<String> kindOfRecord = List.of(
            "a = autorizované záhlaví",
            "b = nesměrovaný odkaz",
            "c = směrovaný odkaz",
            "d = zpřesnění",
            "e = nedpisová položka",
            "f = autorizované záhlaví a zpřesnění",
            "g = odkaz a zpřesnění",
            "- = kód se neuvádí"
    );

    private static final String DESCRIPTIVE_CATALOGING_RULES_LABEL = "10 Pravidla";
    private static final List<String> descriptiveCatalogingRules = List.of(
            "a = starší pravidla",
            "b = AACR1",
            "c = AACR2",
            "d = kompatibilní s AACR2",
            "z = jiný",
            "n = nelze použít",
            "| = kód se neuvádí"
    );

    private static final String SUBJECT_HEADING_SYSTEM_THESAURUS_LABEL = "11 Heslář thesaurus";
    private static final List<String> subjectHeadingSystemThesaurus = List.of(
            "a = Library of Congress Subject Headings",
            "b = LC Subject Headings for Children's Literature",
            "c = Medical Subject Headings",
            "d = National Agricultural Library Subject Authority File",
            "k = Canadian Subject Headings",
            "n = nelze použít",
            "r = Art and Architecture Thesaurus",
            "s = Sears List of Subject Headings",
            "v = Répertoire de vedettes - matière",
            "z = jiný",
            "| = kód se neuvádí"
    );

    private static final String TYPE_OF_SERIES_LABEL = "12 Typ edice";
    private static final List<String> typeOfSeries = List.of(
            "a = monografická edice",
            "b = vícesvazková jednotka",
            "c = označení podobné edici",
            "n = nelze použít",
            "z = jiný",
            "| = kód se neuvádí"
    );

    private static final String NUMBERED_OR_UNNUMBERED_SERIES_LABEL = "13 Číslovaná nebo nečíslovaná edice";
    private static final List<String> numberedOrUnnumberedSeries = List.of(
            "a = číslovaná edice",
            "b = nečíslovaná edice",
            "c = změny číslování",
            "n = nelze použít",
            "| = kód se neuvádí"
    );


    private static final String HEADING_USE_MAIN_OR_ADDED_ENTRY_LABEL = "14 Použití jako hlavní-vedlejší záhlaví";
    private static final List<String> headingUseMainOrAddedEntry = List.of(
            "a = vhodné",
            "b = nevhodné",
            "| = kód se neuvádí"
    );

    private static final String HEADING_USE_SUBJECT_ADDED_ENTRY_LABEL  = "15 Použití jako vedlejší věcné záhlaví";
    private static final List<String> headingUseSubjectAddedEntry = List.of(
            "a = vhodné",
            "b = nevhodné",
            "| = kód se neuvádí"
    );

    private static final String HEADING_USE_SERIES_ADDED_ENTRY_LABEL = "16 Použití jako vedlejší záhlaví pro edici";
    private static final List<String> headingUseSeriesAddedEntry = List.of(
            "a = vhodné",
            "b = nevhodné",
            "| = kód se neuvádí"
    );

    private static final String TYPE_OF_SUBJECT_SUBDIVISION_LABEL = "17 Typ zpřesnění";
    private static final List<String> typeOfSubjectSubdivision = List.of(
            "a = tematický",
            "b = formální",
            "c = chronologický",
            "d = geografický",
            "e = jazyk",
            "n = nelze použít",
            "| = kód se neuvádí"
    );

    private static final String TYPE_OF_GOVERNMENT_AGENCY_LABEL = "28 Typ vládní agentury";
    private static final List<String> typeOfGovernmentAgency = List.of(
            "# = nejedná se o vládní publikaci",
            "a = autonomní nebo částečně autonomní",
            "c = působící ve více lokalitách",
            "f = federální/národní",
            "i = mezinárodní mezivládní",
            "l = lokální",
            "m = působící ve více státech",
            "o = vládní agentura neupřesněného typu",
            "s = státní, oblastní, teritoriální",
            "u = není známo zda se jedná o vládní agenturu",
            "z = jiný",
            "| = kód se neuvádí",
            "- = kód se neuvádí"
    );

    private static final String REFERENCE_EVALUATION_LABEL = "29 Hodnocení odkazu";
    private static final List<String> referenceEvaluation = List.of(
            "a = směrování konzistentní se záhlavím",
            "b = směrování nejsou nutně konzistentní",
            "n = nelze použít",
            "| = kód se neuvádí"
    );

    private static final String RECORD_UPDATE_IN_PROCESS_LABEL = "31 Probíhající aktualizace";
    private static final List<String> recordUpdateInProcess = List.of(
            "a = záznam může být použit",
            "b = probíhá aktualizace",
            "| = kód se neuvádí"
    );

    private static final String UNDIFFERENTIATED_PERSONAL_NAME_LABEL = "32 Rozlišitelné osobní jméno";
    private static final List<String> undifferentiatedPersonalName = List.of(
            "a = rozlišitelné osobní jméno",
            "b = nerozlišitelné osobní jméno",
            "n = nelze použít",
            "| = kód se neuvádí"
    );

    private static final String LEVEL_OF_ESTABLISHMENT_LABEL = "33 Úroveň autorizace";
    private static final List<String> levelOfEstablishment = List.of(
            "a = plně autorizovaný",
            "b = záložní záhlaví",
            "c = provizorní",
            "d = předběžný",
            "n = nelze použít",
            "| = kód se neuvádí"
    );

    private static final String MODIFIED_RECORD_LABEL = "38 Modifikace";
    private static final List<String> modifiedRecord = List.of(
            "# = není modifikován",
            "s = zkrácený",
            "x = chybějící znaky",
            "| = kód se neuvádí",
            "- = kód se neuvádí"
    );

    private static final String CATALOGING_SOURCE_LABEL = "39 Zdroj katalogizace";
    private static final List<String> catalogingSource = List.of(
            "# = národní bibliografická agentura",
            "c = program kooperační katalogizace",
            "d = jiný",
            "u = není znám",
            "| = kód se neuvádí",
            "- = kód se neuvádí"
    );


    private static AuthorityField008StaticCode parseCodeEntity(String rawValue) {
        return new AuthorityField008StaticCode(rawValue.substring(0, 1), rawValue.substring(4));
    }

    private static List<AuthorityField008StaticCode> parseCodeEntities(List<String> rawValues) {
        return ListUtil.convert(rawValues, AuthorityField008EditFormData::parseCodeEntity);
    }

    public static AuthorityField008Position getCatalogingSource() {
        return new AuthorityField008Position(CATALOGING_SOURCE_LABEL, parseCodeEntities(catalogingSource));
    }

    public static AuthorityField008Position getModifiedRecord() {
        return new AuthorityField008Position(MODIFIED_RECORD_LABEL, parseCodeEntities(modifiedRecord));
    }

    public static AuthorityField008Position getLevelOfEstablishment() {
        return new AuthorityField008Position(LEVEL_OF_ESTABLISHMENT_LABEL, parseCodeEntities(levelOfEstablishment));
    }

    public static AuthorityField008Position getUndifferentiatedPersonalName() {
        return new AuthorityField008Position(UNDIFFERENTIATED_PERSONAL_NAME_LABEL, parseCodeEntities(undifferentiatedPersonalName));
    }

    public static AuthorityField008Position getRecordUpdateInProcess() {
        return new AuthorityField008Position(RECORD_UPDATE_IN_PROCESS_LABEL, parseCodeEntities(recordUpdateInProcess));
    }

    public static AuthorityField008Position getReferenceEvaluation() {
        return  new AuthorityField008Position(REFERENCE_EVALUATION_LABEL, parseCodeEntities(referenceEvaluation));
    }

    public static AuthorityField008Position getTypeOfGovernmentAgency() {
        return new AuthorityField008Position(TYPE_OF_GOVERNMENT_AGENCY_LABEL, parseCodeEntities(typeOfGovernmentAgency));
    }

    public static AuthorityField008Position getTypeOfSubjectSubdivision() {
        return new AuthorityField008Position(TYPE_OF_SUBJECT_SUBDIVISION_LABEL, parseCodeEntities(typeOfSubjectSubdivision));
    }

    public static AuthorityField008Position getHeadingUseSeriesAddedEntry() {
        return new AuthorityField008Position(HEADING_USE_SERIES_ADDED_ENTRY_LABEL, parseCodeEntities(headingUseSeriesAddedEntry));
    }

    public static AuthorityField008Position getHeadingUseSubjectAddedEntry() {
        return new AuthorityField008Position(HEADING_USE_SUBJECT_ADDED_ENTRY_LABEL, parseCodeEntities(headingUseSubjectAddedEntry));
    }

    public static AuthorityField008Position getHeadingUseMainOrAddedEntry() {
        return new AuthorityField008Position(HEADING_USE_MAIN_OR_ADDED_ENTRY_LABEL, parseCodeEntities(headingUseMainOrAddedEntry));
    }

    public static AuthorityField008Position getNumberedOrUnnumberedSeries() {
        return new AuthorityField008Position(NUMBERED_OR_UNNUMBERED_SERIES_LABEL, parseCodeEntities(numberedOrUnnumberedSeries));
    }

    public static AuthorityField008Position getTypeOfSeries() {
        return new AuthorityField008Position(TYPE_OF_SERIES_LABEL, parseCodeEntities(typeOfSeries));
    }

    public static AuthorityField008Position getSubjectHeadingSystemThesaurus() {
        return new AuthorityField008Position(SUBJECT_HEADING_SYSTEM_THESAURUS_LABEL, parseCodeEntities(subjectHeadingSystemThesaurus));
    }

    public static AuthorityField008Position getDescriptiveCatalogingRules() {
        return new AuthorityField008Position(DESCRIPTIVE_CATALOGING_RULES_LABEL, parseCodeEntities(descriptiveCatalogingRules));
    }

    public static AuthorityField008Position getKindOfRecord() {
        return new AuthorityField008Position(KIND_OF_RECORD_LABEL, parseCodeEntities(kindOfRecord));
    }

    public static AuthorityField008Position getLanguageOfCatalog() {
        return new AuthorityField008Position(LANGUAGE_OF_CATALOG_LABEL, parseCodeEntities(languageOfCatalog));
    }

    public static AuthorityField008Position getRomanizationScheme() {
        return new AuthorityField008Position(ROMANIZATION_SCHEME_LABEL, parseCodeEntities(romanizationScheme));
    }

    public static AuthorityField008Position getDirectOrIndirectGeographicSubdivision() {
        return new AuthorityField008Position(DIRECT_OR_INDIRECT_GEOGRAPHIC_SUBDIVISION_LABEL, parseCodeEntities(directOrIndirectGeographicSubdivision));
    }

}
