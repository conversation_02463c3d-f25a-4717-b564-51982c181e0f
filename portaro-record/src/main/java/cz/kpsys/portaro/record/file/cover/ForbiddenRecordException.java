package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.commons.object.SeveritedException;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ForbiddenRecordException extends RuntimeException implements UserFriendlyException, SeveritedException {

    public ForbiddenRecordException(@NonNull Record record) {
        super("Record " + record + " is forbidden");
    }

    @Override
    public Text getText() {
        return Texts.ofNative("Záznam je zak<PERSON>zaný");
    }

    @Override
    public int getSeverity() {
        return SEVERITY_WARNING;
    }
}
