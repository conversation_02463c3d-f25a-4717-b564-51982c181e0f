package cz.kpsys.portaro.record.detail.link;

import lombok.NonNull;

public record CustomEntrySubfieldLinkedFieldSpec(

        @NonNull
        String customEntrySubfieldCode

) implements LinkedFieldSpec {

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof CustomEntrySubfieldLinkedFieldSpec that)) {
            return false;
        }
        return customEntrySubfieldCode.equals(that.customEntrySubfieldCode);
    }

    @Override
    public int hashCode() {
        return customEntrySubfieldCode.hashCode();
    }

    @Override
    public String toString() {
        return "{entryTopfield}." + customEntrySubfieldCode;
    }
}
