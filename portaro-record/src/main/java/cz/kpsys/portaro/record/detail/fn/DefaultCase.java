package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import lombok.NonNull;

import java.util.function.Function;

public record DefaultCase<T extends ScalarFieldValue<?>>(
    @NonNull Formula<T> operand
) implements Case<T>, UnaryFunction<T> {

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<T>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        return operandResultDatatypeResolver.apply(operand);
    }

    @Override
    public @NonNull FormulaEvaluation<T> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {
        FormulaEvaluation<T> resolvedOperandValue = evaluator.resolveFormulaValue(sourceNode, operand());
        if (resolvedOperandValue.isFailure()) {
            return resolvedOperandValue.castedFailed();
        }

        return resolvedOperandValue;
    }
}