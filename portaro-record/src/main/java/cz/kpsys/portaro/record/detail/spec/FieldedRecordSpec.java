package cz.kpsys.portaro.record.detail.spec;

import lombok.NonNull;

import java.util.Set;

public sealed interface FieldedRecordSpec extends RecordSpec permits GenericRecordSpec, RecordFieldId {

    @NonNull Set<FieldSpec> existingFields();

    /// Creates a new record spec without given fieldSpec. Does not matter if this contains a given one or not
    default FieldedRecordSpec minus(@NonNull FieldedRecordSpec other) {
        if (other.covers(this)) {
            return RecordSpec.ofEmptyFielded(recordMatcher());
        }
        if (!isOfSameRecordMatcher(other)) {
            return this;
        }
        // create subset
        return minus(other.fieldsSpec());
    }

    default FieldedRecordSpec minus(@NonNull FieldsSpec other) {
        FieldsSpec fieldsResult = fieldsSpec().minus(other);
        return withFieldsSpec(fieldsResult);
    }
}
