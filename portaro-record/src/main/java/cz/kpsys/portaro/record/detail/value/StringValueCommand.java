package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import jakarta.validation.constraints.NotBlank;
import lombok.NonNull;

public record StringValueCommand(

        @NotBlank
        @NonNull
        String value,

        @NonNull
        Department ctx,

        @NonNull
        UserAuthentication currentAuth

) implements ScalarFieldValueCommand<String> {

    @Override
    public boolean equals(Object o) {
        return o instanceof StringValueCommand that && ctx.equals(that.ctx) && value.equals(that.value) && currentAuth.equals(that.currentAuth);
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    @Override
    public String toString() {
        return "StringValueCommand[" + value + ']';
    }
}

