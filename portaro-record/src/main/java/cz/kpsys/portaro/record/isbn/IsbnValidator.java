package cz.kpsys.portaro.record.isbn;

import lombok.NonNull;

public interface IsbnValidator {

    /**
     * Isbn bez pomlcek a bez tech textu v zavorkach na konci-
     */
    String REGEX_10_OR_13_WITHOUT_MINUSES = "^(978|979){0,1}[0-9]{9}[0-9xX]{1}$";
    String REGEX_10_OR_13_WITH_OPTIONAL_MINUSES = "^(?:ISBN(?:-1[03])?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){3})[- 0-9X]{13}$|97[89][0-9]{10}$|(?=(?:[0-9]+[- ]){4})[- 0-9X]{17}$)(?:97[89][- ]?)?[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$";

    static String normalize(String isbn) {
        if (isbn.indexOf('(') >= 0) {
            isbn = isbn.substring(0, isbn.indexOf('('));
        }
        return isbn.replace("-", "").toUpperCase().trim();
    }

    boolean isValid(@NonNull String isbn);

}
