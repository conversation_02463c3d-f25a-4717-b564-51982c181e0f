package cz.kpsys.portaro.record.search;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualFunction;
import cz.kpsys.portaro.commons.contextual.ContextualBiFunction;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.util.Assert;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class ConvertingStandardSearch<SOURCE_ITEM, TARGET_ITEM, PAGING extends Paging> extends AbstractStandardSearch<MapBackedParams, TARGET_ITEM, PAGING> implements Search<MapBackedParams, TARGET_ITEM, PAGING> {

    @NonNull AbstractStandardSearch<MapBackedParams, SOURCE_ITEM, PAGING> delegate;
    @NonNull ContextualFunction<@NonNull ? super List<SOURCE_ITEM>, Department, @NonNull List<TARGET_ITEM>> resultListConverter;

    public static <SOURCE_ITEM, TARGET_ITEM, PAGING extends Paging> ConvertingStandardSearch<SOURCE_ITEM, TARGET_ITEM, PAGING> of(@NonNull AbstractStandardSearch<MapBackedParams, SOURCE_ITEM, PAGING> delegate,
                                                                                                   @NonNull Converter<@NonNull ? super List<SOURCE_ITEM>, @NonNull List<TARGET_ITEM>> resultListConverter) {
        return new ConvertingStandardSearch<>(delegate, ContextIgnoringContextualFunction.ofNullableReturningFunction(resultListConverter::convert));
    }

    public static <SOURCE_ITEM, TARGET_ITEM, PAGING extends Paging> ConvertingStandardSearch<SOURCE_ITEM, TARGET_ITEM, PAGING> of(@NonNull AbstractStandardSearch<MapBackedParams, SOURCE_ITEM, PAGING> delegate,
                                                                                                   @NonNull ContextualFunction<@NonNull ? super List<SOURCE_ITEM>, Department, @NonNull List<TARGET_ITEM>> resultListConverter) {
        return new ConvertingStandardSearch<>(delegate, resultListConverter);
    }

    public static <SOURCE_ITEM, TARGET_ITEM, PAGING extends Paging> ConvertingStandardSearch<SOURCE_ITEM, TARGET_ITEM, PAGING> ofAuthenticated(@NonNull AbstractStandardSearch<MapBackedParams, SOURCE_ITEM, PAGING> delegate,
                                                                                                                @NonNull ContextualBiFunction<@NonNull ? super List<SOURCE_ITEM>, UserAuthentication, Department, @NonNull List<TARGET_ITEM>> resultListConverter,
                                                                                                                @NonNull UserAuthentication currentAuth) {
        return new ConvertingStandardSearch<>(delegate, (input, ctx) ->
            resultListConverter.getOn(input, currentAuth, ctx)
        );
    }

    @Override
    public InternalSearchResult<TARGET_ITEM, MapBackedParams, PAGING> loadResult(PAGING paging, SortingItem sorting, MapBackedParams completeParams, Department ctx, CacheMode cacheMode) {
        InternalSearchResult<SOURCE_ITEM, MapBackedParams, PAGING> originalResult = delegate.loadResult(paging, sorting, completeParams, ctx, cacheMode);
        List<TARGET_ITEM> convertedItems = resultListConverter.getOn(originalResult.content().getItems(), ctx);

        Assert.state(originalResult.content().size() == convertedItems.size(), () -> "Size of converted search items (%s) is not same as original (%s)".formatted(convertedItems.size(), originalResult.content().size()));

        return originalResult.withModifiedContent(originalResult.content().withItems(convertedItems));
    }

    @Override
    public void close() {
        delegate.close();
        super.close();
    }
}
