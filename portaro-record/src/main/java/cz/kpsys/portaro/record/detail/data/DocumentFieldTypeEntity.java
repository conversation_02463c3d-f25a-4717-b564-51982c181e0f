package cz.kpsys.portaro.record.detail.data;

import jakarta.persistence.AttributeOverride;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import static cz.kpsys.portaro.commons.object.Identified.PROPERTY_ID;
import static cz.kpsys.portaro.databasestructure.RecordDb.FDEF.FDEF;
import static cz.kpsys.portaro.databasestructure.RecordDb.FDEF.ID_FDEF;

@Entity
@Table(name = FDEF)
@AttributeOverride(name = PROPERTY_ID, column = @Column(name = ID_FDEF))
public final class DocumentFieldTypeEntity extends FieldTypeEntity {
}