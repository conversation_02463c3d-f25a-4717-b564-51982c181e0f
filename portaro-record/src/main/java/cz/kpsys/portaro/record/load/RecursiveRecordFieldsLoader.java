package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.commons.object.repo.DataAccessException;
import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.TimeMeter;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FondedIdentifiedFieldContainer;
import cz.kpsys.portaro.record.detail.IdentifiedFieldContainer;
import cz.kpsys.portaro.record.detail.spec.FieldSpec;
import cz.kpsys.portaro.record.detail.spec.FieldsSpec;
import cz.kpsys.portaro.record.detail.spec.RecordSpec;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RecursiveRecordFieldsLoader implements RecordFieldsLoader {

    @NonNull RecordFieldLoader<Field<?>> recordFieldLoader;
    @NonNull FieldTypesByFondLoader editableFieldTypesByFondLoader;
    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;

    @Override
    public List<IdentifiedFieldContainer> load(@NonNull List<RecordIdFondPair> recordIdFondPairs) {
        Loading loading = createForLoad(recordIdFondPairs);
        return loadAndAssemble(recordIdFondPairs, loading);
    }

    @Override
    public IdentifiedFieldContainer refresh(@NonNull Record record) {
        Loading loading = createForRefresh(record);
        List<IdentifiedFieldContainer> refresheds = loadAndAssemble(List.of(record.idFondPair()), loading);
        return DataUtils.requireSingle(refresheds, Record.class.getSimpleName(), "refreshed record detail");
    }

    private static @NonNull List<IdentifiedFieldContainer> loadAndAssemble(@NonNull List<RecordIdFondPair> recordIdFondPairs, Loading loading) {
        try {
            LoadedRecords loadedRecords = loading.load();

            List<IdentifiedFieldContainer> result = new ArrayList<>(recordIdFondPairs.size());
            for (RecordIdFondPair masterRecordIdFondPair : recordIdFondPairs) {
                Multifields loadedRecord = loadedRecords.getRecord(masterRecordIdFondPair);
                FondedIdentifiedFieldContainer detail = new RecordDetailAssembler(masterRecordIdFondPair, loadedRecord).assemble();
                result.add(detail);
            }

            return result;

        } catch (Exception e) {
            throw new DataAccessException("Cannot load record fields by %s: %s".formatted(recordIdFondPairs, e.getMessage()), "record fields", e);
        }
    }

    private @NonNull Loading createForLoad(@NonNull List<RecordIdFondPair> recordIdFondPairs) {
        RecordSpecSet originalSpecs = RecordSpecSet.ofCopyOf(ListUtil.convertStrict(recordIdFondPairs, RecordSpec::ofWholeRecord));
        RecordSpecSet loadedSpecs = RecordSpecSet.ofEmptyModifiable();
        LoadedRecords loadedRecords = createLoadedRecords(recordIdFondPairs);
        return new Loading(originalSpecs, loadedSpecs, loadedRecords);
    }

    private @NonNull Loading createForRefresh(@NonNull Record record) {
        RecordSpecSet originalSpecs = RecordSpecSet.of(RecordSpec.ofWholeRecord(record.idFondPair()));
        RecordSpecSet loadedSpecs = RecordSpecSet.ofCopyOf(originalSpecs);
        LoadedRecords loadedRecords = createLoadedRecords(Set.of(record.idFondPair()));
        loadedRecords.addLoadedFields(record.getFields());
        return new Loading(originalSpecs, loadedSpecs, loadedRecords);
    }

    private LoadedRecords createLoadedRecords(@NonNull Collection<RecordIdFondPair> primaryRecords) {
        LoadedRecords loadedRecords = LoadedRecords.empty(editableFieldTypesByFondLoader, recordEntryFieldTypeIdResolver);
        for (RecordIdFondPair primaryRecord : primaryRecords) {
            loadedRecords.addPrimary(primaryRecord);
        }
        return loadedRecords;
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private class Loading {

        public static final int MAX_ITERATIONS = 20;

        @NonFinal int loadingsCount = 0;
        @NonNull @NonFinal RecordSpecSet nextWantedSpecs = RecordSpecSet.ofEmptyUnmodifiable();
        @NonNull RecordSpecSet originalSpecs;
        @NonNull RecordSpecSet loadedSpecs;
        @NonNull LoadedRecords loadedRecords;

        public LoadedRecords load() {
            TimeMeter.measureAndLog(this::recursiveLoad, log, () -> "Recursive fields load of %s records in %s iterations (%s)".formatted(originalSpecs.size(), loadingsCount, originalSpecs), Duration.ofMillis(20), Duration.ofMillis(80));
            return loadedRecords;
        }

        private void recursiveLoad() {
            RecordSpecSet poorNextWantedSpecs = loadedRecords.updateLoadedAndGetNextSpecs(loadedSpecs);
            nextWantedSpecs = enhanceNextWantedSpecs(poorNextWantedSpecs);

            if (nextWantedSpecs.isEmpty()) {
                return;
            }

            loadingsCount++;

            if (loadingsCount > MAX_ITERATIONS) {
                throw new IllegalStateException("Reached maximum of iterations (%s) and some fields still not fully loaded. Residual specs to load = %s".formatted(MAX_ITERATIONS, nextWantedSpecs));
            }

            loadWantedSpecs();

            recursiveLoad();
        }

        private void loadWantedSpecs() {
            List<Field<?>> fields = recordFieldLoader.getAll(nextWantedSpecs);
            loadedSpecs.addAll(nextWantedSpecs);
            loadedRecords.addLoadedFields(fields);
        }

    }

    /// enhances all fields specs with a parent field type, so when spec is `7238c0:[d2400.a,d2400.j]`, enhanced will be `7238c0:[d2400,d2400.a,d2400.j]`
    public static @NonNull RecordSpecSet enhanceNextWantedSpecs(RecordSpecSet origRecordSpecs) {
        RecordSpecSet newRecordSpecs = RecordSpecSet.ofEmptyModifiable();
        for (RecordSpec origRecordSpec : origRecordSpecs) {
            if (origRecordSpec.fieldsSpec().isForAll()) {
                newRecordSpecs = newRecordSpecs.add(origRecordSpec);
            } else {
                FieldsSpec newFieldsSpec = FieldsSpec.ofEmpty();
                for (FieldSpec origSpec : origRecordSpec.fieldsSpec().existingSpecs()) {
                    newFieldsSpec = newFieldsSpec.add(origSpec);
                    if (origSpec.isFieldTypeSpecified() && !origSpec.existingFieldType().isRoot()) {
                        newFieldsSpec = newFieldsSpec.add(FieldSpec.ofFieldType(origSpec.existingFieldType().existingParent()));
                    }
                }
                newRecordSpecs = newRecordSpecs.add(origRecordSpec.withFieldsSpec(newFieldsSpec));
            }
        }
        return newRecordSpecs;
    }

}
