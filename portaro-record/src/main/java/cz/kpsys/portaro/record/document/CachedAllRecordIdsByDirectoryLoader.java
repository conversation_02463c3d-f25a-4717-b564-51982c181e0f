package cz.kpsys.portaro.record.document;

import cz.kpsys.portaro.commons.cache.GuavaTimedDynamicCache;
import cz.kpsys.portaro.commons.cache.SavingDynamicallyCachedAllByIdsLoader;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.file.directory.AllIdsByDirectoryProvider;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Duration;
import java.util.*;

import static cz.kpsys.portaro.commons.db.QueryUtils.TC;
import static cz.kpsys.portaro.databasestructure.RecordDb.RECORD.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CachedAllRecordIdsByDirectoryLoader implements AllIdsByDirectoryProvider {

    @NonNull SavingDynamicallyCachedAllByIdsLoader<DirectoryIdToRecordIds, Integer> cachedLoader;

    public CachedAllRecordIdsByDirectoryLoader(@NonNull NamedParameterJdbcOperations jdbcTemplate, @NonNull QueryFactory queryFactory, @NonNull List<UUID> forbiddenRecordIds, @NonNull List<Integer> forbiddenRecordStatuses) {
        AllByIdsLoadable<DirectoryIdToRecordIds, Integer> directoryIdRecordIdPairLoader = new SpringDbDirectoryIdRecordIdPairLoader(jdbcTemplate, queryFactory, forbiddenRecordIds, forbiddenRecordStatuses);
        this.cachedLoader = new SavingDynamicallyCachedAllByIdsLoader<>(
                new GuavaTimedDynamicCache<>(DirectoryIdToRecordIds::directoryId, Duration.ofSeconds(10), false),
                directoryIdRecordIdPairLoader,
                DirectoryIdToRecordIds::directoryId
        );
    }

    public record DirectoryIdToRecordIds(
            @NonNull Integer directoryId,
            @NonNull List<UUID> recordIds
    ) {}

    @Override
    public List<UUID> getAllByDirectories(@NonNull List<Integer> directoryIds) {
        if (directoryIds.isEmpty()) {
            return List.of();
        }

        List<DirectoryIdToRecordIds> pairs = cachedLoader.getAllByIds(directoryIds);

        return pairs.stream().flatMap(pair -> pair.recordIds().stream()).toList();
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class SpringDbDirectoryIdRecordIdPairLoader implements AllByIdsLoadable<DirectoryIdToRecordIds, Integer> {

        @NonNull NamedParameterJdbcOperations jdbcTemplate;
        @NonNull QueryFactory queryFactory;
        @NonNull List<UUID> forbiddenRecordIds;
        @NonNull List<Integer> forbiddenRecordStatuses;

        @Override
        public List<DirectoryIdToRecordIds> getAllByIds(@NonNull List<Integer> directoryIds) {
            SelectQuery sq = queryFactory.newSelectQuery();
            sq.select(DIRECTORY_ID, ID);
            sq.from(TABLE);
            sq.where().in(DIRECTORY_ID, directoryIds);
            if (ListUtil.hasLength(forbiddenRecordIds)) {
                sq.where().and().notIn(TC(TABLE, ID), forbiddenRecordIds);
            }
            if (ListUtil.hasLength(forbiddenRecordStatuses)) {
                sq.where().and().notIn(TC(TABLE, RECORD_STATUS_ID), forbiddenRecordStatuses);
            }
            return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), new ListResultSetExtractor(directoryIds));
        }


        @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
        @RequiredArgsConstructor
        private static class ListResultSetExtractor implements ResultSetExtractor<List<DirectoryIdToRecordIds>> {

            @NonNull List<Integer> directoryIds;

            @Override
            public List<DirectoryIdToRecordIds> extractData(ResultSet rs) throws SQLException, DataAccessException {
                Map<Integer, DirectoryIdToRecordIds> map = new HashMap<>();

                for (Integer directoryId : directoryIds) {
                    map.computeIfAbsent(directoryId, dirId -> new DirectoryIdToRecordIds(dirId, new ArrayList<>()));
                }

                while (rs.next()) {
                    Integer directoryId = DbUtils.getIntegerNotNull(rs, DIRECTORY_ID);
                    UUID recordId = DbUtils.uuidNotNull(rs, ID);
                    map.get(directoryId).recordIds.add(recordId);
                }

                return List.copyOf(map.values());
            }
        }
    }
}
