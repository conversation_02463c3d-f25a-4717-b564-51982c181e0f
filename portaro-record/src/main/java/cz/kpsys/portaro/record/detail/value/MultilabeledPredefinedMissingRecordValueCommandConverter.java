package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MultilabeledPredefinedMissingRecordValueCommandConverter implements TypedFieldValueConverter<MultilabeledPredefinedMissingRecordValueCommand, FieldPayload<StringFieldValue>, StringFieldValue> {

    @NonNull
    @Override
    public FieldPayload<StringFieldValue> convert(@NonNull MultilabeledPredefinedMissingRecordValueCommand command, @NonNull FieldType<?> fieldType, @NonNull RecordFieldId fieldId) {
        StringFieldValue valueHolder = StringFieldValue.of(
                getLabel(command),
                getText(command),
                Set.of(fieldId.recordIdFondPair())
        );
        return FieldPayload.ofMissingLink(valueHolder);
    }

    private String getLabel(@NonNull MultilabeledPredefinedMissingRecordValueCommand command) {
        Assert.state(!command.subfieldLabels().isEmpty(), "Trying to set multilabeled predefined missing record value command but with no subfield labels");
        return command.subfieldLabels().stream()
                .map(FieldLabel::value)
                .collect(Collectors.joining(" "));
    }

    private Text getText(@NonNull MultilabeledPredefinedMissingRecordValueCommand command) {
        if (command.subfieldLabels().isEmpty()) {
            return Texts.ofEmpty();
        }
        List<Text> texts = command.subfieldLabels().stream()
                .map(FieldLabel::value)
                .filter(StringUtil::hasLength)
                .map(Texts::ofNative)
                .toList();
        return MultiText.ofTexts(texts).withSpaceDelimiter();
    }

    @Override
    public String toString() {
        return getClass().getSimpleName();
    }

}
