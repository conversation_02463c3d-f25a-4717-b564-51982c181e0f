package cz.kpsys.portaro.record.fond;

import cz.kpsys.portaro.record.Record;

public class FondTypeResolver {
    
    public static boolean isDocumentFond(int fondId) {
        return fondId > 0 && fondId < 30;
    }
    
    public static boolean isAuthorityFond(int fondId) {
        return fondId > 30 && fondId <= 99; // 30 se nepouziva
    }
    
    public static boolean isFond(int number) {
        return isDocumentFond(number) || isAuthorityFond(number);
    }

    public static boolean isDocumentFond(Fond fond) {
        return fond.isOfDocument();
    }

    public static boolean isDocumentFond(Record record) {
        return record.getFond().isOfDocument();
    }

    public static boolean isAuthorityFond(Fond fond) {
        return fond.isOfAuthority();
    }

    public static boolean isAuthorityFond(Record record) {
        return record.getFond().isOfAuthority();
    }

    public static boolean isOfSubkind(Fond fond, String subkind) {
        return switch (subkind) {
            case Record.TYPE_DOCUMENT -> isDocumentFond(fond);
            case Record.TYPE_AUTHORITY -> isAuthorityFond(fond);
            default -> throw new IllegalStateException("Record subkind %s is not supported".formatted(subkind));
        };
    }
    
}
