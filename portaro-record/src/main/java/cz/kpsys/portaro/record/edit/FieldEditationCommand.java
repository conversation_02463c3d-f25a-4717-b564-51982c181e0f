package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import cz.kpsys.portaro.record.detail.value.FieldValueCommand;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.With;

@With(AccessLevel.PRIVATE)
public record FieldEditationCommand(

        @NonNull
        RecordFieldId recordFieldId,

        @With
        @NonNull
        FieldValueCommand valueCommand,

        boolean missingHierarchyCreating,

        boolean influencerFieldPreferring
) {

    public static FieldEditationCommand of(@NonNull RecordFieldId recordFieldId, @NonNull FieldValueCommand valueCommand) {
        return new FieldEditationCommand(recordFieldId, valueCommand, false, false);
    }

    public static FieldEditationCommand of(@NonNull RecordIdFondPair recordIdFondPair, @NonNull FieldId fieldId, @NonNull FieldValueCommand valueCommand) {
        return of(RecordFieldId.of(recordIdFondPair, fieldId), valueCommand);
    }

    public static FieldEditationCommand of(@NonNull Record record, @NonNull FieldId fieldId, @NonNull FieldValueCommand valueCommand) {
        return of(record.idFondPair(), fieldId, valueCommand);
    }

    public FieldEditationCommand createMissingHierarchy() {
        return withMissingHierarchyCreating(true);
    }

    public FieldEditationCommand notCreateMissingHierarchy() {
        return withMissingHierarchyCreating(false);
    }

    public FieldEditationCommand preferInfluencerFieldEditation() {
        return withInfluencerFieldPreferring(true);
    }

    public @NonNull FieldId fieldId() {
        return recordFieldId.fieldId();
    }
}
