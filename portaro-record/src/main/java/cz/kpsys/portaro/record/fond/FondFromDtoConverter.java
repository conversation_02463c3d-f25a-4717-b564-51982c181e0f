package cz.kpsys.portaro.record.fond;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.database.DtoToLabeledObjectConverter;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class FondFromDtoConverter extends DtoToLabeledObjectConverter<FondEntity, Fond, Integer> {

    public FondFromDtoConverter() {
        withTrimLabel();
    }

    @Override
    protected Fond create(Integer id, String name, FondEntity source) {
        return new Fond(
                id,
                name,
                source.getOrder(),
                source.getParentId(),
                source.getBinding(),
                source.isForAuthority(),
                source.isEnabled(),
                source.isEditable(),
                source.isPeriodical(),
                source.isRecordable(),
                FondType.CODEBOOK.getById(source.getFondTypeId()),
                ObjectUtil.elvis(source.getEntryFieldTypeId(), FieldTypeId::parse),
                source.getZ39Typ(),
                source.getDocumentCategories(),
                source.getDocumentTypes(),
                source.isWithExemplars(),
                source.getRecordType(),
                source.getBibliographicLevel(), source.isForSourceDocument(),
                source.isAdoptingLinked(),
                source.isAdoptingEdited()
        );
    }

}
