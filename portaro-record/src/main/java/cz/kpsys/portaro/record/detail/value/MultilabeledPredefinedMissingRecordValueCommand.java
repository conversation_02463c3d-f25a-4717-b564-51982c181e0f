package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.record.detail.FieldTypeId;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.util.List;

public record MultilabeledPredefinedMissingRecordValueCommand(

        @NonNull
        @NotEmpty
        List<FieldLabel> subfieldLabels

) implements RecordFieldValueCommand {

    public static MultilabeledPredefinedMissingRecordValueCommand ofSingle(@NonNull FieldTypeId fieldTypeId,
                                                                           @NonNull String value) {
        return new MultilabeledPredefinedMissingRecordValueCommand(List.of(new FieldLabel(fieldTypeId, value)));
    }

}
