package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.value.FieldValueCommand;
import cz.kpsys.portaro.record.edit.fieldshierarchy.FieldValueCommandResolver;
import cz.kpsys.portaro.record.edit.fieldshierarchy.SetFieldValueRequest;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.Delegate;
import lombok.experimental.FieldDefaults;

import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CustomInitialValuesFillingRecordEditationWrapper implements AutoSaveableRecordEditation {

    @Delegate
    @NonNull RecordEditation target;
    @NonNull ByIdLoadable<FieldType<?>, FieldTypeId> fieldTypeLoader;
    @NonNull RecordFieldEditor recordFieldEditor;
    @NonNull FieldValueCommandResolver fieldValueCommandResolver;

    public CustomInitialValuesFillingRecordEditationWrapper(@NonNull RecordEditation target,
                                                            @NonNull ByIdLoadable<FieldType<?>, FieldTypeId> fieldTypeLoader,
                                                            @NonNull RecordFieldEditor recordFieldEditor,
                                                            @NonNull FieldValueCommandResolver fieldValueCommandResolver,
                                                            @NonNull Department ctx,
                                                            @NonNull Map<String, SetFieldValueRequest> initialValues,
                                                            @NonNull UserAuthentication currentAuth) {
        this.target = target;
        this.fieldTypeLoader = fieldTypeLoader;
        this.recordFieldEditor = recordFieldEditor;
        this.fieldValueCommandResolver = fieldValueCommandResolver;
        this.fillWithInitialValues(initialValues, ctx, currentAuth);
    }


    private void fillWithInitialValues(Map<String, SetFieldValueRequest> initialValues, Department ctx, UserAuthentication currentAuth) {
        initialValues.forEach((rawFieldTypeId, fieldValue) -> {
            var fieldTypeId = FieldTypeId.parse(rawFieldTypeId);
            FieldType<?> fieldType = fieldTypeLoader.getById(fieldTypeId);
            FieldId fieldId = fieldTypeId.toFieldIdWithAllFirstIndices();

            FieldValueCommand valueCommand = fieldValueCommandResolver.resolveCommand(fieldValue, fieldType, ctx, currentAuth);
            FieldEditationCommand command = FieldEditationCommand.of(target.getRecord(), fieldId, valueCommand)
                    .createMissingHierarchy()
                    .preferInfluencerFieldEditation();
            recordFieldEditor.editField(target, command);
        });
    }
}
