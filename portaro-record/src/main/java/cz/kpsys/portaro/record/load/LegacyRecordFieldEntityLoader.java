package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.collection.ListCutter;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.TimeMeter;
import cz.kpsys.portaro.record.detail.FieldHelper;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.spec.*;
import cz.kpsys.portaro.sql.generator.Brackets;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import jakarta.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.RowMapperResultSetExtractor;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.util.Assert;

import java.time.Duration;
import java.util.*;

import static cz.kpsys.portaro.commons.db.QueryUtils.TC;
import static cz.kpsys.portaro.commons.db.QueryUtils.VALUE_AS;
import static cz.kpsys.portaro.databasestructure.RecordDb.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class LegacyRecordFieldEntityLoader implements RecordFieldLoader<LegacyRecordFieldEntity> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;

    public List<LegacyRecordFieldEntity> getAll(@NonNull RecordSpecSet specs) {
        if (specs.isEmpty()) {
            return List.of();
        }

        SelectQuery sq = queryFactory.newSelectQuery();
        fillQuery(sq, specs.specs());
        return TimeMeter.measureAndLog(() -> jdbcTemplate.query(sq.getSql(), sq.getParamMap(), createResultSetExtractor()), log, () -> "Load legacy record field entities (%s specs: %s)".formatted(specs.size(), specs), Duration.ofMillis(10), Duration.ofMillis(100));
    }

    protected void fillQuery(@NonNull SelectQuery sq, @NonNull @NotEmpty Collection<? extends RecordSpec> specs) {
        List<SelectQuery> unionSubqueries = new ArrayList<>(2);

        if (!specs.isEmpty()) {
            SelectQuery subquery = queryFactory.newSelectQuery();
            subquery.select(
                    TC(KAT1_1.TABLE, KAT1_1.ID),
                    TC(KAT1_1.TABLE, KAT1_1.RECORD_ID),
                    VALUE_AS(FieldTypeId.NEW_DOCUMENT_NUMBER_PREFIX, KATX_1_COMBINED.FIELD_NUMBER_PREFIX),
                    TC(KAT1_1.TABLE, KAT1_1.CIS_POL),
                    TC(KAT1_1.TABLE, KAT1_1.PORADI_POLE),
                    TC(KAT1_1.TABLE, KAT1_1.INDIK),
                    TC(KAT1_1.TABLE, KAT1_1.OBSAH),
                    TC(KAT1_1.TABLE, KAT1_1.DATCAS),
                    TC(KAT1_1.TABLE, KAT1_1.FK_UZIV)
            );
            subquery.from(KAT1_1.TABLE);
            addRecordSpecCondition(subquery.where().and().brackets(), specs, TC(KAT1_1.TABLE, KAT1_1.RECORD_ID), TC(KAT1_1.TABLE, KAT1_1.CIS_POL), TC(KAT1_1.TABLE, KAT1_1.PORADI_POLE));
            unionSubqueries.add(subquery);
        }

        if (!specs.isEmpty()) {
            SelectQuery subquery = queryFactory.newSelectQuery();
            subquery.select(
                    TC(KATAUT_1.TABLE, KATAUT_1.ID),
                    TC(KATAUT_1.TABLE, KATAUT_1.RECORD_ID),
                    VALUE_AS(FieldTypeId.NEW_AUTHORITY_NUMBER_PREFIX, KATX_1_COMBINED.FIELD_NUMBER_PREFIX),
                    TC(KATAUT_1.TABLE, KATAUT_1.CIS_POL),
                    TC(KATAUT_1.TABLE, KATAUT_1.PORADI_POLE),
                    TC(KATAUT_1.TABLE, KATAUT_1.INDIK),
                    TC(KATAUT_1.TABLE, KATAUT_1.OBSAH),
                    TC(KATAUT_1.TABLE, KATAUT_1.DATCAS),
                    TC(KATAUT_1.TABLE, KATAUT_1.FK_UZIV)
            );
            subquery.from(KATAUT_1.TABLE);
            addRecordSpecCondition(subquery.where().and().brackets(), specs, TC(KATAUT_1.TABLE, KATAUT_1.RECORD_ID), TC(KATAUT_1.TABLE, KATAUT_1.CIS_POL), TC(KATAUT_1.TABLE, KATAUT_1.PORADI_POLE));
            unionSubqueries.add(subquery);
        }

        sq.fromUnionAll(unionSubqueries, "combined");
    }

    private void addRecordSpecCondition(Brackets brackets, Collection<? extends RecordSpec> recordIdSpecs, String recordIdentifierColumn, String fieldCodeColumn, String fieldIndexColumn) {
        ListCutter<RecordSpec> specsCutter = ListCutter.ofCopyOf(recordIdSpecs);

        List<WholeRecordSpec> wholeRecordSpecs = specsCutter.cutCastable(WholeRecordSpec.class);
        if (!wholeRecordSpecs.isEmpty()) {
            brackets.in(recordIdentifierColumn, ListUtil.convertStrict(wholeRecordSpecs, WholeRecordSpec::existingRecordId));
        }

        List<FieldedRecordSpec> fieldedRecordSpecs = specsCutter.cutCastable(FieldedRecordSpec.class);
        Set<QueriedCondition> queriedConditions = new HashSet<>();
        for (FieldedRecordSpec recordSpec : fieldedRecordSpecs) {
            for (FieldSpec fieldSpec : recordSpec.existingFields()) {
                RecordIdentifierRecordMatcher recordMatcher = ObjectUtil.cast(recordSpec.recordMatcher(), RecordIdentifierRecordMatcher.class, () -> "recordMatcher must be of type %s, other variant is not supported in legacy record field loader".formatted(RecordIdentifierRecordMatcher.class.getSimpleName()));
                fillFieldSpecBrackets(brackets, fieldSpec, recordIdentifierColumn, recordMatcher.recordIdFondPair().id().id(), fieldCodeColumn, fieldIndexColumn, queriedConditions);
            }
        }

        specsCutter.assertEmpty();
    }

    private void fillFieldSpecBrackets(Brackets brackets, FieldSpec fieldSpec, String recordIdentifierColumn, UUID recordIdentifierValue, String fieldCodeColumn, String fieldIndexColumn, Set<QueriedCondition> queriedConditions) {
        if (fieldSpec.fieldTypeId() != null) { // loading of specific subfield -> fields are grouped in topfields, so we have to load whole parent topfields
            addFieldTypeIdCondition(brackets, recordIdentifierColumn, recordIdentifierValue, fieldCodeColumn, queriedConditions, fieldSpec.fieldTypeId());
        }

        if (fieldSpec.isMultifieldIdSpecified()) {
            MultifieldId multifieldId = fieldSpec.existingMultifieldId();
            addFieldTypeIdCondition(brackets, recordIdentifierColumn, recordIdentifierValue, fieldCodeColumn, queriedConditions, multifieldId.fieldTypeId());

            if (multifieldId.hasParentFieldId()) {
                addParentFieldIdCondition(brackets, recordIdentifierColumn, recordIdentifierValue, fieldCodeColumn, fieldIndexColumn, queriedConditions, multifieldId.existingParentFieldId());
            }
        }
    }

    private static void addFieldTypeIdCondition(Brackets brackets, String recordIdentifierColumn, UUID recordIdentifierValue, String fieldCodeColumn, Set<QueriedCondition> queriedConditions, @NonNull FieldTypeId fieldTypeId) {
        FieldTypeId topfieldTypeId = switch (fieldTypeId.getLevel()) {
            case FieldTypeId.LEVEL_TOPFIELD -> fieldTypeId;
            case FieldTypeId.LEVEL_SUBFIELD -> fieldTypeId.existingParent();
            case FieldTypeId.LEVEL_SUBSUBFIELD -> fieldTypeId.existingParent().existingParent(); // situace, kdy je hledane pole komplexni autoritou (napr. specificFieldTypeId = D.1610.main.a) -> pak musime hledat rodice jeho rodice
            default -> throw new IllegalStateException("fieldSpec.specificFieldTypeId must be of level %s, other variant is not supported (fieldSpec.fieldTypeId=%s, level=%s)".formatted(FieldTypeId.LEVEL_SUBFIELD, fieldTypeId, fieldTypeId.getLevel()));
        };

        int topfieldNumber = FieldHelper.fieldCodeToNumber(topfieldTypeId.getCode());

        QueriedCondition queriedCondition = new QueriedCondition(recordIdentifierColumn, recordIdentifierValue, fieldCodeColumn, topfieldNumber);
        if (queriedConditions.add(queriedCondition)) {
            Brackets fieldBrackets = brackets.or().brackets();
            fieldBrackets
                    .and().eq(recordIdentifierColumn, recordIdentifierValue)
                    .and().eq(fieldCodeColumn, topfieldNumber);
        }
    }

    private static void addParentFieldIdCondition(Brackets brackets, String recordIdentifierColumn, UUID recordIdentifierValue, String fieldCodeColumn, String fieldIndexColumn, Set<QueriedCondition> queriedConditions, @NonNull FieldId parentFieldId) {
        Assert.state(parentFieldId.getLevel() == FieldId.LEVEL_TOPFIELD, () -> "fieldSpec.multifieldId.parentFieldId must be of level %s, other variant is not supported".formatted(FieldId.LEVEL_TOPFIELD));
        String topfieldCode = parentFieldId.getCode();
        int topfieldNumber = FieldHelper.fieldCodeToNumber(topfieldCode);
        int topfieldIndex = parentFieldId.getRepetition();

        QueriedCondition queriedCondition = new QueriedCondition(recordIdentifierColumn, recordIdentifierValue, fieldCodeColumn, topfieldNumber);
        if (!queriedConditions.contains(queriedCondition)) { // not adding to set, because of additional field index condition
            Brackets fieldBrackets = brackets.or().brackets();
            fieldBrackets
                    .and().eq(recordIdentifierColumn, recordIdentifierValue)
                    .and().eq(fieldCodeColumn, topfieldNumber)
                    .and().eq(fieldIndexColumn, topfieldIndex);
        }
    }

    private ResultSetExtractor<List<LegacyRecordFieldEntity>> createResultSetExtractor() {
        return new RowMapperResultSetExtractor<>(new LegacyRecordFieldEntityRowMapper());
    }

    private record QueriedCondition(
            @NonNull String recordIdentifierColumn,
            @NonNull UUID recordIdentifierValue,
            @NonNull String fieldCodeColumn,
            @NonNull Integer topfieldNumber
    ) {}

}
