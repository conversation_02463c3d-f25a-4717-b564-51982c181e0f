package cz.kpsys.portaro.record.search;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SearchServiceBackedSearch<ITEM> extends AbstractStandardSearch<MapBackedParams, ITEM, RangePaging> implements Search<MapBackedParams, ITEM, RangePaging> {

    @NonNull SearchService<InternalSearchResult<ITEM, MapBackedParams, RangePaging>, MapBackedParams> service;
    @NonFinal boolean isFirstSearch = true;

    @Override
    public InternalSearchResult<ITEM, MapBackedParams, RangePaging> loadResult(RangePaging paging, SortingItem sorting, MapBackedParams completeParams, Department ctx, CacheMode cache) {
        //samotne vyhledani
        InternalSearchResult<ITEM, MapBackedParams, RangePaging> result = service.search(completeParams, paging.range(), sorting, ctx, cache);

        if (isFirstSearch && log.isInfoEnabled()) {
            log.info("Searched {} and found {} items", completeParams, result.totalElements());
        }

        isFirstSearch = false;

        return result;
    }
}
