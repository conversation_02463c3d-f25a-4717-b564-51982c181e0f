package cz.kpsys.portaro.record.edit;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class EditedRecordDetail implements CreatableFieldContainer, FieldTypeListable<EditableFieldType<?>> {

    @NonNull RecordFieldTypesLoader missingAddingRecordEditableFieldTypesLoader;
    @NonNull FieldTypesByFondLoader unknownSupportingEditableTopfieldTypeLoader;
    @NonNull Record record;
    @NonNull Map<FieldTypeId, EditableFieldType<?>> editedFieldTypeCache = new ConcurrentHashMap<>();
    @NonFinal volatile boolean cacheInvalid = true;

    @Override
    public List<Field<?>> getFields() {
        return record.getFields();
    }

    @Override
    public @NonNull Stream<Field<?>> streamFields() {
        return record.streamFields();
    }

    public void replaceAllFields(FieldContainer newFields) {
        record.setDetail(newFields);
    }

    @Override
    public Field<?> createField(EmptyFieldCreation command) {
        EditableFieldType<?> subfieldType = getSubfieldTypeOrParentVirtualGroupTypeFor(command.fieldTypeId());

        Field<?> subfield = subfieldType.createEmptyFieldByParentId(record.idFondPair(), null, command.id());
        record.getDetail().add(subfield);

        if (command.repetition().isPresent()) {
            while (command.repetition().get() != subfield.getRepetition()) {
                FieldMovementCommand movementCommand = subfield.getRepetition() < command.repetition().get()
                        ? FieldMovementCommand.down(subfield.getFieldId())
                        : FieldMovementCommand.up(subfield.getFieldId());
                new RecordFieldMover().moveField(record.getDetail(), movementCommand);
            }
        }

        return subfield;
    }


    @NonNull
    @Override
    public EditableFieldType<?> getSubfieldTypeFor(@NonNull FieldTypeId fieldTypeId) {
        synchronized (editedFieldTypeCache) {
            if (cacheInvalid) {
                refreshCache();
            }
            EditableFieldType<?> fieldType = editedFieldTypeCache.get(fieldTypeId);
            if (fieldType != null) {
                return fieldType;
            }
            return unknownSupportingEditableTopfieldTypeLoader.getTopfieldTypeByFondAndIdOrUnknown(record.getFond(), fieldTypeId);
        }
    }


    @NonNull
    @Override
    public List<? extends EditableFieldType<?>> getSubfieldTypes() {
        return missingAddingRecordEditableFieldTypesLoader.getFieldTypesByRecord(record);
    }


    public boolean changeFond(Fond fond) {
        if (record.getFond().equals(fond)) {
            return false;
        }
        record.setFond(fond);
        refreshCache();
        deleteEmptyFields();
        return true;
    }


    private void refreshCache() {
        synchronized (editedFieldTypeCache) {
            editedFieldTypeCache.clear();
            var map = getSubfieldTypes().stream().collect(Collectors.toMap(FieldType::getFieldTypeId, Function.identity()));
            editedFieldTypeCache.putAll(map);
            cacheInvalid = false;
        }
    }


    private void deleteEmptyFields() {
        deleteEmptyFields(record.getDetail().getFields());
    }

    private void deleteEmptyFields(List<Field<?>> fields) {
        for (int i = fields.size() - 1; i >= 0; i--) { //musime pres i-loop kvuli concurent modification exc
            Field<?> field = fields.get(i);
            deleteEmptyFields(field.getFields());
            //pokud zbyde prazdne pole (nebo je to prazdne kontrolni pole), smazem i to
            if (field.isEmpty()) {
                record.getDetail().remove(field);
            }
        }
    }

    @Override
    public String toString() {
        return "EditedRecordDetail of record %s".formatted(record);
    }

    @JsonIgnore
    @Override
    public EditedRecordDetail copy(@NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId) {
        ObjectUtil.assertCalledClassIsExactly(this, EditedRecordDetail.class);
        return new EditedRecordDetail(
                missingAddingRecordEditableFieldTypesLoader,
                unknownSupportingEditableTopfieldTypeLoader,
                record.copy(newRecordIdFondPair, kindedId)
        );
    }
}
