package cz.kpsys.portaro.record.file;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.LoadedFile;
import cz.kpsys.portaro.file.Resource;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.security.crud.CommandWithAuth;
import lombok.NonNull;

/// @param setAsPrimaryFile - whether to set this file as primary file (cover)
public record RecordWithAttachmentSaveCommand(

        @NonNull Record record,

        @NonNull Resource file,

        boolean setAsPrimaryFile,

        @NonNull UserAuthentication currentAuth,

        @NonNull Department ctx

) implements CommandWithAuth {

    public static RecordWithAttachmentSaveCommand ofCover(Record record, Resource file, UserAuthentication currentAuth, Department ctx) {
        return new RecordWithAttachmentSaveCommand(record, file, true, currentAuth, ctx);
    }

    public static RecordWithAttachmentSaveCommand ofLoadedCover(Record record, LoadedFile file, UserAuthentication currentAuth, Department ctx) {
        return new RecordWithAttachmentSaveCommand(record, file, true, currentAuth, ctx);
    }

    public static RecordWithAttachmentSaveCommand ofLoadedFile(Record record, LoadedFile file, UserAuthentication currentAuth, Department ctx) {
        return new RecordWithAttachmentSaveCommand(record, file, false, currentAuth, ctx);
    }

}
