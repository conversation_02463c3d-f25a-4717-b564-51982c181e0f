package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.department.Department;
import lombok.NonNull;

public record DateRangeValueCommand(

        @NonNull
        DateRange value,

        @NonNull
        Department ctx,

        @NonNull
        UserAuthentication currentAuth

) implements ScalarFieldValueCommand<DateRange> {

    @Override
    public boolean equals(Object o) {
        return o instanceof DateRangeValueCommand that && ctx.equals(that.ctx) && value.equals(that.value) && currentAuth.equals(that.currentAuth);
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    @Override
    public String toString() {
        return "DateRangeValueCommand[" + value + ']';
    }
}
