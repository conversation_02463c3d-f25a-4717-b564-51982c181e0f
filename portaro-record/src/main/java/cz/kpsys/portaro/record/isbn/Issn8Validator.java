package cz.kpsys.portaro.record.isbn;

import cz.kpsys.portaro.commons.util.StringUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Issn8Validator implements IsbnValidator {

    public static final int MIN_LENGTH = 8;

    /**
     * Issn with or without "-", so e.g. "2695-0103" or "26950103"
     */
    public static String REGEX = "^\\d{4}-?\\d{3}(\\d|x|X)$";

    @Override
    public boolean isValid(@NonNull String issn) {
        return StringUtil.hasTrimmedLength(issn) && issn.matches(REGEX);
    }
}
