package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * DAO trida pro nacitaci objektu typu Pole <br/> Vyuziva cachovani tak, ze pri
 * prvnim nacteni nacte data z databaze a ulozi do cache. Pote pracuje jen s
 * cache
 *
 * <AUTHOR>
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CodebookDelegatingFieldTypeLoader implements FieldTypeLoader {

    public static final Set<String> CONTROLFIELD_NUMBERS = Set.of(
            FieldTypes.AUTHORITY_LEADER_FIELD_CODE, "a1", "a2", "a3", "a4", "a5", "a6", "a7", "a8", "a9",
            FieldTypes.DOCUMENT_LEADER_FIELD_CODE, "d1", "d2", "d3", "d4", "d5", "d6", "d7", "d8", "d9"
    );

    @NonNull Codebook<FieldType<?>, String> codebook;
    @NonNull UnknownFieldTypeFactory unknownFieldTypeFactory;


    @Override
    public List<FieldType<?>> getAll() {
        return codebook.getAll();
    }

    @Override
    public FieldType<?> getSubfieldTypeById(FieldTypeId subfieldTypeId) {
        FieldType<?> fieldType = getTopfieldTypeById(subfieldTypeId.existingParent());
        if (!Objects.equals(fieldType.getName(), UnknownFieldTypeFactory.UNKNOWN_FIELD_NAME)) {
            return fieldType.getSubfieldTypeOrParentVirtualGroupTypeFor(subfieldTypeId);
        }
        //vratime nezname podpole
        return unknownFieldTypeFactory.createUnknownSubfieldType(subfieldTypeId);
    }

    @Override
    public FieldType<?> getTopfieldTypeById(FieldTypeId fieldTypeId) {
        return codebook.getById(fieldTypeId.toString());
    }


}
