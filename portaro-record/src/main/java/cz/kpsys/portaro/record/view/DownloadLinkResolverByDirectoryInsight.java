package cz.kpsys.portaro.record.view;

import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.web.Link;
import cz.kpsys.portaro.commons.web.StaticLink;
import cz.kpsys.portaro.file.directory.DirectoryInsight;
import cz.kpsys.portaro.file.directory.DirectoryInsightLoader;
import cz.kpsys.portaro.file.filecategory.FileCategory;
import cz.kpsys.portaro.file.filecategory.FileCategoryBySystemTypeLoader;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DownloadLinkResolverByDirectoryInsight<CTX> implements DownloadLinkContextualResolver<CTX> {

    @NonNull DirectoryInsightLoader directoryInsightLoader;
    @NonNull ContextualFunction<Record, CTX, @NullableNotBlank String> recordMediaViewerUrlGenerator;
    @NonNull FileCategoryBySystemTypeLoader fileCategoryBySystemTypeLoader;

    @Override
    public List<Link> resolveLinks(Record record, CTX ctx) {
        Integer directoryId = record.getDirectoryId();

        if (directoryId != null) {
            DirectoryInsight insight = directoryInsightLoader.getByDirectory(directoryId, List.of(), null, fileCategoryBySystemTypeLoader.allIdsByList(FileCategory.DOWNLOAD_LINK_FILE_CATEGORY_SYSTEM_TYPES));

            if (insight.containsNonCoverFiles() || insight.containsSubdirectories()) {
                String mediaViewerUrl = recordMediaViewerUrlGenerator.getOn(record, ctx);
                return List.of(new StaticLink(mediaViewerUrl, Texts.ofMessageCoded("commons.ZobrazitPlnyText")));
            }
        }

        return List.of();
    }
}
