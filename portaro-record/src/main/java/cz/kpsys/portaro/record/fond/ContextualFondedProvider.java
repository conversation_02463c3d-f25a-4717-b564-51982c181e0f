package cz.kpsys.portaro.record.fond;

import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.setting.FondedValues;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ContextualFondedProvider<CTX, OUT> implements ContextualFunction<Fond, CTX, OUT> {

    @NonNull ContextualProvider<CTX, @NonNull ? extends FondedValues<@NonNull ? extends OUT>> departmentedFondedRecordSearchSortingsLoader;
    @NonNull ContextualProvider<CTX, @NonNull OUT> authorityProvider;

    @Override
    public OUT getOn(Fond fond, CTX ctx) {
        FondedValues<? extends OUT> fondedValues = departmentedFondedRecordSearchSortingsLoader.getOn(ctx);
        Optional<? extends OUT> fonded = fondedValues.findForSpecific(fond.getId());
        if (fonded.isPresent()) {
            return fonded.get();
        }

        if (FondTypeResolver.isAuthorityFond(fond)) {
            return authorityProvider.getOn(ctx);
        }
        return fondedValues.getNoFonded();
    }
}
