package cz.kpsys.portaro.record.document;

import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RichRecordLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordDelegatingDocumentLoader implements DocumentLoader {

    @NonNull RichRecordLoader richRecordLoader;
    boolean loadDetail;

    @Override
    public Record getDetailed(Record basic) {
        return richRecordLoader.getDetailed(basic);
    }

    @Override
    public Record getById(@NonNull UUID id) {
        return richRecordLoader.getById(id, loadDetail);
    }

    @Override
    public List<Record> getAllByIds(@NonNull List<UUID> ids) {
        return richRecordLoader.getAllByIds(ids, loadDetail);
    }

}
