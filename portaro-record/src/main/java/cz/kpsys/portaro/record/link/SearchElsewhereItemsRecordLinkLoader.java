package cz.kpsys.portaro.record.link;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.web.Link;
import cz.kpsys.portaro.commons.web.StaticLink;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SearchElsewhereItemsRecordLinkLoader implements RecordLinkLoader {

    @NonNull List<SearchElsewhereItem> searchElsewhereItems;

    @Override
    public List<? extends Link> getByRecord(final @NonNull Record record) {
        return searchElsewhereItems.stream()
                .filter(searchElsewhereItem -> searchElsewhereItem.isShowable(record))
                .map(searchElsewhereItem -> new StaticLink(searchElsewhereItem.getUrl(record), Texts.ofNative(searchElsewhereItem.getName())))
                .toList();
    }
}
