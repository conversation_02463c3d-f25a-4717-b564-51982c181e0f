package cz.kpsys.portaro.record.detail.dflt;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import io.micrometer.common.lang.Nullable;
import jakarta.validation.constraints.NotNull;
import lombok.NonNull;

public record DefaultFieldValueSaveRequest(

        @NonNull
        FieldTypeId fieldTypeId,

        @NotNull
        Department department,

        @Nullable
        Fond fond,

        @NotNull
        String value

) {}
