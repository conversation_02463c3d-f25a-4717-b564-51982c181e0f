package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.value.FieldValue;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.util.Set;
import java.util.stream.Stream;

public interface UnaryFunction<RES extends FieldValue<?>> extends ResolvableFunction<RES> {

    @NonNull
    Formula<?> operand();

    @NonNull
    @NotEmpty
    default Set<LookupDefinition> dependencies() {
        return operand().dependencies();
    }

    @Override
    @NonNull default Stream<? extends Formula<?>> subformulas() {
        return ListUtil.createStreamListPrepending(operand(), operand().subformulas());
    }
}
