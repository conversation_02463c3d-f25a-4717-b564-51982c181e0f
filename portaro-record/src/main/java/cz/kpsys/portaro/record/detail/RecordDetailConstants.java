package cz.kpsys.portaro.record.detail;

import java.time.format.DateTimeFormatter;

public class RecordDetailConstants {
    public static final DateTimeFormatter RAW_LOCAL_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter TEXT_LOCAL_DATE_FORMAT = DateTimeFormatter.ofPattern("d.M.yyyy");
    public static final DateTimeFormatter RAW_INSTANT_FORMAT = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    public static final DateTimeFormatter TEXT_INSTANT_FORMAT = DateTimeFormatter.ofPattern("d.M.yyyy HH:mm:ss");
}
