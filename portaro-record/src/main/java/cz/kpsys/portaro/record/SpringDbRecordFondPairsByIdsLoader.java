package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.logging.ExecutionTimeLogged;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.commons.object.repo.DataUtils.getByIdUsingAllByIds;
import static cz.kpsys.portaro.databasestructure.RecordDb.RECORD.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbRecordFondPairsByIdsLoader implements IdAndIdsLoadable<RecordIdFondPair, UUID>, RowMapper<RecordIdFondPair> {

    public static final String ORIG = "orig";
    public static final String MASTER = "master";

    public static final String ORIG_ID = "orig_id";
    public static final String ORIG_FOND_ID = "orig_fond_id";
    public static final String MASTER_ID = "master_id";
    public static final String MASTER_FOND_ID = "master_fond_id";
    public static final String MASTER_MASTER_ID = "master_master_id";

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull ByIdLoadable<Fond, Integer> fondLoader;
    @NonNull TransactionTemplate readonlyTransactionTemplate;

    @Transactional(readOnly = true)
    @ExecutionTimeLogged
    @Override
    public RecordIdFondPair getById(@NonNull UUID id) throws ItemNotFoundException {
        return getByIdUsingAllByIds(this, RecordIdFondPair.class, id);
    }

    @Transactional(readOnly = true)
    @ExecutionTimeLogged
    @Override
    public List<RecordIdFondPair> getAllByIds(@NonNull List<UUID> ids) {
        if (ids.isEmpty()) {
            return List.of();
        }
        return DataUtils.loadInChunksWithoutPostSorting(ids, 200, this::getAllByIdsFromDb);
    }

    private List<RecordIdFondPair> getAllByIdsFromDb(@NonNull List<UUID> ids) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(
                AS(TC(ORIG, ID), ORIG_ID),
                AS(TC(ORIG, FOND_ID), ORIG_FOND_ID),
                AS(TC(MASTER, ID), MASTER_ID),
                AS(TC(MASTER, FOND_ID), MASTER_FOND_ID),
                AS(TC(MASTER, MASTER_RECORD_ID), MASTER_MASTER_ID)
        );
        sq.from(AS(TABLE, ORIG));
        sq.joins().addLeft(AS(TABLE, MASTER), COLSEQ(TC(ORIG, MASTER_RECORD_ID), TC(MASTER, ID)));
        sq.where().in(TC(ORIG, ID), ids);

        return readonlyTransactionTemplate.execute(_ ->
                jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this)
        );
    }

    @Override
    public RecordIdFondPair mapRow(@NonNull ResultSet rs, int rowNum) throws SQLException {
        UUID origId = DbUtils.uuidNotNull(rs, ORIG_ID);
        Integer origFondId = DbUtils.getIntegerNotNull(rs, ORIG_FOND_ID);
        UUID masterId = DbUtils.uuidOrNull(rs, MASTER_ID);
        Integer masterFondId = DbUtils.getInteger(rs, MASTER_FOND_ID);

        // jen kontrola, ze nemame vic jak jeden mergovaci skok
        UUID masterMasterId = DbUtils.uuidOrNull(rs, MASTER_MASTER_ID);
        Assert.isNull(masterMasterId, () -> "There are more than 1 merged-record-jump in %s table: %s -> %s -> %s".formatted(TABLE, origId, masterId, masterMasterId));

        // zaznam nebyl mergnuty do jineho
        if (masterId == null) {
            RecordIdentifier actualIdentifier = RecordIdentifier.of(origId);
            Fond fond = fondLoader.getById(origFondId);
            return RecordIdFondPair.of(actualIdentifier, fond);
        }

        // zaznam byl mergnuty do jineho
        RecordIdentifier actualIdentifier = RecordIdentifier.ofMergedRecord(origId, masterId);
        Fond fond = fondLoader.getById(Objects.requireNonNull(masterFondId));
        return RecordIdFondPair.of(actualIdentifier, fond);
    }
}
