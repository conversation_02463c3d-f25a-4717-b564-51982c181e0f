package cz.kpsys.portaro.record.edit.fieldshierarchy;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.date.DatetimeRange;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.value.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.ConversionService;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Objects;
import java.util.UUID;

import static cz.kpsys.portaro.CoreConstants.Datatype.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldValueCommandResolver {

    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull ConversionService conversionService;

    public FieldValueCommand resolveCommand(@NonNull SetFieldValueRequest setFieldValueRequest, @NonNull FieldType<?> type, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        return switch (setFieldValueRequest) {
            case ReferenceFieldValue referenceFieldValue -> resolveReferenceFieldValue(referenceFieldValue, ctx, currentAuth);
            case ContentFieldValue<?> contentFieldValue -> resolveContentFieldValue(contentFieldValue, type, ctx, currentAuth);
        };
    }

    private DetailedRecordValueCommand resolveReferenceFieldValue(ReferenceFieldValue referenceFieldValue, Department ctx, @NonNull UserAuthentication currentAuth) {
        return new DetailedRecordValueCommand(recordLoader.getById(referenceFieldValue.recordId()), ctx, currentAuth);
    }

    private FieldValueCommand resolveContentFieldValue(ContentFieldValue<?> contentFieldValue, FieldType<?> type, Department ctx, UserAuthentication currentAuth) {
        if (type.getDatatypeOrThrow().equals(DATE)) {
            return new DateValueCommand(Objects.requireNonNull(conversionService.convert(contentFieldValue.value(), LocalDate.class)), ctx, currentAuth);
        }

        if (type.getDatatypeOrThrow().equals(DATETIME)) {
            return new InstantValueCommand(Objects.requireNonNull(conversionService.convert(contentFieldValue.value(), Instant.class)), ctx, currentAuth);
        }

        if (type.getDatatypeOrThrow().equals(DATE_RANGE)) {
            return new DateRangeValueCommand(Objects.requireNonNull(conversionService.convert(contentFieldValue.value(), DateRange.class)), ctx, currentAuth);
        }

        if (type.getDatatypeOrThrow().equals(DATETIME_RANGE)) {
            return new DatetimeRangeValueCommand(Objects.requireNonNull(conversionService.convert(contentFieldValue.value(), DatetimeRange.class)), ctx, currentAuth);
        }

        if (type.getDatatypeOrThrow().equals(BOOLEAN)) {
            return new BooleanValueCommand(Objects.requireNonNull((Boolean) contentFieldValue.value()), ctx, currentAuth);
        }

        if (type.getDatatypeOrThrow().equals(NUMBER)) {
            Assert.isInstanceOf(Integer.class, contentFieldValue.value());
            return new NumberValueCommand(BigDecimal.valueOf((Integer) contentFieldValue.value()), ctx, currentAuth);
        }

        if (type.getDatatypeOrThrow().equals(NUMBER_DECIMAL_2) || type.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER_DECIMAL_4)) {
            Object value = contentFieldValue.value();
            return switch (value) {
                case Double v -> new NumberValueCommand(BigDecimal.valueOf(v), ctx, currentAuth);
                case Integer i -> new NumberValueCommand(BigDecimal.valueOf(i), ctx, currentAuth);
                default -> throw new IllegalArgumentException("Unexpected value type: " + value.getClass());
            };
        }

        if (type.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER_DECIMAL_6)) {
            throw new UnsupportedOperationException("Number with precision 6 is not supported");
        }

        if (contentFieldValue.value() instanceof String stringValue) {
            return new StringValueCommand(stringValue, ctx, currentAuth);
        }

        throw new UnsupportedOperationException(String.format("Field value object %s (type %s) is not supported", contentFieldValue.value(), contentFieldValue.value().getClass().getSimpleName()));
    }
}
