package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.NumberUtil;
import cz.kpsys.portaro.number.BigDecimalToStringConverter;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.Field;
import lombok.NonNull;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.Set;

public record NumberFieldValue(

        @NonNull
        BigDecimal value,

        @NonNull
        String label,

        @NonNull
        Text text,

        @NonNull
        Set<RecordIdFondPair> origins

) implements ScalarFieldValue<BigDecimal> {

    public static NumberFieldValue of(@NonNull BigDecimal value, @NonNull Set<RecordIdFondPair> origins) {
        String label = BigDecimalToStringConverter.INSTANCE.convert(value);
        return new NumberFieldValue(value, label, Texts.ofNumber(value), origins);
    }

    public static NumberFieldValue testing(@NonNull BigDecimal value, @NonNull RecordIdFondPair recordIdFondPair) {
        return NumberFieldValue.of(value, Set.of(recordIdFondPair));
    }

    public static Optional<BigDecimal> extract(@NonNull Field<NumberFieldValue> field) {
        return field.typedValue(NumberFieldValue.class).map(NumberFieldValue::value);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        return o instanceof NumberFieldValue that && NumberUtil.isEqual(value(), that.value()); // For BigDecimal use compareTo in place of equals !!!
    }

    @Override
    public int hashCode() {
        return value().hashCode();
    }

}
