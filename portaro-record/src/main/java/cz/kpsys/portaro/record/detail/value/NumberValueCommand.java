package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.util.NumberUtil;
import cz.kpsys.portaro.department.Department;
import lombok.NonNull;

import java.math.BigDecimal;

public record NumberValueCommand(

        @NonNull
        BigDecimal value,

        @NonNull
        Department ctx,

        @NonNull
        UserAuthentication currentAuth

) implements ScalarFieldValueCommand<BigDecimal> {

    @Override
    public boolean equals(Object o) {
        return o instanceof NumberValueCommand that && ctx.equals(that.ctx) && NumberUtil.isEqual(value, that.value) && currentAuth.equals(that.currentAuth);
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    @Override
    public String toString() {
        return "NumberValueCommand[" + value + ']';
    }
}
