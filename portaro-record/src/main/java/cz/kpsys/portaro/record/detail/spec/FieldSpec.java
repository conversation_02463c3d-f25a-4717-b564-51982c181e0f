package cz.kpsys.portaro.record.detail.spec;

import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.Comparator;
import java.util.Objects;

import static cz.kpsys.portaro.commons.util.StringUtil.joinSkippingBlanksAndNullsPrefSuf;

/// Record with predicates for searching in record fields. During searching, all predicates are joined together by AND operation
///
/// @param fieldTypeId Searches for exactly this field ID. `d100.a` -> finds all `d100.a` fields.
/// @param fieldId Searches for exactly this field ID at specified position. `d100#0.a#1` -> finds `d100#0.a#1` field.
/// @param multifieldId `d100#0.a` -> finds all `d100.a` fields under `d100#0`.
public record FieldSpec(

        @Nullable
        FieldTypeId fieldTypeId,

        @Nullable
        FieldId fieldId,

        @Nullable
        MultifieldId multifieldId

) {

    public static Comparator<FieldSpec> NUMERICALLY_COMPATIBLE_SORTER = Comparator.comparing(FieldSpec::fieldTypeId, Comparator.nullsFirst(FieldTypeId.NUMERICALLY_COMPATIBLE_SORTER))
            .thenComparing(FieldSpec::fieldId, Comparator.nullsFirst(FieldId.NUMERICALLY_COMPATIBLE_SORTER))
            .thenComparing(FieldSpec::multifieldId, Comparator.nullsFirst(MultifieldId.NUMERICALLY_COMPATIBLE_SORTER));

    public static FieldSpec ofField(@NonNull FieldId fieldId) {
        return new FieldSpec(null, fieldId, null);
    }

    public static FieldSpec ofMultifield(@NonNull MultifieldId multifieldId) {
        return new FieldSpec(null, null, multifieldId);
    }

    public static FieldSpec ofFieldType(@NonNull FieldTypeId fieldTypeId) {
        return new FieldSpec(fieldTypeId, null, null);
    }

    /// Metoda aktualne podporuje pouze uplny match `this` a `other`
    /// TODO: aktualne vraci null, ale meli bychom spise udelat a vracet novou implementaci EmptyRecordFieldSpec (a z teto tridy samozrejme udelat interface)
    public @Nullable FieldSpec minus(@NonNull FieldSpec other) { // napr. this: "fond", other: "fond#1"
        if (other.covers(this)) {
            return null;
        }
        if (!covers(other)) {
            return this;
        }
        throw new IllegalArgumentException("Subseting of RecordFieldSpec is not supported");
    }

    private boolean matchesFieldType(@NonNull FieldTypeId fieldTypeId) {
        if (!isFieldTypeSpecified()) {
            return true;
        }
        return this.fieldTypeId.equals(fieldTypeId);
    }

    private boolean matchesFieldId(@NonNull FieldId fieldId) {
        if (!isFieldIdSpecified()) {
            return true;
        }
        return existingFieldId().equals(fieldId);
    }

    private boolean matchesMultifieldId(@NonNull MultifieldId multifieldId) {
        if (!isMultifieldIdSpecified()) {
            return true;
        }
        return existingMultifieldId().equals(multifieldId);
    }

    public boolean covers(@NonNull FieldSpec other) {
        if (equals(other)) {
            return true;
        }
        return coversFieldType(other) &&
               coversFieldId(other) &&
               coversMultifieldId(other);
    }

    private boolean coversFieldType(@NonNull FieldSpec other) {
        if (!other.isFieldTypeSpecified()) {
            if (!isFieldTypeSpecified()) {
                return true;
            }
            if (other.isFieldIdSpecified()) { // muzeme jeste zkusit porovnat, jestli tento fieldTypeId ("fond") nepokryva other fieldId ("fond#1")
                return matchesFieldType(other.existingFieldId().toFieldTypeId());
            }
            return false;
        }
        return matchesFieldType(other.existingFieldType());
    }

    private boolean coversFieldId(@NonNull FieldSpec other) {
        if (!other.isFieldIdSpecified()) {
            return !isFieldIdSpecified();
        }
        return matchesFieldId(other.existingFieldId());
    }

    private boolean coversMultifieldId(@NonNull FieldSpec other) {
        if (!other.isMultifieldIdSpecified()) {
            if (!isMultifieldIdSpecified()) {
                return true;
            }
            if (other.isFieldIdSpecified()) { // muzeme jeste zkusit porovnat, jestli tento multifieldId ("fond" nebo "d156#0.a") nepokryva other fieldId ("fond#1" nebo "d156#0.a#2")
                return matchesMultifieldId(other.existingFieldId().toMultifieldId());
            }
            return false;
        }
        return matchesMultifieldId(other.existingMultifieldId());
    }

    public boolean isFieldTypeSpecified() {
        return fieldTypeId != null;
    }

    public boolean isFieldIdSpecified() {
        return fieldId != null;
    }

    public boolean isMultifieldIdSpecified() {
        return multifieldId != null;
    }

    public @NonNull FieldTypeId existingFieldType() {
        return Objects.requireNonNull(fieldTypeId, "fieldTypeId is not specified in recordFieldSpec");
    }

    public @NonNull FieldId existingFieldId() {
        return Objects.requireNonNull(fieldId, "fieldId is not specified in recordFieldSpec");
    }

    public @NonNull MultifieldId existingMultifieldId() {
        return Objects.requireNonNull(multifieldId, "multifieldId is not specified in recordFieldSpec");
    }

    @Override
    public String toString() {
        return joinSkippingBlanksAndNullsPrefSuf(",", "[", "]",
                fieldTypeId,
                fieldId,
                multifieldId
        );
    }
}
