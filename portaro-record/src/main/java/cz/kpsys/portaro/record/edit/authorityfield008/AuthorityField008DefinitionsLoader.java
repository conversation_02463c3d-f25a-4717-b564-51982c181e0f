package cz.kpsys.portaro.record.edit.authorityfield008;

import cz.kpsys.portaro.commons.cache.CacheCleaner;
import cz.kpsys.portaro.commons.object.Provider;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;


@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AuthorityField008DefinitionsLoader implements CacheCleaner {

    @NonNull Provider<AuthorityField008Position> directOrIndirectGeographicSubdivisionProvider;
    @NonNull Provider<AuthorityField008Position> romanizationSchemeProvider;
    @NonNull Provider<AuthorityField008Position> languageOfCatalogProvider;
    @NonNull Provider<AuthorityField008Position> kindOfRecordProvider;
    @NonNull Provider<AuthorityField008Position> descriptiveCatalogingRulesProvider;
    @NonNull Provider<AuthorityField008Position> subjectHeadingSystemThesaurusProvider;
    @NonNull Provider<AuthorityField008Position> typeOfSeriesProvider;
    @NonNull Provider<AuthorityField008Position> numberedOrUnnumberedSeriesProvider;
    @NonNull Provider<AuthorityField008Position> headingUseMainOrAddedEntryProvider;
    @NonNull Provider<AuthorityField008Position> headingUseSubjectAddedEntryProvider;
    @NonNull Provider<AuthorityField008Position> headingUseSeriesAddedEntryProvider;
    @NonNull Provider<AuthorityField008Position> typeOfSubjectSubdivisionProvider;
    @NonNull Provider<AuthorityField008Position> typeOfGovernmentAgencyProvider;
    @NonNull Provider<AuthorityField008Position> referenceEvaluationProvider;
    @NonNull Provider<AuthorityField008Position> recordUpdateInProcessProvider;
    @NonNull Provider<AuthorityField008Position> undifferentiatedPersonalNameProvider;
    @NonNull Provider<AuthorityField008Position> levelOfEstablishmentProvider;
    @NonNull Provider<AuthorityField008Position> modifiedRecordProvider;
    @NonNull Provider<AuthorityField008Position> catalogingSourceProvider;

    public static final String CACHE_NAME = "authorityField008definitions";

    @Cacheable(value = CACHE_NAME)
    public AuthorityField008Definitions getDefinitions() {
        return new AuthorityField008Definitions(
                directOrIndirectGeographicSubdivisionProvider.get(),
                romanizationSchemeProvider.get(),
                languageOfCatalogProvider.get(),
                kindOfRecordProvider.get(),
                descriptiveCatalogingRulesProvider.get(),
                subjectHeadingSystemThesaurusProvider.get(),
                typeOfSeriesProvider.get(),
                numberedOrUnnumberedSeriesProvider.get(),
                headingUseMainOrAddedEntryProvider.get(),
                headingUseSubjectAddedEntryProvider.get(),
                headingUseSeriesAddedEntryProvider.get(),
                typeOfSubjectSubdivisionProvider.get(),
                typeOfGovernmentAgencyProvider.get(),
                referenceEvaluationProvider.get(),
                recordUpdateInProcessProvider.get(),
                undifferentiatedPersonalNameProvider.get(),
                levelOfEstablishmentProvider.get(),
                modifiedRecordProvider.get(),
                catalogingSourceProvider.get());
    }

    @CacheEvict(value = CACHE_NAME, allEntries = true)
    @Override
    public void clearCache() {

    }
}