package cz.kpsys.portaro.record.revision;

import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ConvertingRecordRevisionLoader implements RecordRevisionLoader {

    @NonNull RecordRevisionEntityLoader entityLoader;
    @NonNull Converter<List<? extends RecordRevisionEntity>, List<RecordRevision>> listConverter;

    @Override
    public List<RecordRevision> getAllByRecord(@NonNull Record record) {
        List<RecordRevisionEntity> entities = entityLoader.getAllByRecord(record);
        return listConverter.convert(entities);
    }
}
