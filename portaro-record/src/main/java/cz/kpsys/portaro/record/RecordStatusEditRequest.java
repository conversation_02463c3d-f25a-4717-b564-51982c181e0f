package cz.kpsys.portaro.record;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.util.UUID;

public record RecordStatusEditRequest(

        @Schema(implementation = UUID.class, description = "Record")
        @NotNull
        Record record,

        @Schema(implementation = Integer.class, example = RecordStatus.SCHEMA_EXAMPLE_ID)
        @NotNull
        RecordStatus status

) {}
