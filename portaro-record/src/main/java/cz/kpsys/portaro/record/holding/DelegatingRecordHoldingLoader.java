package cz.kpsys.portaro.record.holding;

import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DelegatingRecordHoldingLoader implements RecordHoldingLoader {

    @NonNull RecordHoldingEntityLoader recordHoldingEntityLoader;
    @NonNull Converter<List<? extends RecordHoldingEntity>, List<RecordHolding>> entitiesToRecordHoldingConverter;
    @NonNull HierarchyLoader<Department> departmentAccessor;

    @Transactional(readOnly = true)
    @Override
    public RecordHolding getById(@NonNull UUID id) throws ItemNotFoundException {
        RecordHoldingEntity entity = recordHoldingEntityLoader.findById(id).orElseThrow(() -> new ItemNotFoundException(RecordHolding.class, id));
        return ListUtil.convertSingle(entity, entitiesToRecordHoldingConverter);
    }

    @Transactional(readOnly = true)
    @Override
    public List<RecordHolding> getAllByRecordId(@NonNull UUID recordId) {
        List<RecordHoldingEntity> entities = recordHoldingEntityLoader.findAllByRecordId(recordId);
        return entitiesToRecordHoldingConverter.convert(entities);
    }

    @Transactional(readOnly = true)
    @Override
    public List<RecordHolding> getAllByRecordIdAndRootDepartment(@NonNull UUID recordId, @NonNull Department rootDepartment) {
        List<Department> subdepartments = departmentAccessor.getAllByScope(rootDepartment, HierarchyLoadScope.SUBTREE);
        return getAllByRecordIdsAndDepartments(Set.of(recordId), subdepartments);
    }

    @Transactional(readOnly = true)
    @Override
    public Optional<RecordHolding> getByRecordIdAndDepartment(@NonNull UUID recordId, @NonNull Department department) {
        return DataUtils.requireMaxOne(getAllByRecordIdsAndDepartments(Set.of(recordId), Set.of(department)), RecordHolding.class, "record=%s,department=%s".formatted(recordId, department));
    }

    @Transactional(readOnly = true)
    @Override
    public List<RecordHolding> getAllByRecordIdsAndDepartments(@NonNull Collection<UUID> recordIds, @NonNull Collection<Department> departments) {
        List<RecordHoldingEntity> entities = recordHoldingEntityLoader.findAllByRecordIdInAndDepartmentIdIn(recordIds, ListUtil.getListOfIds(departments));
        return entitiesToRecordHoldingConverter.convert(entities);
    }

    @Transactional(readOnly = true)
    @Override
    public List<RecordHolding> getAllByDepartments(@NonNull Collection<Department> departments) {
        List<RecordHoldingEntity> entities = recordHoldingEntityLoader.findAllByDepartmentIdIn(ListUtil.getListOfIds(departments));
        return entitiesToRecordHoldingConverter.convert(entities);
    }
}
