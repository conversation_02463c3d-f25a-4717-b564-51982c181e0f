package cz.kpsys.portaro.record.holding;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import lombok.NonNull;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface RecordHoldingLoader extends ByIdLoadable<RecordHolding, UUID> {

    List<RecordHolding> getAllByRecordId(@NonNull UUID recordId);

    List<RecordHolding> getAllByRecordIdAndRootDepartment(@NonNull UUID recordId, @NonNull Department rootDepartment);

    Optional<RecordHolding> getByRecordIdAndDepartment(@NonNull UUID recordId, @NonNull Department department);

    List<RecordHolding> getAllByRecordIdsAndDepartments(@NonNull Collection<UUID> recordIds, @NonNull Collection<Department> departments);

    List<RecordHolding> getAllByDepartments(@NonNull Collection<Department> departments);

}
