package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.FieldsNotResolvableException;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.detail.FieldFinders;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordValidator {

    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;
    @NonNull FieldTypesByFondLoader fieldTypesByFondLoader;

    public Result validate(@NonNull Fond fond, @NonNull FieldContainer recordDetail) {
        var definedTopfields = fieldTypesByFondLoader.getTopfieldTypesByFond(fond);

        var result = new ArrayList<FailedValidation>();
        // Order is important if checks overlap (some may be more specific - put them first)
        addMissingEntryFieldCheckResult(result, fond, recordDetail); // First check entry field (more specific)
        addNonEmptyCheckResult(result, definedTopfields, recordDetail); // Then check requirement for non empty (more general)
        return new Result(result);
    }

    private void addMissingEntryFieldCheckResult(@NonNull List<FailedValidation> failedValidations,
                                                 @NonNull Fond fond,
                                                 @NonNull FieldContainer recordDetail) {
        FieldTypeId entrySubfieldTypeId = recordEntryFieldTypeIdResolver.getEntryNativeSubfield(fond);
        Optional<Field<?>> requiredEntryField = FieldFinders.<FieldContainer, Field<?>>byDeepTypeId(entrySubfieldTypeId).findFirstIn(recordDetail);
        if (requiredEntryField.isEmpty() || requiredEntryField.get().isEmpty()) {
            failedValidations.add(new FailedValidation(Reason.MISSING_ENTRY_FIELD, entrySubfieldTypeId));
        }
    }

    private void addNonEmptyCheckResult(@NonNull List<FailedValidation> failedValidations,
                                        @NonNull List<EditableFieldType<?>> definedTopfields,
                                        @NonNull FieldContainer recordDetail) {
        // Must walk through defined fields (some may be missing in data)
        for (var editableTopfield : definedTopfields) {
            if (!editableTopfield.isRequired()) {
                continue;
            }
            var maybeField = recordDetail.getFirstFieldByTypeId(editableTopfield.getFieldTypeId());
            if (maybeField.isEmpty() || maybeField.get().isEmpty()) {
                failedValidations.add(new FailedValidation(Reason.MISSING_REQUIRED_FIELD, editableTopfield.getFieldTypeId()));
            } else { // Recurse into subfields
                addNonEmptyCheckResult(failedValidations, editableTopfield.getSubfieldTypes(), maybeField.get());
            }
        }
    }


    @RequiredArgsConstructor
    public static class Result {

        private final @NonNull List<FailedValidation> failedValidations;

        public boolean isOk() {
            return failedValidations.isEmpty();
        }

        public void forEachFailure(Consumer<FailedValidation> failureConsumer) {
            failedValidations.forEach(failureConsumer);
        }
    }

    public enum Reason {
        MISSING_ENTRY_FIELD,
        MISSING_REQUIRED_FIELD
    }

    public record FailedValidation(
            @NonNull Reason reason,
            @NonNull FieldTypeId offendingField
    ) {
    }

    public static class FieldInvalidException extends FieldsNotResolvableException {
        public FieldInvalidException(String message, Text text) {
            super(message, text);
        }
    }

}
