package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.commons.object.NonNullOrderedRecord;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;
import lombok.With;

import java.util.Comparator;

@With
public record FondedFieldTypeEntity(

        @NonNull
        Integer styleId,

        @NonNull
        FieldTypeId configPath,

        @NonNull
        FieldDisplayType fieldDisplayType,

        @NonNull
        FieldRequirementType fieldRequirementType,

        @NonNull
        Integer order

) implements NonNullOrderedRecord {

    public static final Comparator<FondedFieldTypeEntity> NUMERICALLY_COMPATIBLE_SORTER = Comparator.comparing(FondedFieldTypeEntity::configPath, FieldTypeId.NUMERICALLY_COMPATIBLE_SORTER);

}
