package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import jakarta.validation.constraints.Null;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.util.List;

public interface FieldTypesByFondLoader {

    sealed interface WhenMissing<RET extends EditableFieldType<?>> {

        WhenMissing<@NonNull EditableFieldType<?>> THROW = new WhenMissingThrow();
        WhenMissing<@Null EditableFieldType<?>> RETURN_EMPTY = new WhenMissingReturnEmpty();
        WhenMissing<@NonNull EditableFieldType<?>> CREATE_UNKNOWN = new WhenMissingCreateUnknown();

        RET onMissing(@NonNull Fond fond, @NonNull FieldTypeId fieldTypeId);
    }

    final class WhenMissingThrow implements WhenMissing<@NonNull EditableFieldType<?>> {

        @Override
        public @NonNull EditableFieldType<?> onMissing(@NonNull Fond fond, @NonNull FieldTypeId fieldTypeId) {
            throw new IllegalArgumentException("No editable field type " + fieldTypeId + " defined for " + fond);
        }
    }

    final class WhenMissingReturnEmpty implements WhenMissing<@Null EditableFieldType<?>> {

        @Override
        public @Nullable EditableFieldType<?> onMissing(@NonNull Fond fond, @NonNull FieldTypeId fieldTypeId) {
            return null;
        }
    }

    final class WhenMissingCreateUnknown implements WhenMissing<@NonNull EditableFieldType<?>> {

        @Override
        public @NonNull EditableFieldType<?> onMissing(@NonNull Fond fond, @NonNull FieldTypeId fieldTypeId) {
            throw new IllegalStateException("Unable to find field type %s for fond %s and %s did not create unknown field type".formatted(fieldTypeId, fond, this.getClass().getSimpleName()));
        }
    }

    List<EditableFieldType<?>> getTopfieldTypesByFond(@NonNull Fond fond);

    <RET extends EditableFieldType<?>> RET findTopfieldTypeByFondAndId(@NonNull Fond fond, @NonNull FieldTypeId topfieldId, @NonNull WhenMissing<RET> whenMissing);

    default @NonNull EditableFieldType<?> getTopfieldTypeByFondAndIdOrUnknown(@NonNull Fond fond, @NonNull FieldTypeId topfieldId) {
        return findTopfieldTypeByFondAndId(fond, topfieldId, WhenMissing.CREATE_UNKNOWN);
    }

    default <RET extends EditableFieldType<?>> RET findByFondAndId(@NonNull Fond fond, @NonNull FieldTypeId fieldTypeId, @NonNull FieldTypesByFondLoader.WhenMissing<RET> whenMissing) {
        return switch (fieldTypeId.getLevel()) {
            case FieldTypeId.LEVEL_TOPFIELD -> {
                RET actual = findTopfieldTypeByFondAndId(fond, fieldTypeId, whenMissing);
                if (actual != null) {
                    yield actual;
                }
                yield whenMissing.onMissing(fond, fieldTypeId);
            }
            case FieldTypeId.LEVEL_SUBFIELD, FieldTypeId.LEVEL_SUBSUBFIELD -> {
                RET parent = findByFondAndId(fond, fieldTypeId.existingParent(), whenMissing);
                if (parent == null) {
                    yield whenMissing.onMissing(fond, fieldTypeId);
                }
                if (whenMissing == WhenMissing.CREATE_UNKNOWN) {
                    EditableFieldType<?> actual = parent.getSubfieldTypeFor(fieldTypeId); // getSubfieldTypeFor returns an unknown type when unknown
                    if (actual != null) {
                        yield (RET) actual;
                    }
                    yield whenMissing.onMissing(fond, fieldTypeId);
                }
                EditableFieldType<?> actual = ListUtil.getByIdOrNull(parent.getSubfieldTypes(), fieldTypeId.toString());
                if (actual != null) {
                    yield (RET) actual;
                }
                yield whenMissing.onMissing(fond, fieldTypeId);
            }
            default -> throw new IllegalArgumentException("Unsupported field level for: " + fieldTypeId);
        };
    }

    default EditableFieldType<?> getByFondAndId(@NonNull Fond fond, @NonNull FieldTypeId fieldTypeId) {
        return findByFondAndId(fond, fieldTypeId, WhenMissing.THROW);
    }

    default StaticFondLoader toStaticLoader(@NonNull Fond fond) {
        return new StaticFondLoader(this, fond);
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    class StaticFondLoader {

        @NonNull FieldTypesByFondLoader delegate;
        @NonNull Fond fond;

        public EditableFieldType<?> getTopfieldTypeById(@NonNull FieldTypeId id) {
            return delegate.getTopfieldTypeByFondAndIdOrUnknown(fond, id);
        }

        public EditableFieldType<?> getSubfieldTypeById(@NonNull FieldTypeId id) {
            return delegate.getByFondAndId(fond, id);
        }

        public EditableFieldType<?> getById(@NonNull FieldTypeId id) {
            return delegate.getByFondAndId(fond, id);
        }
    }

}
