package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import lombok.NonNull;

public interface TypedFieldValueConverter<COMMAND extends FieldValueCommand, PAYLOAD extends FieldPayload<VH>, VH extends ScalarFieldValue<?>> {

    @NonNull
    PAYLOAD convert(@NonNull COMMAND command, @NonNull FieldType<?> fieldType, @NonNull RecordFieldId fieldId);

}
