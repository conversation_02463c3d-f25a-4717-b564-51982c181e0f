package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.NamedLabeledIdentified;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.link.LinkFieldTargetSearchMode;
import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.source.FieldSource;
import cz.kpsys.portaro.record.detail.value.FieldValue;
import cz.kpsys.portaro.record.detail.value.FieldValueConverter;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface FieldType<V extends FieldValue<?>> extends Identified<String>, NamedLabeledIdentified<String>, WithCode, FieldTypeListable<FieldType<?>> {

    /**
     * Vrati zda jsou vsechna dana podpole v ramci komplexni autority.
     * Vraci true, pokud jsou vsechna podpole s record linkem na parent (main) podpole a vsechna maji daneho parenta
     */
    static boolean areAllgroupedFieldTypesOfSameComplexRecordLink(List<? extends FieldType<?>> groupedFieldTypes, FieldType<?> groupFieldType) {
        return groupedFieldTypes.stream()
                .allMatch(groupedFieldType ->
                        !groupedFieldType.getLookups().isEmpty() && groupedFieldType.getLookups().stream().allMatch(lookupDef -> lookupDef.linkFieldSpec().linkFieldSearchMode() == LinkFieldTargetSearchMode.VIRTUAL_GROUP_FIELD_LINK) &&
                        groupedFieldType.getFieldTypeId().existingParent().equals(groupFieldType.getFieldTypeId())
                );
    }

    @JsonIgnore
    @NonNull
    FieldTypeId getFieldTypeId();

    boolean isRepeatable();

    boolean isAutonomous();

    @JsonIgnore
    Optional<ScalarDatatype> getDatatype();

    @JsonIgnore
    Optional<Fond> getLinkRootFond();

    @JsonIgnore
    ScalarDatatype getDatatypeOrThrow();

    FieldExportSetting getExportSetting();

    TransferType getTransferType();

    @JsonIgnore
    boolean isVirtualGroup();

    @JsonIgnore
    boolean isGroup();

    @JsonIgnore
    default boolean hasAutonomousSubfieldTypes() {
        return getSubfieldTypes().stream().anyMatch(FieldType::isAutonomous);
    }

    FieldType<?> addSubfieldType(FieldType<?> subfieldType);

    @Override
    default @NonNull FieldType<?> getSubfieldTypeOrParentVirtualGroupTypeFor(@NonNull String subfieldCode) {
        FieldTypeId subfieldTypeId = getFieldTypeId().sub(subfieldCode);
        return getSubfieldTypeOrParentVirtualGroupTypeFor(subfieldTypeId);
    }

    @JsonIgnore
    FieldValueConverter getValueConverter();

    @JsonIgnore
    @NonNull
    FieldSource getFieldSource();

    @JsonIgnore
    @NonNull
    FdefGeneration getGeneration();

    @JsonIgnore
    @NonNull
    FieldStorageBehaviour getFieldStorageBehaviour();

    @JsonIgnore
    @NonNull
    default Set<LookupDefinition> getLookups() {
        return getFormula().map(Formula::dependencies).orElse(Set.of());
    }

    @JsonIgnore
    @NonNull
    default Optional<Formula<?>> getFormula() {
        return Optional.empty();
    }

    @JsonIgnore
    @NonNull
    default Optional<Codebook<? extends LabeledIdentified<?>, ?>> getCodebook() {
        return Optional.empty();
    }

    @JsonIgnore
    @NullableNotBlank
    String pic();

}
