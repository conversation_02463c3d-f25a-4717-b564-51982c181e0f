package cz.kpsys.portaro.record.detail.constraints;

import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Disjunction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.SearchMatcher;
import lombok.NonNull;

public record SimpleParameterConstraintDef(

        @NonNull Conjunction<SearchField> restriction

) implements RecordConstraintDef {

    public static <E> SimpleParameterConstraintDef create(SearchField param, SearchMatcher matcher) {
        Conjunction<SearchField> conjunction = new Conjunction<>();
        conjunction.add(new Term<>(param, matcher));
        return create(conjunction);
    }

    public static SimpleParameterConstraintDef create(Conjunction<SearchField> restriction) {
        return new SimpleParameterConstraintDef(restriction);
    }

    public static  SimpleParameterConstraintDef create(Disjunction<SearchField> restriction) {
        Conjunction<SearchField> conjunction = new Conjunction<>();
        conjunction.add(restriction);
        return new SimpleParameterConstraintDef(conjunction);
    }

    @Override
    public String toString() {
        return restriction.toString();
    }

}
