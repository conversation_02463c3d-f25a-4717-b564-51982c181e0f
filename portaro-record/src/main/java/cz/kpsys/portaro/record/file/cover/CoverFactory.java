package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.commons.file.FileUtils;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.web.PlaceholderTemplate;
import cz.kpsys.portaro.file.*;
import cz.kpsys.portaro.file.filecategory.FileCategoryBySystemTypeLoader;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.io.ByteArrayInputStream;
import java.io.File;

import static cz.kpsys.portaro.file.filecategory.FileCategorySystemType.COVER;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CoverFactory {

    public static final PlaceholderTemplate DEFAULT_DOCUMENT_COVER_FILENAME_TEMPLATE = new PlaceholderTemplate("cover_{recordId}.{ext}");
    public static final PlaceholderTemplate DEFAULT_DOCUMENT_UNIVERSAL_COVER_FILENAME_TEMPLATE = new PlaceholderTemplate("universal_cover_{recordId}.{ext}");
    public static final String DEFAULT_COVER_NAME = "obálka";

    @NonNull Provider<@NonNull Integer> portaroUserIdProvider;
    @NonNull FileExtensionDetector fileExtensionDetector;
    @NonNull FileCategoryBySystemTypeLoader fileCategoryBySystemTypeLoader;

    public LoadedFile createJustDownloaded(Record record, byte @NonNull [] data, String sourceUrl) {
        String extension = fileExtensionDetector.guessExtension(new ByteArrayInputStream(data)).orElse("jpg");
        String filename = DEFAULT_DOCUMENT_COVER_FILENAME_TEMPLATE
                .withParameter("recordId", record.getKindedId())
                .withParameter("ext", extension)
                .build();
        IdentifiedFile f = IdentifiedFileImpl.createNewFile(filename, data.length, portaroUserIdProvider.get());

        f.setName(DEFAULT_COVER_NAME);
        f.setCategory(fileCategoryBySystemTypeLoader.singleOrThrow(COVER));
        f.setSource(sourceUrl);

        return new ByteArrayLoadedIdentifiedFile(f, data);
    }


    public LoadedIdentifiedFile createJustByUserUploaded(Record record, byte @NonNull [] data, Integer creatorId) {
        String extension = fileExtensionDetector.guessExtension(new ByteArrayInputStream(data)).orElse("jpg");
        String filename = DEFAULT_DOCUMENT_COVER_FILENAME_TEMPLATE
                .withParameter("recordId", record.getKindedId())
                .withParameter("ext", extension)
                .build();
        IdentifiedFile f = IdentifiedFileImpl.createNewFile(filename, data.length, creatorId);

        f.setName(DEFAULT_COVER_NAME);
        f.setCategory(fileCategoryBySystemTypeLoader.singleOrThrow(COVER));

        return new ByteArrayLoadedIdentifiedFile(f, data);
    }


    public LoadedFile createImportedFromFile(File imageFile) {
        byte[] data = FileUtils.getBytesOfFile(imageFile);

        IdentifiedFile f = IdentifiedFileImpl.createNewFile(imageFile.getName(), data.length, portaroUserIdProvider.get());

        f.setName(DEFAULT_COVER_NAME);
        f.setCategory(fileCategoryBySystemTypeLoader.singleOrThrow(COVER));

        return new ByteArrayLoadedIdentifiedFile(f, data);
    }


    public LoadedFile createUniversalCover(@NonNull Record document, byte @NonNull [] data) {
        String extension = fileExtensionDetector.guessExtension(new ByteArrayInputStream(data)).orElse("jpg");
        String filename = DEFAULT_DOCUMENT_UNIVERSAL_COVER_FILENAME_TEMPLATE
                .withParameter("recordId", document.getKindedId())
                .withParameter("ext", extension)
                .build();
        IdentifiedFile f = IdentifiedFileImpl.createNewFile(filename, data.length, portaroUserIdProvider.get());

        f.setName(DEFAULT_COVER_NAME);
        f.setCategory(fileCategoryBySystemTypeLoader.singleOrThrow(COVER));

        return new ByteArrayLoadedIdentifiedFile(f, data);
    }


}
