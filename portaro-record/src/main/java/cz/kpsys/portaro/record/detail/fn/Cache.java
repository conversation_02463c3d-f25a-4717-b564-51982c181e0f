package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.AbsentValueFail;
import cz.kpsys.portaro.record.detail.value.FieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.util.function.Function;

public record Cache<RES extends FieldValue<?>>(
        @NonNull
        @NotEmpty
        Formula<RES> operand

) implements UnaryFunction<RES> {

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<RES>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        return operandResultDatatypeResolver.apply(operand);
    }

    @Override
    @NonNull
    public FormulaEvaluation<RES> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {

        boolean hasBackRefFormula = operand() instanceof BackRef || operand().subformulas().anyMatch(formula -> formula instanceof BackRef);

        if (!searchedRecordSpecs.covers(sourceNode.id().toRecordSpec()) && !hasBackRefFormula) { // the given value is missing and not-yet searched
            return FormulaEvaluation.failure(AbsentValueFail.of(sourceNode.id()));
        }

        return evaluator.resolveFormulaValue(sourceNode, operand);
    }

    @Override
    public String toString() {
        return "Cache(%s)".formatted(operand);
    }
}
