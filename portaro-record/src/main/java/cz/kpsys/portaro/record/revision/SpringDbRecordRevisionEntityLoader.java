package cz.kpsys.portaro.record.revision;

import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.util.StreamUtils;

import java.nio.charset.StandardCharsets;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import static cz.kpsys.portaro.commons.db.QueryUtils.TC;
import static cz.kpsys.portaro.databasestructure.RecordDb.ZAZNAM_HISTORIE.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SpringDbRecordRevisionEntityLoader implements RecordRevisionEntityLoader, RowMapper<RecordRevisionEntity> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;

    @Override
    public List<RecordRevisionEntity> getAllByRecord(@NonNull Record record) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(TABLE);
        sq.where()
                .eq(TC(TABLE, CIS_ZAZ), getCisZaz(record))
                .or()
                .eq(TC(TABLE, CIS_ZAZ), getSpecialCisZaz(record));
        sq.orderBy().addAsc(TC(TABLE, DATCAS));
        return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
    }

    private static String getCisZaz(Record record) {
        return (record.getType().equals(Record.TYPE_DOCUMENT) ? "D" : "A") + record.getKindedId();
    }

    private static String getSpecialCisZaz(Record record) {
        return (record.getType().equals(Record.TYPE_DOCUMENT) ? "MD" : "MA") + record.getKindedId();
    }

    @SneakyThrows
    @Override
    public RecordRevisionEntity mapRow(@NonNull ResultSet rs, int rowNum) throws SQLException {
        return new RecordRevisionEntity(
                DbUtils.getStringNotNull(rs, CIS_ZAZ),
                DbUtils.instantNotNull(rs, DATCAS),
                StreamUtils.copyToString(rs.getBlob(OBSAH).getBinaryStream(), StandardCharsets.UTF_8)
        );
    }
}
