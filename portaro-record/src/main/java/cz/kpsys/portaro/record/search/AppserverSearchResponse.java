package cz.kpsys.portaro.record.search;

import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;

public record AppserverSearchResponse(
        @NonNull Integer totalElements,
        @NonNull List<String> content,
        @NonNull List<FacetDto> facets,
        @NonNull String rawAppserverResponse
) {

    record FacetDto(
            @NonNull String id,
            @NonNull String datatype,
            @NonNull List<FacetKeyDto> keys
    ) {}

    record FacetKeyDto(
            @NonNull String value,
            @Nullable Integer count
    ) {}
}
