package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.record.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.SimpleFieldContainer;
import cz.kpsys.portaro.record.detail.dflt.DefaultFieldValueCommandResolver;
import cz.kpsys.portaro.record.edit.fieldshierarchy.FieldValueCommandResolver;
import cz.kpsys.portaro.record.edit.fieldshierarchy.SetFieldValueRequest;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.holding.RecordHoldingUpserter;
import cz.kpsys.portaro.record.load.RecordFieldsLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RecordEditationFactory {

    @NonNull RecordFieldTypesLoader missingAddingRecordEditableFieldTypesLoader;
    @NonNull RecordValidator recordValidator;
    @NonNull RecordSaver recordSaver;
    @NonNull FieldTypesByFondLoader unknownSupportingEditableTopfieldTypeLoader;
    @NonNull ContextualProvider<Department, RecordStatus> publishingDocumentRecordStatusProvider;
    @NonNull RecordHoldingUpserter recordHoldingUpserter;
    @NonNull FieldValueCommandResolver fieldValueCommandResolver;
    @NonNull ByIdLoadable<FieldType<?>, FieldTypeId> fieldTypeLoader;
    @NonNull RecordFieldEditor recordFieldEditor;
    @NonNull RecordFieldsLoader recordFieldsLoader;
    @NonNull DefaultFieldValueCommandResolver defaultFieldValueCommandResolver;
    @NonNull ContextualFunction<Record, Department, @NonNull Boolean> recordLockedContextualPredicate;
    @NonNull RecordStatusResolver recordStatusResolver;

    /// Create editation on department which will be set as holding
    public RecordAddingBuilder on(@NonNull Department ctx) {
        return new RecordAddingBuilder(HoldingContext.ofHoldingContext(ctx));
    }

    public RecordAddingBuilder withHoldingsWithoutCtx(@NonNull List<Department> holdings, @NonNull Department ctx) {
        return new RecordAddingBuilder(HoldingContext.withoutCtx(holdings, ctx));
    }

    public RecordAddingBuilder withNoHoldings(@NonNull Department ctx) {
        return new RecordAddingBuilder(HoldingContext.withoutCtx(List.of(), ctx));
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public class RecordAddingBuilder {

        @NonNull HoldingContext holdingContext;
        @NonFinal boolean external;

        public RecordAddingBuilder ofExternal() {
            this.external = true;
            return this;
        }

        public FinalBuilder ofNew(UUID recordId, @NonNull Fond fond) {
            Record record = Record.createDraft(RecordIdFondPair.of(recordId, fond), external, new SimpleFieldContainer());
            return new FinalBuilder(holdingContext, record);
        }

        public FinalBuilder ofNew(@NonNull Fond fond) {
            return ofNew(UuidGenerator.forIdentifier(), fond);
        }

        public FinalBuilder ofNew(@NonNull Fond fond, @NonNull FieldContainer detail) {
            RecordIdFondPair recordIdFondPair = RecordIdFondPair.of(UuidGenerator.forIdentifier(), fond);
            Record record = Record.createDraft(recordIdFondPair, external, detail.copy(recordIdFondPair, null));
            return new FinalBuilder(holdingContext, record);
        }

        public FinalBuilder ofExisting(@NonNull Record record) {
            Objects.requireNonNull(record.getFond(), () -> "Record %s has no fond".formatted(record));
            var newHoldingContext = record.getFond().isAdoptingEdited() ? holdingContext : holdingContext.withNoHoldings();
            return new FinalBuilder(newHoldingContext, record.copy(record.idFondPair(), record.getKindedId()));
        }

    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public class FinalBuilder {

        @NonNull HoldingContext holdingContext;
        @NonNull Record record;

        @NonFinal boolean defaultInitialValuesFill = false;
        @NonFinal boolean machineEditation = false;
        @NonFinal Map<String, SetFieldValueRequest> customInitialValues = null;


        public FinalBuilder withDefaultInitialValuesFilled() {
            this.defaultInitialValuesFill = true;
            return this;
        }

        public FinalBuilder withCustomInitialValues(Map<String, SetFieldValueRequest> initialValues) {
            this.customInitialValues = initialValues;
            return this;
        }

        public FinalBuilder byMachineEditation() {
            this.machineEditation = true;
            return this;
        }

        public RecordEditation build(@NonNull UserAuthentication currentAuth) {
            RecordEditation editation = new DefaultRecordEditation(
                    record,
                    holdingContext.ctx(),
                    holdingContext.holdings(),
                    machineEditation,
                    missingAddingRecordEditableFieldTypesLoader,
                    recordValidator,
                    recordSaver,
                    unknownSupportingEditableTopfieldTypeLoader,
                    publishingDocumentRecordStatusProvider,
                    recordHoldingUpserter,
                    recordFieldsLoader,
                    recordStatusResolver
            );

            // Fill default values from settings
            if (defaultInitialValuesFill) {
                editation = new DefaultInitialValuesFillingRecordEditationWrapper(editation, recordFieldEditor, defaultFieldValueCommandResolver, holdingContext.ctx(), currentAuth);
            }

            if (customInitialValues != null) {
                editation = new CustomInitialValuesFillingRecordEditationWrapper(
                        editation,
                        fieldTypeLoader,
                        recordFieldEditor,
                        fieldValueCommandResolver,
                        holdingContext.ctx(),
                        customInitialValues,
                        currentAuth
                );
            }

            return new LockCheckingRecordEditation(editation, recordLockedContextualPredicate);
        }
    }

    public record HoldingContext(
            @NonNull Department ctx,
            @NonNull List<Department> holdings
    ) {
        public static HoldingContext ofHoldingContext(@NonNull Department ctx) {
            return new HoldingContext(ctx, List.of(ctx));
        }

        public static HoldingContext withoutCtx(@NonNull List<Department> holdings, @NonNull Department ctx) {
            return new HoldingContext(ctx, holdings);
        }

        private HoldingContext withNoHoldings() {
            return new HoldingContext(ctx, List.of());
        }
    }

}
