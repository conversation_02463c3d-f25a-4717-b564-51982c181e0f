package cz.kpsys.portaro.record.detail.dflt;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.record.RecordSettingKeys;
import cz.kpsys.portaro.setting.CustomSetting;
import cz.kpsys.portaro.setting.CustomSettingLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CustomSettingLoadingAllDefaultFieldValuesLoader implements AllValuesProvider<CustomSetting<String>> {

    @NonNull CustomSettingLoader customSettingLoader;

    @Override
    public List<CustomSetting<String>> getAll() {
        return customSettingLoader.getAllBySection(RecordSettingKeys.SECTION_DEFVAL);
    }
}
