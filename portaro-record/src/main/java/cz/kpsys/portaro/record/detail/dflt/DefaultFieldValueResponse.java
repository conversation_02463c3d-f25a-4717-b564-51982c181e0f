package cz.kpsys.portaro.record.detail.dflt;

import cz.kpsys.portaro.department.DepartmentResponse;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.fond.SimpleFondResponse;
import io.micrometer.common.lang.Nullable;
import jakarta.validation.constraints.NotNull;
import lombok.NonNull;

public record DefaultFieldValueResponse(

        @NonNull
        FieldTypeId fieldTypeId,

        @NotNull
        DepartmentResponse department,

        @Nullable
        SimpleFondResponse fond,

        @NotNull
        String value

) {}
