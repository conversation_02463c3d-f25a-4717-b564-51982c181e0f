package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.commons.object.SeveritedException;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CoverSearchException extends RuntimeException implements SeveritedException, UserFriendlyException {

    public static final String CHYBEJICI_ISBN = "noIsbn";
    public static final String NO_IDENTIFIER = "noIdentifier";
    public static final String CHYBEJICI_OBALKA = "noCover";
    public static final String CONNECTION_PROBLEM = "connectionProblem";
    public static final String DOCASNE_ZABLOKOVANO = "temporaryBlocked";
    public static final String NOT_A_DOCUMENT = "notADocument";

    @NonNull
    String service;

    @NonNull
    String reason;

    int severity = SeveritedException.SEVERITY_WARNING;

    @NonNull
    Text text;

    public CoverSearchException(@NonNull String service, @NonNull String reason) {
        super(String.format("Cover search problem: %s", reason));
        this.service = service;
        this.reason = reason;
        this.text = Texts.ofMessageCoded("detail.CoverLoadProblem.%s".formatted(reason));
    }

    public CoverSearchException(@NonNull String service, @NonNull String reason,
                                @NonNull Throwable exception) {
        super(String.format("Cover search problem: %s", reason), exception);
        this.service = service;
        this.reason = reason;
        this.text = Texts.ofMessageCoded("detail.CoverLoadProblem.%s".formatted(reason));
    }
    
}
