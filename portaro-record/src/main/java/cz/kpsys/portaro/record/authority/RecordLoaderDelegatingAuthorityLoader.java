package cz.kpsys.portaro.record.authority;

import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RichRecordLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RecordLoaderDelegatingAuthorityLoader implements IdAndIdsLoadable<Record, UUID> {

    @NonNull RichRecordLoader richRecordLoader;

    @Override
    public Record getById(@NonNull UUID id) {
        return richRecordLoader.getById(id);
    }

    @Override
    public List<Record> getAllByIds(@NonNull List<UUID> ids) {
        return richRecordLoader.getAllByIds(ids, false);
    }

}
