package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.AllValuesProvidedCodebook;
import cz.kpsys.portaro.commons.object.repo.FallbackingCodebook;
import cz.kpsys.portaro.record.detail.source.FieldSource;
import cz.kpsys.portaro.record.detail.value.FieldValueConverterFactory;
import lombok.NonNull;

import java.util.Optional;

public class UnknownFieldTypeFactory {

    public static final String UNKNOWN_FIELD_NAME = "Unknown field";
    public static final String UNKNOWN_SUBFIELD_NAME = "Unknown subfield";

    // TODO: make this a bean some day
    @NonNull public static final FieldStorageBehaviourResolver fieldStorageBehaviourResolver = new FieldStorageBehaviourResolver();

    @NonNull Provider<@NonNull TransferType> defaultTransferTypeProvider = TransferType.DEFAULT_PROVIDER;

    public SimpleFieldType<?> createUnknownDatafieldType(FieldTypeId fieldTypeId) {
        return new SimpleFieldType<>(
                fieldTypeId,
                UNKNOWN_FIELD_NAME,
                false,
                false,
                true,
                FieldTypes.EMPTY_FORMULA,
                FieldTypes.NO_CODEBOOK,
                FieldSource.common(),
                defaultTransferTypeProvider.get(),
                FieldTypes.NO_DATATYPE,
                FieldTypes.NO_LINK_FOND,
                FieldExportSetting.toField(fieldTypeId),
                _ -> FieldValueConverterFactory.createDefaultForDatafield(),
                FieldTypes.NO_PIC,
                FdefGeneration.PHYSICAL,
                fieldStorageBehaviourResolver.resolve(FdefGeneration.PHYSICAL)
        );
    }

    public SimpleFieldType<?> createUnknownSubfieldType(FieldTypeId fieldTypeId) {
        return createDefaultUnknownSubfieldType(fieldTypeId);
    }

    // TODO: make it a bean, of possible
    public static SimpleFieldType<?> createDefaultUnknownSubfieldType(FieldTypeId fieldTypeId) {
        if (FieldTypes.INDICATORS_FIELD_CODES.contains(fieldTypeId.getCode())) {
            return createDefaultUnknnownIndicatorFieldType(fieldTypeId);
        }
        return new SimpleFieldType<>(
                fieldTypeId,
                UNKNOWN_SUBFIELD_NAME,
                false,
                true,
                true,
                FieldTypes.EMPTY_FORMULA,
                FieldTypes.NO_CODEBOOK,
                FieldSource.common(),
                TransferType.OVERWRITE,
                FieldTypes.TEXT_DATATYPE,
                FieldTypes.NO_LINK_FOND,
                FieldExportSetting.toSubfield(fieldTypeId),
                _ -> FieldValueConverterFactory.createDefaultForSimpleSubfield(),
                FieldTypes.NO_PIC,
                FdefGeneration.PHYSICAL,
                fieldStorageBehaviourResolver.resolve(FdefGeneration.PHYSICAL)
        );
    }

    private static @NonNull SimpleFieldType<?> createDefaultUnknnownIndicatorFieldType(FieldTypeId fieldTypeId) {
        return new SimpleFieldType<>(
                fieldTypeId,
                IndicatorType.getName(fieldTypeId),
                false,
                false,
                true,
                FieldTypes.EMPTY_FORMULA,
                Optional.of(FallbackingCodebook.notInFindById(AllValuesProvidedCodebook.ofEmptyIdentified(), IndicatorType.unknownIndicatorFactory(fieldTypeId)::getById)),
                FieldSource.common(),
                TransferType.OVERWRITE,
                Optional.of(IndicatorType.createIndicatorDatatype(fieldTypeId)),
                FieldTypes.NO_LINK_FOND,
                FieldExportSetting.toSubfield(fieldTypeId),
                _ -> FieldValueConverterFactory.createDefaultForIndicator(fieldTypeId),
                FieldTypes.NO_PIC,
                FdefGeneration.INDICATORS,
                fieldStorageBehaviourResolver.resolve(FdefGeneration.INDICATORS)
        );
    }

}
