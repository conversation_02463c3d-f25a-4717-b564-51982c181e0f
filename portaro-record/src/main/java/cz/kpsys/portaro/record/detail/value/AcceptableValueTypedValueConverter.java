package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AcceptableValueTypedValueConverter implements TypedFieldValueConverter<StringValueCommand, FieldPayload<AcceptableValueFieldValue<LabeledIdentified<?>>>, AcceptableValueFieldValue<LabeledIdentified<?>>> {

    @NonNull ByIdLoadable<? extends LabeledIdentified<?>, String> acceptableValuesLoader;

    @NonNull
    @Override
    public FieldPayload<AcceptableValueFieldValue<LabeledIdentified<?>>> convert(@NonNull StringValueCommand command, @NonNull FieldType<?> fieldType, @NonNull RecordFieldId fieldId) {
        var acceptableValue = getAcceptableValue(command.value().trim());
        return FieldPayload.of(AcceptableValueFieldValue.ofSomeIdentified(acceptableValue, Set.of(fieldId.recordIdFondPair())));
    }

    private LabeledIdentified<?> getAcceptableValue(String acceptableValueId) {
        return acceptableValuesLoader.getById(acceptableValueId);
    }

    @Override
    public String toString() {
        return getClass().getSimpleName();
    }
}
