package cz.kpsys.portaro.record.detail.spec;

import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

public record RecordRootId(

        @NonNull
        RecordIdFondPair recordIdFondPair

) implements RecordIndexedId {

    public static RecordRootId of(@NonNull RecordIdFondPair recordIdFondPair) {
        return new RecordRootId(recordIdFondPair);
    }

    @Override
    public @Nullable FieldId fieldId() {
        return null;
    }

    @Override
    public boolean hasParentIndexedId() {
        return false;
    }

    @Override
    public @NonNull RecordMultifieldId toSubmultifield(@NonNull FieldTypeId subTypeId) {
        return RecordMultifieldId.of(this, subTypeId);
    }

    @Override
    public @NonNull RecordIndexedId existingParentIndexedId() {
        throw new IllegalStateException("Root structured id has no parent structured id");
    }

    @Override
    public @NonNull RecordMultifieldId existingParentMultifieldId() {
        throw new IllegalStateException("Root structured id has no parent multifield id");
    }

    @Override
    public int nestingLevel() {
        return FieldTypeId.LEVEL_RECORD;
    }

    @Override
    public String toString() {
        return recordIdFondPair.id().abbr();
    }

}
