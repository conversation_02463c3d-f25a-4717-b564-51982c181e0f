package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.record.detail.By;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.detail.FieldId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RecordFieldMover {

    public void moveField(@NonNull FieldContainer fieldContainer, @NonNull FieldMovementCommand command) {
        Field<?> field = getField(fieldContainer, command.fieldId());
        FieldId parentFieldId = command.fieldId().isRoot() ? null : command.fieldId().getParent();
        FieldContainer container = command.fieldId().isRoot()
                ? fieldContainer
                : getField(fieldContainer, parentFieldId);

        List<Field<?>> allFields = container.getFields();

        //najdeme index fieldu v allFields
        int movedTopfield1Idx = allFields.indexOf(field);
        if (movedTopfield1Idx == -1) { //dane podpole v poli neni, coz by ale nemelo nastat
            return;
        }

        //najdeme druhy field, se kterym se vymenime
        int movedTopfield2Idx = movedTopfield1Idx + (command.direction() == FieldMovementDirection.DOWN ? 1 : -1);
        if (movedTopfield2Idx < 0 || movedTopfield2Idx > allFields.size() - 1) { //krajni podpole nemuzeme uz vice posunovat
            return;
        }

        //prohodime pole
        Field<?> movedTopfield2 = allFields.get(movedTopfield2Idx);
        container.swapFields(field, movedTopfield2);
    }

    private static Field<?> getField(@NonNull FieldContainer fieldContainer, FieldId fieldId) {
        return findField(fieldContainer, fieldId).orElseThrow();
    }

    private static Optional<Field<?>> findField(@NonNull FieldContainer fieldContainer, FieldId fieldId) {
        var editedFieldOpt = fieldContainer.getFirstFieldRecursive(By.fieldId(fieldId));
        if (editedFieldOpt.isPresent()) {
            return editedFieldOpt;
        }
        throw new IllegalStateException("Moving field " + fieldId + " does not exist in " + fieldContainer);
    }

}
