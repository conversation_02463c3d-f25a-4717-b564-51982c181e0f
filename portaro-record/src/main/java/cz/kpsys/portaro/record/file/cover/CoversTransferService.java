package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.business.consume.MultipleItemsConsumer;
import cz.kpsys.portaro.commons.file.ParsedFilename;
import cz.kpsys.portaro.commons.image.ImageFileFilter;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.Transferer;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.commons.util.RegExpUtils;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordDescriptor;
import cz.kpsys.portaro.record.RecordRegExpPatterns;
import cz.kpsys.portaro.record.RecordStub;
import cz.kpsys.portaro.record.file.RecordWithAttachmentSaveCommand;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.val;
import org.apache.commons.io.filefilter.AndFileFilter;
import org.apache.commons.io.filefilter.CanReadFileFilter;
import org.apache.commons.io.filefilter.DelegateFileFilter;
import org.apache.commons.io.filefilter.FileFileFilter;
import org.slf4j.Logger;
import org.springframework.core.convert.converter.Converter;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.File;
import java.io.FileFilter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class CoversTransferService {

    @NonNull Transferer<List<RecordWithAttachmentSaveCommand>> transferer;
    @NonNull MultipleItemsConsumer<List<RecordWithAttachmentSaveCommand>, RecordWithAttachmentSaveCommand> saver;
    @NonNull IdAndIdsLoadable<Record, UUID> nonDetailedRichRecordLoader;
    @NonNull CoverFactory coverFactory;


    /**
     * @param folder
     * @param idInFilenamePattern muze byt null, pak se hleda proste cislo v nazvech souboru.
     */
    @Transactional
    public void transfer(String folder, String idInFilenamePattern,
                         @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {

        val coversLoader = new FileCoversLoader(new File(folder), idInFilenamePattern,
                coverFactory, nonDetailedRichRecordLoader, currentAuth, ctx);
        transferer.transfer(coversLoader, saver);
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    private static class FileCoversLoader implements Provider<List<RecordWithAttachmentSaveCommand>> {

        private static final FileFilter FILE_FILTER = new AndFileFilter(List.of(
                CanReadFileFilter.CAN_READ,
                FileFileFilter.INSTANCE,
                new DelegateFileFilter(new ImageFileFilter()))
        );

        @NonNull Converter<File, RecordWithAttachmentSaveCommand> fileToCoverConverter;
        @NonNull File dir;
        @NonNull IdAndIdsLoadable<Record, UUID> nonDetailedRichRecordLoader;
        @NonNull UserAuthentication currentAuth;
        @NonNull Department ctx;

        public FileCoversLoader(@NonNull File dir,
                                @Nullable String idInFilenamePattern,
                                @NonNull CoverFactory coverFactory,
                                @NonNull IdAndIdsLoadable<Record, UUID> nonDetailedRichRecordLoader,
                                @NonNull UserAuthentication currentAuth,
                                @NonNull Department ctx) {
            this.dir = dir;
            this.fileToCoverConverter = new FileToCoverConverter(coverFactory, idInFilenamePattern);
            this.nonDetailedRichRecordLoader = nonDetailedRichRecordLoader;
            this.currentAuth = currentAuth;
            this.ctx = ctx;
        }

        @Override
        public List<RecordWithAttachmentSaveCommand> get() {
            File[] imageFiles = dir.listFiles(FILE_FILTER);
            return Arrays.stream(imageFiles)
                    .map(fileToCoverConverter::convert)
                    .toList();
        }


        @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
        private class FileToCoverConverter implements Converter<File, RecordWithAttachmentSaveCommand> {

            private static final Logger logger = org.slf4j.LoggerFactory.getLogger(FileToCoverConverter.class);
            private static final String WILDCARD_DOCUMENT_ID = "{documentId}";

            @NonNull CoverFactory coverFactory;
            @NonNull Pattern pattern;

            /**
             * Pattern, podle ktereho se z nazvu obrazku (souboru bez koncovky) vyextrahuje id zaznamu. <br/>
             * Napriklad {documentId} nebo obalka_{documentId}.
             */
            public FileToCoverConverter(@NonNull CoverFactory coverFactory,
                                        @Nullable String idInFilenamePattern) {

                this.coverFactory = coverFactory;
                if (StringUtil.isNullOrEmpty(idInFilenamePattern)) {
                    idInFilenamePattern = WILDCARD_DOCUMENT_ID;
                }
                Assert.state(idInFilenamePattern.contains(WILDCARD_DOCUMENT_ID),
                        "Given pattern %s must contain %s wildcard".formatted(idInFilenamePattern, WILDCARD_DOCUMENT_ID));
                this.pattern = Pattern.compile(getRegex(idInFilenamePattern));
            }


            @Override
            public RecordWithAttachmentSaveCommand convert(File imageFile) {
                logger.info("Loading cover (and parsing record id from filename) from image file {}.", imageFile.getAbsolutePath());
                var recordDescriptor = getDocumentFromFilename(imageFile);
                var record = nonDetailedRichRecordLoader.getById(recordDescriptor.getId());
                return RecordWithAttachmentSaveCommand.ofLoadedCover(record, coverFactory.createImportedFromFile(imageFile), currentAuth, ctx);
            }


            private RecordDescriptor getDocumentFromFilename(File imageFile) {
                ParsedFilename parsedFilename = ParsedFilename.parse(imageFile.getName());
                Matcher m = pattern.matcher(parsedFilename.mainName());
                boolean find = m.find();
                if (!find) {
                    throw new IllegalStateException("Document id by pattern %s is not in filename \"%s\".".formatted(pattern.pattern(), parsedFilename.mainName()));
                }
                String group = m.group(1);
                String idGroup = m.group(2);
                Integer recordKindedId = Integer.valueOf(group);
                return new RecordStub(UUID.fromString(idGroup), recordKindedId);
            }

            private String getRegex(String idInFilenamePattern) {
                return idInFilenamePattern.replace(WILDCARD_DOCUMENT_ID, RegExpUtils.group(RecordRegExpPatterns.RECORD_KINDED_ID));
            }

        }

    }

}
