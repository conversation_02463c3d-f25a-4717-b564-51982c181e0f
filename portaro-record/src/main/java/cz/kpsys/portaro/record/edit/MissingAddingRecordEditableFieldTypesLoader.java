package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class MissingAddingRecordEditableFieldTypesLoader implements RecordFieldTypesLoader {

    @NonNull RecordFieldTypesLoader target;
    @NonNull FieldTypesByFondLoader unknownSupportingEditableTopfieldTypeLoader;
    @NonNull ByIdLoadable<FieldType<?>, FieldTypeId> unknownSupportingSubfieldTypeLoader;

    @Override
    public List<EditableFieldType<?>> getFieldTypesByRecord(@NonNull Record record) throws EmptyWorksheetException {
        List<EditableFieldType<?>> editableTopfieldTypes = new ArrayList<>(target.getFieldTypesByRecord(record));

        fillMissingEditableFieldTypes(record.getFond(), record.getFields(), editableTopfieldTypes);

        return editableTopfieldTypes;
    }

    public void fillMissingEditableFieldTypes(@NonNull Fond fond, @NonNull Collection<Field<?>> actualFields, @NonNull List<EditableFieldType<?>> currentFieldTypeDatabaseToFill) {
        for (var topfield : actualFields) {
            EditableFieldType<?> editableTopfieldType = ListUtil.getByIdOrNull(currentFieldTypeDatabaseToFill, topfield.getTypeId());

            if (editableTopfieldType == null) {
                editableTopfieldType = unknownSupportingEditableTopfieldTypeLoader.getTopfieldTypeByFondAndIdOrUnknown(fond, topfield.getType().getFieldTypeId());
                currentFieldTypeDatabaseToFill.add(editableTopfieldType);
            }

            List<EditableFieldType<?>> editableSubfieldTypes = editableTopfieldType.getSubfieldTypes();
            if (!editableSubfieldTypes.isEmpty()) {
                for (var subfield : new ArrayList<>(topfield.getFields())) {
                    FieldType<?> subfieldType = ListUtil.getById(editableSubfieldTypes, subfield.getTypeId());
                    if (subfieldType == null) {
                        // this happens only when field is not defined in fdef (when not defined only in styles, ((EditableDatafieldType) editableTopfieldType).getSubfieldTypes() returns empty subfield)
                        subfieldType = unknownSupportingSubfieldTypeLoader.getById(FieldTypeId.parseSubfield(subfield.getTypeId()));
                        editableTopfieldType.addSubfieldType(subfieldType);
                    }
                }
            }
        }
    }

}
