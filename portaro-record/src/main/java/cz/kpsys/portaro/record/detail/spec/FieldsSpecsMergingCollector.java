package cz.kpsys.portaro.record.detail.spec;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collector;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldsSpecsMergingCollector implements Collector<FieldsSpec, FieldsSpecsMergingCollector.Builder, FieldsSpec> {

    @Override
    public Supplier<Builder> supplier() {
        return Builder::ofEmpty;
    }

    @Override
    public BiConsumer<Builder, FieldsSpec> accumulator() {
        return Builder::addAll;
    }

    @Override
    public BinaryOperator<Builder> combiner() {
        return (builder1, builder2) -> {
            builder1.addAll(builder2);
            return builder1;
        };
    }

    @Override
    public Function<Builder, FieldsSpec> finisher() {
        return Builder::build;
    }

    @Override
    public Set<Characteristics> characteristics() {
        return Set.of(Characteristics.UNORDERED);
    }

    @FieldDefaults(level = AccessLevel.PRIVATE)
    @AllArgsConstructor
    public static class Builder {

        @Nullable HashSet<FieldSpec> set;

        public static Builder ofEmpty() {
            return new Builder(new HashSet<>());
        }

        private void setAll() {
            set = null;
        }

        private boolean forAll() {
            return set == null;
        }

        private HashSet<FieldSpec> existingSpecs() {
            return Objects.requireNonNull(set);
        }

        private boolean isEmpty() {
            return set != null && set.isEmpty();
        }

        public void addAll(FieldsSpec other) {
            if (other.isEmpty()) {
                return;
            }
            if (forAll()) {
                return;
            }
            if (other.isForAll()) {
                setAll();
                return;
            }
            if (isEmpty()) {
                set = new HashSet<>(other.existingSpecs());
                return;
            }

            for (FieldSpec otherSpec : other.existingSpecs()) {
                merge(existingSpecs(), otherSpec);
            }
        }

        public void addAll(Builder other) {
            if (other.isEmpty()) {
                return;
            }
            if (forAll()) {
                return;
            }
            if (other.forAll()) {
                setAll();
                return;
            }
            if (isEmpty()) {
                set = other.existingSpecs();
                return;
            }

            for (FieldSpec otherSpec : other.existingSpecs()) {
                merge(existingSpecs(), otherSpec);
            }
        }

        public FieldsSpec build() {
            if (forAll()) {
                return FieldsSpec.ofAll();
            }
            if (isEmpty()) {
                return FieldsSpec.ofEmpty();
            }
            return FieldsSpec.of(existingSpecs());
        }

        private static void merge(@NonNull HashSet<FieldSpec> fieldSpecs, FieldSpec other) {
            if (fieldSpecs.stream().anyMatch(existingSpec -> existingSpec.covers(other))) {
                return;
            }
            fieldSpecs.removeIf(other::covers);
            fieldSpecs.add(other);
        }
    }
}
