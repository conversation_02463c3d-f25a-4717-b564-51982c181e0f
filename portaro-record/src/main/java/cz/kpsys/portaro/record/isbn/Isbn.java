package cz.kpsys.portaro.record.isbn;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.util.StringUtil;

import java.io.Serializable;
import java.util.List;

public class Isbn implements Serializable {
    
    private final String value;
    private String normalizedValue;
    private boolean valid;

    public Isbn(String value) {
        this.value = value;
        normalizeValue();
    }

    @Override
    public String toString() {
        return getValue();
    }

    public String getNormalizedValue() {
        return normalizedValue;
    }

    public String getValue() {
        return value;
    }

    @JsonIgnore
    public String getCoreValue() {
        if (normalizedValue == null) {
            return null;
        }
        if (IsbnChecker.isValidIsbn10(normalizedValue)) {
            return normalizedValue.substring(0, normalizedValue.length() - 1);
        }
        if (IsbnChecker.isValidIsbn13(normalizedValue)) {
            return normalizedValue.substring(3, normalizedValue.length() - 1);
        }
        if (IsbnChecker.isValidIssn8(normalizedValue)) {
            return normalizedValue.substring(0, normalizedValue.length() - 1);
        }
        if (IsbnChecker.isValidIssn13(normalizedValue)) {
            return normalizedValue.substring(3, normalizedValue.length() - 3);
        }
        throw new IllegalStateException(String.format("Given isbn/issn '%s' is not valid", normalizedValue));
    }

    @JsonIgnore
    public List<String> getAllPossibleNormalizedVariants() {
        if (normalizedValue != null) {
            if (IsbnChecker.isValidIsbn10(normalizedValue)) {
                return List.of(normalizedValue, toIsbn13With978Prefix(), toIsbn13With979Prefix());
            }
            if (IsbnChecker.isValidIsbn13(normalizedValue)) {
                return List.of(normalizedValue, toIsbn10());
            }
            if (IsbnChecker.isValidIssn8(normalizedValue)) {
                return List.of(normalizedValue, toIssn13());
            }
            if (IsbnChecker.isValidIssn13(normalizedValue)) {
                return List.of(normalizedValue); // toIssn8 method is not yet implemented - we dont know how to compute check digit
            }
        }
        throw new IllegalStateException("Value %s is not valid isbn/issn".formatted(value));
    }

    public boolean isValid() {
        return valid;
    }

    /**
     * nastavi hodnotu trimmedValue (otrimuje, oreze pomlcky apod.)
     */
    private void normalizeValue() {
        if (StringUtil.isNullOrBlank(value)) {
            valid = false;
            normalizedValue = null;
            return;
        }
        
        //otrimovani isbn stringu
        String trimmedIsbn = value.replace("-", "");
        if (trimmedIsbn.contains("(")) {
            //existuje vysvetlivka
            trimmedIsbn = trimmedIsbn.substring(0, trimmedIsbn.indexOf('('));
        }
        if (trimmedIsbn.contains(":")) {
            //existuje cena
            trimmedIsbn = trimmedIsbn.substring(0, trimmedIsbn.indexOf(':'));
        }
        trimmedIsbn = trimmedIsbn.trim();
        
        //pokud je trimmed isbn kratsi nez nekaja rozumna hodnota, nejedna se o isbn
        valid = IsbnChecker.isValidIsbn(value) || IsbnChecker.isValidIssn(value);

        if (valid) {
            normalizedValue = trimmedIsbn;
        }
    }

    private String toIsbn13With978Prefix() {
        String coreValue = getCoreValue();
        String valueWithoutChecksum = "978" + coreValue;
        return valueWithoutChecksum + Isbn13Validator.computeEan13Checksum(valueWithoutChecksum);
    }

    private String toIsbn13With979Prefix() {
        String coreValue = getCoreValue();
        String valueWithoutChecksum = "979" + coreValue;
        return valueWithoutChecksum + Isbn13Validator.computeEan13Checksum(valueWithoutChecksum);
    }

    private String toIsbn10() {
        String coreValue = getCoreValue();
        return coreValue + Isbn10Validator.computeIsbn10CheckDigit(coreValue);
    }

    private String toIssn13() {
        String coreValue = getCoreValue();
        String valueWithoutChecksum = "977" + coreValue + "00";
        return valueWithoutChecksum + Isbn13Validator.computeEan13Checksum(valueWithoutChecksum);
    }

}
