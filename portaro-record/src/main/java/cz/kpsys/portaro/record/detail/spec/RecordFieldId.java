package cz.kpsys.portaro.record.detail.spec;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;
import lombok.With;

import java.util.Set;

@With
public record RecordFieldId(

        @NonNull
        RecordIdFondPair recordIdFondPair,

        @NonNull
        FieldId fieldId

) implements RecordIndexedId, FieldedRecordSpec, RecordDatableId {

    public static RecordFieldId of(@NonNull RecordIdFondPair recordIdFondPair, @NonNull FieldId fieldId) {
        return new RecordFieldId(recordIdFondPair, fieldId);
    }

    @JsonIgnore
    @Override
    public boolean hasParentIndexedId() {
        return true;
    }

    @JsonIgnore
    public boolean hasParentFieldId() {
        return fieldId.hasParent();
    }

    @JsonIgnore
    public boolean isRootFieldId() {
        return fieldId.isRoot();
    }

    @JsonIgnore
    @Override
    public @NonNull RecordFieldId existingParentFieldId() {
        return of(recordIdFondPair(), fieldId.existingParent());
    }

    @Override
    public @NonNull RecordMatcher recordMatcher() {
        return RecordIdentifierRecordMatcher.of(recordIdFondPair);
    }

    @Override
    public @NonNull FieldsSpec fieldsSpec() {
        return FieldsSpec.of(FieldSpec.ofField(fieldId()));
    }

    @Override
    public @NonNull Set<FieldSpec> existingFields() {
        return Set.of(FieldSpec.ofField(fieldId()));
    }

    @Override
    public @NonNull FieldedRecordSpec withFieldsSpec(@NonNull FieldsSpec fieldsSpec) {
        return RecordSpec.ofGeneric(recordIdFondPair, fieldsSpec);
    }

    @Override
    public @NonNull RecordMultifieldId toSubmultifield(@NonNull FieldTypeId subTypeId) {
        return RecordMultifieldId.of(this, subTypeId);
    }

    @Override
    public @NonNull RecordMultifieldId existingParentMultifieldId() {
        return RecordMultifieldId.of(existingParentIndexedId(), fieldId.toFieldTypeId());
    }

    @Override
    public @NonNull RecordIndexedId existingParentIndexedId() {
        return hasParentFieldId() ? existingParentFieldId() : RecordRootId.of(recordIdFondPair);
    }

    @Override
    public @NonNull RecordFieldTypeId toRecordFieldTypeId() {
        return RecordFieldTypeId.of(recordIdFondPair, fieldId.toFieldTypeId());
    }

    @Override
    public @NonNull RecordSpec toRecordSpec() {
        return this;
    }

    @Override
    public boolean isEmpty() {
        return false;
    }

    @Override
    public int nestingLevel() {
        return fieldId().getLevel();
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, FieldedRecordSpec.class, FieldedRecordSpec::recordMatcher, FieldedRecordSpec::fieldsSpec);
    }

    @Override
    public int hashCode() {
        int result = recordIdFondPair.hashCode();
        result = 31 * result + fieldId.hashCode();
        return result;
    }

    @Override
    public String toString() {
        return recordIdFondPair.id().abbrDelim() + fieldId;
    }

}
