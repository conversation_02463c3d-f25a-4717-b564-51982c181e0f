package cz.kpsys.portaro.record.detail.binding;

import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.value.AcceptableValueFieldValue;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.fond.Bindings;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.StaticParamsModifier;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserConstants;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UserBoundFieldsLoader implements Function<Set<RecordIdFondPair>, Map<RecordIdFondPair, ? extends ScalarFieldValue<?>>> {

    @NonNull ParameterizedSearchLoader<MapBackedParams, User> userSearchLoader;

    @Override
    public Map<RecordIdFondPair, AcceptableValueFieldValue<User>> apply(@NonNull Set<RecordIdFondPair> recordIdFondPairs) {
        List<RecordIdFondPair> relevantRecordIds = getRelevantRecordIds(recordIdFondPairs); // this should be removed after binding definition will be in field type, not in fond.binding ?
        AllByIdsLoadable<User, UUID> usersByRecordIdsLoader = recordIds -> userSearchLoader.getContent(RangePaging.forAll(), StaticParamsModifier.of(UserConstants.SearchParams.RECORD_ID, recordIds));
        return DataUtils.batchLoadDtoValue(relevantRecordIds, recordIdFondPair -> recordIdFondPair.id().id(), User::getRecordId, usersByRecordIdsLoader, false, true) // users do not have to have record ids
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> AcceptableValueFieldValue.ofGeneric(e.getValue(), Set.of(e.getKey()))));
    }

    private static List<RecordIdFondPair> getRelevantRecordIds(@NonNull Set<RecordIdFondPair> recordIdFondPairs) {
        return ListUtil.filter(recordIdFondPairs, recordIdFondPair -> {
            Fond fond = recordIdFondPair.fond();
            return Bindings.USER.equals(fond.getBinding());
        });
    }
}
