package cz.kpsys.portaro.record.evaluation;

import lombok.NonNull;
import lombok.Value;

import java.io.Serializable;
import java.time.Instant;
import java.util.UUID;

@Value
public class DocumentRating implements Serializable {

    @NonNull Integer id;
    @NonNull UUID recordId;
    @NonNull Integer count;
    @NonNull Double value;
    @NonNull Instant lastRatingDate;

    public String getFormattedValue() {
        return "" + Math.round(getValue() * 100) / (double) 100;
    }

}
