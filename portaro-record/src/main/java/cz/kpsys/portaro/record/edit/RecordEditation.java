package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.RecordStatus;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.List;

public interface RecordEditation extends Identified<String>, CreatableFieldContainer, FieldTypeListable<FieldType<?>>, RecordEditationNotifier {

    boolean isDraft();

    boolean isDeleted();

    boolean isRevisionSaved();

    boolean isAuthority();

    @NonNull
    Fond getFond();

    @NonNull
    Record getRecord();

    @NonNull
    List<? extends EditableFieldType<?>> getSubfieldTypes();

    RecordEditation replaceAllFields(FieldContainer newFields);

    RecordEditation moveFields(FieldMovementCommand command);

    RecordEditation changeFond(Fond fond);

    RecordEditation changeStatus(RecordStatus status, Department ctx);

    void copyTopfield(Field<?> targetField, Field<?> sourceTopfield);

    /**
     * Sets record status to published (concrete enum value depends on record kind) and saves record.
     */
    RecordEditation publish(@NonNull Department ctx, @NonNull UserAuthentication currentAuth) throws IllegalStateException;

    RecordEditation saveIfModified(@NonNull Department ctx, @NonNull UserAuthentication currentAuth);

    @Override
    default RecordEditation copy(@NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId) {
        throw new IllegalArgumentException("RecordEditations are not copiable!");
    }
}
