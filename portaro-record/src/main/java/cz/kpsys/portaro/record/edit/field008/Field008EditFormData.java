package cz.kpsys.portaro.record.edit.field008;

import cz.kpsys.portaro.commons.util.ListUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class Field008EditFormData {

    private static final List<String> typy_dok = List.of(
            "BK = Knihy",
            "CF = Počítačové soubory",
            "MP = Mapy",
            "MU = Hudba",
            "CR = Pokračující zdroje",
            "VM = Vizuální dokumenty",
            "MX = Smíšené dokumenty");


    private static final List<String> popisky_bk = List.of(
            "18 Ilustrace",
            "19 Ilustrace",
            "20 Ilustrace",
            "21 Ilustrace",
            "22 Uživatelské určení",
            "23 Forma popisné jednotky",
            "24 Povaha obsahu",
            "25 Povaha obsahu",
            "26 Povaha obsahu",
            "27 Povaha obsahu",
            "28 Vládn<PERSON> publikace",
            "29 Publikace z konference",
            "30 Jubilejní s<PERSON>",
            "31 Rejst<PERSON><PERSON>",
            "32",
            "33 Literární forma",
            "34 Biografie");

    private static final List<Integer> posice_bk = List.of(18, 18, 18, 18, 22, 23, 24, 24, 24, 24, 28, 29, 30, 31, 0, 33, 34);


    private static final List<String> popisky_cf = List.of(
            "18",
            "19",
            "20",
            "21",
            "22 Uživatelsk<PERSON> určen<PERSON>",
            "23 Forma popisn<PERSON> jednotky",
            "24",
            "25",
            "26 Typ počítačového souboru",
            "27",
            "28 Vládní publikace",
            "29",
            "30",
            "31",
            "32",
            "33",
            "34");

    private static final List<Integer> posice_cf = List.of(0, 0, 0, 0, 22, 23, 0, 0, 26, 0, 28, 0, 0, 0, 0, 0, 0);


    private static final List<String> popisky_mp = List.of(
            "18 Reliéf",
            "19 Reliéf",
            "20 Reliéf",
            "21 Reliéf",
            "22-23 Zobrazení",
            "",
            "24",
            "25 Typ kartografického dokumentu",
            "26",
            "27",
            "28 Vládní publikace",
            "29 Forma popisné jednotky",
            "30",
            "31 Rejstřík",
            "32",
            "33 Speciální formát",
            "34 Speciální formát");

    private static final List<Integer> posice_mp = List.of(18, 18, 18, 18, 22, 0, 0, 25, 0, 0, 28, 29, 0, 31, 0, 33, 33);


    private static final List<String> popisky_mu = List.of(
            "18-19 Forma skladby",
            "",
            "20 Formát hudebniny",
            "21 Hudební hlasy",
            "22 Uživatelské určení",
            "23 Forma popisné jednotky",
            "24 Doprovodný materiál",
            "25 Doprovodný materiál",
            "26 Doprovodný materiál",
            "27 Doprovodný materiál",
            "28 Doprovodný materiál",
            "29 Doprovodný materiál",
            "30 Literární text pro zvukové záznamy",
            "31 Literární text pro zvukové záznamy",
            "32",
            "33 Transpozice a aranžmá",
            "34");

    private static final List<Integer> posice_mu = List.of(18, 0, 20, 21, 22, 23, 24, 24, 24, 24, 24, 24, 30, 30, 0, 33, 0);


    private static final List<String> popisky_cr = List.of(
            "18 Periodicita",
            "19 Pravidelnost",
            "20 Středisko ISSN",
            "21 Typ pokračujícího zdroje",
            "22 Forma původní popis.jednotky",
            "23 Forma popisné jednotky",
            "24 Povaha celého díla",
            "25 Povaha obsahu",
            "26 Povaha obsahu",
            "27 Povaha obsahu",
            "28 Vládní publikace",
            "29 Publikace z konference",
            "30",
            "31",
            "32",
            "33 Původní abeceda",
            "34 Konvence tvorby záznamu");

    private static final List<Integer> posice_cr = List.of(18, 19, 20, 21, 22, 23, 24, 25, 25, 25, 28, 29, 0, 0, 0, 33, 34);


    private static final List<String> popisky_vm = List.of(
            "18",
            "19",
            "20",
            "21",
            "22 Uživatelské určení",
            "23",
            "24",
            "25",
            "26",
            "27",
            "28 Vládní publikace",
            "29 Forma popisné jednotky",
            "30",
            "31",
            "32",
            "33 Typ vizuálního dokumentu",
            "34 Technika");

    private static final List<Integer> posice_vm = List.of(0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 28, 29, 0, 0, 0, 33, 34);


    private static final List<String> popisky_mx = List.of(
            "18",
            "19",
            "20",
            "21",
            "22",
            "23 Forma popisné jednotky",
            "24",
            "25",
            "26",
            "27",
            "28",
            "29",
            "30",
            "31",
            "32",
            "33",
            "34");

    private static final List<Integer> posice_mx = List.of(0, 0, 0, 0, 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);


    private static final List<String> publicationStatus = List.of(
            "b = data neuvedena",
            "| = kód se neuvádí",
            "e = podrobné datum",
            "s = jedno známé(pravděpodobné) datum",
            "i = data zahrnuta ve sbírce",
            "k = data většiny sbírky",
            "m = složená data",
            "p = datum distribuce",
            "r = datum reprintu",
            "t = datum vydání a datum copyrightu",
            "n = neznámá data",
            "q = nejisté datum",
            "u = status neznámý",
            "c = průběžně vycházející",
            "d = nevycházející");


    private static final List<String> modifiedRecord = List.of(
            "| = Kód se neuvádí",
            "o = Plně v latince",
            "# = Nemodifikován",
            "d = Vynechán podrobný popis",
            "r = Plně v latince-Tisk v nelatince",
            "s = zkrácený",
            "x = vynechané znaky");


    private static final List<String> catalogingSource = List.of(
            "| = kód se neuvádí",
            "# = Národní bibliograf.agentura",
            "c = program kooper. katalogizace",
            "d = jiný zdroj",
            "u = není znám");



    public static List<Field008DocumentTypeEntity> getDocumentTypeEntities() {
        return IntStream
                .range(0, typy_dok.size())
                .mapToObj(index -> new Field008DocumentTypeEntity(index, typy_dok.get(index).substring(0, 2), typy_dok.get(index).substring(5))) // take list index as ID
                .collect(Collectors.toList());
    }

    public static List<Field008LabelEntity> getLabelEntities() {
        List<Field008LabelEntity> labelEntities = new ArrayList<>();

        labelEntities.addAll(createLabelEntities("BK", Field008EditFormData.popisky_bk, Field008EditFormData.posice_bk));
        labelEntities.addAll(createLabelEntities("CF", Field008EditFormData.popisky_cf, Field008EditFormData.posice_cf));
        labelEntities.addAll(createLabelEntities("MP", Field008EditFormData.popisky_mp, Field008EditFormData.posice_mp));
        labelEntities.addAll(createLabelEntities("MU", Field008EditFormData.popisky_mu, Field008EditFormData.posice_mu));
        labelEntities.addAll(createLabelEntities("CR", Field008EditFormData.popisky_cr, Field008EditFormData.posice_cr));
        labelEntities.addAll(createLabelEntities("VM", Field008EditFormData.popisky_vm, Field008EditFormData.posice_vm));
        labelEntities.addAll(createLabelEntities("MX", Field008EditFormData.popisky_mx, Field008EditFormData.posice_mx));

        return labelEntities;
    }

    public static String LANGUAGE_LABEL = "Jazyk obsahu";
    public static String PLACE_OF_PUBLICATION_LABEL = "Kód země";

    public static Field008Position getPublicationStatus() {
        return new Field008Position("Status publikování", parseCodeEntities(publicationStatus));
    }

    public static Field008Position getModifiedRecord() {
        return new Field008Position("Modifikace záznamu", parseCodeEntities(modifiedRecord));
    }

    public static Field008Position getCatalogingSource() {
        return new Field008Position("Zdroj katalogizace", parseCodeEntities(catalogingSource));
    }

    private static Field008StaticCode parseCodeEntity(String rawValue) {
        return new Field008StaticCode(rawValue.substring(0, 1), rawValue.substring(4));
    }

    private static List<Field008StaticCode> parseCodeEntities(List<String> rawValues) {
        return ListUtil.convert(rawValues, Field008EditFormData::parseCodeEntity);
    }

    private static List<Field008LabelEntity> createLabelEntities(String documentTypeCode, List<String> rawLabels, List<Integer> positions) {
        return IntStream
                .range(0, rawLabels.size())
                .mapToObj(index -> new Field008LabelEntity(index + 18, documentTypeCode, rawLabels.get(index), positions.get(index) > 0))
                .collect(Collectors.toList());
    }
}
