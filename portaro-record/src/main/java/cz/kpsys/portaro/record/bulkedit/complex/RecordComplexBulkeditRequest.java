package cz.kpsys.portaro.record.bulkedit.complex;

import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.List;

@Form(id = "recordComplexBulkedit", title = "{commons.Uprava}")
@FormSubmit(path = "/api/records/fields/bulkedit/complex")
public record RecordComplexBulkeditRequest(

        @NotNull
        @NotEmpty
        List<@Valid RecordReusingRequest> reusing,

        @NotNull
        @NotEmpty
        List<@Valid FieldRequest> value

) {}
