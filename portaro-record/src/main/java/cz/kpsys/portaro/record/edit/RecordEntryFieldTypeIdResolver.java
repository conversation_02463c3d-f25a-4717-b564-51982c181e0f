package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.link.CustomEntrySubfieldLinkedFieldSpec;
import cz.kpsys.portaro.record.detail.link.LinkedFieldSpec;
import cz.kpsys.portaro.record.detail.link.NativeEntrySubfieldLinkedFieldSpec;
import cz.kpsys.portaro.record.detail.link.SpecificLinkedFieldSpec;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.NonNull;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface RecordEntryFieldTypeIdResolver {

    default @NonNull Optional<FieldTypeId> getEntryNativeSubfieldOpt(@NonNull Fond fond) {
        if (!fond.hasEntryFieldType()) {
            return Optional.empty();
        }
        return Optional.of(getEntryNativeSubfield(fond));
    }

    @NonNull FieldTypeId getEntryNativeSubfield(@NonNull Fond fond);

    @NonNull FieldTypeId getEntryCustomSubfield(@NonNull Fond fond, @NonNull String subfieldCode);

    default @NonNull FieldTypeId getLinkedFieldType(@NonNull Fond fond, @NonNull LinkedFieldSpec linkedFieldSpec) {
        return switch (linkedFieldSpec) {
            case NativeEntrySubfieldLinkedFieldSpec _ -> getEntryNativeSubfield(fond);
            case CustomEntrySubfieldLinkedFieldSpec spec -> getEntryCustomSubfield(fond, spec.customEntrySubfieldCode());
            case SpecificLinkedFieldSpec spec -> spec.targetFieldTypeId();
        };
    }

    /**
     * Returns map of recordable fond to its entry subfield
     */
    default @NonNull Map<Fond, FieldTypeId> getRecordableFondEntryNativeSubfieldTypes(@NonNull Collection<Fond> fonds) {
        return fonds.stream()
                .filter(Fond::isRecordable)
                .distinct()
                .collect(Collectors.toMap(Function.identity(), this::getEntryNativeSubfield));
    }

    /**
     * Returns map of fond to its entry subfield. Applicable to all fonds (ignores fond views).
     */
    default @NonNull Map<Fond, FieldTypeId> getFondEntryNativeSubfieldTypes(@NonNull Collection<Fond> fonds) {
        return fonds.stream()
                .filter(Fond::hasEntryFieldType)
                .distinct()
                .collect(Collectors.toMap(Function.identity(), this::getEntryNativeSubfield));
    }

}
