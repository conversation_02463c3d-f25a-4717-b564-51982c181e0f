package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.PrefabCodebookDelegatingFieldTypeIdByConfigPathLoader;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class DefaultRecordEntryFieldTypeIdResolver implements RecordEntryFieldTypeIdResolver {

    @NonNull PrefabCodebookDelegatingFieldTypeIdByConfigPathLoader fieldTypeIdByConfigPathLoader;

    @Override
    public @NonNull FieldTypeId getEntryNativeSubfield(@NonNull Fond fond) {
        FieldTypeId nativeEntrySubfieldConfigPath = fond.existingEntryFieldTypeConfigId();
        return fieldTypeIdByConfigPathLoader.findByConfigPath(nativeEntrySubfieldConfigPath)
                .orElseThrow(() -> new IllegalStateException("Invalid fdef[aut] configuration - subfield config id %s (as entryfield of fond %s) does not exist".formatted(nativeEntrySubfieldConfigPath, fond.getId())));
    }

    @Override
    public @NonNull FieldTypeId getEntryCustomSubfield(@NonNull Fond fond, @NonNull String subfieldCode) {
        FieldTypeId nativeEntrySubfieldConfigPath = fond.existingEntryFieldTypeConfigId();
        FieldTypeId customEntrySubfieldConfigPath = nativeEntrySubfieldConfigPath.withCode(subfieldCode);
        Optional<FieldTypeId> customEntrySubfieldTypeId = fieldTypeIdByConfigPathLoader.findByConfigPath(customEntrySubfieldConfigPath);
        if (customEntrySubfieldTypeId.isEmpty()) {
            log.warn("Invalid fdef[aut] configuration - subfield config path {} (as sibling entryfield of fond {}) does not exist", customEntrySubfieldConfigPath, fond.getId());
            return customEntrySubfieldConfigPath;
        }
        return customEntrySubfieldTypeId.get();
    }

}
