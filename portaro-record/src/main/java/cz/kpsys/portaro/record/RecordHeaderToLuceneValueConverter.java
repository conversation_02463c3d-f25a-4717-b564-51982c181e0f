package cz.kpsys.portaro.record;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordHeaderToLuceneValueConverter implements Converter<RecordIdFondPair, String> {

    @Override
    public String convert(RecordIdFondPair source) {
        return "\"%s\"".formatted(source.getId().id());
    }

}
