package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.spec.RecordFieldTypeId;
import lombok.NonNull;

public record NotAScalarFail(

        @NonNull Text text

) implements NonAbsentDataFail {

    public static @NonNull FailedResult of(@NonNull RecordFieldTypeId srcRecordFieldType, @NonNull Formula<?> operand) {
        return new NotAScalarFail(Texts.ofNative("Not a scalar in " + operand));
    }

    @Override
    public String toString() {
        return "NotAScalar";
    }

}
