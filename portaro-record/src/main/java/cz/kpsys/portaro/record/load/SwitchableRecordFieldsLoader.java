package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.IdentifiedFieldContainer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SwitchableRecordFieldsLoader implements RecordFieldsLoader {

    @NonNull Provider<@NonNull Boolean> mainLoaderEnabledProvider;
    @NonNull RecordFieldsLoader mainLoader;
    @NonNull RecordFieldsLoader altLoader;

    @Override
    public List<IdentifiedFieldContainer> load(@NonNull List<RecordIdFondPair> recordIdFondPairs) {
        if (mainLoaderEnabledProvider.get()) {
            return mainLoader.load(recordIdFondPairs);
        } else {
            return altLoader.load(recordIdFondPairs);
        }
    }

    @Override
    public IdentifiedFieldContainer refresh(@NonNull Record record) {
        if (mainLoaderEnabledProvider.get()) {
            return mainLoader.refresh(record);
        } else {
            return altLoader.refresh(record);
        }
    }
}
