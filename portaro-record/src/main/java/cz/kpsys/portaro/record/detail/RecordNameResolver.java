package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.detail.value.StringFieldValue;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RecordNameResolver {

    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;

    public String resolve(Record record) {
        FieldTypeId entrySubfield = recordEntryFieldTypeIdResolver.getEntryNativeSubfield(record.getFond());
        Field<?> field = FieldFinders.<FieldContainer, Field<?>>byDeepTypeId(entrySubfield).findFirstIn(record.getDetail()).orElseThrow();
        ScalarFieldValue<?> valueHolder = field.getExistingValueHolder();
        StringFieldValue stringFieldValue = ObjectUtil.cast(valueHolder, StringFieldValue.class, () -> "Name field value holder must be StringFieldValueHolder, but is " + valueHolder.getClass().getSimpleName());
        return stringFieldValue.value();
    }
}
