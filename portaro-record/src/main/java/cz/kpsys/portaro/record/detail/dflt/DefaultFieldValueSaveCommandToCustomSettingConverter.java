package cz.kpsys.portaro.record.detail.dflt;

import cz.kpsys.portaro.commons.object.BasicIdentified;
import cz.kpsys.portaro.record.RecordSettingKeys;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.setting.CustomSetting;
import cz.kpsys.portaro.setting.CustomSettingId;
import cz.kpsys.portaro.setting.CustomSettingLoader;
import cz.kpsys.portaro.setting.SettingTypeId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DefaultFieldValueSaveCommandToCustomSettingConverter implements Converter<DefaultFieldValueSaveCommand, CustomSetting<String>> {

    @NonNull CustomSettingLoader customSettingLoader;

    @Override
    public CustomSetting<String> convert(@NonNull DefaultFieldValueSaveCommand source) {
        String settingName = source.fieldTypeId().value().replace(FieldTypeId.DELIMITER, CustomSettingToDefaultFieldValueConverter.FIELD_CODE_DELIMITER);
        CustomSettingId customSettingId = new CustomSettingId(new SettingTypeId(RecordSettingKeys.SECTION_DEFVAL, settingName), source.department().getId(), source.fond().map(BasicIdentified::getId).orElse(null));
        var loadedCustomSetting = customSettingLoader.getByCompleteIdOrCreateEmpty(customSettingId);
        return new CustomSetting<>(customSettingId, source.value(), null, loadedCustomSetting.getUuid());
    }

}
