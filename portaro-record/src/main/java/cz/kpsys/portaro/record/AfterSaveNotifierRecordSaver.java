package cz.kpsys.portaro.record;

import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.scheduler.DatabaseQueue;
import cz.kpsys.portaro.tx.AfterCommitCallbackRegistrar;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AfterSaveNotifierRecordSaver implements RecordSaver {

    @NonNull RecordSaver delegate;
    @NonNull DatabaseQueue<UUID> databaseQueue;

    @Override
    public Record save(RecordSaveCommand cmd) {
        Record save = delegate.save(cmd);
        if (false) {
            AfterCommitCallbackRegistrar.register(() -> databaseQueue.enqueue("record-processing", UuidGenerator.forIdentifier(), cmd.record().getId()));
        }
        return save;
    }
}
