package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.commons.object.SeveritedException;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RecordIsLockedException extends RuntimeException implements SeveritedException, UserFriendlyException {

    @NonNull Record record;

    public RecordIsLockedException(@NonNull String message, @NonNull Record record) {
        super(message);
        this.record = record;
    }

    @Override
    public Text getText() {
        return Texts.ofNative("Record(" + record.getId() + ") is locked for editation.");
    }

    @Override
    public int getSeverity() {
        return SEVERITY_WARNING;
    }
}