package cz.kpsys.portaro.record.edit.field008;

import cz.kpsys.portaro.commons.object.Identified;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.Value;

import java.io.Serializable;

@Value
@AllArgsConstructor
public class Field008DocumentTypeEntity implements Identified<Integer>, Serializable {

    @NonNull
    Integer id; // id is required for acceptable value editor to work properly

    @NonNull
    String code;

    @NonNull
    String description;
}
