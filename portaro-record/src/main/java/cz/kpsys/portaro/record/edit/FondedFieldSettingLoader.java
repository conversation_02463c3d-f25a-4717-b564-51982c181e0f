package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.NonNull;

import java.util.Map;

public interface FondedFieldSettingLoader {

    Map<FieldTypeId, FieldSettings> getFieldTypedByEffectiveFond(@NonNull Fond fond);

    Map<Fond, FieldSettings> getEffectiveFondedByFieldTypeId(@NonNull FieldTypeId fieldTypeId);

}
