package cz.kpsys.portaro.record.search.restriction;

import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.isbn.IsbnChecker;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.field.StaticSearchFields;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.FalseRestriction;
import cz.kpsys.portaro.search.restriction.RawRestriction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import cz.kpsys.portaro.search.restriction.matcher.EqWords;
import cz.kpsys.portaro.search.restriction.modifier.RestrictionModifier;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import static cz.kpsys.portaro.record.RecordConstants.SearchParams.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DocumentFieldsSearchParamsRestrictionModifier implements RestrictionModifier<MapBackedParams> {

    @NonNull ContextualFunction<@NonNull String, @NonNull Department, @NonNull String> recordGlobalSearchQToRawQueryConverter;

    @Override
    public Conjunction<SearchField> modify(Conjunction<SearchField> documentConjunction, MapBackedParams p, Department ctx) {

        if (p.hasNotNull(ISBN_OR_ISSN)) {
            String isbnOrIssnValue = p.get(ISBN_OR_ISSN);
            if (IsbnChecker.isValidIsbn(isbnOrIssnValue)) {
                documentConjunction.add(new Term<>(StaticSearchFields.DOCUMENT_ISBN, new EqWords(isbnOrIssnValue)));
            } else if (IsbnChecker.isValidIssn(isbnOrIssnValue)) {
                documentConjunction.add(new Term<>(StaticSearchFields.DOCUMENT_ISSN, new EqWords(isbnOrIssnValue)));
            } else {
                documentConjunction.add(FalseRestriction.create());
            }
        }
        documentConjunction.addIfHas(p, AUTHOR, val -> new Term<>(StaticSearchFields.AUTHOR, new EqWords(val)));
        documentConjunction.addIfHas(p, PUBLICATION_YEAR, val -> new Term<>(StaticSearchFields.DOCUMENT_YEAR, new Eq(val)));

        if (p.hasNotNull(CoreSearchParams.Q) && !p.hasNotNull(CoreSearchParams.FIELD)) {
            String rawQuery = recordGlobalSearchQToRawQueryConverter.getOn(p.get(CoreSearchParams.Q), ctx);
            documentConjunction.add(new RawRestriction<>(rawQuery));
        }

        return documentConjunction;
    }
}
