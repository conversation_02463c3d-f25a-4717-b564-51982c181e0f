package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.commons.object.NonNullOrderedRecord;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.record.RecordIdentifier;
import jakarta.annotation.Nullable;
import lombok.NonNull;

import java.time.Instant;

public record LegacyRecordTopfield<V extends LegacyRecordFieldValue>(

        @NonNull
        Integer id,

        @NonNull
        RecordIdentifier recordIdentifier,

        @NonNull
        String topfieldCode,

        @NonNull
        Integer sameCodeIndex,

        @Nullable
        @NullableNotBlank
        String ind1,

        @Nullable
        @NullableNotBlank
        String ind2,

        @NonNull
        V value,

        @NonNull
        Instant lastUpdateDate,

        @NonNull
        Integer lastUpdateUserId

) implements IdentifiedRecord<Integer>, NonNullOrderedRecord, LegacyRecordFieldable<V> {

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equalsIdentified(this, o, LegacyRecordTopfield.class);
    }

    @Override
    public int hashCode() {
        return getId().hashCode();
    }
}
