package cz.kpsys.portaro.record.authority;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.HierarchyTree;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.record.BasicRecordDescriptor;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordDescriptor;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.COLSEQ;
import static cz.kpsys.portaro.commons.db.QueryUtils.TC;
import static cz.kpsys.portaro.databasestructure.RecordDb.*;
import static cz.kpsys.portaro.databasestructure.RecordDb.KATAUT_4.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbAuthorityHierarchyLoader implements RecordHierarchyLoader {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;


    @Override
    public List<HierarchyTree<UUID, RecordHierarchyTreeNode>> getHierarchyTrees(@NonNull UUID recordId) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.fromFunction(SPROC_AUT_STROM.SPROC_AUT_STROM, recordId);
        List<RecordHierarchyTreeNode> nodes = jdbcTemplate.query(sq.getSql(), sq.getParamMap(), new RowMapper<>() {

            /**
             * Vrati spravny typ podle daneho typu vazby (sloupec TYP_VAZBY v procedure
             * sproc_aut_strom)
             */
            private RecordHierarchyTreeNode.RecordHierarchyTreeNodeType getNodeTypeByRelationType(int relType) {
                return switch (relType) {
                    case 0 -> RecordHierarchyTreeNode.RecordHierarchyTreeNodeType.CURRENT;
                    case 1 -> RecordHierarchyTreeNode.RecordHierarchyTreeNodeType.PARENT;
                    case 2 -> RecordHierarchyTreeNode.RecordHierarchyTreeNodeType.CHILD;
                    case 3 -> RecordHierarchyTreeNode.RecordHierarchyTreeNodeType.SEE;
                    case 4 -> RecordHierarchyTreeNode.RecordHierarchyTreeNodeType.SEE_ALSO;
                    default -> null;
                };
            }

            @Override
            public RecordHierarchyTreeNode mapRow(ResultSet rs, int rowNum) throws SQLException {
                UUID recordId = DbUtils.uuidNotNull(rs, SPROC_AUT_STROM.RECORD_ID);
                String name = rs.getString(SPROC_AUT_STROM.NAZEV);
                int relationType = rs.getInt(SPROC_AUT_STROM.TYP_VAZBY);
                int level = rs.getInt(SPROC_AUT_STROM.UROVEN);

                return new RecordHierarchyTreeNode(
                        recordId,
                        Texts.ofNative(name),
                        getNodeTypeByRelationType(relationType),
                        level
                );
            }

        });
        List<HierarchyTree<UUID, RecordHierarchyTreeNode>> trees = new ArrayList<>();
        for (RecordHierarchyTreeNode node : nodes) {
            if (node.getLevel() == 1) {
                trees.add(new HierarchyTree<>());
            }
            trees.getLast().addNode(node);
        }
        return trees;
    }

    @Override
    public List<RecordDescriptor> getSubauthorities(@NonNull Record a) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(
                TC(RECORD.TABLE, RECORD.ID),
                TC(RECORD.TABLE, RECORD.NAME)
        );
        sq.from(AUT_VAZBY.AUT_VAZBY);
        sq.joins().add(TABLE, COLSEQ(TC(AUT_VAZBY.AUT_VAZBY, AUT_VAZBY.FK_AUTPOD), TC(TABLE, ID_AUT)));
        sq.joins().add(RECORD.TABLE, COLSEQ(TC(RECORD.TABLE, RECORD.ID), TC(KATAUT_4.TABLE, KATAUT_4.RECORD_ID)));
        sq.where()
                .and().eq(TC(AUT_VAZBY.AUT_VAZBY, AUT_VAZBY.FK_AUTNAD), a.getKindedId())
                .and().eq(TC(AUT_VAZBY.AUT_VAZBY, AUT_VAZBY.TYP_NADR), AUT_VAZBY.TYP_NADR_PARENT_CHILD)
                .and().isNull(TC(RECORD.TABLE, RECORD.DELETION_EVENT_ID))
                .and().isNotNull(TC(RECORD.TABLE, RECORD.ACTIVATION_EVENT_ID));
        sq.orderBy().addAsc(AUT_VAZBY.PORADI);
        return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), (rs, rowNum) -> mapRecordDescriptor(rs));
    }

    @Override
    public List<RecordDescriptor> getSeeAlsos(@NonNull Record a) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(
                TC(RECORD.TABLE, RECORD.ID),
                TC(RECORD.TABLE, RECORD.NAME)
        );
        sq.from(AUT_VAZBY.AUT_VAZBY);
        sq.joins().add(TABLE, COLSEQ(TC(AUT_VAZBY.AUT_VAZBY, AUT_VAZBY.FK_AUTPOD), TC(TABLE, ID_AUT)));
        sq.joins().add(RECORD.TABLE, COLSEQ(TC(RECORD.TABLE, RECORD.ID), TC(KATAUT_4.TABLE, KATAUT_4.RECORD_ID)));
        sq.where()
                .and().eq(TC(AUT_VAZBY.AUT_VAZBY, AUT_VAZBY.FK_AUTNAD), a.getKindedId())
                .and().eq(TC(AUT_VAZBY.AUT_VAZBY, AUT_VAZBY.TYP_NADR), AUT_VAZBY.TYP_NADR_SEE_ALSO)
                .and().isNull(TC(RECORD.TABLE, RECORD.DELETION_EVENT_ID))
                .and().isNotNull(TC(RECORD.TABLE, RECORD.ACTIVATION_EVENT_ID));
        sq.orderBy().addAsc(AUT_VAZBY.PORADI);
        return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), (rs, _) -> mapRecordDescriptor(rs));
    }

    @Override
    public List<RecordDescriptor> getSees(@NonNull Record a) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(
                TC(RECORD.TABLE, RECORD.ID),
                TC(RECORD.TABLE, RECORD.NAME)
        );
        sq.from(TABLE);
        sq.joins().add(RECORD.TABLE, COLSEQ(TC(RECORD.TABLE, RECORD.ID), TC(KATAUT_4.TABLE, KATAUT_4.RECORD_ID)));
        sq.where()
                .and().notEq(TC(TABLE, ID_AUT), a.getKindedId())
                .and().eq(TC(TABLE, FK_AUT), a.getKindedId())
                .and().isNull(TC(RECORD.TABLE, RECORD.DELETION_EVENT_ID))
                .and().isNotNull(TC(RECORD.TABLE, RECORD.ACTIVATION_EVENT_ID));
        return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), (rs, rowNum) -> mapRecordDescriptor(rs));
    }

    private RecordDescriptor mapRecordDescriptor(ResultSet rs) throws SQLException {
        UUID id = DbUtils.uuidNotNull(rs, RECORD.ID);
        @NullableNotBlank String name = rs.getString(RECORD.NAME);
        return new BasicRecordDescriptor(id, name);
    }

}
