package cz.kpsys.portaro.record.creation;

import cz.kpsys.portaro.auth.SideThreadAuthenticationIsolator;
import cz.kpsys.portaro.commons.file.ParsedFilename;
import cz.kpsys.portaro.commons.object.LabeledIdRecord;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.file.ByteArrayLoadedIdentifiedFile;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.IdentifiedFileImpl;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.deletion.RecordDeleter;
import cz.kpsys.portaro.record.deletion.RecordDeletionCommand;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import cz.kpsys.portaro.record.file.RecordWithAttachmentSaveCommand;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import java.io.IOException;
import java.util.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordsFromFilesCreatorService {

    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;
    @NonNull Saver<RecordWithAttachmentSaveCommand, ?> recordWithAttachmentSaver;
    @NonNull RecordDeleter recordDeleter;
    @NonNull SideThreadAuthenticationIsolator authIsolator;
    @NonNull TransactionTemplate transactionTemplate;

    public List<LabeledIdRecord<UUID>> createRecords(CreateRecordsFromFilesCommand command) {
        return transactionTemplate.execute(_ -> createRecordsNonTransactional(command));
    }

    @SneakyThrows(IOException.class)
    private List<LabeledIdRecord<UUID>> createRecordsNonTransactional(CreateRecordsFromFilesCommand command) {
        List<LabeledIdRecord<UUID>> createdRecordIds = new ArrayList<>();

        for (var file : command.files()) {
            String filename = Objects.requireNonNull(file.getOriginalFilename(), "Cannot process file without name!");

            String recordName = recordNameFromFilename(filename);
            Assert.isTrue(! StringUtil.isNullOrBlank(recordName), () -> String.format("File '%s' must have meaningful name!", filename));

            RecordEditation creation = recordEditationFactory
                    .on(command.ctx())
                    .ofNew(command.fond())
                    .withDefaultInitialValuesFilled()
                    .build(command.currentAuth());

            var entrySubfield = recordEntryFieldTypeIdResolver.getEntryNativeSubfield(command.fond());
            recordEditationHelper.setStringSubfieldValue(recordName, false, entrySubfield.existingParent(), false, entrySubfield, creation, command.ctx(), command.currentAuth());

            Record newRecord = creation.publish(command.ctx(), command.currentAuth()).getRecord();
            try {
                IdentifiedFile newFile = IdentifiedFileImpl.createNewFile(filename, file.getSize(), command.currentAuth().getActiveUser().getId());
                ByteArrayLoadedIdentifiedFile loadedFile = new ByteArrayLoadedIdentifiedFile(newFile, file.getBytes());
                recordWithAttachmentSaver.save(RecordWithAttachmentSaveCommand.ofLoadedCover(newRecord, loadedFile, command.currentAuth(), command.ctx()));
                createdRecordIds.add(LabeledIdRecord.of(newRecord));
            } catch (IOException | RuntimeException e) {
                // Delete record manually until transactions work
                authIsolator.doAuthenticated(command.ctx(), auth -> recordDeleter.delete(new RecordDeletionCommand(newRecord, Set.of(command.ctx()), Set.of(), command.ctx(), auth)));
                throw e;
            }
        }

        return createdRecordIds;
    }

    private static String recordNameFromFilename(String filename) {
        return ParsedFilename.parse(filename)
                .mainName().replace('_', ' ');
    }

}
