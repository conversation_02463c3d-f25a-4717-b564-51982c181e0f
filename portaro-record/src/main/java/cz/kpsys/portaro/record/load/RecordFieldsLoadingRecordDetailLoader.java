package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.IdentifiedFieldContainer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RecordFieldsLoadingRecordDetailLoader implements AllByIdsLoadable<IdentifiedFieldContainer, Record> {

    @NonNull RecordFieldsLoader recordFieldsLoader;

    @Override
    public List<IdentifiedFieldContainer> getAllByIds(@NonNull List<Record> records) {
        if (records.isEmpty()) {
            return List.of();
        }
        return recordFieldsLoader.load(ListUtil.convertStrict(records, Record::idFondPair));
    }


}
