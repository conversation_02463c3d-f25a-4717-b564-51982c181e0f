package cz.kpsys.portaro.record;

import cz.kpsys.portaro.record.datasource.Datasource;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.holding.RecordHolding;
import cz.kpsys.portaro.security.Action;

public class RecordSecurityActions {

    public static final Action<Void> RECORDS_SHOW = Action.withoutSubject("RecordsShow");
    public static final Action<Record> RECORD_SHOW = Action.withSubject("RecordShow", Record.class);
    public static final Action<Record> RECORD_LOCK_OR_UNLOCK = Action.withSubject("RecordLock", Record.class);
    public static final Action<Record> RECORD_EDIT_FAST = Action.withSubject("RecordEditFast", Record.class);
    public static final Action<Record> RECORD_EDIT = Action.withSubject("RecordEdit", Record.class);
    public static final Action<Void> RECORD_CREATE_ANY = Action.withoutSubject("RecordCreateAny");
    public static final Action<Void> RECORD_CREATE_OF_DOCUMENT_FOND = Action.withoutSubject("RecordCreateOfDocumentFond");
    public static final Action<Void> RECORD_CREATE_OF_AUTHORITY_FOND = Action.withoutSubject("RecordCreateOfAuthorityFond");
    public static final Action<String> RECORD_CREATE_OF_KIND = Action.withSubject("RecordCreateOfKind", String.class);
    public static final Action<Fond> RECORD_CREATE_DRAFT_OF_FOND = Action.withSubject("RecordCreateDraftOfFond", Fond.class);
    public static final Action<Fond> RECORD_CREATE_OF_FOND = Action.withSubject("RecordCreateOfFond", Fond.class);
    public static final Action<Record> RECORD_DRAFT_SAVE = Action.withSubject("RecordDraftSave", Record.class);
    public static final Action<Record> RECORD_PUBLISH = Action.withSubject("RecordPublish", Record.class);
    public static final Action<Record> RECORD_HOLDING_DELETE_OF_RECORD = Action.withSubject("RecordHoldingDeleteOfRecord", Record.class);
    public static final Action<Record> RECORD_HOLDING_DISCARD_OF_RECORD = Action.withSubject("RecordHoldingDiscardOfRecord", Record.class);
    public static final Action<RecordHolding> RECORD_HOLDING_DELETE = Action.withSubject("RecordHoldingDelete", RecordHolding.class);
    public static final Action<RecordHolding> RECORD_HOLDING_DISCARD = Action.withSubject("RecordHoldingDiscard", RecordHolding.class);
    public static final Action<Record> RECORD_MERGE_FROM = Action.withSubject("RecordMergeFrom", Record.class);
    public static final Action<Record> RECORD_MERGE_TO = Action.withSubject("RecordMergeTo", Record.class);
    public static final Action<Record> FILES_MANAGE_OF_RECORD = Action.withSubject("FilesManageOfRecord", Record.class);
    public static final Action<Void> RECORD_OPERATION_SEARCH = Action.withoutSubject("RecordOperationSearch");
    public static final Action<Void> RECORD_REQUEST_SEARCH = Action.withoutSubject("RecordRequestSearch");
    public static final Action<Record> RECORD_OPERATIONS_SHOW_OF_RECORD = Action.withSubject("RecordOperationsShowOfRecord", Record.class);
    public static final Action<Record> RECORD_REVISIONS_SHOW_OF_RECORD = Action.withSubject("RecordRevisionsShowOfRecord", Record.class);
    public static final Action<Record> RECORD_INFORMATION_SHOW_OF_RECORD = Action.withSubject("RecordInformationShowOfRecord", Record.class);
    public static final Action<Datasource> RECORD_SEARCH_IN_DATASOURCE = Action.withSubject("RecordSearchIn", Datasource.class);
}
