package cz.kpsys.portaro.record.detail;

import org.springframework.util.Assert;

import java.util.function.Predicate;

public class FieldRepetitionFilter<E extends WithRepetition> implements Predicate<E> {

    private final int repetition;

    public FieldRepetitionFilter(int repetition) {
        Assert.isTrue(repetition >= 0, "Field repetition must be >= 0, but is %s".formatted(repetition));
        this.repetition = repetition;
    }

    @Override
    public boolean test(E sf) {
        return sf != null && sf.getRepetition() == repetition;
    }
}
