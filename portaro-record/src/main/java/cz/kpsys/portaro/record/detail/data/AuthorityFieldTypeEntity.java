package cz.kpsys.portaro.record.detail.data;

import jakarta.persistence.AttributeOverride;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import static cz.kpsys.portaro.commons.object.Identified.PROPERTY_ID;
import static cz.kpsys.portaro.databasestructure.RecordDb.FDEFAUT.FDEFAUT;
import static cz.kpsys.portaro.databasestructure.RecordDb.FDEFAUT.ID_FDEFAUT;

@Entity
@Table(name = FDEFAUT)
@AttributeOverride(name = PROPERTY_ID, column = @Column(name = ID_FDEFAUT))
public final class AuthorityFieldTypeEntity extends FieldTypeEntity {
}
