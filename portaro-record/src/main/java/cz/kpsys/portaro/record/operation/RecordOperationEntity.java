package cz.kpsys.portaro.record.operation;

import cz.kpsys.portaro.commons.object.Identified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.time.Instant;
import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.RecordOperationDb.RECORD_OPERATION.*;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class RecordOperationEntity implements Identified<UUID> {

    @Id
    @Column(name = ID)
    @EqualsAndHashCode.Include
    @NonNull
    UUID id;

    @Column(name = RECORD_ID)
    @NonNull
    UUID recordId;

    @Column(name = EVENT_ID)
    @Nullable
    UUID eventId;

    @Column(name = FOND_ID)
    @NonNull
    Integer fondId;

    @Column(name = DEPARTMENT_ID)
    @NonNull
    Integer departmentId;

    @Column(name = FK_UZIV)
    @NonNull
    Integer userId;

    @Column(name = TYPE_ID)
    @NonNull
    Integer recordOperationTypeId;

    @Column(name = CREATION_DATE)
    @NonNull
    Instant date;

}
