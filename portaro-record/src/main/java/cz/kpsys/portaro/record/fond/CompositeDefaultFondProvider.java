package cz.kpsys.portaro.record.fond;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdOptLoadable;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;
import java.util.function.Predicate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CompositeDefaultFondProvider implements Provider<@NonNull Fond> {

    @NonNull AllValuesProvider<Fond> enabledFondsProvider;
    @NonNull ByIdOptLoadable<Fond, Integer> fondLoader;
    @NonNull Integer fondId;
    @NonNull Predicate<Fond> matchPredicate;
    @NonNull String predicateDescription;

    @NonNull
    @Override
    public Fond get() {
        return getFondBySpecifiedId(fondId, matchPredicate)
                .or(() -> findFondByFirstPredicate(matchPredicate))
                .orElseThrow(() -> new ItemNotFoundException(Fond.class, predicateDescription, Texts.ofNative("There is no '%s' fond in database".formatted(predicateDescription))));
    }

    private Optional<Fond> findFondByFirstPredicate(Predicate<Fond> fondPredicate) {
        return enabledFondsProvider.getAll().stream()
                .filter(fondPredicate)
                .findFirst();
    }

    private Optional<Fond> getFondBySpecifiedId(@NonNull Integer fondId, Predicate<Fond> predicate) {
        Optional<Fond> fondOpt = fondLoader.findById(fondId);
        if (fondOpt.isPresent() && predicate.test(fondOpt.get())) {
            return fondOpt;
        }
        return Optional.empty();
    }
}
