package cz.kpsys.portaro.record.extract;

import cz.kpsys.portaro.commons.barcode.EanBarcodeValidator;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.By;
import cz.kpsys.portaro.record.detail.Field;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.stream.Stream;

import static cz.kpsys.portaro.record.RecordWellKnownFields.DocumentEan;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordEanExtractor implements RecordValueExtractor {

    @NonNull RecordRawIsbnExtractor recordRawIsbnExtractor = new RecordRawIsbnExtractor();

    @Override
    public Stream<String> extractValue(Record record) {
        // Disable loading of EAN with URN codes. Obalkyknih returns wrong data in NPMK library.
        var fromField24a = record.getDetail().streamFields(By.typeId(DocumentEan.TYPE_ID))
                .filter(By.havingSubfieldWithValue(DocumentEan.Source.TYPE_ID, "urn").negate())
                .flatMap(field -> field.streamFields(By.<Field<?>>notEmpty().and(By.typeId(DocumentEan.Value.TYPE_ID))))
                .map(Field::getRaw);

        var fromField20a = recordRawIsbnExtractor.extractValue(record) // pahyly z palmknih mivaji EAN v poli 20 mezi isbn
                .filter(EanBarcodeValidator.EAN13_VALIDATOR::isValid);

        return Stream.concat(fromField24a, fromField20a).distinct();
    }
}
