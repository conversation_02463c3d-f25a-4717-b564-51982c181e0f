package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.NumberFieldValue;
import cz.kpsys.portaro.record.detail.value.VectorFieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.math.BigDecimal;
import java.util.List;

public interface NumericNaryOperator extends NaryOperator<NumberFieldValue> {

    @Override
    default @NonNull FormulaEvaluation<NumberFieldValue> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {
        List<FormulaEvaluation<VectorFieldValue<NumberFieldValue, BigDecimal>>> evals = ListUtil.convertStrict(operands(), operandFormula -> {
            FormulaEvaluation<NumberFieldValue> operandEval = evaluator.resolveFormulaValue(sourceNode, operandFormula);
            return operandEval.asVector().ensureVectorOf(NumberFieldValue.class, sourceNode, operandFormula);
        });
        FormulaEvaluations<VectorFieldValue<NumberFieldValue, BigDecimal>> operandEvals = FormulaEvaluations.extract(evals);
        if (operandEvals.hasFail()) {
            return operandEvals.toMostCriticalMultiFailEval().castedFailed();
        }
        List<VectorFieldValue<NumberFieldValue, BigDecimal>> successVectors = operandEvals.successes();
        List<NumberFieldValue> scalars = successVectors.stream().flatMap(vector -> vector.values().stream()).toList();
        return compute(scalars);
    }

    @NonNull
    FormulaEvaluation<NumberFieldValue> compute(@NonNull @NotEmpty List<NumberFieldValue> operandVector);

}
