package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.commons.util.DateUtils;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CoverSearchManager {

    @NonNull Provider<List<BatchCoverImporter>> coverImportersProvider;
    @NonNull CoversToSearchLoader coversToSearchLoader;
    @NonNull UnfoundCoverDeleter unfoundCoverDeleter;
    @NonNull ExecutorService executorService;
    @NonNull CanContinueProvider canContinueProvider = new CanContinueProvider();
    @NonNull TransactionTemplate readonlyTransactionTemplate;

    @NonFinal List<BatchCoverImporter> cachedCoverImporters;

    /**
     * Spusti prohledavani obalek v externich sluzbach.
     * vyhledavani v jednotlivych sluzbach je autonomni (nezavisle na ostatnich) a lze kdykoliv ukoncit.
     */
    public void startCoverSearching(@NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth) {
        canContinueProvider.reset();
        canContinueProvider.setSearchingTime(Calendar.HOUR, 6);

        deleteConnectionProblemUnfoundedCoversDatabase();
        deleteOldUnfoundedCoversDatabase();

        log.info("Cover downloading started");

        var coverImporters = getNewCoverImporters();
        for (final BatchCoverImporter coverImporter : coverImporters) {
            executorService.submit(() -> {
                Provider<List<Record>> recordsListProvider = () -> readonlyTransactionTemplate.execute(
                        _ -> coversToSearchLoader.getAll(coverImporter.getServiceName(), Range.forFirstPage(10)));
                coverImporter.run(recordsListProvider, canContinueProvider, currentDepartment, currentAuth);
            });
        }
    }

    private List<BatchCoverImporter> getCoverImporters() {
        if (cachedCoverImporters == null) {
            cachedCoverImporters = coverImportersProvider.get();
        }
        return cachedCoverImporters;
    }

    private List<BatchCoverImporter> getNewCoverImporters() {
        return cachedCoverImporters = coverImportersProvider.get();
    }


    /**
     * Smaze databazi nenalezenych obalek z duvodu problemu s pripojenim starsich nez 2 tydny.
     */
    public void deleteConnectionProblemUnfoundedCoversDatabase() {
        Date olderThanDate = DateUtils.addWeeks(new Date(), -2);
        unfoundCoverDeleter.deleteConnectionProblemedOlderThan(olderThanDate);
    }

    /**
     * Smaze databazi nenalezenych obalek starsich nez 3 mesice.
     */
    public void deleteOldUnfoundedCoversDatabase() {
        Date olderThanDate = DateUtils.addMonths(new Date(), -3);
        unfoundCoverDeleter.deleteOlderThan(olderThanDate);
    }




    public void zastavVyhledavaniObalek() {
        canContinueProvider.stopSearching();
    }


    public Object[][] getStavVyhledavaniObalek() {
        var coverImporters = getCoverImporters();
        Object[][] stavyLoaderu = new Object[coverImporters.size()][6];
        for (int i = 0; i < coverImporters.size(); i++) {
            stavyLoaderu[i][0] = coverImporters.get(i).getServiceName();
            stavyLoaderu[i][1] = coverImporters.get(i).getState();
            stavyLoaderu[i][2] = coverImporters.get(i).getNumOfSearchedCoversLastTime();
            stavyLoaderu[i][3] = coverImporters.get(i).getNumOfFoundedCoversLastTime();
            stavyLoaderu[i][4] = coverImporters.get(i).getLastSomeCoversSearchStartTime();
            stavyLoaderu[i][5] = coverImporters.get(i).getLastSomeCoversSearchStopTime();
        }
        return stavyLoaderu;
    }




    private static class CanContinueProvider implements Provider<Boolean> {

        private boolean forceStop;
        private long stopTime;

        public CanContinueProvider() {
            reset();
        }

        @Override
        public Boolean get() {
            return (System.currentTimeMillis() < stopTime) && !forceStop;
        }

        public void setSearchingTime(int timeUnit, int countOfTimeUnits) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(timeUnit, countOfTimeUnits);
            setStopTime(calendar.getTimeInMillis());
        }

        public void setStopTime(long stopTime) {
            this.stopTime = stopTime;
        }

        public void stopSearching() {
            forceStop = true;
        }

        public void reset() {
            forceStop = false;
            stopTime = 0;
        }
    }
}
