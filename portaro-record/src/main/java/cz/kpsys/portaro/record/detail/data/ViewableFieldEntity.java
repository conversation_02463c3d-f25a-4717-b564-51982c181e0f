package cz.kpsys.portaro.record.detail.data;

import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

import static cz.kpsys.portaro.databasestructure.RecordDb.STYLY.*;

@Entity
@Table(name = TABLE)
@IdClass(ViewableFieldId.class)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class ViewableFieldEntity implements Serializable {

    @Id
    @Column(name = ID_STYL)
    @EqualsAndHashCode.Include
    Integer styleId;

    @Id
    @Column(name = CIS_POL)
    @EqualsAndHashCode.Include
    Integer fieldNumber;

    @Id
    @Column(name = PODPOLE)
    String subfieldCode;

    @Column(name = PORADI_POLE)
    Integer fieldOrder;

    @Column(name = PORADI_PODP)
    Integer subfieldOrder;

    @Column(name = ZPUS_ZOBR)
    Integer displayType;

}