package cz.kpsys.portaro.record.search.factory;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.datasource.Datasource;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.SearchParams;
import cz.kpsys.portaro.search.factory.SearchFactoryResolverMatcher;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SearchFactoryResolverMatcherByDatasourceGroup implements SearchFactoryResolverMatcher {

    @NonNull String desiredGroup;

    @Override
    public boolean matches(SearchParams params) {
        String datasourceGroup = ObjectUtil.castedGetOrNull(params, MapBackedParams.class, p -> p.get(RecordConstants.SearchParams.DATASOURCE_GROUP));
        if (datasourceGroup != null) {
            return datasourceGroup.equals(desiredGroup);
        }
        Datasource datasource = ObjectUtil.castedGetOrNull(params, MapBackedParams.class, p -> p.get(RecordConstants.SearchParams.DATASOURCE));
        return datasource != null && datasource.isOfGroup(desiredGroup);
    }
}
