package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.record.RecordIdFondPair;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SimpleFieldContainer implements FieldContainer, Serializable {

    @NonNull List<Field<?>> fields;

    public SimpleFieldContainer() {
        this(new ArrayList<>());
    }

    public SimpleFieldContainer(Collection<Field<?>> fields) {
        this.fields = new ArrayList<>(fields);
    }

    public SimpleFieldContainer(@NonNull SimpleFieldContainer original, @NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId) {
        List<Field<?>> newList = new ArrayList<>();
        for (Field<?> field: original.fields) {
            newList.add(field.copy(newRecordIdFondPair, kindedId));
        }
        this.fields = newList;
    }

    @JsonIgnore
    @NonNull
    @Override
    public List<Field<?>> getFields() {
        return fields;
    }

    @Override
    public @NonNull Field<?> add(Field<?> field) {
        Field<?> added = FieldContainer.super.add(field);
        fields.sort(Field.NUMERICALLY_COMPATIBLE_SORTER);
        return added;
    }

    @Override
    public String toString() {
        return "SimpleFieldContainer{with %s fields}".formatted(fields.size());
    }

    @JsonIgnore
    @Override
    public SimpleFieldContainer copy(@NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId) {
        return new SimpleFieldContainer(this, newRecordIdFondPair, kindedId);
    }
}
