package cz.kpsys.portaro.record.detail.spec;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.record.RecordIdFondPair;
import lombok.NonNull;

public sealed interface RecordDatableId permits RecordFieldId, RecordMultifieldId {

    @JsonIgnore
    @NonNull
    RecordIdFondPair recordIdFondPair();

    @JsonIgnore
    @NonNull
    RecordFieldTypeId toRecordFieldTypeId();

    @JsonIgnore
    @NonNull
    RecordSpec toRecordSpec();

    @JsonIgnore
    @NonNull
    RecordFieldId existingParentFieldId();
}
