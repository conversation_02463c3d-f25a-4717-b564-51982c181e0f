package cz.kpsys.portaro.record.authority;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.database.SelectedColumnRowMapper;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.search.AbstractSingleColumnSpringDbSearchLoader;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.Brackets;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.RecordDb.*;
import static cz.kpsys.portaro.record.RecordKeyType.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SpringDbAuthoritySearchLoader extends AbstractSingleColumnSpringDbSearchLoader<MapBackedParams, UUID, RangePaging> {

    public SpringDbAuthoritySearchLoader(@NonNull NamedParameterJdbcOperations jdbcTemplate,
                                         @NonNull QueryFactory queryFactory) {
        super(jdbcTemplate, queryFactory, RECORD.TABLE, RECORD.ID, new SelectedColumnRowMapper<>(UUID.class, RECORD.ID));
    }

    @Override
    protected void select(@NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        sq.selectDistinct(
                TC(RECORD.TABLE, RECORD.ID),
                TC(RECORD.TABLE, RECORD.SORTING_KEY) // sorting key is in order-by, so in PG must be in select query
        );
    }

    @Override
    protected boolean fillQuery(boolean count, @NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        sq.from(RECORD.TABLE);

        if (p.hasNotNull(RecordConstants.SearchParams.RECORD_RELATED_RECORD_FOND)) {
            if (!p.hasLength(RecordConstants.SearchParams.RECORD_RELATED_RECORD_FOND)) {
                return false;
            }
            sq.joins().add(KAT1_7.KAT1_7, COLSEQ(TC(RECORD.TABLE, RECORD.ID), TC(KAT1_7.KAT1_7, KAT1_7.TARGET_RECORD_ID)))
                    .add(AS(RECORD.TABLE, "related_record"), new Brackets(sq).eqRaw(TC(KAT1_7.KAT1_7, KAT1_7.SOURCE_RECORD_ID), TC("related_record", RECORD.ID))
                            .and().in(TC("related_record", RECORD.FOND_ID), ListUtil.getListOfIds(p.get(RecordConstants.SearchParams.RECORD_RELATED_RECORD_FOND))).toString()); //musime vyfiltrovat ty autority, ktere jsou pouzite jen v povolenych fondech dokumentu
        }

        if (p.isEmptyOrFalse(CoreSearchParams.INCLUDE_DRAFT)) {
            sq.where().and().isNotNull(TC(RECORD.TABLE, RECORD.ACTIVATION_EVENT_ID));
        }

        if (p.isEmptyOrFalse(CoreSearchParams.INCLUDE_DELETED)) {
            sq.where().and().isNull(withoutIndexString(TC(RECORD.TABLE, RECORD.DELETION_EVENT_ID)));
        }

        if (p.hasNotNull(RecordConstants.SearchParams.FOND)) {
            if (!p.hasLength(RecordConstants.SearchParams.FOND)) {
                return false;
            }
            sq.where().and().in(TC(RECORD.TABLE, RECORD.FOND_ID), ListUtil.getListOfIds(p.get(RecordConstants.SearchParams.FOND)));
        }

        if (p.hasNotNull(RecordConstants.SearchParams.PREFIX)) {
            if (p.get(RecordConstants.SearchParams.PREFIX).length() > Record.RECORD_KEY_FLAT_PRIMARY_NAME_VAL_MAX_LENGTH) {
                return false;
            }
            String prefixTable = "prefix";
            String uppercasedFlattenPrefix = StringUtil.flatString(p.get(RecordConstants.SearchParams.PREFIX)).toUpperCase();
            Brackets brackets = new Brackets(sq).eqRaw(TC(RECORD.TABLE, RECORD.ID), TC(prefixTable, RECORD_KEY.RECORD_ID));
            Brackets bracketsInner = brackets.and().brackets();
            bracketsInner
                    .eq(TC(prefixTable, RECORD_KEY.NAME), FLAT_PRIMARY_NAME.getId()).and().like(TC(prefixTable, RECORD_KEY.VAL), uppercasedFlattenPrefix, false, true, Record.RECORD_KEY_FLAT_PRIMARY_NAME_VAL_MAX_LENGTH)
                    .or()
                    .eq(TC(prefixTable, RECORD_KEY.NAME), FLAT_ALTERNATIVE_NAME.getId()).and().like(TC(prefixTable, RECORD_KEY.VAL), uppercasedFlattenPrefix, false, true, Record.RECORD_KEY_FLAT_ALTERNATIVE_NAME_VAL_MAX_LENGTH);
            sq.joins().add(AS(RECORD_KEY.TABLE, prefixTable), brackets.toString());
        }

        if (p.hasNotNull(RecordConstants.SearchParams.DIACRITICAL_PREFIX)) {
            if (p.get(RecordConstants.SearchParams.DIACRITICAL_PREFIX).length() > Record.RECORD_KEY_FLAT_PRIMARY_NAME_VAL_MAX_LENGTH) {
                return false;
            }

            String diacriticalPrefixTable = "diaPrefix";
            String uppercasedPrefix = p.get(RecordConstants.SearchParams.DIACRITICAL_PREFIX).toUpperCase();
            Brackets brackets = new Brackets(sq).eqRaw(TC(RECORD.TABLE, RECORD.ID), TC(diacriticalPrefixTable, RECORD_KEY.RECORD_ID));
            Brackets bracketsInner = brackets.and().brackets();
            bracketsInner
                    .eq(TC(diacriticalPrefixTable, RECORD_KEY.NAME), ORIG_PRIMARY_NAME.getId()).and().like(upper(TC(diacriticalPrefixTable, RECORD_KEY.VAL)), uppercasedPrefix, false, true, Record.RECORD_KEY_FLAT_ALTERNATIVE_NAME_VAL_MAX_LENGTH)
                    .or()
                    .eq(TC(diacriticalPrefixTable, RECORD_KEY.NAME), ORIG_ALTERNATIVE_NAME.getId()).and().like(upper(TC(diacriticalPrefixTable, RECORD_KEY.VAL)), uppercasedPrefix, false, true, Record.RECORD_KEY_FLAT_ALTERNATIVE_NAME_VAL_MAX_LENGTH);
            sq.joins().add(AS(RECORD_KEY.TABLE, diacriticalPrefixTable), brackets.toString());
        }

        if (p.hasNotNull(RecordConstants.SearchParams.ICO)) {
            if (p.get(RecordConstants.SearchParams.ICO).isBlank()) {
                return false;
            }
            String icoTable = "ico";
            sq.joins().add(AS(RECORD_KEY.TABLE, icoTable), new Brackets(sq).eqRaw(TC(RECORD.TABLE, RECORD.ID), TC(icoTable, RECORD_KEY.RECORD_ID))
                    .and().eq(TC(icoTable, RECORD_KEY.NAME), CNA_OR_ICO.getId())
                    .and().like(TC(icoTable, RECORD_KEY.VAL), p.get(RecordConstants.SearchParams.ICO), false, true, Record.RECORD_KEY_VAL_MAX_LENGTH).toString());
        }

        return true;
    }

    @Override
    protected Sorting mandatorySorting(@Nullable SortingItem customSorting, @NonNull MapBackedParams p) {
        return Sorting.ofAsc(TC(RECORD.TABLE, RECORD.SORTING_KEY));
    }
}
