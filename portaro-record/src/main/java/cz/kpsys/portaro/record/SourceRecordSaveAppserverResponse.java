package cz.kpsys.portaro.record;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.NonNull;

import java.util.UUID;

/**
 * V podstate nas zajima jen sourceAuthorityId
 */
@JacksonXmlRootElement(localName = "record")
public record SourceRecordSaveAppserverResponse(

        @JacksonXmlProperty(localName = SOURCE_RECORD_AUTHORITY_RECORD_ID)
        @NonNull
        UUID sourceAuthorityId,

        @JacksonXmlProperty(localName = SOURCE_RECORD_DOCUMENT_RECORD_ID)
        @NonNull
        UUID sourceDocumentId,

        @JacksonXmlProperty(localName = SOURCE_RECORD_AUTHORITY_ID)
        @NonNull
        Integer sourceAuthorityKindedId,

        @JacksonXmlProperty(localName = SOURCE_RECORD_AUTHORITY_FOND_ID)
        @NonNull
        Integer sourceAuthorityFondId,

        @JacksonXmlProperty(localName = SOURCE_RECORD_DOCUMENT_ID)
        @NonNull
        Integer sourceDocumentKindedId,

        @JacksonXmlProperty(localName = SOURCE_RECORD_DOCUMENT_FOND_ID)
        @NonNull
        Integer sourceDocumentFondId

) {

    public static final String SOURCE_RECORD_AUTHORITY_RECORD_ID = "aut_record_id";
    public static final String SOURCE_RECORD_DOCUMENT_RECORD_ID = "doc_record_id";
    public static final String SOURCE_RECORD_AUTHORITY_ID = "ID";
    public static final String SOURCE_RECORD_AUTHORITY_FOND_ID = "AUTH_FOND";
    public static final String SOURCE_RECORD_DOCUMENT_ID = "DOC_ID";
    public static final String SOURCE_RECORD_DOCUMENT_FOND_ID = "DOC_FOND";
}
