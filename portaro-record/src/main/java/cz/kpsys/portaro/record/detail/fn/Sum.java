package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.datatype.DatatypeUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.value.NumberFieldValue;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.function.Function;

public record Sum(

        @NonNull
        @NotEmpty
        List<Formula<NumberFieldValue>> operands

) implements NumericNaryOperator {

    /// Presnost scitani je presnost nejpresnejsiho operandu
    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<NumberFieldValue>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        List<ScalarDatatype> operandDatatypes = ListUtil.convertStrict(operands, operandResultDatatypeResolver::apply);
        return DatatypeUtil.getMostPreciseDatatype(operandDatatypes);
    }

    @Override
    public @NonNull FormulaEvaluation<NumberFieldValue> compute(@NonNull @NotEmpty List<NumberFieldValue> operandVector) {
        BigDecimal value = BigDecimal.ZERO;
        var origins = new HashSet<RecordIdFondPair>();
        for (ScalarFieldValue<BigDecimal> operandValue : operandVector) {
            value = value.add(operandValue.value());
            origins.addAll(operandValue.origins());
        }
        return FormulaEvaluation.success(NumberFieldValue.of(value, origins));
    }

    @Override
    public String toString() {
        return "Sum(%s)".formatted(StringUtil.listToString(operands, ", "));
    }
}
