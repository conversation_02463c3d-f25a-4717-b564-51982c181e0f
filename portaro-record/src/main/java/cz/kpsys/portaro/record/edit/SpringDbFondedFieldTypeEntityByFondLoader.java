package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.databasestructure.FondDb.DEF_FOND;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import static cz.kpsys.portaro.commons.db.QueryUtils.COLSEQ;
import static cz.kpsys.portaro.commons.db.QueryUtils.TC;
import static cz.kpsys.portaro.databasestructure.RecordDb.STYLY.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SpringDbFondedFieldTypeEntityByFondLoader {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull TransactionTemplate readonlyTransactionTemplate;

    public List<FondedFieldTypeEntity> loadAll() {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(
                ID_STYL,
                PORADI_POLE,
                PORADI_PODP,
                ZPUS_ZOBR,
                CIS_POL,
                PODPOLE,
                REQUIREMENT,
                DEF_FOND.JE_AUTORITNI
        );
        sq.from(TABLE);
        sq.joins().add(DEF_FOND.TABLE, COLSEQ(TC(TABLE, ID_STYL), TC(DEF_FOND.TABLE, DEF_FOND.ID_FOND)));
        return readonlyTransactionTemplate.execute(_ ->
            jdbcTemplate.query(sq.getSql(), sq.getParamMap(), new Extractor())
        );
    }

    public List<FondedFieldTypeEntity> loadStyle(@NonNull Fond fond) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(
                ID_STYL,
                PORADI_POLE,
                PORADI_PODP,
                ZPUS_ZOBR,
                CIS_POL,
                PODPOLE,
                REQUIREMENT,
                DEF_FOND.JE_AUTORITNI
        );
        sq.joins().add(DEF_FOND.TABLE, COLSEQ(TC(TABLE, ID_STYL), TC(DEF_FOND.TABLE, DEF_FOND.ID_FOND)));
        sq.where().eq(ID_STYL, fond.getId());
        return readonlyTransactionTemplate.execute(_ ->
            jdbcTemplate.query(sq.getSql(), sq.getParamMap(), new Extractor())
        );
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class Extractor implements RowMapper<FondedFieldTypeEntity> {

        @Override
        public FondedFieldTypeEntity mapRow(@NonNull ResultSet rs, int rowNum) throws SQLException {
            Integer styleId = DbUtils.getIntegerNotNull(rs, ID_STYL);
            boolean forAuthority = DbUtils.getBooleanNotNull(rs, DEF_FOND.JE_AUTORITNI);
            String fieldCode = String.valueOf(rs.getInt(CIS_POL));
            String subfieldCode = StringUtil.notBlankTrimmedString(rs.getString(PODPOLE));
            FieldTypeId configPath = subfieldCode == null ? FieldTypeId.recordField(forAuthority, fieldCode) : FieldTypeId.recordField(forAuthority, fieldCode).sub(subfieldCode);
            FieldDisplayType fieldDisplayType = FieldDisplayType.CODEBOOK.getById(DbUtils.getIntegerNotNull(rs, ZPUS_ZOBR));
            FieldRequirementType fieldRequirementType = FieldRequirementType.CODEBOOK.getById(DbUtils.getIntegerNotNull(rs, REQUIREMENT));
            Integer order = subfieldCode == null ? rs.getInt(PORADI_POLE) : rs.getInt(PORADI_PODP);

            return new FondedFieldTypeEntity(
                    styleId,
                    configPath,
                    fieldDisplayType,
                    fieldRequirementType,
                    order
            );
        }
    }

}
