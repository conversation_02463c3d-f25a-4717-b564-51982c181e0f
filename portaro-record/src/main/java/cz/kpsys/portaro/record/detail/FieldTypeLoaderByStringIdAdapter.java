package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldTypeLoaderByStringIdAdapter implements ByIdLoadable<FieldType<?>, String> {

    @NonNull FieldTypeLoader fieldTypeLoader;

    @Override
    public FieldType<?> getById(@NonNull String fieldTypeId) {
        FieldTypeId id = FieldTypeId.parse(fieldTypeId);
        return fieldTypeLoader.getById(id);
    }

}
