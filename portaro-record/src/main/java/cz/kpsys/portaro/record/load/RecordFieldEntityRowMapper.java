package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.date.DatetimeRange;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.database.DbObjectExtractor;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.record.RecordIdentifier;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.RecordDb.RECORD_FIELD.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordFieldEntityRowMapper implements RowMapper<RecordFieldEntity> {

    @NonNull DbObjectExtractor dbObjectExtractor;

    @Override
    public RecordFieldEntity mapRow(@NonNull ResultSet rs, int rowNum) throws SQLException {
        UUID id = DbUtils.uuidNotNull(rs, ID);
        UUID recordId = DbUtils.uuidNotNull(rs, RECORD_ID);
        String fieldCode = DbUtils.getStringNotNullNotBlank(rs, FIELD_CODE);
        Integer fieldIndex = DbUtils.getIntegerNotNull(rs, FIELD_INDEX);
        String subfieldCode = StringUtil.requireNullableNotBlank(rs.getString(SUBFIELD_CODE));
        Integer subfieldIndex = DbUtils.getInteger(rs, SUBFIELD_INDEX);
        String textValue = rs.getString(TEXT_VALUE);
        String textSuffix = rs.getString(TEXT_SUFFIX);
        UUID targetRecordId = DbUtils.uuidOrNull(rs, TARGET_RECORD_ID);
        Instant lastModificationDate = DbUtils.instantNotNull(rs, LAST_MODIFICATION_DATE);
        Instant lastProcessDate = DbUtils.instantOrNull(rs, LAST_PROCESS_DATE);
        String largeTextValue = rs.getString(LARGE_TEXT_VALUE);
        BigDecimal numericValue = DbUtils.bigDecimalOrNull(rs, NUMERIC_VALUE);
        LocalDate dateValue = DbUtils.getLocalDateOrNull(rs, DATE_VALUE);
        Instant datetimeValue = DbUtils.instantOrNull(rs, DATETIME_VALUE);
        DateRange dateRangeValue = dbObjectExtractor.getDateRangeNullable(rs, DATERANGE_VALUE);
        DatetimeRange datetimeRangeValue = dbObjectExtractor.getDatetimeRangeNullable(rs, DATETIMERANGE_VALUE);
        Boolean booleanValue = DbUtils.getBoolean(rs, BOOLEAN_VALUE);
        boolean cacheInvalid = !rs.getBoolean(CACHE_VALID);
        UUID originRecordId = DbUtils.uuidOrNull(rs, ORIGIN_RECORD_ID);

        return new RecordFieldEntity(
                id,
                RecordIdentifier.of(recordId),
                fieldCode,
                fieldIndex,
                subfieldCode,
                subfieldIndex,
                textValue,
                ObjectUtil.elvis(targetRecordId, RecordIdentifier::of),
                textSuffix,
                lastModificationDate,
                lastProcessDate,
                largeTextValue,
                numericValue,
                dateValue,
                datetimeValue,
                dateRangeValue,
                datetimeRangeValue,
                booleanValue,
                cacheInvalid,
                ObjectUtil.elvis(originRecordId, RecordIdentifier::of)
        );
    }

}
