package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.spec.RecordFieldTypeId;
import lombok.NonNull;

public record NotAVectorFail(

        @NonNull Text text

) implements NonAbsentDataFail {

    public static @NonNull FailedResult of(RecordFieldTypeId srcRecordFieldType, @NonNull Formula<?> operand) {
        return new NotAVectorFail(Texts.ofNative("Not a vector in " + operand));
    }

    @Override
    public String toString() {
        return "NotAVector";
    }

}
