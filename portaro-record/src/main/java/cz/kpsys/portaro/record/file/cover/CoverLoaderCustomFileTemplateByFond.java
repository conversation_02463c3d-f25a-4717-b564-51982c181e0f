package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.object.repo.ByIdOptLoadable;
import cz.kpsys.portaro.commons.web.PlaceholderTemplate;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.LoadedFile;
import cz.kpsys.portaro.file.custom.CustomFile;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CoverLoaderCustomFileTemplateByFond implements CoverLoader {

    @NonNull ByIdOptLoadable<CustomFile, String> hierarchyTraversingCustomFileLoader;
    @NonNull FileDataStreamer fileDataStreamer;
    @NonNull String customDirectoryName;
    @NonNull PlaceholderTemplate customFilenameTemplate;

    /**
     * @param customFilenameTemplate custom file filename, containing wildcard {fond},
     */
    public CoverLoaderCustomFileTemplateByFond(@NonNull ByIdOptLoadable<CustomFile, String> hierarchyTraversingCustomFileLoader,
                                               @NonNull FileDataStreamer fileDataStreamer,
                                               @NonNull String customDirectoryName,
                                               @NonNull String customFilenameTemplate) {
        this(hierarchyTraversingCustomFileLoader, fileDataStreamer,
                customDirectoryName, new PlaceholderTemplate(customFilenameTemplate));
    }

    @Override
    public LoadedFile getCover(Record record, Department currentDepartment) {
        return loadCoverDataForFondAndDepartmentOrNull(record.getFond(), currentDepartment)
                .orElse(null);
    }

    protected Optional<LoadedFile> loadCoverDataForFondAndDepartmentOrNull(Fond f, Department currentDepartment) {
        String fondId = String.valueOf(f.getId());
        String customFilename = customFilenameTemplate
                .withParameter("fond", fondId)
                .build();
        String customFileId = CustomFile.createId(currentDepartment.getId(), customDirectoryName, customFilename);

        return hierarchyTraversingCustomFileLoader.findById(customFileId)
                .map(customFile -> customFile.loadFile(fileDataStreamer));
    }
}
