package cz.kpsys.portaro.record.fond;

import lombok.NonNull;

import java.util.*;

public class FondHierarchicalSorter {

    public static @NonNull List<Fond> sortHierarchically(@NonNull List<@NonNull Fond> fonds) {
        if (fonds.isEmpty()) {
            return List.of();
        }

        List<Fond> roots = new ArrayList<>();
        Map<Integer, List<Fond>> byParent = new HashMap<>();

        for (Fond fond : fonds) {
            if (fond.getParentId() == null) {
                roots.add(fond);
            } else {
                byParent.computeIfAbsent(fond.getParentId(), _ -> new ArrayList<>()).add(fond);
            }
        }

        roots.sort(Comparator.comparing(Fond::getOrder, Comparator.nullsLast(Integer::compareTo)));

        List<Fond> result = new ArrayList<>();

        List<Fond> currentLevel = new ArrayList<>(roots);

        while (!currentLevel.isEmpty()) {
            result.addAll(currentLevel);

            List<Fond> nextLevel = new ArrayList<>();
            for (Fond parent : currentLevel) {
                List<Fond> children = byParent.getOrDefault(parent.getId(), Collections.emptyList());
                nextLevel.addAll(children);
            }

            nextLevel.sort(Comparator.comparing(Fond::getOrder, Comparator.nullsLast(Integer::compareTo)));

            currentLevel = nextLevel;
        }

        return result;
    }
}