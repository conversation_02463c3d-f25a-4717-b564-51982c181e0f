package cz.kpsys.portaro.record.detail.convert;

import cz.kpsys.portaro.record.RecordStatus;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.UUID;

public interface DetailToMarcXmlConverter {

    String convert(boolean forAuthority,
                   @NonNull FieldContainer detail,
                   Fond fond,
                   @Nullable Integer recordKindedId,
                   @NonNull UUID recordId,
                   boolean active,
                   RecordStatus status,
                   @Nullable Fond sourceRecordAuthorityFond);
    
}
