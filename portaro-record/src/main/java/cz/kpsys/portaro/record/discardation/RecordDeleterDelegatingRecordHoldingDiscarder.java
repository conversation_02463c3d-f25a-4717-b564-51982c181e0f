package cz.kpsys.portaro.record.discardation;

import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Deleter;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Set;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordDeleterDelegatingRecordHoldingDiscarder implements Deleter<RecordHoldingDiscardationCommand> {

    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull RecordDiscarder recordHoldingDiscarder;
    @NonNull ContextualFunction<Record, Department, Boolean> singleNodeDepartmentedRecordHasExemplarsPredicate;

    @Override
    public void delete(RecordHoldingDiscardationCommand command) {

        Record record = recordLoader.getById(command.recordHolding().getRecordId());

        Boolean recordHasExemplars = singleNodeDepartmentedRecordHasExemplarsPredicate.getOn(record, command.recordHolding().getDepartment());
        if (recordHasExemplars) {
            throw new RecordHoldingHasExemplarsException();
        }

        RecordDiscardationCommand discardationCommand = new RecordDiscardationCommand(
                record,
                Set.of(command.recordHolding().getDepartment()),
                Set.of(),
                command.ctx(),
                command.currentAuth());
        recordHoldingDiscarder.discard(discardationCommand);
    }
}
