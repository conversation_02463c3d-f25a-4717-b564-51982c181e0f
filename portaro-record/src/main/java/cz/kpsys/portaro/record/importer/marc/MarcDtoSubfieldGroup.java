package cz.kpsys.portaro.record.importer.marc;

import cz.kpsys.portaro.marcxml.model.StrictVerbisSubfieldMarcDto;
import cz.kpsys.portaro.marcxml.model.SubfieldMarcDto;
import lombok.NonNull;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public record MarcDtoSubfieldGroup(

        boolean complex,

        @NonNull
        List<StrictVerbisSubfieldMarcDto> subfields

) {

    public static MarcDtoSubfieldGroup notComplex(StrictVerbisSubfieldMarcDto singleSubfield) {
        return new MarcDtoSubfieldGroup(false, List.of(singleSubfield));
    }

    public static MarcDtoSubfieldGroup complex(StrictVerbisSubfieldMarcDto singleSubfield) {
        return new MarcDtoSubfieldGroup(true, new ArrayList<>(List.of(singleSubfield)));
    }

    public void add(StrictVerbisSubfieldMarcDto singleSubfield) {
        Assert.state(complex, "Group is not complex, cannot add more than 1 subfield");
        subfields.add(singleSubfield);
    }

    public SubfieldMarcDto getSingleSubfield() {
        Assert.state(!complex, "Group is complex, cannot get only single subfield");
        return subfields.getFirst();
    }

}
