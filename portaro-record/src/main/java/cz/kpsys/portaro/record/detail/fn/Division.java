package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.util.NumberUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.value.DivisionByZeroFail;
import cz.kpsys.portaro.record.detail.value.NumberFieldValue;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;

public record Division(

        @NonNull
        Integer scale,

        @NonNull
        @NotEmpty
        List<Formula<NumberFieldValue>> operands

) implements NumericNaryOperator {

    public static Division ofDecimal4(@NonNull Formula<NumberFieldValue> dividend, @NonNull Formula<NumberFieldValue> divider) {
        return new Division(CoreConstants.Datatype.datatypeToScale(CoreConstants.Datatype.NUMBER_DECIMAL_4), List.of(dividend, divider));
    }

    /// Presnost deleni musi byt dana explicitne
    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<NumberFieldValue>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        return CoreConstants.Datatype.scaleToDatatype(scale);
    }

    /// dividend = dělenec
    /// divider = dělitel
    @Override
    public @NonNull FormulaEvaluation<NumberFieldValue> compute(@NonNull @NotEmpty List<NumberFieldValue> operandVector) {
        Assert.state(operandVector.size() > 1, () -> "Division requires at least two operands, but got " + operandVector.size());

        List<? extends ScalarFieldValue<BigDecimal>> dividers = operandVector.subList(1, operandVector.size());
        ScalarFieldValue<BigDecimal> firstDividend = operandVector.getFirst();
        BigDecimal resultValue = firstDividend.value();
        Set<RecordIdFondPair> resultOrigins = new HashSet<>(firstDividend.origins());

        if (NumberUtil.isZero(resultValue)) {
            return FormulaEvaluation.success(NumberFieldValue.of(resultValue, resultOrigins));
        }

        for (ScalarFieldValue<BigDecimal> divider : dividers) {
            if (NumberUtil.isZero(divider.value())) {
                return FormulaEvaluation.failure(DivisionByZeroFail.of());
            }
            resultValue = resultValue.divide(divider.value(), scale, RoundingMode.HALF_UP);
            resultOrigins.addAll(divider.origins());
        }

        return FormulaEvaluation.success(NumberFieldValue.of(resultValue, resultOrigins));
    }

    @Override
    public String toString() {
        return "Division(%s)".formatted(StringUtil.listToString(operands, ", "));
    }


}
