package cz.kpsys.portaro.record.holding;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EntityToRecordHoldingConverter implements Converter<RecordHoldingEntity, RecordHolding> {

    @NonNull ByIdLoadable<Department, Integer> departmentLoader;

    @Override
    public RecordHolding convert(RecordHoldingEntity entity) {
        return new RecordHolding(
                entity.getId(),
                entity.getRecordId(),
                getDepartment(entity),
                entity.getCreationEventId(),
                entity.getDiscardionEventId(),
                entity.getDeletionEventId()
        );
    }

    private Department getDepartment(RecordHoldingEntity entity) {
        Integer departmentId = Objects.requireNonNull(entity.getDepartmentId(), () -> "RecordHoldingEntity.departmentId is null (%s)".formatted(entity));
        return departmentLoader.getById(departmentId);
    }

}
