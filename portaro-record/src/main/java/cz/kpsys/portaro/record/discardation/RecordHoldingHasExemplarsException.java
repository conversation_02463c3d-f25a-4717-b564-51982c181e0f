package cz.kpsys.portaro.record.discardation;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.logging.LogOnlyAsDebug;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@Getter
@ResponseStatus(HttpStatus.BAD_REQUEST)
@LogOnlyAsDebug
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RecordHoldingHasExemplarsException extends RuntimeException implements UserFriendlyException {

    @NonNull
    Text text = Texts.ofNative("Nelze vyřadit záznam, protože k němu existují exemlář(e). Nejprve vyřaďte jednotlivé exempláře.");

    public RecordHoldingHasExemplarsException() {
        super("Cannot discard record holding, because it has exemplars. Discard exemplars first.");
    }
}
