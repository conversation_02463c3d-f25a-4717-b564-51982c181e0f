package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;

public interface RecordFieldTypesLoader {

    List<EditableFieldType<?>> getFieldTypesByRecord(@NonNull Record record) throws EmptyWorksheetException;

    default Optional<EditableFieldType<?>> findById(@NonNull Record record, @NonNull FieldTypeId id) throws EmptyWorksheetException {
        List<EditableFieldType<?>> editableFieldTypes = getFieldTypesByRecord(record);
        return ListUtil.findFirstMatching(editableFieldTypes, ft -> ft.getFieldTypeId().equals(id));
    }

}
