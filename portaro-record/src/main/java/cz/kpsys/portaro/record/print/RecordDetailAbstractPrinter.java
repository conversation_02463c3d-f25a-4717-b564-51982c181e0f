package cz.kpsys.portaro.record.print;

import cz.kpsys.portaro.commons.localization.NoTranslatorSupportingText;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.ViewableRecord;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.fond.FondTypeResolver;
import cz.kpsys.portaro.record.query.SingleValFieldGroup;
import cz.kpsys.portaro.record.query.ValFieldGroup;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public abstract class RecordDetailAbstractPrinter implements RecordDetailPrinter {

    private static final Pattern MESSAGE_CODE_PATTERN = Pattern.compile("((?<!\\$)\\{[\\da-zA-z\\.]*\\})"); //hledac vzoru "{xyz.abc}"
    private static final String DEFAULT_PREFIX = "";
    private static final String DEFAULT_SUFFIX = "";
    private static final String DEFAULT_FIELD_DELIMITER = "; ";
    private static final String DEFAULT_SUBFIELD_DELIMITER = " ";

    @NonNull
    SimpleSubfieldSorter subfieldSorter = new SimpleSubfieldSorter();

    @Nullable
    Translator<Department> translator;

    @Nullable
    Department currentDepartment;

    @Nullable
    Locale locale;

    /**
     * Pro vypsani textu ktere nemame v planu lokalizovat.
     */
    public RecordDetailAbstractPrinter() {
        this(null, null, null);
    }


    @Override
    public String field(@NonNull ViewableRecord record, @NonNull String maybeLegacyFieldCode, @Nullable String subfieldCodesString, @Nullable String prefix, @Nullable String suffix, @Nullable String fieldDelimiter, @Nullable String subfieldDelimiter) {
        String fieldCode = FieldHelper.convertLegacyCodeToNewPrefixedCode(maybeLegacyFieldCode, () -> FondTypeResolver.isAuthorityFond(record.getFond()));

        //nastaveni defaultnich hodnot pokud jsou null
        final List<String> subfieldCodes = StringUtil.isNullOrEmpty(subfieldCodesString) ? null : StringUtil.createListOfSingleLetters(subfieldCodesString);
        prefix = ObjectUtil.firstNotNull(prefix, DEFAULT_PREFIX);
        suffix = ObjectUtil.firstNotNull(suffix, DEFAULT_SUFFIX);
        fieldDelimiter = ObjectUtil.firstNotNull(fieldDelimiter, DEFAULT_FIELD_DELIMITER);
        subfieldDelimiter = ObjectUtil.firstNotNull(subfieldDelimiter, DEFAULT_SUBFIELD_DELIMITER);

        ViewableFieldContainer viewableDetail = record.getFlattenViewableFieldList();

        //pokud nemame zadna pole k vypsani, rovnou vratime ""
        List<? extends ViewableField> fields = viewableDetail.getFields(By.code(fieldCode));
        if (ListUtil.isNullOrEmpty(fields)) {
            return "";
        }
        List<? extends ViewableField> writableDatafields = fields.stream()
                .filter(datafield -> {
                    if (subfieldCodes == null) {
                        return !datafield.getFields().isEmpty();
                    }
                    return !datafield.getFields(By.anyCode(subfieldCodes)).isEmpty();
                })
                .toList();
        if (ListUtil.isNullOrEmpty(writableDatafields)) {
            return "";
        }

        //zde uz je jasne, ze se alespon jedno podpole vypise

        StringBuilder sb = new StringBuilder();

        //prefix
        sb.append(writePlainString(prefix));

        int lastDatafieldIdx = writableDatafields.size() - 1;
        for (int fIdx = 0; fIdx <= lastDatafieldIdx; fIdx++) { //iterujeme pres pole
            ViewableField df = writableDatafields.get(fIdx);
            var subfields = subfieldSorter.getAndSort(df, subfieldCodes);
            int lastSubfieldIdx = subfields.size() - 1;
            for (int sIdx = 0; sIdx <= lastSubfieldIdx; sIdx++) { //iterujeme pres podpole
                ViewableField sf = subfields.get(sIdx);

                //vypsani podpole
                sb.append(writeValuedField(sf, viewableDetail));

                //delimitery
                if (!(fIdx == lastDatafieldIdx && sIdx == lastSubfieldIdx)) { //delimiter nemuze byt prvni (coz neni pac je az za vypisem podpole) ani posledni
                    if (sIdx == lastSubfieldIdx) { //field delimiter
                        sb.append(writePlainString(fieldDelimiter));
                    } else { //subfield delimiter
                        sb.append(writePlainString(subfieldDelimiter));
                    }
                }

            }
        }

        //suffix
        sb.append(writePlainString(suffix));


        log.debug("Writing field {}.{} as '{}' (pref:{}, suff:{}, f-delim:{}, sf-delim:{})", fieldCode, subfieldCodes, sb, prefix, suffix, fieldDelimiter, subfieldDelimiter);

        return sb.toString();
    }


    @Override
    public String group(@NonNull ValFieldGroup group) {
        return group(group, null, null, null, null);
    }


    @Override
    public String group(@NonNull ValFieldGroup group, String prefix, String suffix, String fieldDelimiter, String subfieldDelimiter) {
        if (group.isEmpty()) {
            return "";
        }

        prefix = ObjectUtil.firstNotNull(prefix, DEFAULT_PREFIX);
        suffix = ObjectUtil.firstNotNull(suffix, DEFAULT_SUFFIX);
        fieldDelimiter = ObjectUtil.firstNotNull(fieldDelimiter, DEFAULT_FIELD_DELIMITER);
        subfieldDelimiter = ObjectUtil.firstNotNull(subfieldDelimiter, DEFAULT_SUBFIELD_DELIMITER);

        StringBuilder sb = new StringBuilder();

        //prefix
        sb.append(writePlainString(prefix));

        SingleValFieldGroup last = null;
        for (SingleValFieldGroup single : group) {
            if (last != null) { //nechceme oddelovac na zacatku
                if (isOfSameTopField(last, single)) { //porad vypisujeme stejne pole, jen jine podpole
                    sb.append(writePlainString(subfieldDelimiter));
                } else { //jine pole nez predtim
                    sb.append(writePlainString(fieldDelimiter));
                }
            }
            sb.append(writeValuedField(single, group.getOriginalDetail()));
            last = single;
        }

        //suffix
        sb.append(writePlainString(suffix));


        log.debug("Writing field group {} as '{}' (pref:{}, suff:{}, f-delim:{}, sf-delim:{})", group, sb, prefix, suffix, fieldDelimiter, subfieldDelimiter);

        return sb.toString();
    }


    private boolean isOfSameTopField(SingleValFieldGroup last, SingleValFieldGroup current) {
        return last.getFieldId().getGreatestCommonRootLevel(current.getFieldId()) >= FieldId.LEVEL_TOPFIELD;
    }


    public String writePlainString(String string) {
        if (string.indexOf('{') > -1 && string.indexOf('}') > -1) { //pro rychlejsi zpracovani textu, ktery jiste neobsahuje lokalizaci
            Matcher m = MESSAGE_CODE_PATTERN.matcher(string);
            List<String> messageCodesWithBrackets = new ArrayList<>();
            while (m.find()) {
                String group = m.group(1);
                messageCodesWithBrackets.add(group);
            }
            //nalezene message kody nahradime lokaizovanymi texty
            for (String messageCodeWithBrackets : messageCodesWithBrackets) {
                String messageCode = messageCodeWithBrackets.substring(1, messageCodeWithBrackets.length() - 1);
                String localized = localize(Texts.ofMessageCoded(messageCode));
                string = string.replace(messageCodeWithBrackets, localized);
            }
        }
        return string;
    }


    public String writeValuedField(ViewableFieldable valfield, @NonNull ViewableFieldContainer originalDetail) {
        if (valfield.hasRecordLink()) {
            //autoritni podpole
            return writeRecordLinkSubfield(valfield);
        }

        if (valfield.isUrl()) {
            //podpole s url adresou
            return writeUrlSubfield(valfield, originalDetail);
        }

        //normalni textove podpole
        return writeNormalField(valfield, originalDetail);
    }


    protected String localize(Text text) {
        if (text.isEmpty()) {
            return "";
        }
        if (translator == null || locale == null) {
            if (text instanceof NoTranslatorSupportingText) {
                return ((NoTranslatorSupportingText) text).getNoTranslatorFallback();
            }
            throw new UnsupportedOperationException("cannot translate %s -%s%s".formatted(text, translator == null ? " translator cannot be null" : "", locale == null ? " locale cannot be null" : ""));
        }
        Department department = ObjectUtil.firstNotNull(() -> currentDepartment, Department::testingRoot);
        return text.localize(translator, department, locale);
    }








    public abstract String writeRecordLinkSubfield(ViewableFieldable sf);


    public abstract String writeUrlSubfield(@NonNull ViewableFieldable sf, @NonNull ViewableFieldContainer originalDetail);


    public abstract String writeNormalField(ViewableFieldable sf, @NonNull ViewableFieldContainer originalDetail);


}
