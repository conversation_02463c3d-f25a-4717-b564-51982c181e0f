package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.link.def.NoLinkDef;
import cz.kpsys.portaro.record.detail.source.FieldSource;
import cz.kpsys.portaro.record.fond.Fond;

import java.util.Optional;
import java.util.Set;

public interface FieldTypes {

    String IND_1_FIELD_CODE = "ind1";
    String IND_2_FIELD_CODE = "ind2";
    Set<String> INDICATORS_FIELD_CODES = Set.of(IND_1_FIELD_CODE, IND_2_FIELD_CODE);
    String VIRTUAL_GROUP_FIELD_CODE = "main";
    String DEFAULT_NATIVE_NAME = "Unnamed field";

    Optional<Formula<?>> EMPTY_FORMULA = Optional.empty();
    Optional<ScalarDatatype> TEXT_DATATYPE = Optional.of(CoreConstants.Datatype.TEXT);
    Optional<ScalarDatatype> NO_DATATYPE = Optional.empty();
    Optional<Fond> NO_LINK_FOND = Optional.empty();
    Optional<Codebook<? extends LabeledIdentified<?>, ?>> NO_CODEBOOK = Optional.empty();
    String NO_PIC = null;

    String FIELD_001_FIELD_CODE = "d1";
    FieldTypeId FIELD_001_FIELD_TYPE_ID = FieldTypeId.top(FIELD_001_FIELD_CODE);

    String AUTHORITY_LEADER_FIELD_CODE = "aleader";
    FieldTypeId AUTHORITY_LEADER_FIELD_TYPE_ID = FieldTypeId.top(AUTHORITY_LEADER_FIELD_CODE);
    PrefabFieldType AUTHORITY_LEADER_PREFAB_FIELD_TYPE = new PrefabFieldType(
            AUTHORITY_LEADER_FIELD_TYPE_ID,
            AUTHORITY_LEADER_FIELD_TYPE_ID,
            "Leader",
            false,
            false,
            true,
            new NoLinkDef(),
            FieldSource.common(),
            TransferType.OVERWRITE,
            PrefabFieldType.NO_LINK_ROOT_FOND,
            CoreConstants.Datatype.TEXT,
            FieldExportSetting.toField(AUTHORITY_LEADER_FIELD_TYPE_ID),
            PrefabFieldType.NO_PIC,
            FdefGeneration.GENERATED_PHYSICAL
    );

    String DOCUMENT_LEADER_FIELD_CODE = "dleader";
    FieldTypeId DOCUMENT_LEADER_FIELD_TYPE_ID = FieldTypeId.top(DOCUMENT_LEADER_FIELD_CODE);
    PrefabFieldType DOCUMENT_LEADER_PREFAB_FIELD_TYPE = new PrefabFieldType(
            DOCUMENT_LEADER_FIELD_TYPE_ID,
            DOCUMENT_LEADER_FIELD_TYPE_ID,
            "Leader",
            false,
            false,
            true,
            new NoLinkDef(),
            FieldSource.common(),
            TransferType.OVERWRITE,
            PrefabFieldType.NO_LINK_ROOT_FOND,
            CoreConstants.Datatype.TEXT,
            FieldExportSetting.toField(DOCUMENT_LEADER_FIELD_TYPE_ID),
            PrefabFieldType.NO_PIC,
            FdefGeneration.GENERATED_PHYSICAL
    );

    String FOND_FIELD_CODE = "fond";
    FieldTypeId FOND_FIELD_TYPE_ID = FieldTypeId.top(FOND_FIELD_CODE);
    PrefabFieldType FOND_PREFAB_FIELD_TYPE = new PrefabFieldType(
            FOND_FIELD_TYPE_ID,
            FOND_FIELD_TYPE_ID,
            "Typ",
            false,
            false,
            true,
            new NoLinkDef(),
            FieldSource.fond(),
            TransferType.OVERWRITE,
            PrefabFieldType.NO_LINK_ROOT_FOND,
            RecordConstants.Datatype.FOND,
            FieldExportSetting.disabled(),
            PrefabFieldType.NO_PIC,
            FdefGeneration.BOUND
    );

    String RECORD_ID_FIELD_CODE = "rid";
    FieldTypeId RECORD_ID_FIELD_TYPE_ID = FieldTypeId.top(RECORD_ID_FIELD_CODE);
    PrefabFieldType RECORD_ID_PREFAB_FIELD_TYPE = new PrefabFieldType(
            RECORD_ID_FIELD_TYPE_ID,
            RECORD_ID_FIELD_TYPE_ID,
            "Record ID",
            false,
            false,
            true,
            new NoLinkDef(),
            FieldSource.common(),
            TransferType.OVERWRITE,
            PrefabFieldType.NO_LINK_ROOT_FOND,
            CoreConstants.Datatype.TEXT,
            FieldExportSetting.disabled(),
            PrefabFieldType.NO_PIC,
            FdefGeneration.BOUND
    );

    String DOCUMENT_KINDED_ID_FIELD_CODE = "dkid";
    FieldTypeId DOCUMENT_KINDED_ID_FIELD_TYPE_ID = FieldTypeId.top(DOCUMENT_KINDED_ID_FIELD_CODE);
    PrefabFieldType DOCUMENT_KINDED_ID_PREFAB_FIELD_TYPE = new PrefabFieldType(
            DOCUMENT_KINDED_ID_FIELD_TYPE_ID,
            DOCUMENT_KINDED_ID_FIELD_TYPE_ID,
            "Document kinded ID",
            false,
            false,
            true,
            new NoLinkDef(),
            FieldSource.common(),
            TransferType.OVERWRITE,
            PrefabFieldType.NO_LINK_ROOT_FOND,
            CoreConstants.Datatype.TEXT,
            FieldExportSetting.disabled(),
            PrefabFieldType.NO_PIC,
            FdefGeneration.BOUND
    );

    String AUTHORITY_KINDED_ID_FIELD_CODE = "akid";
    FieldTypeId AUTHORITY_KINDED_ID_FIELD_TYPE_ID = FieldTypeId.top(AUTHORITY_KINDED_ID_FIELD_CODE);
    PrefabFieldType AUTHORITY_KINDED_ID_PREFAB_FIELD_TYPE = new PrefabFieldType(
            AUTHORITY_KINDED_ID_FIELD_TYPE_ID,
            AUTHORITY_KINDED_ID_FIELD_TYPE_ID,
            "Authority kinded ID",
            false,
            false,
            true,
            new NoLinkDef(),
            FieldSource.common(),
            TransferType.OVERWRITE,
            PrefabFieldType.NO_LINK_ROOT_FOND,
            CoreConstants.Datatype.TEXT,
            FieldExportSetting.disabled(),
            PrefabFieldType.NO_PIC,
            FdefGeneration.BOUND
    );

    String USER_FIELD_CODE = "user";
    FieldTypeId USER_FIELD_TYPE_ID = FieldTypeId.top(USER_FIELD_CODE);
    PrefabFieldType USER_PREFAB_FIELD_TYPE = new PrefabFieldType(
            USER_FIELD_TYPE_ID,
            USER_FIELD_TYPE_ID,
            "User",
            false,
            false,
            true,
            new NoLinkDef(),
            FieldSource.binding("user"),
            TransferType.OVERWRITE,
            PrefabFieldType.NO_LINK_ROOT_FOND,
            CoreConstants.Datatype.USER,
            FieldExportSetting.disabled(),
            PrefabFieldType.NO_PIC,
            FdefGeneration.BOUND
    );

    String TOC_FIELD_CODE = "toc";
    FieldTypeId TOC_FIELD_TYPE_ID = FieldTypeId.top(TOC_FIELD_CODE);
    PrefabFieldType TOC_PREFAB_FIELD_TYPE = new PrefabFieldType(
            TOC_FIELD_TYPE_ID,
            TOC_FIELD_TYPE_ID,
            "TOC",
            false,
            false,
            true,
            new NoLinkDef(),
            FieldSource.common(),
            TransferType.OVERWRITE,
            PrefabFieldType.NO_LINK_ROOT_FOND,
            PrefabFieldType.NO_EXPLICIT_VALUE_DATATYPE,
            FieldExportSetting.disabled(),
            PrefabFieldType.NO_PIC,
            FdefGeneration.GENERATED_NOPERSIST
    );

    String TOC_URL_FIELD_CODE = "url";
    FieldTypeId TOC_URL_FIELD_TYPE_ID = TOC_FIELD_TYPE_ID.sub(TOC_URL_FIELD_CODE);
    PrefabFieldType TOC_URL_PREFAB_FIELD_TYPE = new PrefabFieldType(
            TOC_URL_FIELD_TYPE_ID,
            TOC_URL_FIELD_TYPE_ID,
            "URL",
            false,
            false,
            true,
            new NoLinkDef(),
            FieldSource.common(),
            TransferType.OVERWRITE,
            PrefabFieldType.NO_LINK_ROOT_FOND,
            CoreConstants.Datatype.TEXT,
            FieldExportSetting.disabled(),
            PrefabFieldType.NO_PIC,
            FdefGeneration.GENERATED_NOPERSIST
    );

    String TOC_CONTENT_FIELD_CODE = "content";
    FieldTypeId TOC_CONTENT_FIELD_TYPE_ID = TOC_FIELD_TYPE_ID.sub(TOC_CONTENT_FIELD_CODE);
    PrefabFieldType TOC_VALUE_PREFAB_FIELD_TYPE = new PrefabFieldType(
            TOC_CONTENT_FIELD_TYPE_ID,
            TOC_CONTENT_FIELD_TYPE_ID,
            "Obsah",
            false,
            false,
            true,
            new NoLinkDef(),
            FieldSource.common(),
            TransferType.OVERWRITE,
            PrefabFieldType.NO_LINK_ROOT_FOND,
            CoreConstants.Datatype.TEXT,
            FieldExportSetting.disabled(),
            PrefabFieldType.NO_PIC,
            FdefGeneration.GENERATED_NOPERSIST
    );
}
