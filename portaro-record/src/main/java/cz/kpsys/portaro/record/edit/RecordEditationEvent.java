package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.record.detail.FieldId;
import jakarta.annotation.Nullable;
import lombok.NonNull;

import java.util.Collection;
import java.util.Set;
import java.util.UUID;

public record RecordEditationEvent(

        @NonNull
        UUID id,

        @NonNull
        UUID recordId,

        @NonNull
        Type type,

        @NonNull
        Boolean wholeRecord,

        @Nullable
        Object data

) {

    public enum Type {
        RECORD_DRAFT_CREATION,
        RECORD_PUBLICATION,
        RECORD_FOND_CHANGE,
        RECORD_LOCK,
        RECORD_UNLOCK,
        RECORD_STATUS_CHANGE_TO_UNFINISHED_CATALOGING,
        RECORD_STATUS_CHANGE_TO_FINISHED_CATALOGING,
        RECORD_STATUS_CHANGE_TO_CASLIN_SENT,
        RECORD_STATUS_CHANGE_TO_CASLIN_ACCEPTED,
        RECORD_STATUS_CHANGE_TO_OTHER,
        FIELD_VALUE_EDITATION,
        FIELD_VALUE_SETTING,
        FIELD_VALUE_DELETION,
        FIELD_CREATION,
        FIELD_REMOVAL,
        FIELD_MOVEMENT
    }

    public static RecordEditationEvent createRecordEvent(@NonNull UUID recordId, @NonNull Type type) {
        return new RecordEditationEvent(UuidGenerator.forIdentifier(), recordId, type, true, null);
    }

    public static RecordEditationEvent createFieldEvent(@NonNull UUID recordId, @NonNull Type type, @NonNull FieldId data) {
        return new RecordEditationEvent(UuidGenerator.forIdentifier(), recordId, type, false, data);
    }

    public static boolean containsAnyPersistableChange(@NonNull Collection<RecordEditationEvent> events) {
        return !events.isEmpty();
    }

    public static boolean containsAnyOfType(@NonNull Collection<RecordEditationEvent> events, @NonNull Type... types) {
        Set<@NonNull Type> typeSet = Set.of(types);
        return events.stream().anyMatch(modification -> typeSet.contains(modification.type()));
    }

}
