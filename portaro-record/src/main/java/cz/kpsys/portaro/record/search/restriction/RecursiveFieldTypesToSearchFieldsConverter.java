package cz.kpsys.portaro.record.search.restriction;

import com.google.common.collect.Streams;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.search.field.SearchField;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.stream.Stream;

public class RecursiveFieldTypesToSearchFieldsConverter implements Converter<List<? extends FieldType<?>>, List<SearchField>> {

    @Override
    public List<SearchField> convert(@NonNull List<? extends FieldType<?>> source) {
        return source.stream()
                .flatMap(RecursiveFieldTypesToSearchFieldsConverter::streamSearchFields)
                .toList();
    }

    private static @NonNull Stream<SearchField> streamSearchFields(FieldType<?> fieldType) {
        return Streams.concat(
                getValueSearchField(fieldType),
                getLinkSearchField(fieldType),
                getOriginSearch<PERSON>ield(fieldType),
                getNestedSearchFields(fieldType)
        );
    }

    private static @NonNull Stream<SearchField> getValueSearchField(FieldType<?> fieldType) {
        if (fieldType.isAutonomous() && !fieldType.isGroup()) {
            return Stream.of(FieldTypedSearchFieldParsing.ofValue(fieldType.getFieldTypeId()).toSearchField(fieldType));
        }
        return Stream.empty();
    }

    private static @NonNull Stream<SearchField> getLinkSearchField(FieldType<?> fieldType) {
        if (fieldType.isAutonomous() && fieldType.getLinkRootFond().isPresent()) {
            return Stream.of(FieldTypedSearchFieldParsing.ofLink(fieldType.getFieldTypeId()).toSearchField(fieldType));
        }
        return Stream.empty();
    }

    private static @NonNull Stream<SearchField> getOriginSearchField(FieldType<?> fieldType) {
        if (fieldType.isAutonomous() && fieldType.getLinkRootFond().isEmpty()) {
            return Stream.of(FieldTypedSearchFieldParsing.ofOrigin(fieldType.getFieldTypeId()).toSearchField(fieldType));
        }
        return Stream.empty();
    }

    private static @NonNull Stream<SearchField> getNestedSearchFields(FieldType<?> fieldType) {
        return fieldType.getSubfieldTypes().stream()
                .flatMap(RecursiveFieldTypesToSearchFieldsConverter::streamSearchFields);
    }
}
