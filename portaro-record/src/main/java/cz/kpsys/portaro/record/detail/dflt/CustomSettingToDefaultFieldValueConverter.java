package cz.kpsys.portaro.record.detail.dflt;

import cz.kpsys.portaro.commons.object.IdentifiedValue;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.fond.FondTypeResolver;
import cz.kpsys.portaro.setting.CustomSetting;
import cz.kpsys.portaro.setting.CustomSettingId;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.Objects;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CustomSettingToDefaultFieldValueConverter implements Converter<CustomSetting<String>, IdentifiedValue<DefaultFieldValueId, @NotBlank String>> {

    public static final String FIELD_CODE_DELIMITER = "_";

    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull ByIdLoadable<Fond, Integer> fondLoader;

    @Override
    public IdentifiedValue<DefaultFieldValueId, @NotBlank String> convert(CustomSetting<String> source) {
        CustomSettingId customSettingId = source.getId();

        Department department = departmentLoader.getById(Objects.requireNonNull(customSettingId.getDepartmentId()));
        Optional<Fond> fond = Optional.ofNullable(customSettingId.getFondId()).map(fondLoader::getById);
        FieldTypeId fieldTypeId = isNewFormat(customSettingId.getName()) ? getFieldTypeId(customSettingId.getName()) : getOldFieldTypeId(customSettingId.getName(), fond);

        DefaultFieldValueId id = new DefaultFieldValueId(fieldTypeId, department, fond);
        String value = StringUtil.requireNotBlank(source.getValue(), "Default field value defined in custom settings (INI) %s cannot be blank".formatted(source.getId()));
        return IdentifiedValue.of(id, value);
    }

    private static boolean isNewFormat(String fieldNumber) {
        return fieldNumber.startsWith(FieldTypeId.NEW_DOCUMENT_NUMBER_PREFIX) || fieldNumber.startsWith(FieldTypeId.NEW_AUTHORITY_NUMBER_PREFIX);
    }

    private static FieldTypeId getFieldTypeId(String customSettingName) {
        String fieldTypeIdString = customSettingName.replace(FIELD_CODE_DELIMITER, FieldTypeId.DELIMITER);
        return FieldTypeId.parse(fieldTypeIdString);
    }

    private static FieldTypeId getOldFieldTypeId(String customSettingName, Optional<Fond> fond) {
        String[] split = customSettingName.split(FIELD_CODE_DELIMITER);
        String fieldNumber;
        String subfieldCode;
        switch (split.length) {
            case 1 -> {
                fieldNumber = split[0];
                subfieldCode = null;
            }
            case 2 -> {
                fieldNumber = split[0];
                subfieldCode = split[1];
            }
            default -> throw new IllegalArgumentException("Incorrect default field value identifier (ini key) %s".formatted(customSettingName));
        }

        boolean inAuthorityRecord = fond.isPresent() && FondTypeResolver.isAuthorityFond(fond.get());
        FieldTypeId fieldTypeId = FieldTypeId.recordField(inAuthorityRecord, fieldNumber);
        if (subfieldCode != null) {
            fieldTypeId = fieldTypeId.sub(subfieldCode);
        }
        return fieldTypeId;
    }

}
