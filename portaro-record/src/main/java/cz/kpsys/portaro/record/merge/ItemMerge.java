package cz.kpsys.portaro.record.merge;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public class ItemMerge<E> {

    public enum ItemMergingWay {
        MOVE, REPLACE, DELETE;
    }

    @NonNull E item;
    ItemMergingWay way;
    @Nullable E replacement;

    public static <E> ItemMerge<E> byReplace(E item, E replacement) {
        return new ItemMerge<>(item, ItemMergingWay.REPLACE, replacement);
    }

    public static <E> ItemMerge<E> byInsert(E item) {
        return new ItemMerge<>(item, ItemMergingWay.MOVE, null);
    }

    public static <E> ItemMerge<E> byDelete(E item) {
        return new ItemMerge<>(item, ItemMergingWay.DELETE, null);
    }

}
