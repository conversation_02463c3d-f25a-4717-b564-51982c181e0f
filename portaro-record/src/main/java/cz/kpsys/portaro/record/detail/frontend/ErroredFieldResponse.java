package cz.kpsys.portaro.record.detail.frontend;

import com.fasterxml.jackson.annotation.JsonProperty;
import cz.kpsys.portaro.commons.object.LabeledRecord;
import cz.kpsys.portaro.record.detail.value.*;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

public interface ErroredFieldResponse extends LabeledRecord {

    @JsonProperty
    @NonNull FailedResultType type();

    static ErroredFieldResponse map(@Nullable FailedResult failedResult) {
        if (failedResult == null) {
            return null;
        }
        return switch (failedResult) {
            case EmptyCoalesceFail fail -> new ErroredFieldGenericResponse(fail.text(), FailedResultType.EMPTY_COALESCE);
            case AbsentLinkFail fail -> new ErroredFieldGenericResponse(fail.text(), FailedResultType.ABSENT_LINK);
            case AbsentLinkingFieldsFail fail -> new AbsentLinkingFieldsResponse(fail.text(), FailedResultType.ABSENT_LINKING_FIELDS, fail.missingLinkingRecordsLinkFieldTypeId(), fail.missingLinkingRecordsLink(), fail.missingLinkingRecordsValueFieldTypeId());
            case AbsentValueFail fail -> new ErroredFieldGenericResponse(fail.text(), FailedResultType.ABSENT_VALUE);
            case MultiAbsentDataFail fail -> new ErroredFieldGenericResponse(fail.text(), FailedResultType.MULTI_ABSENT_FAIL);
            case NotAScalarFail fail -> new ErroredFieldGenericResponse(fail.text(), FailedResultType.NOT_A_SCALAR);
            case NotAVectorFail fail -> new ErroredFieldGenericResponse(fail.text(), FailedResultType.NOT_A_VECTOR);
            case MultiNonAbsentFailFail fail -> new ErroredFieldGenericResponse(fail.text(), FailedResultType.MULTI_FAIL);
            case IncorrectTypeFail fail -> new ErroredFieldGenericResponse(fail.text(), FailedResultType.INCORRECT_TYPE);
            case ArithmeticFail fail -> new ErroredFieldGenericResponse(fail.text(), FailedResultType.ARITHMETIC_FAIL);
            case FalseCaseEvaluation fail -> new ErroredFieldGenericResponse(fail.text(), FailedResultType.FAILED_CASE);
            case EmptyMatchFail fail -> new ErroredFieldGenericResponse(fail.text(), FailedResultType.EMPTY_SWITCH);
        };
    }

}
