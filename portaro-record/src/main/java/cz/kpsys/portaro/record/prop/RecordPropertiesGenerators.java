package cz.kpsys.portaro.record.prop;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Labeled;
import cz.kpsys.portaro.commons.object.LabeledName;
import cz.kpsys.portaro.commons.object.LabeledRefOrName;
import cz.kpsys.portaro.commons.object.LabeledValue;
import cz.kpsys.portaro.commons.util.NumberUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.prop.ObjectProperties;
import cz.kpsys.portaro.prop.ObjectProperty;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.export.RecordViewFunctions;
import cz.kpsys.portaro.record.isbn.Isbn;
import cz.kpsys.portaro.record.isbn.IsbnChecker;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.*;

public class RecordPropertiesGenerators {

    @NonNull
    public static ObjectProperties createForDocumentByExplicit(@Nullable String name,
                                                               @Nullable String author,
                                                               @Nullable String isxnString,
                                                               @Nullable String publisher,
                                                               @Nullable Integer publicationYear) {
        Set<ObjectProperty<?>> res = new HashSet<>();
        if (StringUtil.hasTrimmedLength(name)) {
            res.add(ObjectProperty.createScalar(RecordPropertyKeys.NAME, name));
        }
        if (StringUtil.hasTrimmedLength(author)) {
            res.add(ObjectProperty.createList(RecordPropertyKeys.PRIMARY_AUTHORS, List.of(LabeledName.of(author))));
        }
        if (StringUtil.hasTrimmedLength(isxnString)) {
            if (IsbnChecker.isValidIsbn(isxnString)) {
                Isbn isxn = new Isbn(isxnString);
                res.add(ObjectProperty.createList(RecordPropertyKeys.ISBNS, List.of(LabeledValue.of(isxn.getNormalizedValue(), Texts.ofNative(isxn.getValue())))));
            } else if (IsbnChecker.isValidIssn(isxnString)) {
                Isbn isxn = new Isbn(isxnString);
                res.add(ObjectProperty.createList(RecordPropertyKeys.ISSNS, List.of(LabeledValue.of(isxn.getNormalizedValue(), Texts.ofNative(isxn.getValue())))));
            }
        }
        if (StringUtil.hasTrimmedLength(publisher)) {
            res.add(ObjectProperty.createList(RecordPropertyKeys.PUBLISHERS, List.of(LabeledName.of(publisher))));
        }
        if (NumberUtil.isPositive(publicationYear)) {
            res.add(ObjectProperty.createScalar(RecordPropertyKeys.PUBLICATION_START_YEAR, LabeledValue.ofNumber(publicationYear)));
        }
        return ObjectProperties.of(res);
    }

    @NonNull
    public static Collection<ObjectProperty<?>> createForDocumentByDetail(@NonNull FieldContainer recordDetail) {
        List<ObjectProperty<?>> res = new ArrayList<>();

        RecordViewFunctions.documentName(recordDetail)
                .ifPresent(stringValue -> res.add(ObjectProperty.createScalar(RecordPropertyKeys.NAME, stringValue)));

        List<Labeled> subtitles = RecordViewFunctions.subtitle(recordDetail);
        if (!subtitles.isEmpty()) {
            res.add(ObjectProperty.createList(RecordPropertyKeys.SUBTITLE, subtitles));
        }

        RecordViewFunctions.responsibilityStatement(recordDetail)
                .ifPresent(labeled -> res.add(ObjectProperty.createScalar(RecordPropertyKeys.RESPONSIBILITY_STATEMENT, labeled)));

        List<LabeledRefOrName> primaryAuthors = RecordViewFunctions.primaryAuthors(recordDetail);
        if (!primaryAuthors.isEmpty()) {
            res.add(ObjectProperty.createList(RecordPropertyKeys.PRIMARY_AUTHORS, primaryAuthors));
        }

        List<LabeledValue<String>> isbns = RecordViewFunctions.normalizedIsbns(recordDetail);
        if (!isbns.isEmpty()) {
            res.add(ObjectProperty.createList(RecordPropertyKeys.ISBNS, isbns));
        }

        List<LabeledValue<String>> issns = RecordViewFunctions.normalizedIssns(recordDetail);
        if (!issns.isEmpty()) {
            res.add(ObjectProperty.createList(RecordPropertyKeys.ISSNS, issns));
        }

        List<LabeledRefOrName> publishers = RecordViewFunctions.publishers(recordDetail);
        if (!publishers.isEmpty()) {
            res.add(ObjectProperty.createList(RecordPropertyKeys.PUBLISHERS, publishers));
        }

        RecordViewFunctions.publicationStartYear(recordDetail)
                .ifPresent(labeledInteger -> res.add(ObjectProperty.createScalar(RecordPropertyKeys.PUBLICATION_START_YEAR, labeledInteger)));

        RecordViewFunctions.primaryCpkId(recordDetail)
                .ifPresent(stringValue -> res.add(ObjectProperty.createScalar(RecordPropertyKeys.PRIMARY_CPK_ID, stringValue)));

        return res;
    }

    @NonNull
    public static Collection<ObjectProperty<?>> createForAuthorityByDetail(@NonNull FieldContainer recordDetail) {
        List<ObjectProperty<?>> res = new ArrayList<>();

        List<String> alternativeNames = RecordViewFunctions.authorityAlternativeNames(recordDetail);
        if (!alternativeNames.isEmpty()) {
            res.add(ObjectProperty.createList(RecordPropertyKeys.ALTERNATIVE_NAMES, alternativeNames));
        }

        return res;
    }
}
