package cz.kpsys.portaro.record.print;

import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.ViewableField;
import cz.kpsys.portaro.record.detail.ViewableFieldContainer;
import cz.kpsys.portaro.record.detail.ViewableFieldable;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

import java.util.Locale;
import java.util.UUID;

public class RecordDetailRichHtmlPrinter extends RecordDetailPlainHtmlPrinter {

    public static final String CLASS_PREFIX_FIELD_NUMBER = "field_";
    
    public RecordDetailRichHtmlPrinter(Translator<Department> translator, Department currentDepartment, Locale locale, Converter<UUID, String> recordIdToDetailUrlConverter) {
        super(translator, currentDepartment, locale, recordIdToDetailUrlConverter);
    }


    @Override
    public String writeValuedField(ViewableFieldable fieldable, @NonNull ViewableFieldContainer originalDetail) {
        String cssClassMarcFieldTypeId = getCssClassMarcFieldTypeId(fieldable);
        return String.format("<span class=\"%s%s %s\" title=\"%s\">%s</span>", RecordDetailRichHtmlPrinter.CLASS_PREFIX_FIELD_NUMBER, fieldable.getCode(), cssClassMarcFieldTypeId, localize(fieldable.getFieldTypeText()), super.writeValuedField(fieldable, originalDetail));
    }

    private String getCssClassMarcFieldTypeId(ViewableFieldable fieldable) {
        if (fieldable instanceof ViewableField) {
            String suffix = ((ViewableField) fieldable).getFieldTypeId().value();
            suffix = suffix.replace(FieldId.DELIMITER_CHAR, '-');
            return "fieldtype-" + suffix;
        }
        return "";
    }
}
