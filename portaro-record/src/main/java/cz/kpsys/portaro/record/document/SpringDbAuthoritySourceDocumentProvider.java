package cz.kpsys.portaro.record.document;

import cz.kpsys.portaro.databasestructure.RecordDb;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.Optional;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbAuthoritySourceDocumentProvider implements AuthoritySourceDocumentProvider {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;

    @Override
    public Optional<UUID> getAuthoritySourceDocumentId(Record a) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(RecordDb.KAT1_7.SOURCE_RECORD_ID);
        sq.from(RecordDb.KAT1_7.KAT1_7);
        sq.where()
                .lt(RecordDb.KAT1_7.CIS_FOND, 30)
                .and()
                .eq(RecordDb.KAT1_7.TARGET_RECORD_ID, a.getId());
        return jdbcTemplate.queryForList(sq.getSql(), sq.getParamMap(), UUID.class).stream().findFirst();
    }
}
