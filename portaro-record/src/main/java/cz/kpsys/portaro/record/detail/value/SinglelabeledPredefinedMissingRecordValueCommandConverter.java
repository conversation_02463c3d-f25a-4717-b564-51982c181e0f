package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SinglelabeledPredefinedMissingRecordValueCommandConverter implements TypedFieldValueConverter<SinglelabeledPredefinedMissingRecordValueCommand, FieldPayload<StringFieldValue>, StringFieldValue> {

    @NonNull
    @Override
    public FieldPayload<StringFieldValue> convert(@NonNull SinglelabeledPredefinedMissingRecordValueCommand command, @NonNull FieldType<?> fieldType, @NonNull RecordFieldId fieldId) {
        StringFieldValue valueHolder = StringFieldValue.of(
                command.fieldLabel(),
                getText(command),
                Set.of(fieldId.recordIdFondPair())
        );
        return FieldPayload.ofMissingLink(valueHolder);
    }

    private Text getText(SinglelabeledPredefinedMissingRecordValueCommand input) {
        return StringUtil.hasTrimmedLength(input.fieldLabel()) ? Texts.ofNative(input.fieldLabel()) : Texts.ofEmpty();
    }

    @Override
    public String toString() {
        return getClass().getSimpleName();
    }
}
