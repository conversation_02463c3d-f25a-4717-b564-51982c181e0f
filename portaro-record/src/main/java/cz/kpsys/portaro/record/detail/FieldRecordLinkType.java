package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum FieldRecordLinkType implements LabeledIdentified<Integer> {

    NO_LINK(0, Texts.ofMessageCoded("record.detail.NotAuthority")),
    THIS_FIELD_ENTRYFIELD_LINK(2, Texts.ofMessageCoded("record.detail.SimpleAuthority")),
    PARENT_VIRTUAL_GROUP_ENTRYFIELD_LINK(3, Texts.ofMessageCoded("record.detail.ComplexAuthority")),
    SPECIFIC_FIELD_LINK(101, Texts.ofNative("Generic link")),
    LOCAL_RECORD(102, Texts.ofNative("This record")),
    FORMULA(103, Texts.ofNative("Formula"));

    public static final Codebook<FieldRecordLinkType, Integer> CODEBOOK = new StaticCodebook<>(values());

    @NonNull Integer id;
    @NonNull Text text;
}
