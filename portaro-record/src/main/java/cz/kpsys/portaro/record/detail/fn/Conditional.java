package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.datatype.DatatypeUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.BooleanFieldValue;
import cz.kpsys.portaro.record.detail.value.FieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import lombok.NonNull;

import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public record Conditional<RES extends FieldValue<?>>(

        @NonNull
        Formula<BooleanFieldValue> condition,

        @NonNull
        Formula<RES> onTrue,

        @NonNull
        Formula<RES> onFalse

) implements ResolvableFunction<RES> {

    public static <RES extends FieldValue<?>> @NonNull Conditional<RES> create(@NonNull Formula<BooleanFieldValue> condition,
                                                                               @NonNull Formula<RES> onTrue,
                                                                               @NonNull Formula<RES> onFalse) {
        return new Conditional<>(condition, onTrue, onFalse);
    }

    @Override
    public @NonNull Set<LookupDefinition> dependencies() {
        return Stream.of(condition, onTrue, onFalse)
                .map(Formula::dependencies)
                .flatMap(Set::stream)
                .collect(Collectors.toUnmodifiableSet());
    }

    @Override
    public @NonNull Stream<? extends Formula<?>> subformulas() {
        return Stream.of(condition(), onTrue(), onFalse())
                .flatMap(f -> Stream.concat(Stream.of(f), f.subformulas()));
    }

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<RES>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        List<ScalarDatatype> scalarDatatypes = ResolvableFunction.resolveOperandDatatypes(List.of(onTrue, onFalse), operandResultDatatypeResolver);
        return DatatypeUtil.getMostPreciseDatatypeIfNumberOrRequireSingleUniqueDatatype(scalarDatatypes, getClass().getSimpleName());
    }

    @Override
    public @NonNull FormulaEvaluation<RES> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {
        // resolve condition
        FormulaEvaluation<BooleanFieldValue> conditionResult = evaluator.resolveFormulaValue(sourceNode, condition);
        if (conditionResult.isFailure()) {
            return conditionResult.castedFailed();
        }
        Boolean conditionValue = conditionResult.existingSuccess().value();

        if (conditionValue) {
            return evaluator.resolveFormulaValue(sourceNode, onTrue);
        }
        return evaluator.resolveFormulaValue(sourceNode, onFalse);
    }

    @Override
    public String toString() {
        return "If(%s ? %s : %s)".formatted(condition, onTrue, onFalse);
    }
}
