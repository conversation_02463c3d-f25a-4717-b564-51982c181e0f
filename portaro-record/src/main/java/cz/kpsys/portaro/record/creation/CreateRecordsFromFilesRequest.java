package cz.kpsys.portaro.record.creation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.fond.Fond;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.NonNull;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public record CreateRecordsFromFilesRequest(

        @Schema(implementation = Integer.class, description = "Fond ID", example = Fond.SCHEMA_EXAMPLE_MONOGRAPHY_ID)
        @NotNull Fond fond,

        @Schema(description = "Files encoded as multipart data")
        @NotEmpty List<MultipartFile> files

) {

    public CreateRecordsFromFilesCommand toCommand(@NonNull Department ctx,
                                                   @NonNull UserAuthentication currentAuth) {
        return new CreateRecordsFromFilesCommand(fond(), files(), ctx, currentAuth);
    }

}
