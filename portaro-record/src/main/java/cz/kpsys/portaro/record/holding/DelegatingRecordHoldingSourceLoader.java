package cz.kpsys.portaro.record.holding;

import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DelegatingRecordHoldingSourceLoader implements RecordHoldingSourceLoader {

    @NonNull RecordHoldingSourceEntityLoader recordHoldingSourceEntityLoader;
    @NonNull Converter<List<? extends RecordHoldingSourceEntity>, List<RecordHoldingSource>> entitiesToRecordHoldingSourceConverter;

    @Override
    public RecordHoldingSource getById(@NonNull UUID id) throws ItemNotFoundException {
        RecordHoldingSourceEntity entity = recordHoldingSourceEntityLoader.findById(id).orElseThrow(() -> new ItemNotFoundException(RecordHoldingSource.class, id));
        return ListUtil.convertSingle(entity, entitiesToRecordHoldingSourceConverter);
    }

    @Override
    public List<RecordHoldingSource> getAllByIds(@NonNull List<UUID> ids) {
        Iterable<RecordHoldingSourceEntity> entities = recordHoldingSourceEntityLoader.findAllById(ids);
        return ListUtil.convertIterable(entities, entitiesToRecordHoldingSourceConverter);
    }

    @Override
    public List<RecordHoldingSource> getAllByRecordHoldingId(@NonNull UUID recordHoldingId) {
        List<RecordHoldingSourceEntity> entities = recordHoldingSourceEntityLoader.findAllByRecordHoldingId(recordHoldingId);
        return entitiesToRecordHoldingSourceConverter.convert(entities);
    }
}
