package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.convert.StringToUuidConverter;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.util.ComparatorForExplicitIdSorting;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.record.Record.TYPE_DOCUMENT;

/**
 *
 * Univerzalni loader seznamu idecek zaznamu. <br/>
 * Pokud jsou idecka bez prefixu, povazuje id za
 * <br/> 1) dokument, pokud je zde podpora pro dokumenty (tzn. nastaven documentLoader)
 * <br/> 2) autoritu, pokud je zde podpora pro autority (tzn. nastaven authorityLoader)
 * <AUTHOR> <PERSON>
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
public class IdsToRecordsConverter implements Converter<List<String>, List<Record>> {

    @Nullable AllByIdsLoadable<Record, Integer> authorityLoader;
    @Nullable AllByIdsLoadable<Record, Integer> documentLoader;
    @Nullable AllByIdsLoadable<Record, UUID> richRecordLoader;

    public IdsToRecordsConverter withDocumentSupport(AllByIdsLoadable<Record, Integer> documentLoader) {
        this.documentLoader = documentLoader;
        return this;
    }

    public IdsToRecordsConverter withAuthoritySupport(AllByIdsLoadable<Record, Integer> authorityLoader) {
        this.authorityLoader = authorityLoader;
        return this;
    }

    public IdsToRecordsConverter withRecordSupport(AllByIdsLoadable<Record, UUID> richRecordLoader) {
        this.richRecordLoader = richRecordLoader;
        return this;
    }

    @Override
    public List<Record> convert(List<String> idList) {
        List<Integer> documentIds = new ArrayList<>(idList.size());
        List<Integer> authorityIds = new ArrayList<>(idList.size());
        List<UUID> recordIds = new ArrayList<>(idList.size());
        for (String recordId : idList) {
            if (Character.isDigit(recordId.charAt(0))) {
                if (documentLoader != null) {
                    documentIds.add(Integer.parseInt(recordId));
                } else if (authorityLoader != null) {
                    authorityIds.add(Integer.parseInt(recordId));
                } else {
                    throw new UnsupportedOperationException(String.format("No document or authority loader for record %s", recordId));
                }
            } else if (StringToUuidConverter.isUuid(recordId)) {
                recordIds.add(StringToUuidConverter.INSTANCE.convert(recordId));
            } else if (recordId.startsWith(RecordConstants.DOCUMENT_PREFIX)) {
                documentIds.add(Integer.parseInt(recordId.substring(1)));
            } else if (recordId.startsWith(RecordConstants.AUTHORITY_PREFIX)) {
                authorityIds.add(Integer.parseInt(recordId.substring(1)));
            } else {
                throw new UnsupportedOperationException(String.format("Unknown or unsupported record id format \"%s\"", recordId));
            }
        }

        List<Record> records = new ArrayList<>(idList.size());

        if (!documentIds.isEmpty()) {
            Assert.notNull(documentLoader, "Search result contains documens, but documentLoader is not present");
            records.addAll(documentLoader.getAllByIds(documentIds));
        }
        if (!authorityIds.isEmpty()) {
            Assert.notNull(authorityLoader, "Search result contains authorities, but authorityLoader is not present");
            records.addAll(authorityLoader.getAllByIds(authorityIds));
        }
        if (!recordIds.isEmpty()) {
            Assert.notNull(richRecordLoader, "Search result contains record uuids, but richRecordLoader is not present");
            records.addAll(richRecordLoader.getAllByIds(recordIds));
        }

        records.sort(new ComparatorForExplicitIdSorting<>(idList, record -> {
            if (isUuidFormat(idList)) {
                return record.getId().toString();
            }
            return (record.getType().equals(TYPE_DOCUMENT) ? RecordConstants.DOCUMENT_PREFIX : RecordConstants.AUTHORITY_PREFIX) + record.getKindedId();
        }));

        return records;
    }

    private boolean isUuidFormat(List<String> idList) {
        return !idList.isEmpty() && StringToUuidConverter.isUuid(idList.getFirst());
    }

}
