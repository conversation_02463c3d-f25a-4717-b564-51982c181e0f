package cz.kpsys.portaro.record.detail.dflt;

import cz.kpsys.portaro.commons.object.repo.ByIdOptLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.Indicator;
import cz.kpsys.portaro.record.detail.IndicatorType;
import cz.kpsys.portaro.record.fond.Fond;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class IndicatorDefaultFieldValueResolver implements DefaultFieldValueResolver {

    @NonNull ByIdOptLoadable<IndicatorType, FieldTypeId> indicatorTypeLoader;

    @Override
    public Optional<@NotBlank String> findOn(@NonNull FieldTypeId fieldTypeId, @NonNull Fond fond, @NonNull Department ctx) {
        Optional<IndicatorType> indicatorType = indicatorTypeLoader.findById(fieldTypeId);
        if (indicatorType.isEmpty()) {
            return Optional.empty();
        }

        List<Indicator> indicators = indicatorType.get().getAcceptableValues();
        Optional<String> firstDefault = getFirstDefault(indicators);
        if (firstDefault.isPresent()) {
            return firstDefault;
        }
        Optional<String> first = getFirst(indicators);
        log.warn("There is no default {} indicator, using first of list '{}'", fieldTypeId, first.orElse(null));
        return first;
    }

    private @NonNull Optional<String> getFirstDefault(@NonNull List<Indicator> indicators) {
        return indicators.stream()
                .filter(Indicator::isDefault)
                .map(Indicator::getId)
                .findFirst();
    }

    private @NonNull Optional<String> getFirst(List<Indicator> indicators) {
        return indicators.stream()
                .map(Indicator::getId)
                .findFirst();
    }

}
