package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.commons.object.SeveritedException;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EmptyWorksheetException extends RuntimeException implements SeveritedException, UserFriendlyException {

    @NonNull Fond fond;

    public EmptyWorksheetException(@NonNull String message, @NonNull Fond fond) {
        super(message);
        this.fond = fond;
    }

    @Override
    public Text getText() {
        return Texts.ofMessageCodedWithLocalizedArgs("record.PracovniListProFondXJePrazdny", fond.getText());
    }

    @Override
    public int getSeverity() {
        return SEVERITY_WARNING;
    }
}
