package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import lombok.NonNull;

public record FalseCaseEvaluation(

        @NonNull Text text

) implements NonAbsentDataFail {

    public static @NonNull FalseCaseEvaluation of() {
        return new FalseCaseEvaluation(Texts.ofNative("Switch case condition evaluated to false"));
    }

    @Override
    public String toString() {
        return "FalseCaseEvaluation";
    }

}
