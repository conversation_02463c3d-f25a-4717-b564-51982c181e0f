package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.concurrent.GlobalLocker;
import cz.kpsys.portaro.commons.concurrent.SemaphoreLocker;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.commons.util.ListUtil.getListOfIds;
import static cz.kpsys.portaro.databasestructure.RecordDb.KAT1_7;
import static cz.kpsys.portaro.databasestructure.RecordDb.RECORD_HOLDING;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SpringDbSimilarRecordsLoader implements SimilarRecordsLoader, RowMapper<SpringDbSimilarRecordsLoader.RecordIdQuantity> {

    public static final String OCCURRENCES_COUNT_ALIAS = "pocet_vyskytu";

    @NonNull GlobalLocker locker = new SemaphoreLocker(1);
    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull TransactionTemplate readonlyTransactionTemplate;
    @NonNull List<UUID> forbiddenRecordIds;
    @NonNull AllValuesProvider<Fond> linkedRecordAllowedFondsProvider;
    @NonNull AllByIdsLoadable<Record, UUID> recordLoader;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;

    @Override
    public List<Record> getAllByRecord(Record record, Department ctx, int maxCount) {
        return locker.lock(() -> {
            List<RecordIdQuantity> sameAuthoritiesRecords = Objects.requireNonNull(readonlyTransactionTemplate.execute(_ -> loadRecordIdQuantitiesInTransaction(record, ctx, maxCount)));
            return recordLoader.getAllByIds(ListUtil.convert(sameAuthoritiesRecords, RecordIdQuantity::recordId));
        });
    }

    private List<RecordIdQuantity> loadRecordIdQuantitiesInTransaction(Record record, Department ctx, int maxCount) {
        List<Department> departments = contextHierarchyLoader.getAllByScope(ctx, HierarchyLoadScope.FAMILY);
        return getDocumentsWithSameAuthorities(record.getId(), departments, maxCount, false, false);
    }

    /**
     * Popis dotazu: <br/>
     * Najdu autority, ktere se vyskytuji u zaznamu Z. <br/>
     * K temto autoritam najdu vsechny jejich zaznamy (krome zaznamu Z). <br/>
     * Tyto zaznamy seskupim a zjistim pocet jednotlivych vyskytu
     */
    private List<RecordIdQuantity> getDocumentsWithSameAuthorities(UUID recordId, List<Department> departments, int maxPocet, boolean includeExcluded, boolean includeDeleted) {
        if (maxPocet == 0) {
            return List.of();
        }

        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(
                TC(RECORD_HOLDING.TABLE, RECORD_HOLDING.RECORD_ID),
                AS(COUNT(TC(RECORD_HOLDING.TABLE, RECORD_HOLDING.RECORD_ID)), OCCURRENCES_COUNT_ALIAS)
        );
        sq.from(AS(KAT1_7.KAT1_7, "k1"));
        sq.joins().addLeft(AS(KAT1_7.KAT1_7, "k2"), COLSEQ(TC("k1", KAT1_7.TARGET_RECORD_ID), TC("k2", KAT1_7.TARGET_RECORD_ID)));
        sq.joins().add(RECORD_HOLDING.TABLE, COLSEQ(TC(RECORD_HOLDING.TABLE, RECORD_HOLDING.RECORD_ID), TC("k2", KAT1_7.SOURCE_RECORD_ID)));

        if (ListUtil.hasLength(forbiddenRecordIds)) {
            sq.where().and().notIn(TC(RECORD_HOLDING.TABLE, RECORD_HOLDING.RECORD_ID), forbiddenRecordIds);
        }

        if (!includeExcluded) {
            sq.where().and().isNull(withoutIndexString(TC(RECORD_HOLDING.TABLE, RECORD_HOLDING.DISCARDION_EVENT_ID)));
        }

        if (!includeDeleted) {
            sq.where().and().isNull(withoutIndexString(TC(RECORD_HOLDING.TABLE, RECORD_HOLDING.DELETION_EVENT_ID)));
        }

        if (departments.isEmpty()) {
            return List.of();
        }
        sq.where().and().in(TC(RECORD_HOLDING.TABLE, RECORD_HOLDING.DEPARTMENT_ID), getListOfIds(departments));

        sq.where()
                .and()
                .in(TC("k1", KAT1_7.CIS_FOND), ListUtil.getListOfIds(linkedRecordAllowedFondsProvider.getAll()))
                .and()
                .eq(TC("k1", KAT1_7.SOURCE_RECORD_ID), recordId)
                .and()
                .notEq(TC("k2", KAT1_7.SOURCE_RECORD_ID), recordId);
        sq.groupBy(TC(RECORD_HOLDING.TABLE, RECORD_HOLDING.RECORD_ID));
        sq.orderBy().addDesc(OCCURRENCES_COUNT_ALIAS);

        sq.setRange(Range.forFirstPage(maxPocet));

        return notAutoCommittingJdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
    }

    @Override
    public RecordIdQuantity mapRow(@NonNull ResultSet rs, int rowNum) throws SQLException {
        UUID recordId = DbUtils.uuidNotNull(rs, RECORD_HOLDING.RECORD_ID);
        int occurrencesCount = rs.getInt(OCCURRENCES_COUNT_ALIAS);
        return new RecordIdQuantity(recordId, occurrencesCount);
    }


    public record RecordIdQuantity(@NonNull UUID recordId, @NonNull Integer quantity) {}

}
