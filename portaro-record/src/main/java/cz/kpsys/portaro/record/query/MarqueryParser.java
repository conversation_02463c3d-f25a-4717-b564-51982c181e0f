package cz.kpsys.portaro.record.query;

import cz.kpsys.portaro.commons.convert.ChainingConverter;
import cz.kpsys.portaro.commons.convert.ListToModifiedListConverter;
import cz.kpsys.portaro.commons.convert.StringToIntegerConverter;
import cz.kpsys.portaro.commons.convert.StringToStringListConverter;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.NonNull;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cz.kpsys.portaro.commons.util.RegExpUtils.*;

public class MarqueryParser {
    
    private static final String F_NUMBER_REGEX = "(?:[0-9a-z]+)";
    private static final String F_REP_REGEX = "(?:[0-9]+)";
    private static final String SF_CODES_REGEX = "(?:[0-9a-z]+)";
    private static final String SF_REP_REGEX = "(?:[0-9]+)";
    private static final Pattern PATTERN = Pattern.compile(startEnd(
            listWithoutBrackets(
                    optionalOne(listInf(F_NUMBER_REGEX, true), false) +
                    optionalOne("#" + listInf(F_REP_REGEX, true), false) +
                    optionalOne("\\." + listInf(SF_CODES_REGEX, true), false) +
                    optionalOne("#" + listInf(SF_REP_REGEX, true), false),
                false)
    ));
    
    private static final Converter<String, List<Integer>> NUMBER_LIST_CONVERTER = new ChainingConverter<>(
            new StringToStringListConverter(true, ",")
                    .throwOnBlankItems(),
            new ListToModifiedListConverter<>(new StringToIntegerConverter()));

    

    public static String sf(int fieldNumber, String subfieldCode) {
        return fieldNumber + "." + subfieldCode;
    }
    
    
    public boolean isValid(String query) {
        return PATTERN.matcher(query).matches();
    }
    
    public ValFieldGroup parse(String query, FieldGroupFiller filler) {
        
        // 1,5,13,25   #2,6,14,26   .3,7,15,27   #4,8,16,28      ,      9,17,29 #10,18,30 .11,19,31e #12,20,32    ,      21,33 #22,34 .23,35 #24,36    ,      37 #38 .39 #40
        
        Matcher matcher = PATTERN.matcher(query);
        if (matcher.matches()) {
            int mainGroups = 15; // number of parts in RegExpUtils.listWithoutBrackets
            int groupsInSingleGroup = 4; // number of parts within 1 part
            for (int i = 1; i <= ((mainGroups  - 1) * groupsInSingleGroup) + 1; i += groupsInSingleGroup) {
                if (matcher.group(i) != null) {
                    filler.add(
                            convertFieldCodes(matcher.group(i), true), //field numbers
                            NUMBER_LIST_CONVERTER.convert(matcher.group(i + 1)), //field repetitions
                            convertFieldCodes(matcher.group(i + 2), false), //subfield codes
                            NUMBER_LIST_CONVERTER.convert(matcher.group(i + 3)) //subfield repetitions
                    );
                }
            }
        }
        
        return filler.getResult();
    }

    @NonNull
    private List<String> convertFieldCodes(String group, boolean strictLists) {
        if (group != null && group.contains("[") &&  group.contains("]")) {
            String withoutBrackets = StringUtil.removeEnclosingBrackets(group, '[', ']');
            return Arrays.stream(withoutBrackets.split(","))
                    .map(String::trim)
                    .toList();
        }
        if (strictLists) {
            return ListUtil.singletonListOrEmptyIfNull(StringUtil.notEmptyString(group));
        }
        return StringUtil.createListOfSingleLetters(group);
    }


}
