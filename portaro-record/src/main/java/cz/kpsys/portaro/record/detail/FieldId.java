package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import cz.kpsys.portaro.commons.util.*;
import cz.kpsys.portaro.record.detail.spec.MultifieldId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;

import static cz.kpsys.portaro.record.detail.FieldTypeId.*;

@Schema(
        description = "Specific field ID in a record",
        type = "string",
        format = "fieldid",
        pattern = "[da]\\d+#\\d+(\\.[a-z]#\\d+){0,2}$",
        example = FieldId.SCHEMA_EXAMPLE_FIELD_ID
)
@EqualsAndHashCode
public class FieldId {

    public static final int FIRST_FIELD_REPETITION = 0;
    public static final char DELIMITER_CHAR = FieldTypeId.DELIMITER_CHAR;
    public static final String DELIMITER = FieldTypeId.DELIMITER;
    public static final char REPETITION_DELIMITER = '#';
    public static final String REPETITION_DELIMITER_STRING = String.valueOf(REPETITION_DELIMITER);

    public static Comparator<FieldId> NUMERICALLY_COMPATIBLE_SORTER = new SameLevelFieldIdComparator(
            new PrioritizingComparator<>(List.of(FieldTypes.AUTHORITY_LEADER_FIELD_CODE, FieldTypes.DOCUMENT_LEADER_FIELD_CODE, FieldTypes.IND_1_FIELD_CODE, FieldTypes.IND_2_FIELD_CODE))
                    .thenComparing(new PrefixedNumberAwareStringComparator(List.of(NEW_AUTHORITY_NUMBER_PREFIX_CHAR, NEW_DOCUMENT_NUMBER_PREFIX_CHAR)))
                    .thenComparing(Comparator.naturalOrder())
    );

    public static final int LEVEL_RECORD = FieldTypeId.LEVEL_RECORD;
    public static final int LEVEL_TOPFIELD = FieldTypeId.LEVEL_TOPFIELD;
    public static final int LEVEL_SUBFIELD = FieldTypeId.LEVEL_SUBFIELD;
    public static final int LEVEL_SUBSUBFIELD = FieldTypeId.LEVEL_SUBSUBFIELD;
    public static final int LEVEL_HIGHEST = LEVEL_TOPFIELD;

    public static final String SCHEMA_EXAMPLE_FIELD_ID = "d245#0.a#0";

    @JsonIgnore
    @Getter
    @Nullable
    private final FieldId parent;

    @Getter
    private final String code;

    @Getter
    private final int repetition;

    private FieldId(@Nullable FieldId parent, @NonNull String code, int repetition) {
        Assert.hasLength(code, "Field code is empty string");
        Assert.state(repetition >= 0, () -> String.format("Field repetition cannot be negative number, but is %s", repetition));
        this.parent = parent;
        this.code = code;
        this.repetition = repetition;
    }

    public static FieldId top(@NonNull String code, int repetition) {
        Assert.state(code.equals(FieldTypes.RECORD_ID_FIELD_CODE) || code.equals(FieldTypes.DOCUMENT_KINDED_ID_FIELD_CODE) || code.equals(FieldTypes.AUTHORITY_KINDED_ID_FIELD_CODE) || code.equals(FieldTypes.FOND_FIELD_CODE) || code.equals(FieldTypes.USER_FIELD_CODE) || code.equals(FieldTypes.TOC_FIELD_CODE) || code.startsWith(NEW_DOCUMENT_NUMBER_PREFIX) || code.startsWith(NEW_AUTHORITY_NUMBER_PREFIX), () -> "Invalid topfield code %s - this check is only temporal to fix potential problems".formatted(code)); // docasne, abychom mohli zjistit problem
        return new FieldId(null, code, repetition);
    }

    public FieldId sub(@NonNull String code, int repetition) {
        return new FieldId(this, code, repetition);
    }

    public static FieldId parse(@NonNull String id) {
        return parse(id, REPETITION_DELIMITER_STRING);
    }

    @NonNull
    public static FieldId parse(@NonNull String id, @NonNull String repetitionDelimiter) {
        if (StringUtil.isNullOrBlank(id)) {
            throw new IllegalArgumentException(String.format("Field id \"%s\" is invalid", id));
        }

        boolean supportOldPrefixFormat = true;
        if (supportOldPrefixFormat) {
            Optional<String> authorityWithoutPrefix = StringUtil.removePrefixOpt(id, OLD_AUTHORITY_CODE_WITH_DOT);
            if (authorityWithoutPrefix.isPresent()) {
                id = NEW_AUTHORITY_NUMBER_PREFIX + authorityWithoutPrefix.get();
            }
            Optional<String> documentWithoutPrefix = StringUtil.removePrefixOpt(id, OLD_DOCUMENT_CODE_WITH_DOT);
            if (documentWithoutPrefix.isPresent()) {
                id = NEW_DOCUMENT_NUMBER_PREFIX + documentWithoutPrefix.get();
            }
        }

        FieldId res = null;
        String[] splits = id.split(RegExpUtils.escape(DELIMITER));
        for (String split : splits) {
            String[] splitSplits = split.split(repetitionDelimiter);
            Assert.state(splitSplits.length == 2, "Field id \"" + id + "\" is invalid");
            String code = splitSplits[0];
            int repetition = Integer.parseInt(splitSplits[1]);
            if (res == null) {
                res = top(code, repetition);
            } else {
                res = res.sub(code, repetition);
            }
        }

        return Objects.requireNonNull(res, "Field id \"" + id + "\" is invalid");
    }

    @JsonIgnore
    @Override
    public String toString() {
        return value();
    }

    @JsonProperty("value")
    public String value() {
        StringBuilder sb = new StringBuilder();
        if (hasParent()) {
            sb.append(existingParent()).append(FieldTypeId.DELIMITER_CHAR);
        }
        sb.append(code).append(REPETITION_DELIMITER).append(repetition);
        return sb.toString();
    }

    @JsonIgnore
    public int getLevel() {
        if (isRoot()) {
            return LEVEL_TOPFIELD;
        }
        if (existingParent().isRoot()) {
            return LEVEL_SUBFIELD;
        }
        if (existingParent().existingParent().isRoot()) {
            return LEVEL_SUBSUBFIELD;
        }
        throw new IllegalStateException("Max %s nesting levels of fields are supported".formatted(LEVEL_SUBSUBFIELD));
    }

    @JsonIgnore
    public boolean hasParent() {
        return parent != null;
    }

    @JsonIgnore
    public boolean isRoot() {
        return !hasParent();
    }

    @JsonIgnore
    @NonNull
    public FieldId existingParent() {
        return Objects.requireNonNull(parent, () -> "Cannot get a parent of root field id (%s)".formatted(this));
    }

    @JsonIgnore
    public boolean hasParent(@NonNull FieldId fieldId) {
        return existingParent().equals(fieldId);
    }

    @JsonIgnore
    public FieldId getParentOfLevel(int desiredLevel) {
        Assert.state(desiredLevel >= LEVEL_HIGHEST, () -> "Cannot access getParentOfLevel with desired level < %s".formatted(LEVEL_HIGHEST));

        int currentLevel = getLevel();

        if (currentLevel == desiredLevel) {
            return this;
        }

        Assert.state(hasParent(), () -> "Desired level is > %s but this field id is root (has not a parent)".formatted(LEVEL_HIGHEST));

        return existingParent().getParentOfLevel(desiredLevel);
    }

    @JsonIgnore
    public FieldId getOfLevel(int desiredLevel) {
        if (desiredLevel == getLevel()) {
            return this;
        }
        return getParentOfLevel(desiredLevel);
    }

    /**
     * Returns most common parent level. If not any common parent, returns 0.
     */
    public int getGreatestCommonRootLevel(FieldId other) {
        for (int level = LEVEL_HIGHEST; level <= LEVEL_SUBSUBFIELD; level++) {
            FieldId parentOfLevel = getParentOfLevel(level);
            FieldId othersParentOfLevel = other.getParentOfLevel(level);
            if (parentOfLevel.code.equals(othersParentOfLevel.code) && parentOfLevel.repetition == othersParentOfLevel.repetition) {
                continue;
            }
            // nalezli jsme neshodu, spolecny je ten minuly parent
            return level - 1;
        }
        return LEVEL_SUBSUBFIELD;
    }

    @JsonIgnore
    public FieldTypeId toFieldTypeId() {
        if (isRoot()) {
            return FieldTypeId.top(code);
        }
        return existingParent().toFieldTypeId().sub(code);
    }

    @JsonIgnore
    public MultifieldId toMultifieldId() {
        if (isRoot()) {
            return MultifieldId.ofRoot(toFieldTypeId());
        }
        return MultifieldId.ofSub(existingParent(), toFieldTypeId());
    }

    @JsonIgnore
    public List<FieldId> getBottomUpParentChain() {
        List<FieldId> parentChain = new ArrayList<>();
        var curr = parent;
        while (curr != null) {
            parentChain.add(curr);
            curr = curr.parent;
        }
        return parentChain;
    }

    @JsonIgnore
    public List<FieldId> getTopDownParentChain() {
        return ListUtil.toReversed(getBottomUpParentChain());
    }

    @JsonIgnore
    public List<FieldId> asSegments() {
        return ListUtil.createNewListAppending(getTopDownParentChain(), this);
    }

    @JsonIgnore
    public FieldId remapCodes(Function<String, String> codeRemapper) {
        FieldId thisRemapped = withCode(codeRemapper.apply(code));
        if (parent == null) {
            return thisRemapped;
        }
        FieldId remappedParent = parent.remapCodes(codeRemapper);
        return thisRemapped.withParent(remappedParent);
    }

    public FieldId withParent(@NonNull FieldId parent) {
        return new FieldId(parent, code, repetition);
    }

    public FieldId withIndex(int index) {
        return new FieldId(parent, code, index);
    }

    public FieldId withCode(String code) {
        return new FieldId(parent, code, repetition);
    }

}
