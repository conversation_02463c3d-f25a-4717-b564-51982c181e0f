package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.validation.nullablenotempty.NullableNotEmpty;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import jakarta.annotation.Nullable;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
@Getter
public class RecordField {

    @NonNull
    UUID id;

    @NonNull
    RecordFieldId recordFieldId;

    @NonNull
    EditableFieldType<?> fieldType;

    @NonNull
    Integer databaseOrder;

    @NonNull
    Instant lastModificationDate;

    @NonNull
    List<RecordField> subfields;

    @Nullable
    @NullableNotBlank
    @Setter
    @NonFinal
    String textValue;

    @Nullable
    @Setter
    @NonFinal
    RecordIdFondPair recordLink;

    @Nullable
    @NullableNotEmpty
    @Setter
    @NonFinal
    String textSuffix;

    @Nullable
    @Setter
    @NonFinal
    RecordIdFondPair originRecordId;

    @Nullable
    @Setter
    @NonFinal
    RecordFieldEntity sourceEntity;


    public @NonNull Integer getCurrentLevelFieldIndex() {
        return recordFieldId.fieldId().getRepetition();
    }

    public @NonNull FieldId getFieldId() {
        return recordFieldId.fieldId();
    }

    @Override
    public String toString() {
        return "RecordField{" +
                "recordFieldId=" + recordFieldId +
                ", textValue='" + textValue + '\'' +
                ", recordLink=" + recordLink +
                ", subfields=" + subfields +
                ", originRecordId =" + originRecordId +
                ", sourceEntity=" + sourceEntity +
                '}';
    }

}
