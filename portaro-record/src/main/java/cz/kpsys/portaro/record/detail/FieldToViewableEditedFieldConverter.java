package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.record.LabeledRecordRef;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldToViewableEditedFieldConverter implements Converter<FieldContainer, List<ViewableEditedField>> {

    @Override
    public @NonNull List<ViewableEditedField> convert(@NonNull FieldContainer parent) {
        return convert(parent, null);
    }

    private @NonNull List<ViewableEditedField> convert(@NonNull FieldContainer parent, @Nullable String parentIdPath) {
        return parent.streamFields().map(field -> convertToSingleField(field, parentIdPath)).toList();
    }

    private @NonNull ViewableEditedField convertToSingleField(@NonNull Field<?> field, @Nullable String parentIdPath) {
        String idPath = parentIdPath == null
                ? field.getId().toString()
                : parentIdPath + ViewableEditedField.ID_PATH_DELIMITER + field.getId();
        return new ViewableEditedField(
                field.getId(),
                field.getCode(),
                idPath,
                field.getTypeId(),
                field.getFieldTypeText(),
                convert(field, idPath),
                field.getText(),
                field.hasRecordLink() ? LabeledRecordRef.ofRecordLink(field.getExistingRecordLink(), field.getText()) : null,
                field.getValue()
        );
    }

}
