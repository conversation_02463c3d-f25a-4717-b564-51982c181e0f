package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.bool.StringToBooleanConverter;
import cz.kpsys.portaro.collection.ListCutter;
import cz.kpsys.portaro.commons.date.*;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.marcxml.MarcConstants;
import cz.kpsys.portaro.number.StringToBigDecimalConverter;
import cz.kpsys.portaro.record.ReconsCache;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.RecordIdentifier;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import cz.kpsys.portaro.record.detail.value.*;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import jakarta.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;
import org.springframework.core.convert.converter.Converter;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class LegacyRecordFieldsToFieldsConverter implements Function<List<LegacyRecordTopfield<? extends LegacyRecordFieldValue>>, List<? extends Field<?>>> {

    @NonNull AllByIdsLoadable<RecordIdFondPair, UUID> recordFondPairsLoader;
    @NonNull FieldTypesByFondLoader editableFieldTypesByFondLoader;
    @NonNull StringToLocalDateConverter marcXmlSubfieldStringToLocalDateConverter = StringToLocalDateConverter.withoutIsoFallback(MarcConstants.MARCXML_LOCAL_DATE_FORMATTER);
    @NonNull StringToInstantConverter marcXmlSubfieldStringToInstantConverter;
    @NonNull StringToDateRangeConverter marcXmlSubfieldStringToDateRangeConverter;
    @NonNull StringToDatetimeRangeConverter marcXmlSubfieldStringToDatetimeRangeConverter;
    @NonNull StringToBigDecimalConverter marcXmlSubfieldStringToNumberConverter = StringToBigDecimalConverter.INSTANCE;
    @NonNull Converter<@NonNull String, @NonNull Boolean> booleanConverter = StringToBooleanConverter.notAllowingNullSource();

    @Override
    public List<? extends Field<?>> apply(@NonNull List<LegacyRecordTopfield<? extends LegacyRecordFieldValue>> legacyRecordTopfields) {
        // Fill the record-fond-pair cache
        ReconsCache reconsCache = ReconsCache.empty();
        List<UUID> partialRecordIds = collectAllNeededRecordIdsToLoad(legacyRecordTopfields);
        reconsCache.loadAbsents(partialRecordIds, recordFondPairsLoader);

        return ListUtil.convert(legacyRecordTopfields, legacyRecordField -> mapTopfield(legacyRecordField, reconsCache));
    }

    private static @NonNull List<UUID> collectAllNeededRecordIdsToLoad(@NonNull List<LegacyRecordTopfield<? extends LegacyRecordFieldValue>> legacyRecordTopfields) {
        Stream<UUID> recordIds = LegacyRecordFieldable.nestedBrowse(legacyRecordTopfields)
                .map(LegacyRecordFieldable::recordIdentifier)
                .flatMap(RecordIdentifier::streamIds);
        Stream<UUID> referencedRecordIds = LegacyRecordFieldable.nestedBrowse(legacyRecordTopfields)
                .map(LegacyRecordFieldable::value)
                .filter(legacyRecordFieldValue -> legacyRecordFieldValue instanceof LegacyRecordFieldRecordValue)
                .map(LegacyRecordFieldRecordValue.class::cast)
                .map(LegacyRecordFieldRecordValue::recordId);
        return Stream.concat(recordIds, referencedRecordIds)
                .distinct()
                .toList();
    }

    private @NonNull Field<?> mapTopfield(LegacyRecordTopfield<? extends LegacyRecordFieldValue> legacyRecordTopfield, ReconsCache reconsCache) {
        RecordIdFondPair recordIdFondPair = reconsCache.get(legacyRecordTopfield.recordIdentifier());
        FieldTypeId fieldTypeId = FieldTypeId.top(legacyRecordTopfield.topfieldCode());
        RecordFieldId fieldId = RecordFieldId.of(recordIdFondPair, FieldId.top(legacyRecordTopfield.topfieldCode(), legacyRecordTopfield.sameCodeIndex()));
        EditableFieldType<?> fieldType = editableFieldTypesByFondLoader.getTopfieldTypeByFondAndIdOrUnknown(recordIdFondPair.fond(), fieldTypeId);
        return createTopfield(fieldType, fieldId, legacyRecordTopfield, reconsCache);
    }

    private @NonNull List<Field<?>> getSubfields(@NonNull EditableFieldType<?> parentFieldType, @NonNull RecordFieldId parentFieldId, @NonNull LegacyRecordFieldValue legacyRecordFieldValue, @Nullable @NullableNotBlank String ind1Value, @Nullable @NullableNotBlank String ind2Value, @NonNull ReconsCache reconsCache) {
        if (!(legacyRecordFieldValue instanceof LegacyRecordFieldSubfieldsValue subfieldsValue)) {
            return Field.noSubfields();
        }

        ListCutter<LegacyRecordSubfield<? extends LegacyRecordFieldValue>> subfieldsCutter = ListCutter.ofCopyOf(subfieldsValue.subfields());
        List<Field<?>> subfields = new ArrayList<>();

        if (ind1Value != null) {
            subfields.add(createIndSubfield(parentFieldType, FieldTypes.IND_1_FIELD_CODE, ind1Value, parentFieldId));
        }

        if (ind2Value != null) {
            subfields.add(createIndSubfield(parentFieldType, FieldTypes.IND_2_FIELD_CODE, ind2Value, parentFieldId));
        }

        while (subfieldsCutter.hasRemainingItems()) {
            LegacyRecordSubfield<? extends LegacyRecordFieldValue> legacyRecordSubfield = subfieldsCutter.cutFirst();
            EditableFieldType<?> fieldType = parentFieldType.getSubfieldTypeOrParentVirtualGroupTypeFor(legacyRecordSubfield.subfieldCode());
            RecordFieldId fieldId = parentFieldId.toSubfieldId(fieldType.getFieldTypeId(), legacyRecordSubfield.sameCodeIndex());

            if (fieldType.isVirtualGroup()) {
                List<? extends FieldType<?>> groupedFieldTypes = fieldType.getSubfieldTypes();
                List<LegacyRecordSubfield<? extends LegacyRecordFieldValue>> nextSiblingSubfields = subfieldsCutter.cut(subfield -> groupedFieldTypes.stream().anyMatch(groupedFieldType -> subfield.subfieldCode().equals(groupedFieldType.getCode())));
                List<LegacyRecordSubfield<? extends LegacyRecordFieldValue>> allSiblingSubfields = ListUtil.createNewListPrepending(legacyRecordSubfield, nextSiblingSubfields);
                LegacyRecordFieldValue recordValueFromAnySubfield = findRecordValueFromGroupedSubfields(parentFieldId, allSiblingSubfields)
                        .orElseGet(() -> new LegacyRecordFieldSubfieldsValue(allSiblingSubfields)); // falback, pokud je bordel v datech, tzn. pokud je v podpoli misto linku normalni text

                // Virtual group subfield
                subfields.add(createSubfield(fieldType, fieldId, recordValueFromAnySubfield, reconsCache));

            } else { // Ordinary subfield
                subfields.add(createSubfield(fieldType, fieldId, legacyRecordSubfield.value(), reconsCache));
            }
        }

        return subfields;
    }

    private static Optional<LegacyRecordFieldValue> findRecordValueFromGroupedSubfields(@NonNull RecordFieldId parentFieldId, @NotEmpty List<LegacyRecordSubfield<? extends LegacyRecordFieldValue>> siblingSubfields) {
        Optional<LegacyRecordFieldRecordValue> firstRecordValue = siblingSubfields.stream()
                .filter(siblingSubfield -> siblingSubfield.value() instanceof LegacyRecordFieldRecordValue)
                .map(siblingSubfield -> (LegacyRecordFieldRecordValue) siblingSubfield.value())
                .findFirst();
        if (firstRecordValue.isEmpty()) {
            log.warn("There must be at least 1 subfield of {} with record link, but there are only {}", parentFieldId, siblingSubfields);
        }
        return Optional.ofNullable(firstRecordValue.orElse(null)); // prewrapujeme kvuli priblbejm "extends" generikam
    }

    private Field<AcceptableValueFieldValue<LabeledIdentified<?>>> createIndSubfield(@NonNull EditableFieldType<?> parentFieldType, String indFieldCode, @NonNull String indValue, RecordFieldId parentFieldId) {
        EditableFieldType<?> ind1Type = parentFieldType.getSubfieldTypeOrParentVirtualGroupTypeFor(indFieldCode);
        Codebook<? extends LabeledIdentified<?>, String> indCodebook = (Codebook<? extends LabeledIdentified<?>, String>) ind1Type.getCodebook().orElseThrow(() -> new IllegalStateException("Indicator type %s must have codebook, but it doesn't".formatted(ind1Type)));
        LabeledIdentified<?> acceptableValue = indCodebook.getById(indValue);
        RecordFieldId fieldId = parentFieldId.toSubfieldId(ind1Type.getFieldTypeId(), FieldId.FIRST_FIELD_REPETITION);

        return new Field<>(
                UuidGenerator.forIdentifier(),
                fieldId,
                ind1Type,
                Field.NO_LINK,
                Field.NO_SUFFIX,
                AcceptableValueFieldValue.ofSomeIdentified(acceptableValue, resolveOrigin(fieldId)),
                Field.noSubfields()
        );
    }

    private @NonNull Field<?> createTopfield(@NonNull EditableFieldType<?> fieldType, RecordFieldId fieldId, LegacyRecordTopfield<? extends LegacyRecordFieldValue> legacyRecordTopfield, @NonNull ReconsCache reconsCache) {
        return new Field<>(
                UuidGenerator.forIdentifier(),
                fieldId,
                fieldType,
                getRecordLink(legacyRecordTopfield.value(), reconsCache),
                getSuffix(legacyRecordTopfield.value()),
                getValue(fieldType, fieldId, legacyRecordTopfield.value()),
                getSubfields(fieldType, fieldId, legacyRecordTopfield.value(), legacyRecordTopfield.ind1(), legacyRecordTopfield.ind2(), reconsCache)
        );
    }

    private @NonNull Field<?> createSubfield(@NonNull EditableFieldType<?> fieldType, @NonNull RecordFieldId fieldId, @NonNull LegacyRecordFieldValue legacyRecordFieldValue, @NonNull ReconsCache reconsCache) {
        return new Field<>(
                UuidGenerator.forIdentifier(),
                fieldId,
                fieldType,
                getRecordLink(legacyRecordFieldValue, reconsCache),
                getSuffix(legacyRecordFieldValue),
                getValue(fieldType, fieldId, legacyRecordFieldValue),
                getSubfields(fieldType, fieldId, legacyRecordFieldValue, null, null, reconsCache)
        );
    }

    private @Nullable ScalarFieldValue<?> getValue(@NonNull EditableFieldType<?> fieldType, @NonNull RecordFieldId fieldId, @NonNull LegacyRecordFieldValue legacyRecordFieldValue) {
        if (!(legacyRecordFieldValue instanceof LegacyRecordFieldSimpleValue(String content))) {
            return Field.noValue();
        }

        Set<RecordIdFondPair> origin = resolveOrigin(fieldId);

        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.DATE)) {
            LocalDate value = marcXmlSubfieldStringToLocalDateConverter.convert(content);
            return LocalDateFieldValue.of(value, origin);
        }
        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.DATETIME)) {
            Instant value = marcXmlSubfieldStringToInstantConverter.convert(content);
            return InstantFieldValue.of(value, origin);
        }
        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.DATE_RANGE)) {
            DateRange value = marcXmlSubfieldStringToDateRangeConverter.convert(content);
            return DateRangeFieldValue.of(value, origin);
        }
        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.DATETIME_RANGE)) {
            DatetimeRange value = marcXmlSubfieldStringToDatetimeRangeConverter.convert(content);
            return DatetimeRangeFieldValue.of(value, origin);
        }
        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.BOOLEAN)) {
            Boolean value = Objects.requireNonNull(booleanConverter.convert(content));
            return BooleanFieldValue.of(value, origin);
        }
        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER) || fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER_DECIMAL_2) || fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER_DECIMAL_4)) {
            BigDecimal value = marcXmlSubfieldStringToNumberConverter.convert(content);
            return NumberFieldValue.of(value, origin);
        }
        if (fieldType.getDatatypeOrThrow().equals(CoreConstants.Datatype.NUMBER_DECIMAL_6)) {
            throw new UnsupportedOperationException("Number with precision 6 is not supported");
        }
        if (fieldType.getCodebook().isPresent()) {
            Codebook<? extends LabeledIdentified<?>, String> codebook = (Codebook<? extends LabeledIdentified<?>, String>) fieldType.getCodebook().get();
            LabeledIdentified<?> acceptableValue = codebook.getById(content);
            return AcceptableValueFieldValue.ofSomeIdentified(acceptableValue, origin);
        }
        return StringFieldValue.of(content, origin);
    }

    private @Nullable RecordIdFondPair getRecordLink(@NonNull LegacyRecordFieldValue legacyRecordFieldValue, @NonNull ReconsCache reconsCache) {
        if (!(legacyRecordFieldValue instanceof LegacyRecordFieldRecordValue recordValue)) {
            return Field.NO_LINK;
        }
        return Objects.requireNonNull(reconsCache.get(recordValue.recordId()));
    }

    private @Nullable String getSuffix(@NonNull LegacyRecordFieldValue legacyRecordFieldValue) {
        if (!(legacyRecordFieldValue instanceof LegacyRecordFieldRecordValue recordValue)) {
            return Field.NO_SUFFIX;
        }
        return recordValue.suffix();
    }

    private @NonNull Set<RecordIdFondPair> resolveOrigin(@NonNull RecordFieldId fieldId) {
        return Set.of(fieldId.recordIdFondPair());
    }

}
