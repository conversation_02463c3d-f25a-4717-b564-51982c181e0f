package cz.kpsys.portaro.record.detail;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Collection;
import java.util.Set;
import java.util.function.Predicate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldCodeFilter<E extends WithCode> implements Predicate<E> {

    @NonNull Collection<String> acceptableCodes;

    public FieldCodeFilter(@NonNull String acceptableCode) {
        this.acceptableCodes = Set.of(acceptableCode);
    }

    public static <E extends Field<?>> FieldCodeFilter<E> of(@NonNull String... acceptableCodes) {
        return new FieldCodeFilter<E>(Set.of(acceptableCodes));
    }

    @Override
    public boolean test(E f) {
        return f != null && acceptableCodes.contains(f.getCode());
    }

}
