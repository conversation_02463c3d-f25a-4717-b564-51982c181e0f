package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.LoadedFile;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.file.RecordWithAttachmentSaveCommand;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class BatchCoverImporter {

    public static final String STATE_RUNNING = "Running";
    public static final String STATE_STOPPED_BLOCKED = "StoppedBecauseOfBlocking";
    public static final String STATE_STOPPED_ALL_DOWNLOADED = "StoppedBecauseOfAllCoversDownloaded";
    public static final String STATE_STOPPED = "Stopped";
    public static final String STATE_STOPPED_ERROR = "StoppedBecauseOfUnknownError";

    @NonNull NamedCoverLoader coverLoader;
    @NonNull Saver<RecordWithAttachmentSaveCommand, ?> recordWithAttachmentSaver;
    @NonNull UnfoundCoverSaver unfoundCoverSaver;
    @NonNull TransactionTemplate transactionTemplate;

    @Getter
    @NonNull
    @NonFinal
    String state = STATE_STOPPED;

    @Getter
    @NonFinal
    Date lastSomeCoversSearchStartTime;

    @Getter
    @NonFinal
    Date lastSomeCoversSearchStopTime;

    @Getter
    @NonFinal
    int numOfSearchedCoversLastTime;

    @Getter
    @NonFinal
    int numOfFoundedCoversLastTime;

    public String getServiceName() {
        return coverLoader.getServiceName();
    }

    public void run(@NonNull Provider<List<Record>> recordsWithoutCoverProvider,
                    @NonNull Provider<Boolean> canContinueProvider,
                    @NonNull Department ctx,
                    @NonNull UserAuthentication currentAuth) {
        List<Record> recordsToDownload;
        state = STATE_RUNNING;
        numOfSearchedCoversLastTime = 0;
        numOfFoundedCoversLastTime = 0;

        try {

            while (!(recordsToDownload = recordsWithoutCoverProvider.get()).isEmpty()) {
                for (Record record : recordsToDownload) {

                    if (!canContinueProvider.get()) {
                        log.info("Stopping cover searching by {}", getServiceName());
                        stop(STATE_STOPPED);
                        return;
                    }

                    log.debug("Loading cover of record {} by {}", record.getId(), getServiceName());

                    try {
                        if (numOfSearchedCoversLastTime == 0) {
                            lastSomeCoversSearchStartTime = new Date();
                        }
                        numOfSearchedCoversLastTime++;
                        LoadedFile cover = coverLoader.getCover(record, ctx);
                        numOfFoundedCoversLastTime++;

                        log.info("Successfully found cover of {} ({}), saving it.", record, cover.getSource());
                        transactionTemplate.executeWithoutResult(_ ->
                            recordWithAttachmentSaver.save(RecordWithAttachmentSaveCommand.ofLoadedCover(record, cover, currentAuth, ctx))
                        );

                    } catch (CoverSearchException e) {
                        if (e.getReason().equals(CoverSearchException.DOCASNE_ZABLOKOVANO)) {
                            log.info("zastavuji stahovani kvuli zablokovani ve sluzbe {}", getServiceName());
                            stop(STATE_STOPPED_BLOCKED);
                            return;
                        }
                        unfoundCoverSaver.save(record, e.getService(), e.getReason());
                    }

                }
            }
            log.info("Finished cover searching by {} (no covers to download)", getServiceName());
            stop(STATE_STOPPED_ALL_DOWNLOADED);


        } catch (Exception e) {
            stop(STATE_STOPPED_ERROR);
            log.error("Neznama chyba pri stahovani obalek ve sluzbe {}", getServiceName(), e);
        }

    }

    private void stop(String state) {
        this.state = state;
        if (numOfSearchedCoversLastTime > 0) {
            this.lastSomeCoversSearchStopTime = new Date();
        }
    }

}
