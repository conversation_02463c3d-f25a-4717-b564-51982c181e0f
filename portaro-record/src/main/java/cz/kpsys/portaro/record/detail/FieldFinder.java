package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.commons.object.repo.MoreThanOneItemFoundException;
import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Stream;

public interface FieldFinder<CONT, F> {

    Stream<F> findIn(CONT fieldContainer);

    default @NonNull Stream<F> findNotEmptyIn(CONT fieldContainer) {
        return ListUtil.nonEmptyStream(findIn(fieldContainer), () -> new MissingFieldException(this, fieldContainer));
    }

    default Optional<F> findFirstIn(CONT fieldContainer) {
        return findIn(fieldContainer).findFirst();
    }

    default <R> @NonNull Optional<R> findFirstIn(CONT fieldContainer, Function<? super F, ? extends Optional<R>> extractor) {
        return findFirstIn(fieldContainer).flatMap(extractor);
    }

    default @NonNull F getFirstIn(CONT fieldContainer) {
        return findFirstIn(fieldContainer).orElseThrow(() -> new MissingFieldException(this, fieldContainer));
    }

    default <R> @NonNull R getFirstIn(CONT fieldContainer, Function<? super F, ? extends Optional<R>> extractor) {
        return findFirstIn(fieldContainer, extractor).orElseThrow(() -> new MissingFieldException(this, fieldContainer));
    }

    default Optional<F> findSingleIn(CONT fieldContainer) {
        List<F> maxTwoItems = findIn(fieldContainer).limit(2).toList();
        MoreThanOneItemFoundException.check(maxTwoItems.size(), "field", this, "Required zero or one field matching %s, but more found in %s".formatted(this, fieldContainer));
        return ListUtil.firstOrEmptyOptional(maxTwoItems);
    }

    default @NonNull F getSingleIn(CONT fieldContainer) {
        List<F> maxTwoItems = findNotEmptyIn(fieldContainer).limit(2).toList();
        MoreThanOneItemFoundException.check(maxTwoItems.size(), "field", this, "Required exactly one field matching %s, but more found in %s".formatted(this, fieldContainer));
        return DataUtils.requireSingle(maxTwoItems, "field", this);
    }

}
