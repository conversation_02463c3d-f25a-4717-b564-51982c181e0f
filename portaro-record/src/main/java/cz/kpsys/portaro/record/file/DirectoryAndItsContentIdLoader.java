package cz.kpsys.portaro.record.file;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.databasestructure.RecordDb;
import cz.kpsys.portaro.file.directory.DirectoryLoader;
import cz.kpsys.portaro.file.directory.ParentableDirectory;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DirectoryAndItsContentIdLoader {

    @NonNull NamedParameterJdbcOperations template;
    @NonNull TransactionTemplate transactionTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull DirectoryLoader directoryLoader;


    public List<ParentableDirectory> getDirectoryIdAndContentIds(UUID recordId) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(RecordDb.RECORD.DIRECTORY_ID)
                .from(RecordDb.RECORD.TABLE);
        sq.where()
                .eq(RecordDb.RECORD.ID, recordId);

        Integer directoryId = transactionTemplate.execute(_ ->
                ListUtil.firstOrNull(template.queryForList(sq.getSql(), sq.getParamMap(), Integer.class))
        );
        if (directoryId == null) {
            return List.of();
        }

        List<ParentableDirectory> allDirs = new ArrayList<>(directoryLoader.getAllDescendantsByParentAsList(directoryId));
        allDirs.add(directoryLoader.getById(directoryId));

        return allDirs;
    }
}
