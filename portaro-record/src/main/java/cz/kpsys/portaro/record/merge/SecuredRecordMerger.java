package cz.kpsys.portaro.record.merge;

import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordSecurityActions;
import cz.kpsys.portaro.security.SecurityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SecuredRecordMerger implements RecordMerger {

    @NonNull SecurityManager securityManager;
    @NonNull RecordMerger delegate;

    @Override
    public void merge(@NonNull RecordMergeCommand command) {
        securityManager.throwIfCannot(RecordSecurityActions.RECORD_MERGE_TO, command.currentAuth(), command.currentDepartment(), command.targetRecord());
        for (Record sourceRecord : command.sourceRecords()) {
            securityManager.throwIfCannot(RecordSecurityActions.RECORD_MERGE_FROM, command.currentAuth(), command.currentDepartment(), sourceRecord);
        }

        delegate.merge(command);
    }
}
