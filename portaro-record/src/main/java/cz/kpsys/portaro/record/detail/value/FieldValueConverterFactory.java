package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.util.Objects;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldValueConverterFactory {

    @NonNull FieldAcceptableValuesResolver fieldAcceptableValuesResolver;
    @NonNull ByIdLoadable<IndicatorType, FieldTypeId> indicatorTypeLoader;
    @NonNull RecordEntryFieldTypeIdResolver entryFieldTypeIdResolver;


    public FieldValueConverter createForSimpleSubfield(FieldType<?> fieldType) {
        if (FieldTypes.INDICATORS_FIELD_CODES.contains(fieldType.getCode())) {
            return createForIndicator(fieldType.getFieldTypeId());
        }

        DefaultFieldValueConverter fieldValueConverter = createDefaultForSimpleSubfield();

        Optional<Codebook<? extends LabeledIdentified<?>, ?>> acceptableValues = fieldAcceptableValuesResolver.resolve(fieldType.getDatatypeOrThrow());
        if (acceptableValues.isPresent()) {
            Assert.state(Identified.class.isAssignableFrom(fieldType.getDatatypeOrThrow().getJavaType()), () -> "Acceptable values are only supported for Identified<?> type, but actual datatype of " + fieldType.getDatatypeOrThrow() + " in " + fieldType.getId() + " is " + fieldType.getDatatypeOrThrow().getJavaType().getSimpleName());
            Class<?> idType = Objects.requireNonNull(fieldType.getDatatypeOrThrow().getJavaIdType(), () -> "Acceptable values id type is not defined for field type " + fieldType.getId());
            if (idType == String.class) {
                ByIdLoadable<? extends LabeledIdentified<String>, String> valuesLoader = (ByIdLoadable<? extends LabeledIdentified<String>, String>) acceptableValues.get();
                fieldValueConverter.register(StringValueCommand.class, new AcceptableValueTypedValueConverter(valuesLoader));
            } else if (idType == Integer.class) {
                ByIdLoadable<? extends LabeledIdentified<Integer>, Integer> valuesLoader = (ByIdLoadable<? extends LabeledIdentified<Integer>, Integer>) acceptableValues.get();
                fieldValueConverter.register(NumberValueCommand.class, new NumericAcceptableValueTypedValueConverter(valuesLoader));
            } else {
                throw new UnsupportedOperationException("Unsupported acceptable values id type " + idType.getSimpleName() + " for field type " + fieldType.getId());
            }
        }

        fieldType.getLinkRootFond()
                .ifPresent(_ -> fieldValueConverter.register(DetailedRecordValueCommand.class, new ComplexDetailedRecordValueCommandConverter(entryFieldTypeIdResolver)));

        return fieldValueConverter;
    }

    public FieldValueConverter createForIndicator(@NonNull FieldTypeId fieldTypeId) {
        IndicatorType indicatorType = indicatorTypeLoader.getById(fieldTypeId);
        return new DefaultFieldValueConverter()
                .register(StringValueCommand.class, new AcceptableValueTypedValueConverter(indicatorType.toFallbackingCodebook()));
    }

    public static FieldValueConverter createDefaultForControlfield() {
        return new DefaultFieldValueConverter()
                .register(StringValueCommand.class, new SimpleStringTypedValueConverter())
                .register(NumberValueCommand.class, new NumberTypedValueConverter())
                .register(BooleanValueCommand.class, new BooleanTypedValueConverter())
                .register(DateValueCommand.class, new LocalDateTypedValueConverter())
                .register(InstantValueCommand.class, new InstantTypedValueConverter())
                .register(DateRangeValueCommand.class, new DateRangeTypedValueConverter())
                .register(DatetimeRangeValueCommand.class, new DatetimeRangeTypedValueConverter());
    }

    public static FieldValueConverter createDefaultForDatafield() {
        return new DefaultFieldValueConverter();
    }

    public static DefaultFieldValueConverter createDefaultForSimpleSubfield() {
        return new DefaultFieldValueConverter()
                .register(StringValueCommand.class, new SimpleStringTypedValueConverter())
                .register(NumberValueCommand.class, new NumberTypedValueConverter())
                .register(BooleanValueCommand.class, new BooleanTypedValueConverter())
                .register(DateValueCommand.class, new LocalDateTypedValueConverter())
                .register(InstantValueCommand.class, new InstantTypedValueConverter())
                .register(DateRangeValueCommand.class, new DateRangeTypedValueConverter())
                .register(DatetimeRangeValueCommand.class, new DatetimeRangeTypedValueConverter())
                .register(MultilabeledPredefinedRecordValueCommand.class, new MultilabeledPredefinedRecordValueCommandConverter())
                .register(MultilabeledPredefinedMissingRecordValueCommand.class, new MultilabeledPredefinedMissingRecordValueCommandConverter())
                .register(SinglelabeledPredefinedRecordValueCommand.class, new SinglelabeledPredefinedRecordValueCommandConverter())
                .register(SinglelabeledPredefinedMissingRecordValueCommand.class, new SinglelabeledPredefinedMissingRecordValueCommandConverter());
    }

    public static DefaultFieldValueConverter createDefaultForIndicator(FieldTypeId fieldTypeId) {
        return new DefaultFieldValueConverter()
                .register(StringValueCommand.class, new AcceptableValueTypedValueConverter(IndicatorType.unknownIndicatorFactory(fieldTypeId)));
    }

}
