package cz.kpsys.portaro.record.document;

import cz.kpsys.portaro.record.Record;

import java.util.Optional;
import java.util.function.Function;

public class ByFondIdDocumentContentTypeResolver implements Function<Record, Optional<ContentType>> {


    @Override
    public Optional<ContentType> apply(Record r) {
        return switch (r.getFond().getId()) {
            case 1 -> Optional.of(ContentType.BOOK); //BOOK
            case 2 -> Optional.of(ContentType.MAP); //MAP
            case 3 -> Optional.of(ContentType.PERIODICAL); //JFULL (Full Journal)
            case 8 -> Optional.of(ContentType.AUDIO); //SOUND
            case 9 -> Optional.of(ContentType.REPORT); //vyzkumne zpravy - RPRT
            case 11 -> Optional.of(ContentType.ARTICLE); //JOUR
            case 13 -> Optional.of(ContentType.VIDEO);
            default -> Optional.empty(); //ADVS
        };
    }

}
