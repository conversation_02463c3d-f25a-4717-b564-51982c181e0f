package cz.kpsys.portaro.record.print;

import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.object.Labeled;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.detail.ViewableFieldContainer;
import cz.kpsys.portaro.record.detail.ViewableFieldable;
import lombok.NonNull;

import java.util.Locale;

public class RecordDetailUserFriendlyTextPrinter extends RecordDetailAbstractPrinter {

    public RecordDetailUserFriendlyTextPrinter(@NonNull Translator<Department> translator, @NonNull Department currentDepartment, @NonNull Locale locale) {
        super(translator, currentDepartment, locale);
    }

    /**
     * Slouzi k uprave vypisovaneho textu podpoli.
     * Ve vychozim stavu vypisuje valfield.getText().
     * <br/>
     * Metoda je pro eventuelni prepsani.
     */
    protected String getFieldText(Labeled field) {
        return localize(field.getText());
    }



    /* IMPLEMENTACE HLAVNICH METOD */

    @Override
    public String writeRecordLinkSubfield(ViewableFieldable sf) {
        return getFieldText(sf);
    }

    @Override
    public String writeUrlSubfield(@NonNull ViewableFieldable sf, @NonNull ViewableFieldContainer originalDetail) {
        return getFieldText(sf);
    }

    @Override
    public String writeNormalField(ViewableFieldable sf, @NonNull ViewableFieldContainer originalDetail) {
        return getFieldText(sf);
    }

}
