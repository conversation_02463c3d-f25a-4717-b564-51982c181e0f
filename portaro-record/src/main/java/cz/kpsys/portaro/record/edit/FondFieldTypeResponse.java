package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.NamedLabeledIdentifiedRecord;
import lombok.NonNull;
import jakarta.annotation.Nullable;

import java.util.List;

public record FondFieldTypeResponse(
        @NonNull String code,
        @Nullable String editorType,
        @NonNull Boolean repeatable,
        @NonNull List<FondFieldTypeResponse> subfieldTypes,
        @NonNull Boolean entryElement,
        @NonNull Boolean required,
        // Props from LabeledNamedIdentifiedRecord
        @NonNull String id,
        @NonNull String name,
        @NonNull Text text
) implements NamedLabeledIdentifiedRecord<String> {}