package cz.kpsys.portaro.record.edit.authorityfield008;

import lombok.NonNull;

import java.io.Serializable;

public record AuthorityField008Definitions(
        @NonNull AuthorityField008Position directOrIndirectGeographicSubdivision,
        @NonNull AuthorityField008Position romanizationScheme,
        @NonNull AuthorityField008Position languageOfCatalog,
        @NonNull AuthorityField008Position kindOfRecord,
        @NonNull AuthorityField008Position descriptiveCatalogingRules,
        @NonNull AuthorityField008Position subjectHeadingSystemThesaurus,
        @NonNull AuthorityField008Position typeOfSeries,
        @NonNull AuthorityField008Position numberedOrUnnumberedSeries,
        @NonNull AuthorityField008Position headingUseMainOrAddedEntry,
        @NonNull AuthorityField008Position headingUseSubjectAddedEntry,
        @NonNull AuthorityField008Position headingUseSeriesAddedEntry,
        @NonNull AuthorityField008Position typeOfSubjectSubdivision,
        @NonNull AuthorityField008Position typeOfGovernmentAgency,
        @NonNull AuthorityField008Position referenceEvaluation,
        @NonNull AuthorityField008Position recordUpdateInProcess,
        @NonNull AuthorityField008Position undifferentiatedPersonalName,
        @NonNull AuthorityField008Position levelOfEstablishment,
        @NonNull AuthorityField008Position modifiedRecord,
        @NonNull AuthorityField008Position catalogingSource

) implements Serializable {}
