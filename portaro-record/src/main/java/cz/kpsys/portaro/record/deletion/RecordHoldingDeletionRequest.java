package cz.kpsys.portaro.record.deletion;

import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.valueeditor.bool.BooleanValueEditor;
import cz.kpsys.portaro.form.valueeditor.bool.BooleanValueEditorModifier;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.validation.AfterIntegrityValidationViolation;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ClientsideRequiredValidation;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorModifier;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.bool.BooleanEditor;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.holding.RecordHolding;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.With;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;
import org.springframework.lang.Nullable;

import java.util.UUID;

@Form(id = "recordHoldingDeletion", title = "{commons.ReallyDelete}")
@FormSubmit(path = "/api/record-holdings/delete")
@AfterIntegrityValidationViolation(bean = "recordHoldingDeletionRequestDefaulter")
@RecordHoldingDeletionValid
@With
@FieldNameConstants
public record RecordHoldingDeletionRequest(

        @NotNull
        RecordHolding recordHolding,

        @FormPropertyLabel("{recordHoldingDeletion.acceptingExemplarsDeletion}")
        @ValueEditorModifier("recordHoldingDeletionAcceptingExemplarsDeletionEditorModifier")
        @BooleanEditor
        @RecordHoldingDeletionValid.PotentiallyRequired
        @ClientsideRequiredValidation
        @Nullable
        @AssertTrue
        Boolean acceptingExemplarsDeletion,

        @Nullable
        @AssertTrue
        Boolean acceptingToDeletableExemplarStatusChange

) {

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static final class RecordHoldingDeletionAcceptingExemplarsDeletionEditorModifier implements BooleanValueEditorModifier<RecordHoldingDeletionRequest> {

        @NonNull IdAndIdsLoadable<Record, UUID> recordLoader;
        @NonNull ContextualFunction<Record, Department, Boolean> singleNodeDepartmentedRecordHasExemplarsPredicate;

        @Override
        public BooleanValueEditor modify(BooleanValueEditor editor, RecordHoldingDeletionRequest formObject, Department ctx) {
            Record record = recordLoader.getById(formObject.recordHolding().getRecordId());
            Boolean recordHasExemplars = singleNodeDepartmentedRecordHasExemplarsPredicate.getOn(record, formObject.recordHolding().getDepartment());
            return editor.withVisible(recordHasExemplars);
        }
    }

}
