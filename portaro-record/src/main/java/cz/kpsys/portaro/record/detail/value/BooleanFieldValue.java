package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.Field;
import lombok.NonNull;

import java.util.Optional;
import java.util.Set;

public record BooleanFieldValue(

    @NonNull
    Boolean value,

    @NonNull
    String label,

    @NonNull
    Text text,

    @NonNull
    Set<RecordIdFondPair> origins

) implements ScalarFieldValue<Boolean> {

    public static BooleanFieldValue of(@NonNull Boolean value, @NonNull Set<RecordIdFondPair> origins) {
        return new BooleanFieldValue(value, value.toString(), Texts.ofBoolean(value), origins);
    }

    public static Optional<Boolean> extract(@NonNull Field<BooleanFieldValue> field) {
        return field.typedValue(BooleanFieldValue.class).map(BooleanFieldValue::value);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        return o instanceof BooleanFieldValue that && value.equals(that.value);
    }

    @Override
    public int hashCode() {
        return value().hashCode();
    }

}
