package cz.kpsys.portaro.record.detail.dflt;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.IdentifiedValue;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedActionResponse;
import cz.kpsys.portaro.department.DepartmentResponse;
import cz.kpsys.portaro.formannotation.annotations.form.ValidFormObject;
import cz.kpsys.portaro.record.RecordWebConstants;
import cz.kpsys.portaro.record.fond.SimpleFondResponse;
import cz.kpsys.portaro.web.GenericApiController;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RequestMapping(RecordWebConstants.API_PATH)
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class DefaultFieldValueApiController extends GenericApiController {

    @NonNull AllValuesProvider<IdentifiedValue<DefaultFieldValueId, @NotBlank String>> defaultFieldValueLoader;
    @NonNull Saver<DefaultFieldValueSaveCommand, ?> defaultFieldValueSaver;

    @GetMapping
    public List<DefaultFieldValueResponse> getAll() {
        return ListUtil.convert(defaultFieldValueLoader.getAll(), DefaultFieldValueApiController::mapDefaultFieldValueToResponse);
    }

    @PostMapping
    public ActionResponse save(@RequestBody @ValidFormObject DefaultFieldValueSaveRequest saveRequest,
                               UserAuthentication currentAuth) {
        defaultFieldValueSaver.save(mapRequestToDefaultFieldValue(saveRequest));
        return FinishedActionResponse.ok();
    }

    @NonNull
    private static DefaultFieldValueResponse mapDefaultFieldValueToResponse(IdentifiedValue<DefaultFieldValueId, @NotBlank String> source) {
        return new DefaultFieldValueResponse(
                source.id().fieldTypeId(),
                new DepartmentResponse(
                        source.id().department().getId(),
                        source.id().department().getName(),
                        source.id().department().getText()
                ),
                source.id().fond().map(SimpleFondResponse::mapFromFond).orElse(null),
                source.value()
        );
    }

    @NonNull
    private static DefaultFieldValueSaveCommand mapRequestToDefaultFieldValue(DefaultFieldValueSaveRequest saveRequest) {
        return new DefaultFieldValueSaveCommand(
                saveRequest.fieldTypeId(),
                saveRequest.department(),
                Optional.ofNullable(saveRequest.fond()),
                saveRequest.value()
        );
    }

}
