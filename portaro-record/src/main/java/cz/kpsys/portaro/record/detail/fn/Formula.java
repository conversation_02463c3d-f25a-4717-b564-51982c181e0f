package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.value.FieldValue;
import lombok.NonNull;

import java.util.Set;
import java.util.stream.Stream;

public sealed interface Formula<RES extends FieldValue<?>> permits BackRef, Ref, ResolvableFunction {

    @NonNull
    Set<LookupDefinition> dependencies();

    @NonNull
    Stream<? extends Formula<?>> subformulas();
}
