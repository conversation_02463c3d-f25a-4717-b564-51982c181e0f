package cz.kpsys.portaro.record.authority;

import cz.kpsys.portaro.commons.object.HierarchyTree;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordDescriptor;
import lombok.NonNull;

import java.util.List;
import java.util.UUID;

public interface RecordHierarchyLoader {

    List<HierarchyTree<UUID, RecordHierarchyTreeNode>> getHierarchyTrees(@NonNull UUID recordId);

    List<RecordDescriptor> getSubauthorities(@NonNull Record a);

    List<RecordDescriptor> getSeeAlsos(@NonNull Record a);

    List<RecordDescriptor> getSees(@NonNull Record a);
}
