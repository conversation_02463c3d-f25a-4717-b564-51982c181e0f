package cz.kpsys.portaro.record.list;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class RecordIdStorageImpl implements RecordIdStorage, Serializable {

    private List<UUID> recordIds = new ArrayList<>();

    @Override
    public void add(UUID recordId) {
        if (!recordIds.contains(recordId)) {
            recordIds.add(recordId);
        }
    }

    @Override
    public void addAll(List<UUID> recordIdList) {
        recordIds.addAll(recordIdList);
    }

    @Override
    public void remove(UUID recordId) {
        recordIds.remove(recordId);
    }

    @Override
    public void clear() {
        recordIds.clear();
    }

    @Override
    public boolean contains(UUID recordId) {
        return recordIds.contains(recordId);
    }

    @Override
    public boolean isEmpty() {
        return recordIds.isEmpty();
    }

    @Override
    public int getSize() {
        return recordIds.size();
    }

    @Override
    public List<UUID> getRecordIds() {
        return recordIds;
    }

    @Override
    public void setRecordIds(List<UUID> recordIds) {
        this.recordIds = recordIds;
    }


}
