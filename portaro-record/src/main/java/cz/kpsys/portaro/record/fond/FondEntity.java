package cz.kpsys.portaro.record.fond;

import cz.kpsys.portaro.commons.object.OrderedNamedIdentified;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import jakarta.validation.constraints.Min;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import static cz.kpsys.portaro.databasestructure.FondDb.DEF_FOND.*;

@Entity
@Table(name = TABLE)
@AttributeOverrides({
        @AttributeOverride(name = "id", column = @Column(name = ID_FOND)),
        @AttributeOverride(name = "name", column = @Column(name = NAZEV)),
        @AttributeOverride(name = "order", column = @Column(name = PORADI))
})
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Getter
public class FondEntity extends OrderedNamedIdentified<Integer> {

    @Column(name = PARENT_ID)
    @Min(1)
    @Nullable
    Integer parentId;

    @Column(name = JE_AUTORITNI)
    boolean forAuthority;

    @Column(name = JE_POVOL)
    boolean enabled;

    @Column(name = JE_PERIO)
    boolean periodical;

    @Column(name = RECORDABLE)
    boolean recordable;

    @Column(name = FOND_TYPE_ID)
    String fondTypeId;

    @Column(name = BINDING)
    @Nullable
    @NullableNotBlank
    String binding;

    @Column(name = KATEG_DOK)
    String documentCategories;

    @Column(name = TYP_DOK)
    String documentTypes;

    @Column(name = TYP_ZAZ)
    String recordType;

    @Column(name = BIB_UROV)
    String bibliographicLevel;

    @Column(name = JE_EXEMP)
    boolean withExemplars;

    @Column(name = ENTRY_FIELD_TYPE_ID)
    @Nullable
    @NullableNotBlank
    String entryFieldTypeId;

    @Column(name = JE_NEWEDIT)
    boolean editable;

    @Column(name = Z39_TYP)
    Integer z39Typ;

    @Column(name = JE_ZDROJ)
    boolean forSourceDocument;

    @Column(name = ADOPTING_LINKED)
    boolean adoptingLinked;

    @Column(name = ADOPTING_EDITED)
    boolean adoptingEdited;

    public FondEntity(Integer id,
                      String name,
                      @Nullable Integer parentId,
                      int order,
                      boolean forAuthority,
                      boolean enabled,
                      boolean periodical,
                      boolean recordable,
                      @NonNull String fondTypeId,
                      @Nullable String binding,
                      String documentCategories,
                      String documentTypes,
                      String recordType,
                      String bibliographicLevel,
                      boolean withExemplars,
                      @Nullable @NullableNotBlank String entryFieldTypeId,
                      boolean editable,
                      Integer z39Typ,
                      Boolean forSourceDocument,
                      boolean adoptingLinked,
                      boolean adoptingEdited) {
        super(id, name, order);
        this.parentId = parentId;
        this.forAuthority = forAuthority;
        this.enabled = enabled;
        this.periodical = periodical;
        this.recordable = recordable;
        this.fondTypeId = fondTypeId;
        this.binding = binding;
        this.documentCategories = documentCategories;
        this.documentTypes = documentTypes;
        this.recordType = recordType;
        this.bibliographicLevel = bibliographicLevel;
        this.withExemplars = withExemplars;
        this.entryFieldTypeId = entryFieldTypeId;
        this.editable = editable;
        this.z39Typ = z39Typ;
        this.forSourceDocument = forSourceDocument;
        this.adoptingLinked = adoptingLinked;
        this.adoptingEdited = adoptingEdited;
    }

}
