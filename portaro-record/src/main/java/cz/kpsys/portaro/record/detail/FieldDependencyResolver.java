package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.record.detail.link.LinkedFieldSpec;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import cz.kpsys.portaro.record.detail.spec.RecordSpec;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldDependencyResolver {

    @NonNull RecordEntryFieldTypeIdResolver entryFieldTypeIdResolver;

    @JsonIgnore
    public Stream<RecordFieldId> streamFieldInfluencers(@NonNull Field<?> field) {
        return field.getType().getLookups().stream()
                .<RecordFieldId>mapMulti((lookupDef, consumer) -> {
                    // influencer je jednak pole s linkem
                    RecordFieldId linkFieldId = lookupDef.linkFieldSpec().toLinkFieldSpecOnSpecifiedField(field.getRecordFieldId());
                    consumer.accept(linkFieldId);

                    // druhak, pokud link je primo v mem poli, zname ho, takze muzeme pridat i linkovane pole
                    Optional<RecordFieldId> linkedFieldId = getLinkedFieldId(field, linkFieldId, lookupDef.linkedFieldSpec());
                    if (linkedFieldId.isPresent()) {
                        consumer.accept(linkedFieldId.get());
                    }
                })
                .filter(influencerFullId -> !field.getRecordFieldId().equals(influencerFullId));
    }

    @JsonIgnore
    public boolean isFieldFollowerOf(@NonNull Field<?> field, @NonNull RecordFieldId refInfluencer) {
        return streamFieldInfluencers(field).anyMatch(influencer -> influencer.equals(refInfluencer));
    }

    /// pokud link je primo v mem poli (a link v nem opravdu mame), zname ho, takze muzeme pridat i linkovane pole
    private @NonNull Optional<RecordFieldId> getLinkedFieldId(@NonNull Field<?> field, @NonNull RecordFieldId linkFieldId, @NonNull LinkedFieldSpec linkedFieldSpec) {
        if (!field.hasRecordLink()) {
            return Optional.empty();
        }
        if (!linkFieldId.equals(field.getRecordFieldId())) {
            return Optional.empty();
        }
        FieldTypeId linkedFieldType = entryFieldTypeIdResolver.getLinkedFieldType(field.getExistingRecordLink().fond(), linkedFieldSpec);
        RecordFieldId linkedFieldId = RecordSpec.ofRecordAndField(field.getExistingRecordLink(), linkedFieldType.toFieldIdWithAllFirstIndices());
        return Optional.of(linkedFieldId);
    }

}
