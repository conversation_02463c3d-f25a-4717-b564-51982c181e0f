package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.Labeled;
import cz.kpsys.portaro.record.LabeledRecordRef;
import lombok.NonNull;
import org.springframework.lang.Nullable;

public interface ViewableFieldable extends WithFieldType, FieldLike, Labeled {

    @NonNull
    FieldId getFieldId();

    @JsonIgnore
    @NonNull
    FieldTypeId fieldTypeId();

    @JsonIgnore
    @NonNull
    FieldType<?> getType();

    Text getFieldTypeText();

    @Nullable
    String getRaw();

    @JsonIgnore
    default boolean hasRecordLink() {
        return getRecordLink() != null;
    }

    @Nullable
    LabeledRecordRef getRecordLink();

    boolean isUrl();

}
