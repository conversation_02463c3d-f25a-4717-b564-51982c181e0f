package cz.kpsys.portaro.record.detail.convert;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.commons.object.SeveritedException;
import cz.kpsys.portaro.record.RecordConstants;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RecordDetailFromMarcXmlConstructionException extends RuntimeException implements SeveritedException, UserFriendlyException {

    public static final String DEFAULT_MESSAGE = "Error while constructing detail from MARC21 xml";

    @Getter
    @NonNull
    String xml;

    @Getter
    int severity = SeveritedException.SEVERITY_ERROR;

    @Getter
    @NonNull
    Text text = Texts.ofMessageCoded(RecordConstants.LocalizationCodes.MarcXmlReconstructionError);

    public RecordDetailFromMarcXmlConstructionException(String xml, Throwable t) {
        super(DEFAULT_MESSAGE, t);
        this.xml = xml;
    }
    
}
