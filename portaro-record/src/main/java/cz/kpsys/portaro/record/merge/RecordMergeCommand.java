package cz.kpsys.portaro.record.merge;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import lombok.NonNull;

import java.util.List;

public record RecordMergeCommand(

        @NonNull
        Department currentDepartment,

        @NonNull
        UserAuthentication currentAuth,

        @NonNull
        Record targetRecord,

        @NonNull
        List<Record> sourceRecords,

        @NonNull
        Boolean mergeDetail

) {}
