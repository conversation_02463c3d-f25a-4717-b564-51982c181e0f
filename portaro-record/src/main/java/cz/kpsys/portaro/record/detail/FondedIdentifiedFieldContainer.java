package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.record.RecordIdFondPair;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;

import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class FondedIdentifiedFieldContainer extends SimpleFieldContainer implements IdentifiedFieldContainer {

    @Getter
    @NonNull
    RecordIdFondPair recordIdFondPair;

    public FondedIdentifiedFieldContainer(@NonNull RecordIdFondPair recordIdFondPair) {
        super();
        this.recordIdFondPair = recordIdFondPair;
    }

    public FondedIdentifiedFieldContainer(@NonNull FondedIdentifiedFieldContainer original, @NonNull RecordIdFondPair recordIdFondPair, @Nullable Integer kindedId) {
        super(original, recordIdFondPair, kindedId);
        this.recordIdFondPair = recordIdFondPair;
    }

    @Override
    public UUID getId() {
        return recordIdFondPair.id().id();
    }

    @Override
    public String toString() {
        return "FondedIdentifiedFieldContainer{%s with %s fields}".formatted(recordIdFondPair, getFields().size());
    }

    @JsonIgnore
    @Override
    public FondedIdentifiedFieldContainer copy(@NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId) {
        return new FondedIdentifiedFieldContainer(this, newRecordIdFondPair, kindedId);
    }

}
