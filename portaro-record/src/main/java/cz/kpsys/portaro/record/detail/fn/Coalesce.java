package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.datatype.DatatypeUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.AbsentDataFail;
import cz.kpsys.portaro.record.detail.value.EmptyCoalesceFail;
import cz.kpsys.portaro.record.detail.value.FieldValue;
import cz.kpsys.portaro.record.detail.value.NonAbsentDataFail;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.util.*;
import java.util.function.Function;

public record Coalesce<RES extends FieldValue<?>>(

        @NonNull
        @NotEmpty
        List<Formula<RES>> operands,

        @NonNull
        FetchType fetchType

) implements NaryOperator<RES> {

    public enum FetchType {
        LAZY, EAGER
    }

    @SafeVarargs
    public static <RES extends FieldValue<?>> Coalesce<RES> createLazy(@NonNull Formula<RES> primary, @NonNull Formula<RES> secondary, @NonNull Formula<RES>... others) {
        return create(primary, secondary, others, FetchType.LAZY);
    }

    @SafeVarargs
    public static <RES extends FieldValue<?>> Coalesce<RES> createEager(@NonNull Formula<RES> primary, @NonNull Formula<RES> secondary, @NonNull Formula<RES>... others) {
        return create(primary, secondary, others, FetchType.EAGER);
    }

    private static <RES extends FieldValue<?>> @NonNull Coalesce<RES> create(@NonNull Formula<RES> primary, @NonNull Formula<RES> secondary, @NonNull Formula<RES>[] others, FetchType fetchType) {
        List<Formula<RES>> operands = new ArrayList<>(2 + others.length);
        operands.add(primary);
        operands.add(secondary);
        operands.addAll(Arrays.asList(others));
        return new Coalesce<>(operands, fetchType);
    }

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<RES>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        List<ScalarDatatype> operandDatatypes = ResolvableFunction.resolveOperandDatatypes(operands, operandResultDatatypeResolver);
        return DatatypeUtil.getMostPreciseDatatypeIfNumberOrRequireSingleUniqueDatatype(operandDatatypes, getClass().getSimpleName());
    }

    @Override
    @NonNull
    public FormulaEvaluation<RES> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {
        Map<Formula<?>, AbsentDataFail> failedOperandResults = new HashMap<>(operands.size());
        for (Formula<RES> operand : operands) {
            FormulaEvaluation<RES> resolvedOperandValue = evaluator.resolveFormulaValue(sourceNode, operand);
            if (resolvedOperandValue.isSuccess()) {
                return resolvedOperandValue;
            }
            if (resolvedOperandValue.isFailure() && resolvedOperandValue.existingFail() instanceof NonAbsentDataFail) {
                return resolvedOperandValue;
            }
            AbsentDataFail absentDataFail = resolvedOperandValue.existingFailOf(AbsentDataFail.class);
            if (!searchedRecordSpecs.covers(absentDataFail.toSpec())) { // the given value is missing and not-yet searched
                return resolvedOperandValue;
            }
            failedOperandResults.put(operand, absentDataFail);
        }
        return FormulaEvaluation.failure(EmptyCoalesceFail.of(sourceNode.id().toRecordFieldTypeId(), failedOperandResults));
    }

    @Override
    public String toString() {
        return "Coalesce(%s)".formatted(StringUtil.listToString(operands, ", "));
    }
}
