package cz.kpsys.portaro.record.detail.binding;

import cz.kpsys.portaro.record.InternalRecordLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.RecordIdentifier;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.detail.value.StringFieldValue;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class KindedIdBoundFieldsLoader implements Function<Set<RecordIdFondPair>, Map<RecordIdFondPair, ? extends ScalarFieldValue<?>>> {

    @NonNull InternalRecordLoader internalRecordAllByIdsLoader;
    @NonNull Boolean isDocumented;

    @Override
    public Map<RecordIdFondPair, StringFieldValue> apply(@NonNull Set<RecordIdFondPair> recordIdentifiers) {
        List<UUID> recordIds = getRelevantRecordIds(recordIdentifiers);
        return internalRecordAllByIdsLoader.getAllByIds(recordIds)
                .stream()
                .collect(
                        Collectors.toMap(
                                Record::idFondPair,
                                record -> StringFieldValue.of(String.valueOf(record.getKindedId()), Set.of(record.idFondPair())
                                )
                        )
                );
    }

    private List<UUID> getRelevantRecordIds(@NonNull Set<RecordIdFondPair> recordIdFondPairs) {
        return recordIdFondPairs.stream()
                .filter(recordIdFondPair -> recordIdFondPair.fond().isOfDocument() == isDocumented)
                .map(RecordIdFondPair::getId)
                .map(RecordIdentifier::id)
                .toList();
    }
}
