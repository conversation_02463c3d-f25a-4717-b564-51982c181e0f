package cz.kpsys.portaro.record;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedSaveResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.formannotation.annotations.form.ValidFormObject;
import cz.kpsys.portaro.marcxml.model.LenientVerbisRecordMarcDto;
import cz.kpsys.portaro.marcxml.model.RecordMarcDto;
import cz.kpsys.portaro.marcxml.model.StrictVerbisRecordMarcDto;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.importer.marc.RecordImporter;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.http.MediaType.APPLICATION_XML_VALUE;

@RequestMapping("/api/records/import")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordImportApiController extends GenericApiController {

    @NonNull Converter<String, StrictVerbisRecordMarcDto> stringToStrictRecordMarcDtoConverter;
    @NonNull Converter<String, LenientVerbisRecordMarcDto> stringToLenientVerbisRecordMarcDtoConverter;
    @NonNull RecordImporter<RecordMarcDto> recordMarcDtoImporter;

    @PostMapping(path = "marc21", consumes = APPLICATION_XML_VALUE)
    public ActionResponse importMarc21XML(@RequestBody String request, @RequestParam("fond") Fond fond, @CurrentDepartment Department currentDepartment, UserAuthentication currentAuth) {
        Identified<UUID> identifier = recordMarcDtoImporter.importRecord(stringToStrictRecordMarcDtoConverter.convert(request), fond, currentDepartment, currentAuth);
        return new FinishedSaveResponse<>(Texts.ofNative("Record was successfully imported."), identifier);
    }

    @PostMapping(consumes = APPLICATION_JSON_VALUE)
    public ActionResponse importRecord(@RequestBody @ValidFormObject RecordImportRequest request, @CurrentDepartment Department currentDepartment, UserAuthentication currentAuth) {
        Assert.isTrue(request.format().equals("marc21.lenient"), "Only marc21 format is supported");
        LenientVerbisRecordMarcDto recordMarcDto = stringToLenientVerbisRecordMarcDtoConverter.convert(request.content());
        Identified<UUID> identifier = recordMarcDtoImporter.updateRecord(recordMarcDto, request.record(), currentDepartment, currentAuth);
        return new FinishedSaveResponse<>(Texts.ofNative("Record was successfully imported."), identifier);
    }

}
