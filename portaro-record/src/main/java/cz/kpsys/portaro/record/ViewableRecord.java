package cz.kpsys.portaro.record;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.html.TableRow;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.id.IDdFile;
import cz.kpsys.portaro.prop.ObjectProperties;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.fond.FondTypeResolver;
import cz.kpsys.portaro.record.query.FieldGroupFillerImpl;
import cz.kpsys.portaro.record.query.MarqueryParser;
import cz.kpsys.portaro.record.query.ValFieldGroup;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ViewableRecord implements RecordDescriptor {

    public static final List<RecordStatusTransitionResponse> NO_RECORD_STATUS_TRANSITIONS = List.of();

    @NonNull
    MarqueryParser queryParser = new MarqueryParser();

    @JsonIgnore
    @Getter(AccessLevel.PROTECTED)
    @NonNull
    Record target;

    @Getter
    @NonNull
    RecordStatusResponse status;

    @Getter
    @NonNull
    List<RecordStatusTransitionResponse> recordStatusTransitions;

    @NonNull
    ObjectProperties props;

    @Getter
    @NonNull
    Map<String, String> exports;

    @Getter
    @Nullable
    Integer directoryId;

    @Getter
    @Nullable
    IDdFile cover;

    @JsonIgnore
    @NonNull
    ViewableFieldContainer notflattenViewableFieldList;

    @JsonIgnore
    @Getter
    @NonNull
    ViewableFieldContainer flattenViewableFieldList;

    @Getter
    @NonNull
    List<TableRow> detailTableRows;

    @Getter
    boolean withForeignOccurrences;

    @NonNull
    Boolean forbidden;

    public ViewableRecord(@NonNull Record target,
                          @NonNull RecordStatusResponse status,
                          @NonNull List<RecordStatusTransitionResponse> recordStatusTransitions,
                          @NonNull Map<String, String> exports,
                          @NonNull ObjectProperties props,
                          @NonNull ViewableFieldContainer notflattenViewableFieldList,
                          @NonNull ViewableFieldContainer flattenViewableFieldList,
                          @NonNull List<TableRow> detailTableRows,
                          boolean withForeignOccurrences,
                          @NonNull Boolean forbidden) {
        this.target = target;
        this.status = status;
        this.recordStatusTransitions = recordStatusTransitions;
        this.props = props;
        this.exports = exports;
        this.directoryId = target.getDirectoryId();
        this.cover = target.getCover();
        this.notflattenViewableFieldList = notflattenViewableFieldList;
        this.flattenViewableFieldList = flattenViewableFieldList;
        this.detailTableRows = detailTableRows;
        this.withForeignOccurrences = withForeignOccurrences;
        this.forbidden = forbidden;
    }

    public static ViewableRecord ofDeletedRecord(@NonNull Record target, @NonNull RecordStatusResponse status, List<RecordStatusTransitionResponse> recordStatusTransitions) {
        return new ViewableRecord(target, status, recordStatusTransitions, Map.of(), ObjectProperties.empty(), SimpleViewableFieldContainer.emptyUnmodifiable(), SimpleViewableFieldContainer.emptyUnmodifiable(), List.of(), false, true);
    }


    @Override
    public UUID getId() {
        return target.getId();
    }

    @Override
    public Text getText() {
        return target.getText();
    }

    @Override
    public String getName() {
        return target.getName();
    }

    @NonNull
    public String getType() {
        return target.getType();
    }

    @JsonAnyGetter
    public Map<String, ?> getProps() {
        return props.toMap();
    }

    @NonNull
    public Fond getFond() {
        return target.getFond();
    }

    public boolean isExternal() {
        return getTarget().isExternal();
    }

    public boolean isActive() {
        return getTarget().isActive();
    }

    public boolean isDeleted() {
        return getTarget().isDeleted();
    }

    public boolean isValid() {
        return getTarget().isValid();
    }

    public boolean isForbidden() {
        return forbidden;
    }

    public List<? extends ViewableField> getFields() {
        return notflattenViewableFieldList.getFields();
    }

    public List<Field<?>> getFields(String fieldNumber) {
        String fieldCode = FieldHelper.convertLegacyCodeToNewPrefixedCode(fieldNumber, () -> FondTypeResolver.isAuthorityFond(getFond()));
        return target.getFields(fieldCode);
    }

    public Field<?> getField(String fieldNumber, int repetition) {
        if (target.getDetail() == null) {
            return null;
        }
        String fieldCode = FieldHelper.convertLegacyCodeToNewPrefixedCode(fieldNumber, () -> FondTypeResolver.isAuthorityFond(getFond()));
        return target.getDetail().getFirstField(By.codeAndRepetition(fieldCode, repetition)).orElse(null);
    }

    public ValFieldGroup query(String q) {
        return queryParser.parse(q, new FieldGroupFillerImpl(flattenViewableFieldList));
    }

    @Deprecated
    public ViewableField getField(int fieldNumber, int repetition) {
        FieldTypeId fieldTypeId = getFieldTypeId(fieldNumber);
        return flattenViewableFieldList.getFirstField(By.codeAndRepetition(fieldTypeId.getCode(), repetition)).orElse(null);
    }

    @Deprecated
    public List<? extends ViewableField> getSubfields(int fieldNumber, final String code) {
        FieldTypeId fieldTypeId = getFieldTypeId(fieldNumber).sub(code);
        return flattenViewableFieldList.getFields(By.code(fieldTypeId.existingParent().getCode()), By.code(fieldTypeId.getCode()));
    }

    @Deprecated
    public List<? extends ViewableField> getFields(int fieldNumber) {
        FieldTypeId fieldTypeId = getFieldTypeId(fieldNumber);
        return flattenViewableFieldList.getFields(By.code(fieldTypeId.getCode()));
    }

    @Deprecated
    public ViewableField getField(int fieldNumber) {
        return getField(fieldNumber, FieldId.FIRST_FIELD_REPETITION);
    }

    @Deprecated
    public ViewableField getSubfield(int fieldNumber, String subfieldCode) {
        return getSubfields(fieldNumber, subfieldCode)
                .stream()
                .findFirst()
                .orElse(null);
    }

    @Deprecated
    public ViewableField getSubfield(int fieldNumber, int fieldRep, String subfieldCode) {
        ViewableField field = getField(fieldNumber, fieldRep);
        if (field == null) {
            return null;
        }
        return field.streamFields()
                .filter(viewableField -> viewableField.getCode().equals(subfieldCode))
                .findFirst()
                .orElse(null);
    }

    @Deprecated
    public ViewableField getSubfield(int fieldNumber, int fieldRepetition, String subfieldCode, int subfieldRepetition) {
        FieldTypeId fieldTypeId = getFieldTypeId(fieldNumber);
        return flattenViewableFieldList.getFields(By.codeAndRepetition(fieldTypeId.getCode(), fieldRepetition), By.codeAndRepetition(subfieldCode, subfieldRepetition))
                .stream()
                .findFirst()
                .orElse(null);
    }

    private @NonNull FieldTypeId getFieldTypeId(int fieldNumber) {
        return FieldTypeId.recordField(FondTypeResolver.isAuthorityFond(getFond()), String.valueOf(fieldNumber));
    }

    public ViewableField getNewFormatField(String fieldCode, int fieldIndex) {
        return flattenViewableFieldList.getFirstField(By.codeAndRepetition(fieldCode, fieldIndex)).orElse(null);
    }

    public List<? extends ViewableField> getNewFormatFields(String fieldCode) {
        return flattenViewableFieldList.getFields(By.code(fieldCode));
    }

    public ViewableField getNewFormatSubfield(String fieldCode, int fieldIndex, String subfieldCode, int subfieldIndex) {
        return flattenViewableFieldList.getFields(By.codeAndRepetition(fieldCode, fieldIndex), By.codeAndRepetition(subfieldCode, subfieldIndex))
                .stream()
                .findFirst()
                .orElse(null);
    }

    public List<? extends ViewableField> getNewFormatSubfields(String fieldCode, String subfieldCode) {
        return flattenViewableFieldList.getFields(By.code(fieldCode), By.code(subfieldCode));
    }
}
