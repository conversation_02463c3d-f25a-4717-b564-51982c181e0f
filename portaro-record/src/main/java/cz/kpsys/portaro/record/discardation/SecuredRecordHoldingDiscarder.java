package cz.kpsys.portaro.record.discardation;

import cz.kpsys.portaro.record.RecordSecurityActions;
import cz.kpsys.portaro.security.SecurityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SecuredRecordHoldingDiscarder implements RecordHoldingDiscarder {

    @NonNull RecordDeleterDelegatingRecordHoldingDiscarder delegate;
    @NonNull SecurityManager securityManager;

    @Override
    public void discard(RecordHoldingDiscardationCommand command) {
        securityManager.throwIfCannot(RecordSecurityActions.RECORD_HOLDING_DISCARD, command.currentAuth(), command.ctx(), command.recordHolding());
        delegate.delete(command);
    }

}
