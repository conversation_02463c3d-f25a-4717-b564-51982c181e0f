package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.sql.generator.InsertQuery;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import static cz.kpsys.portaro.record.file.cover.SpringDbCoversToSearchLoader.UnfoundCovers.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbUnfoundCoverSaver implements UnfoundCoverSaver {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;

    @Override
    public void save(cz.kpsys.portaro.record.Record record, String service, String reason) {
        InsertQuery is = queryFactory.newInsertQuery();
        is.insert(OPAC_NENALEZENE_OBALKY);
        is.values()
                .add(RECORD_ID, record.getId())
                .add(SLUZBA, service)
                .add(DUVOD, reason);
        jdbcTemplate.update(is.getSql(), is.getParamMap());
    }
    
}
