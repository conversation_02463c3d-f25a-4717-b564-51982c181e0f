package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.UUID;


@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
public class RecordForbiddenEvaluator {

    @NonNull ContextualProvider<Department, @NonNull List<RecordStatus>> forbiddenRecordStatusesProvider;
    @NonNull ContextualProvider<Department, @NonNull List<UUID>> forbiddenRecordIdsProvider;

    public boolean evaluate(@NonNull Record record, @NonNull Department ctx) {
        return forbiddenRecordIdsProvider.getOn(ctx).contains(record.getId()) ||
                forbiddenRecordStatusesProvider.getOn(ctx).contains(record.getStatus());
    }
}

