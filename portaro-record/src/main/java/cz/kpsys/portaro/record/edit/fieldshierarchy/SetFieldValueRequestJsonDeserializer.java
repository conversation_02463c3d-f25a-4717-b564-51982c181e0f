package cz.kpsys.portaro.record.edit.fieldshierarchy;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.deser.std.StdScalarDeserializer;

import java.io.IOException;

public class SetFieldValueRequestJsonDeserializer extends StdScalarDeserializer<SetFieldValueRequest> {

    protected SetFieldValueRequestJsonDeserializer() {
        super(SetFieldValueRequest.class);
    }

    @Override
    public SetFieldValueRequest deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        ObjectCodec mapper = p.getCodec();
        JsonNode node = mapper.readTree(p);

        if (node.size() == 1 && node.has(ReferenceFieldValue.Fields.recordId)) {
            return mapper.treeToValue(node, ReferenceFieldValue.class);
        }
        if (node.size() == 1 && node.has(ContentFieldValue.Fields.value)) {
            return mapper.treeToValue(node, ContentFieldValue.class);
        }
        throw new JsonParseException(p, "SetFieldValueRequest has no value");
    }
}