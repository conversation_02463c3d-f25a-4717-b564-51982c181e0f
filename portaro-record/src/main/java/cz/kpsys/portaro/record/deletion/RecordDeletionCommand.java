package cz.kpsys.portaro.record.deletion;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import lombok.NonNull;

import java.util.Set;

public record RecordDeletionCommand(

        @NonNull
        Record record,

        @NonNull
        Set<Department> holdingDepartments,

        @NonNull
        Set<Department> exemplarDepartments,

        @NonNull
        Department ctx,

        @NonNull
        UserAuthentication currentAuth,

        @NonNull
        Boolean withToDeletableExemplarStatusChange,

        @NonNull
        Boolean withMvsCancelling,

        @NonNull
        Boolean withCaslinCancelling

) {

    public RecordDeletionCommand(@NonNull Record record, @NonNull Set<Department> holdingDepartments, @NonNull Set<Department> exemplarDepartments, @NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth) {
        this(record, holdingDepartments, exemplarDepartments, currentDepartment, currentAuth, false, false, false);
    }

    public static RecordDeletionCommand withoutHoldingsAndExemplars(@NonNull Record record, @NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth) {
        return new RecordDeletionCommand(record, Set.of(), Set.of(), currentDepartment, currentAuth, false, false, false);
    }

}
