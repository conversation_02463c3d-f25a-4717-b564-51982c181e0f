package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import lombok.NonNull;

import java.time.LocalDate;

public record DateValueCommand(

        @NonNull
        LocalDate value,

        @NonNull
        Department ctx,

        @NonNull
        UserAuthentication currentAuth

) implements ScalarFieldValueCommand<LocalDate> {

    @Override
    public boolean equals(Object o) {
        return o instanceof DateValueCommand that && ctx.equals(that.ctx) && value.equals(that.value) && currentAuth.equals(that.currentAuth);
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    @Override
    public String toString() {
        return "DateValueCommand[" + value + ']';
    }
}
