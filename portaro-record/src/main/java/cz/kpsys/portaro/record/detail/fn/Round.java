package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.datatype.DatatypeUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.NumberFieldValue;
import cz.kpsys.portaro.record.load.ValuableFieldNode;
import lombok.NonNull;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.function.Function;

public record Round(

        @NonNull
        Formula<NumberFieldValue> operand,

        @NonNull
        Integer scale

) implements UnaryFunction<NumberFieldValue> {

    public static Round create(@NonNull Formula<NumberFieldValue> operand, @NonNull ScalarDatatype scale) {
        return new Round(operand, CoreConstants.Datatype.datatypeToScale(scale));
    }

    /// Presnost zaokrouhlovani je maximalne presnost scale
    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<NumberFieldValue>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        ScalarDatatype operandDatatype = operandResultDatatypeResolver.apply(operand);
        ScalarDatatype scaleDatatype = CoreConstants.Datatype.scaleToDatatype(scale);
        return DatatypeUtil.getLessPreciseDatatype(List.of(operandDatatype, scaleDatatype));
    }

    @Override
    public @NonNull FormulaEvaluation<NumberFieldValue> compute(@NonNull ValuableFieldNode sourceNode, @NonNull FormulaEvaluator evaluator, @NonNull RecordSpecSet searchedRecordSpecs) {
        FormulaEvaluation<NumberFieldValue> resolvedOperandValue = evaluator.resolveFormulaValue(sourceNode, operand).ensureOf(NumberFieldValue.class, sourceNode, operand);
        if (resolvedOperandValue.isFailure()) {
            return resolvedOperandValue.castedFailed();
        }
        return compute(resolvedOperandValue.existingSuccess());
    }

    public @NonNull FormulaEvaluation<NumberFieldValue> compute(@NonNull NumberFieldValue operandValue) {
        BigDecimal value = operandValue.value().setScale(scale, RoundingMode.HALF_UP);
        return FormulaEvaluation.success(NumberFieldValue.of(value, operandValue.origins()));
    }

    @Override
    public String toString() {
        return "Round(#.%s of %s)".formatted("#".repeat(scale), operand);
    }
}
