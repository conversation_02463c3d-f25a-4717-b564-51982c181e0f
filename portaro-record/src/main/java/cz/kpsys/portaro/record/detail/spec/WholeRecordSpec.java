package cz.kpsys.portaro.record.detail.spec;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.RecordIdFondPair;
import lombok.NonNull;

import java.util.UUID;

public record WholeRecordSpec(

        @NonNull
        RecordIdFondPair recordIdFondPair

) implements RecordSpec {

    public static WholeRecordSpec of(@NonNull RecordIdFondPair recordIdFondPair) {
        return new WholeRecordSpec(recordIdFondPair);
    }

    @Override
    public @NonNull RecordMatcher recordMatcher() {
        return RecordIdentifierRecordMatcher.of(recordIdFondPair);
    }

    @Override
    public @NonNull FieldsSpec fieldsSpec() {
        return FieldsSpec.ofAll();
    }

    public @NonNull UUID existingRecordId() {
        return recordIdFondPair.id().id();
    }

    @Override
    public @NonNull FieldedRecordSpec withFieldsSpec(@NonNull FieldsSpec fieldsSpec) {
        return RecordSpec.ofGeneric(recordIdFondPair, fieldsSpec);
    }

    @Override
    public boolean isEmpty() {
        return false;
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, WholeRecordSpec.class, WholeRecordSpec::recordIdFondPair);
    }

    @Override
    public int hashCode() {
        return recordIdFondPair.hashCode();
    }

    @Override
    public String toString() {
        return recordIdFondPair.id().abbrDelim() + "*";
    }

}
