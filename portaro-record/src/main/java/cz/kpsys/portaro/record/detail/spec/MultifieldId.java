package cz.kpsys.portaro.record.detail.spec;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.util.Assert;

import java.util.Comparator;
import java.util.Objects;

/// Napr. d245 nebo d245#0.a
public record MultifieldId(

        @Nullable
        FieldId parentFieldId,

        @NonNull
        FieldTypeId fieldTypeId

) {

    public static Comparator<MultifieldId> NUMERICALLY_COMPATIBLE_SORTER = Comparator.comparing(MultifieldId::parentFieldId, Comparator.nullsFirst(FieldId.NUMERICALLY_COMPATIBLE_SORTER))
            .thenComparing(MultifieldId::fieldTypeId, FieldTypeId.NUMERICALLY_COMPATIBLE_SORTER);

    public static MultifieldId ofRoot(@NonNull FieldTypeId fieldTypeId) {
        return new MultifieldId(null, fieldTypeId);
    }

    public static MultifieldId ofSub(@NonNull FieldId parentFieldId, @NonNull FieldTypeId fieldTypeId) {
        return new MultifieldId(parentFieldId, fieldTypeId);
    }

    @JsonIgnore
    public boolean hasParentFieldId() {
        return !isRoot();
    }

    @JsonIgnore
    public boolean isRoot() {
        return parentFieldId == null;
    }

    public @NonNull FieldId existingParentFieldId() {
        Assert.state(hasParentFieldId(), () -> "Cannot get parent field id from root record id (this is " + this + ")");
        return Objects.requireNonNull(parentFieldId);
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof MultifieldId that)) {
            return false;
        }
        return Objects.equals(parentFieldId(), that.parentFieldId()) && fieldTypeId().equals(that.fieldTypeId());
    }

    @Override
    public int hashCode() {
        int result = Objects.hashCode(parentFieldId());
        result = 31 * result + fieldTypeId().hashCode();
        return result;
    }

    @Override
    public String toString() {
        return (parentFieldId == null ? "" : parentFieldId.toString() + FieldTypeId.DELIMITER_CHAR) + fieldTypeId.getCode();
    }
}
