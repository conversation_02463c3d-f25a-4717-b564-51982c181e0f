package cz.kpsys.portaro.record;

public class RecordRegExpPatterns {

    /**
     * Identifikator dokumentu v poli 001 (kpw0235843 apod.)
     */
    public static final String DOCUMENT_ID_FIELD_001 = "^[a-zA-Z_:-]+[0-9][0-9][0-9]+$";
    
    public static final String RECORD_KINDED_ID = "[0-9]+";
    public static final String RECORD_KINDED_ID_WHOLE = "^" + RECORD_KINDED_ID + "$";

    /**
     * P a jakekoliv cislo, Napriklad P245.
     */
    public static String SEARCH_FIELD_FIELD = "^P[0-9]+$";
    
    /**
     * P, jakekoliv cislo, podtr<PERSON><PERSON>ko a role, napr. "P700_prt"
     */
    public static String SEARCH_FIELD_FIELD_WITH_ROLE = "^P[0-9]+_.+$";
}
