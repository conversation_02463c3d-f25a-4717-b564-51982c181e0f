package cz.kpsys.portaro.record.merge;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.NonNull;

import java.util.List;

@JacksonXmlRootElement(localName = "sluc_aut")
public record AuthorityMergeAppserverRequest(

        @JacksonXmlElementWrapper(useWrapping = false)
        @JacksonXmlProperty(localName = "CIS_AUT")
        @NonNull
        List<Integer> sourceAuthorityKindedIds,

        @JacksonXmlProperty(localName = "ID_AUT")
        @NonNull
        Integer targetAuthorityKindedId

) {}
