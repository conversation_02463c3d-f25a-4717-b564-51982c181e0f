package cz.kpsys.portaro.record.deletion;

import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.validation.ValidationUtils;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordHoldingDeletionValidator implements ConstraintValidator<RecordHoldingDeletionValid, RecordHoldingDeletionRequest> {

    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull ContextualFunction<Record, Department, Boolean> singleNodeDepartmentedRecordHasExemplarsPredicate;

    @Override
    public boolean isValid(RecordHoldingDeletionRequest request, ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();

        if (request == null) {
            return true;
        }

        if (ObjectUtil.isNullOrFalse(request.acceptingExemplarsDeletion())) {
            Record record = recordLoader.getById(request.recordHolding().getRecordId());
            if (singleNodeDepartmentedRecordHasExemplarsPredicate.getOn(record, request.recordHolding().getDepartment())) {
                ValidationUtils.setFieldFailedValidationToContextWithMessage(RecordHoldingDeletionRequest.Fields.acceptingExemplarsDeletion, context, "This field is required");
                return false;
            }
        }

        return true;
    }

}