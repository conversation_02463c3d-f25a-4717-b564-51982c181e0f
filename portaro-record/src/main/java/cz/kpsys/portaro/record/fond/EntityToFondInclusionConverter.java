package cz.kpsys.portaro.record.fond;

import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

public class EntityToFondInclusionConverter implements Converter<FondInclusionEntity, FondInclusion> {

    @Override
    public FondInclusion convert(@NonNull FondInclusionEntity source) {
        return new FondInclusion(source.getId(), source.getFondId(), source.getIncludedFondId());
    }
}
