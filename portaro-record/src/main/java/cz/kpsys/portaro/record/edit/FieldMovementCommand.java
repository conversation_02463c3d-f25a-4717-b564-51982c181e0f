package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.record.detail.FieldId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.With;

@With(AccessLevel.PRIVATE)
public record FieldMovementCommand(

        @NonNull
        FieldId fieldId,

        @NonNull
        FieldMovementDirection direction

) {

    public static FieldMovementCommand of(@NonNull FieldId fieldId, @NonNull FieldMovementDirection direction) {
        return new FieldMovementCommand(fieldId, direction);
    }

    public static FieldMovementCommand up(@NonNull FieldId fieldId) {
        return of(fieldId, FieldMovementDirection.UP);
    }

    public static FieldMovementCommand down(@NonNull FieldId fieldId) {
        return of(fieldId, FieldMovementDirection.DOWN);
    }
}
