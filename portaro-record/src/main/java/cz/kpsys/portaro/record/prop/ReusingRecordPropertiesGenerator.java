package cz.kpsys.portaro.record.prop;

import cz.kpsys.portaro.prop.ObjectProperties;
import cz.kpsys.portaro.prop.ObjectPropertiesGenerator;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ReusingRecordPropertiesGenerator implements ObjectPropertiesGenerator<Record> {

    @NonNull
    @Override
    public ObjectProperties generate(@NonNull Record record) {
        return record.getProps();
    }

}
