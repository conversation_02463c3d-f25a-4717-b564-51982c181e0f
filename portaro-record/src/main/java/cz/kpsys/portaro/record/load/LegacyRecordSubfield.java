package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.commons.object.NonNullOrderedRecord;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.record.RecordIdentifier;
import lombok.NonNull;

import java.util.Objects;

public record LegacyRecordSubfield<V extends LegacyRecordFieldValue>(

        @NonNull
        RecordIdentifier recordIdentifier,

        @NonNull
        String subfieldCode,

        @NonNull
        Integer sameCodeIndex,

        @NonNull
        V value

) implements NonNullOrderedRecord, LegacyRecordFieldable<V> {

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, LegacyRecordSubfield.class, LegacyRecordSubfield::recordIdentifier, LegacyRecordSubfield::subfieldCode, LegacyRecordSubfield::sameCodeIndex);
    }

    @Override
    public int hashCode() {
        return Objects.hash(recordIdentifier.id(), subfieldCode, sameCodeIndex);
    }

}
