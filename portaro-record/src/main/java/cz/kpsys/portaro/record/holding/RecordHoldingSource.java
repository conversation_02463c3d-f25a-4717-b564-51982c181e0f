package cz.kpsys.portaro.record.holding;

import cz.kpsys.portaro.commons.object.Identified;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.Value;

import java.util.UUID;

@Value
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class RecordHoldingSource implements Identified<UUID> {

    @EqualsAndHashCode.Include
    @NonNull
    UUID id;

    @NonNull
    UUID recordHoldingId;

    @NonNull
    RecordHoldingSourceType type;

    @NonNull
    UUID creationEventId;

    public boolean isOfSameHoldingAndType(RecordHoldingSource other) {
        return other.getRecordHoldingId().equals(recordHoldingId) && isOfSameType(other);
    }

    public boolean isOfSameType(RecordHoldingSource other) {
        return other.getType().equals(type);
    }

}
