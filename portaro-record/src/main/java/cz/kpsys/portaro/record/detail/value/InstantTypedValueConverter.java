package cz.kpsys.portaro.record.detail.value;

import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.RecordDetailConstants;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class InstantTypedValueConverter implements TypedFieldValueConverter<InstantValueCommand, FieldPayload<InstantFieldValue>, InstantFieldValue> {

    public static final DateTimeFormatter rawDateFormat = RecordDetailConstants.RAW_INSTANT_FORMAT;

    @NonNull
    @Override
    public FieldPayload<InstantFieldValue> convert(@NonNull InstantValueCommand command, @NonNull FieldType<?> fieldType, @NonNull RecordFieldId fieldId) {
        return FieldPayload.of(InstantFieldValue.of(command.value(), Set.of(fieldId.recordIdFondPair())));
    }

    public static @NonNull String formatLabel(@NonNull Instant date) {
        return rawDateFormat.format(date);
    }

    @Override
    public String toString() {
        return getClass().getSimpleName();
    }
}
