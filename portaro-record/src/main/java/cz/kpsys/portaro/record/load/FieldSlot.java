package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import cz.kpsys.portaro.record.detail.spec.RecordMultifieldId;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.FailedResult;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.detail.value.VectorFieldValue;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static cz.kpsys.portaro.record.load.Multifields.Ordering.INSERTION_ORDER;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public final class FieldSlot implements ValuableFieldNode {

    @NonNull RecordIdFondPair record;
    @NonNull RecordFieldId id;
    @NonNull EditableFieldType<?> fieldType;
    @NonNull @NonFinal Field<?> field;
    @NonNull Multifields multifields;

    public static FieldSlot createFilled(@NonNull RecordIdFondPair record, @NonNull RecordFieldId id, @NonNull EditableFieldType<?> fieldType, @NonNull Field<?> field) {
        Multifields multifields = Multifields.create(record, id, fieldType.getSubfieldTypes(), INSERTION_ORDER);
        multifields.addFields(field.getFields());
        return new FieldSlot(record, id, fieldType, field, multifields);
    }

    public static FieldSlot createFilledAndCreateGeneratedMultifields(@NonNull RecordIdFondPair record, @NonNull RecordFieldId recordFieldId, @NonNull EditableFieldType<?> fieldType, @NonNull Field<?> field) {
        Multifields multifields = Multifields.createWithMultifields(record, recordFieldId, fieldType.getSubfieldTypes(), INSERTION_ORDER);
        multifields.addFields(field.getFields());
        return new FieldSlot(record, recordFieldId, fieldType, field, multifields);
    }

    public void setField(@NonNull Field<?> loadedField) {
        if (loadedField.equals(this.field)) {
            return;
        }
        this.field.setPayload(loadedField.getPayload());
        multifields.addFields(loadedField.getFields());
    }

    public RecordSpecSet updateLoadedAndGetNextSpecs(@NonNull LoadedRecords loadedRecords, @NonNull RecordSpecSet searchedRecordSpecs) {
        RecordSpecSet missingSpecs = loadedRecords.resolveAndGetMissingSpecs(searchedRecordSpecs, this);
        RecordSpecSet unsearchedSpecs = missingSpecs.minus(searchedRecordSpecs);

        RecordSpecSet result = RecordSpecSet.ofEmptyModifiable();
        result.addAll(unsearchedSpecs);
        result.addAll(multifields.updateLoadedAndGetNextSpecs(loadedRecords, searchedRecordSpecs));
        return result;
    }

    public List<Multifield> findMultifieldsByFieldTypeId(@NonNull FieldTypeId fieldTypeId) {
        return multifields.findMultifieldsByFieldTypeId(fieldTypeId);
    }

    public Optional<FieldSlot> findSingleByFieldId(@NonNull RecordFieldId recordFieldId) {
        return multifields.findSingleByFieldId(recordFieldId);
    }

    public List<FieldSlot> findAllByMultifieldId(@NonNull RecordMultifieldId recordMultifieldId) {
        return multifields.findAllByMultifieldId(recordMultifieldId);
    }

    public int index() {
        return id.fieldId().getRepetition();
    }

    @Override
    public @NonNull RecordIdFondPair record() {
        return record;
    }

    @Override
    public @NonNull RecordFieldId id() {
        return id;
    }

    @Override
    public @NonNull Optional<Formula<?>> formula() {
        return fieldType.getFormula();
    }

    @Override
    public @NonNull Set<LookupDefinition> lookups() {
        return fieldType.getLookups();
    }

    @Override
    public boolean hasRecordLink() {
        return field.hasRecordLink();
    }

    public @NonNull RecordIdFondPair getExistingRecordLink() {
        return field.getExistingRecordLink();
    }

    @Override
    public void setGeneratedRecordLink(@NonNull RecordIdFondPair recordLink) {
        field.setRecordLink(recordLink);
    }

    @Override
    public boolean hasVectorValue() {
        return false;
    }

    @Override
    public @NonNull VectorFieldValue<?, ?> existingVectorValue() {
        throw new IllegalStateException("Cannot set generated vector value to field " + id + ", field slot is never vector-generatable");
    }

    @Override
    public void setGeneratedVectorValue(@NonNull VectorFieldValue<?, ?> value) {
        throw new IllegalStateException("Cannot set generated vector value to field " + id + ", field slot is never vector-generatable (value: " + value + ")");
    }

    @Override
    public boolean hasScalarValue() {
        return field.hasValueHolder();
    }

    @Override
    public @NonNull ScalarFieldValue<?> existingScalarValue() {
        return field.getExistingValueHolder();
    }

    @Override
    public void setGeneratedScalarValue(@NonNull ScalarFieldValue<?> value) {
        field.setValue(value);
    }

    @Override
    public boolean hasError() {
        return field.hasError();
    }

    @Override
    public @NonNull FailedResult getExistingError() {
        return field.getExistingError();
    }

    @Override
    public void setGeneratedValueError(@NonNull FailedResult failedResult) {
        field.setError(failedResult);
    }

    public Field<?> field() {
        return field;
    }

    public @NonNull Multifields multifields() {
        return multifields;
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof FieldSlot fieldSlot)) {
            return false;
        }
        return id.equals(fieldSlot.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }

    @Override
    public String toString() {
        String multifieldsPart = multifields.isEmpty() ? "<no multifields>" : multifields.toString();
        return "Slot[" + field + ", " + multifieldsPart + ']';
    }
}
