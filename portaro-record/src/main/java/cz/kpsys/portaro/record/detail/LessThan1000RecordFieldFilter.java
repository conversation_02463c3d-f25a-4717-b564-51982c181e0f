package cz.kpsys.portaro.record.detail;

import java.util.Optional;
import java.util.function.Predicate;

public class LessThan1000RecordFieldFilter<F extends WithFieldType> implements Predicate<F> {

    @Override
    public boolean test(F f) {
        FieldTypeId fieldTypeId = f.getType().getFieldTypeId();
        if (fieldTypeId.getLevel() != FieldTypeId.LEVEL_TOPFIELD) {
            return true;
        }
        Optional<Integer> numberIfPrefixedNumericCode = FieldHelper.fieldCodeToNumberOpt(fieldTypeId.getCode());
        return numberIfPrefixedNumericCode
                .map(fieldNumber -> fieldNumber < 1000)
                .orElse(true);
    }

}
