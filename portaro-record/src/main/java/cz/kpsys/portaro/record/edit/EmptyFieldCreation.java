package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;
import lombok.With;

import java.util.Optional;
import java.util.UUID;

@With
public record EmptyFieldCreation(

    @NonNull
    UUID id,

    @NonNull
    FieldTypeId fieldTypeId,

    @NonNull
    Optional<Integer> repetition

) {
    public static EmptyFieldCreation toEnd(@NonNull FieldTypeId fieldTypeId) {
        return toEnd(UuidGenerator.forIdentifier(), fieldTypeId);
    }

    public static EmptyFieldCreation toEnd(@NonNull UUID id, @NonNull FieldTypeId fieldTypeId) {
        return new EmptyFieldCreation(id, fieldTypeId, Optional.empty());
    }

    public static EmptyFieldCreation to(@NonNull FieldTypeId fieldTypeId, @NonNull Integer repetition) {
        return to(UuidGenerator.forIdentifier(), fieldTypeId, repetition);
    }

    public static EmptyFieldCreation to(@NonNull UUID id, @NonNull FieldTypeId fieldTypeId, @NonNull Integer repetition) {
        return new EmptyFieldCreation(id, fieldTypeId, Optional.of(repetition));
    }

}
