package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.LabeledIdentifiedRecord;
import cz.kpsys.portaro.department.Department;
import lombok.NonNull;

import java.util.Optional;

public record RecordStatusResponse(

        @NonNull
        Integer id,

        @NonNull
        Text text,

        boolean locked

) implements LabeledIdentifiedRecord<Integer> {

    public static RecordStatusResponse createFinishedCat() {
        return map(RecordStatus.FINISHED_CATALOGING, Optional.empty());
    }

    public static RecordStatusResponse map(RecordStatus status, Optional<RecordStatus> editLockThreshold) {
        boolean locked = editLockThreshold.isPresent() && status.isSameOrHigherThan(editLockThreshold.get());
        return new RecordStatusResponse(
                status.getId(),
                status.getText(),
                locked
        );
    }

    public static RecordStatusResponse map(RecordStatus status, RecordStatusResolver recordStatusResolver, Department ctx) {
        boolean locked = recordStatusResolver.isLockedState(status, ctx);
        return new RecordStatusResponse(
                status.getId(),
                status.getText(),
                locked
        );
    }

}
