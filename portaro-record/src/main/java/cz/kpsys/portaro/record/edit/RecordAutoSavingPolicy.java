package cz.kpsys.portaro.record.edit;

import com.fasterxml.jackson.annotation.JsonFormat;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum RecordAutoSavingPolicy implements LabeledIdentified<String> {

    AFTER_EACH_CHANGE_WHEN_DRAFT("after-each-change-when-draft", Texts.ofNative("Automatic saving when record is draft")),
    AFTER_EACH_CHANGE_ALWAYS("after-each-change-always", Texts.ofNative("Automatic saving always"));

    public static final Codebook<RecordAutoSavingPolicy, String> CODEBOOK = new StaticCodebook<>(values());

    @NonNull String id;
    @NonNull Text text;

}
