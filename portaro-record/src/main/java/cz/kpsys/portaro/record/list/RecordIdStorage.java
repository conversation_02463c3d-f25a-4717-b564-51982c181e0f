package cz.kpsys.portaro.record.list;

import java.util.List;
import java.util.UUID;

public interface RecordIdStorage {
    
    void add(UUID recordId);
    
    void addAll(List<UUID> recordIdList);
    
    void remove(UUID recordId);
    
    void clear();
    
    boolean contains(UUID recordId);
    
    boolean isEmpty();
    
    int getSize();

    List<UUID> getRecordIds();

    void setRecordIds(List<UUID> recordIds);
    
    
}
