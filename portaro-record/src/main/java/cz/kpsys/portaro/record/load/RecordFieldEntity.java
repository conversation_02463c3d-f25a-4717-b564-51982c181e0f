package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.date.DatetimeRange;
import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.PrioritizingComparator;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.validation.nullablenotempty.NullableNotEmpty;
import cz.kpsys.portaro.record.RecordIdentifier;
import cz.kpsys.portaro.record.detail.FieldTypes;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotBlank;
import lombok.NonNull;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static cz.kpsys.portaro.record.load.EntityFieldCodeMapper.dbCodeToCode;

public record RecordFieldEntity(

        @NonNull
        UUID id,

        @NonNull
        RecordIdentifier recordId,

        @NonNull
        @NotBlank
        String fieldCode,

        @NonNull
        Integer fieldIndex,

        @Nullable
        @NullableNotBlank
        String databaseSubfieldCode,

        @Nullable
        Integer subfieldIndex,

        @Nullable
        @NullableNotBlank
        String textValue,

        @Nullable
        RecordIdentifier targetRecordId,

        @Nullable
        @NullableNotEmpty
        String textSuffix,

        @NonNull
        Instant lastModificationDate,

        @Nullable
        Instant lastProcessDate,

        @Nullable
        String largeTextValue,

        @Nullable
        BigDecimal numericValue,

        @Nullable
        LocalDate dateValue,

        @Nullable
        Instant datetimeValue,

        @Nullable
        DateRange dateRangeValue,

        @Nullable
        DatetimeRange datetimeRangeValue,

        @Nullable
        Boolean booleanValue,

        boolean cacheInvalid,

        @Nullable
        RecordIdentifier originRecordId

) implements IdentifiedRecord<UUID> {

    public static final Comparator<RecordFieldEntity> COMPARATOR = Comparator.comparing(RecordFieldEntity::fieldCode, new PrioritizingComparator<>(List.of(FieldTypes.AUTHORITY_LEADER_FIELD_CODE, FieldTypes.DOCUMENT_LEADER_FIELD_CODE)).thenComparing(Comparator.naturalOrder()))
            .thenComparing(RecordFieldEntity::fieldIndex, Comparator.naturalOrder())
            .thenComparing(RecordFieldEntity::subfieldIndex, Comparator.nullsFirst(Comparator.naturalOrder()))
            .thenComparing(RecordFieldEntity::subfieldCode, Comparator.nullsFirst(new PrioritizingComparator<>(List.of(FieldTypes.IND_1_FIELD_CODE, FieldTypes.IND_2_FIELD_CODE)).thenComparing(Comparator.naturalOrder())));

    public boolean isRecordLink() {
        return targetRecordId != null && (!recordId.equals(targetRecordId));
    }

    public boolean isControlfield() {
        return databaseSubfieldCode == null || subfieldIndex == null;
    }

    public @NonNull String existingSubfieldCode() {
        return Objects.requireNonNull(subfieldCode());
    }

    public @Nullable String subfieldCode() {
        if (databaseSubfieldCode == null) {
            return null;
        }
        return dbCodeToCode(databaseSubfieldCode);
    }

    /// Case of simple authority which has stale data cached in textValue
    public boolean isStaleRecordLink() {
        return cacheInvalid && textValue != null && isRecordLink();
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equalsIdentified(this, o, RecordFieldEntity.class);
    }

    @Override
    public int hashCode() {
        return getId().hashCode();
    }

}
