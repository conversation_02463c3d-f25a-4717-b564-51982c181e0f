package cz.kpsys.portaro.record.holding;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.appserver.Appserver;
import cz.kpsys.portaro.appserver.XmlAppserverRequest;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.appserver.mapping.NoOpAppserverResponseHandler;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.record.operation.RecordOperation;
import cz.kpsys.portaro.record.operation.RecordOperationType;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.Instant;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AppserverRecordHoldingUpserter implements RecordHoldingUpserter {

    private static final String PATH = Appserver.APIPATH_RECORD_ADOPTION;

    @NonNull ObjectMapper xmlMapper;
    @NonNull MappingAppserverService mappingAppserver;
    @NonNull RecordHoldingLoader recordHoldingLoader;
    @NonNull ByIdLoadable<RecordOperationType, Integer> recordOperationTypeLoader;
    @NonNull Saver<RecordOperation, ?> recordOperationSaver;

    @Override
    public RecordHoldingUpsertResult upsert(@NonNull RecordHoldingUpsertCommand command) {
        Optional<RecordHolding> existing = recordHoldingLoader.getByRecordIdAndDepartment(command.record().getId(), command.department());
        if (existing.isEmpty()) {
            RecordOperationType adoptionOperationType = recordOperationTypeLoader.getById(RecordOperationType.ID_RECORD_ADOPTION);
            recordOperationSaver.save(new RecordOperation(UuidGenerator.forIdentifier(), null, command.record(), command.ctx(), command.currentAuth().getActiveUser(), adoptionOperationType, Instant.now()));
        }

        RecordHoldingUpsertAppserverRequest requestData = new RecordHoldingUpsertAppserverRequest(
                command.record().getId(),
                command.department().getId().longValue(),
                command.discarded()
        );
        XmlAppserverRequest request = XmlAppserverRequest.byPost(PATH, requestData, xmlMapper);

        mappingAppserver.call(request, NoOpAppserverResponseHandler.create());


        RecordHolding savedHolding = recordHoldingLoader.getByRecordIdAndDepartment(command.record().getId(), command.department()).orElseThrow();
        return new RecordHoldingUpsertResult(savedHolding);
    }
}
