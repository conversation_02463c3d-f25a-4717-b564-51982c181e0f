package cz.kpsys.portaro.record.deletion;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.appserver.Appserver;
import cz.kpsys.portaro.appserver.XmlAppserverRequest;
import cz.kpsys.portaro.appserver.handler.ConcreteErrorHandler;
import cz.kpsys.portaro.appserver.handler.HandlerFindingOrChainingAppserverErrorHandler;
import cz.kpsys.portaro.appserver.handler.ThrowingErrorHandler;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.appserver.mapping.NoOpAppserverResponseHandler;
import cz.kpsys.portaro.commons.cache.CacheDeletableById;
import cz.kpsys.portaro.commons.contextual.ContextualConsumer;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordStatus;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.toUnmodifiableSet;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AppserverRecordDeleter implements RecordDeleter {

    public static final String PATH = Appserver.APIPATH_SMAZANI_ZAZNAMU;
    public static final Map<Integer, ConcreteErrorHandler<RecordDeletionAppserverRequest, ?>> APPSERVER_ERROR_HANDLER_MAP = Map.of(
            1, new ThrowingErrorHandler<>("record.deletion.NelzeSmazatExistujiExemplare"),
            2, new ThrowingErrorHandler<>("record.deletion.NelzeSmazatVazbaPresZdrojovyDokument"),
            3, new ThrowingErrorHandler<>("record.deletion.NelzeSmazatJizSmazano"),
            4, new ThrowingErrorHandler<>("record.deletion.NelzeSmazatNedostatecnaPrava"),
            5, new ThrowingErrorHandler<>("record.deletion.NelzeSmazatZaznamObsahujeChranenaPole"),
            6, new ThrowingErrorHandler<>("record.deletion.OpravduSmazatZaznamMaRocniky"),
            7, new ThrowingErrorHandler<>("record.deletion.OpravduSmazatAktivniObjednavky"),
            8, new ThrowingErrorHandler<>("record.deletion.OpravduSmazatAktivniFaktury"),
            11, new ThrowingErrorHandler<>("record.deletion.JeVNevyrizeneMvsZrusit"),
            12, new ThrowingErrorHandler<>("record.deletion.BylOdeslanDoCaslinZrusit")
    );

    @NonNull Map<Integer, ConcreteErrorHandler<RecordDeletionAppserverRequest, ?>> errorHandlers = APPSERVER_ERROR_HANDLER_MAP;

    @NonNull MappingAppserverService mappingAppserver;
    @NonNull ContextualConsumer<RecordDeletionCommand, Department> subtreeDepartmentedRecordExemplarsDeleter;
    @NonNull ObjectMapper xmlMapper;
    @NonNull List<CacheDeletableById> cacheDeletablesByRecordId;


    @Override
    public void delete(@NonNull RecordDeletionCommand command) {
        if (command.record().isDeleted()) {
           throw new IllegalStateException("Can not delete already deleted record");
        }
        Record record = command.record();

        subtreeDepartmentedRecordExemplarsDeleter.acceptOn(command, command.ctx());

        RecordDeletionAppserverRequest appserverRequest = new RecordDeletionAppserverRequest(
                record.getId(),
                command.holdingDepartments().stream().map(Identified::getId).collect(toUnmodifiableSet()),
                RecordStatus.DELETED.getId(),
                ObjectUtil.firstNotNull(command.withMvsCancelling(), false),
                ObjectUtil.firstNotNull(command.withCaslinCancelling(), false)
        );
        XmlAppserverRequest request = XmlAppserverRequest.byPost(PATH, appserverRequest, xmlMapper);

        mappingAppserver.call(
                request,
                NoOpAppserverResponseHandler.create(),
                new HandlerFindingOrChainingAppserverErrorHandler<>(appserverRequest, errorHandlers)
        );

        //smazeme zaznam z cache
        cacheDeletablesByRecordId.forEach(cacheDeletableById -> cacheDeletableById.deleteFromCacheById(record.getId()));
    }
    
}
