package cz.kpsys.portaro.record.detail.link;

import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;

import java.util.Objects;

public record LookupDefinition(

        @NonNull
        LinkFieldSpec linkFieldSpec,

        @NonNull
        LinkedFieldSpec linkedFieldSpec

) {

    public static LookupDefinition ofSelfLink(@NonNull String customEntrySubfieldCode) {
        return new LookupDefinition(LinkFieldSpec.ofSelfLink(), LinkedFieldSpec.ofCustomEntrySubfield(customEntrySubfieldCode));
    }

    public static LookupDefinition ofVirtualGroupToCustomEntrySubfield(@NonNull FieldTypeId virtualGroupTypeId, @NonNull String customEntrySubfieldCode) {
        return new LookupDefinition(LinkFieldSpec.ofVirtualGroupField(virtualGroupTypeId), LinkedFieldSpec.ofCustomEntrySubfield(customEntrySubfieldCode));
    }

    public static LookupDefinition ofSelfLinkNativeName() {
        return new LookupDefinition(LinkFieldSpec.ofSelfLink(), LinkedFieldSpec.ofNativeName());
    }

    public static LookupDefinition ofSpecificFieldLink(@NonNull FieldTypeId linkFieldTypeId, @NonNull FieldTypeId linkedRecordFieldTypeId) {
        return new LookupDefinition(LinkFieldSpec.ofSpecificFieldLink(linkFieldTypeId), LinkedFieldSpec.ofSpecific(linkedRecordFieldTypeId));
    }

    public static LookupDefinition ofSelfRecord(@NonNull FieldTypeId linkedFieldTypeId) {
        return new LookupDefinition(LinkFieldSpec.ofSelfRecord(), LinkedFieldSpec.ofSpecific(linkedFieldTypeId));
    }

    public static LookupDefinition ofSelfRecordNativeName() {
        return new LookupDefinition(LinkFieldSpec.ofSelfRecord(), LinkedFieldSpec.ofNativeName());
    }

    public boolean isSelfLinkOfSelfRecord() {
        return linkFieldSpec.linkFieldSearchMode() == LinkFieldTargetSearchMode.SELF_LINK || linkFieldSpec.linkFieldSearchMode() == LinkFieldTargetSearchMode.SELF_RECORD;
    }

    public boolean isSelfRecord() {
        return linkFieldSpec.linkFieldSearchMode() == LinkFieldTargetSearchMode.SELF_RECORD;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof LookupDefinition that)) {
            return false;
        }
        return linkFieldSpec.equals(that.linkFieldSpec) && Objects.equals(linkedFieldSpec, that.linkedFieldSpec);
    }

    @Override
    public int hashCode() {
        int result = linkFieldSpec.hashCode();
        result = 31 * result + Objects.hashCode(linkedFieldSpec);
        return result;
    }

    @Override
    public String toString() {
        return "{" + linkFieldSpec + "}:" + linkedFieldSpec;
    }
}
