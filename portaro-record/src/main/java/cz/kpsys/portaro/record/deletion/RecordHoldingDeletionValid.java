package cz.kpsys.portaro.record.deletion;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({TYPE})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = RecordHoldingDeletionValidator.class)
public @interface RecordHoldingDeletionValid {

    String message() default "{jakarta.validation.constraints.RecordHoldingDeletionValid.message}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};


    @Target({ElementType.FIELD})
    @Retention(RUNTIME)
    @Documented
    @interface PotentiallyRequired {}
}
