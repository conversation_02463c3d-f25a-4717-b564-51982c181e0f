package cz.kpsys.portaro.record.search;

import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.lucene.StringQueryBuilder;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class AppserverLuceneSearchService implements SearchService<InternalSearchResult<String, MapBackedParams, RangePaging>, MapBackedParams> {

    @NonNull AppserverSearchEngine appserverSearchEngine;
    @NonNull SearchResponseToSearchResultConverter responseToSearchResultConverter;
    @NonNull StringQueryBuilder<MapBackedParams> queryBuilder;

    @Override
    public InternalSearchResult<String, MapBackedParams, RangePaging> search(MapBackedParams completeParams, Range range, SortingItem sorting, Department ctx, CacheMode cacheMode) {
        String query = queryBuilder.buildFinalQuery(completeParams, ctx);
        AppserverSearchResponse response = appserverSearchEngine.search(query, completeParams, range, sorting, ctx);
        return responseToSearchResultConverter.mapSearchResult(response, query, range);
    }
}
