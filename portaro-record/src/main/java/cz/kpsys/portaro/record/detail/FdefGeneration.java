package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum FdefGeneration implements Identified<String> {

    /// User defined field. Storage both in RECORD_FIELD and KAT1_1.
    PHYSICAL("physical"),

    /// Field in DB, containing link to another record. Does not contain value.
    PHYSICAL_LINK_NO_VALUE("physical_lnk_no_val"),

    /// Field in DB, containing link to another record. Value is generated.
    PHYSICAL_LINK_GENERATED_VALUE("physical_lnk_gen_val"),

    /// Dynamically computed field. Cached only.
    GENERATED("generated"),

    /// Parent of generated field.
    GENERATED_FIELD_PARENT("generated_parent"),

    /// Field initialized by computation but then persisted and behaving like {@link #PHYSICAL}.
    GENERATED_PHYSICAL("generated_physical"),

    /// Dynamically computed field. Never persisted in any way. Exists only in memory.
    GENERATED_NOPERSIST("generated_nopersist"),

    /// Bound fields. Never persisted.
    BOUND("internal_bound"),

    /// Indicator fields. They exist as standalone fields only in RECORD_FIELD.
    @Deprecated
    INDICATORS("internal_indicators"),
    ;

    @NonNull String id;

    public static final Codebook<FdefGeneration, String> CODEBOOK = new StaticCodebook<>(values());

}
