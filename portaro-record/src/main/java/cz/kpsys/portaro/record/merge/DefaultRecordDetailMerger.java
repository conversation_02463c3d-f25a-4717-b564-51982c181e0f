package cz.kpsys.portaro.record.merge;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.By;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldHelper;
import cz.kpsys.portaro.record.detail.TransferType;
import cz.kpsys.portaro.record.edit.EmptyFieldCreation;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.holding.RecordHolding;
import cz.kpsys.portaro.record.holding.RecordHoldingLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;


@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class DefaultRecordDetailMerger implements RecordDetailMerger {

    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordHoldingLoader recordHoldingLoader;

    @Override
    public void merge(Record target, Record source, Department ctx, UserAuthentication currentAuth) {
        try {
            var sourceHoldings = recordHoldingLoader.getAllByRecordId(source.getId()).stream().map(RecordHolding::getDepartment).toList();
            var targetHoldings = recordHoldingLoader.getAllByRecordId(target.getId()).stream().map(RecordHolding::getDepartment).toList();

            var mergedHoldings = new HashSet<>(sourceHoldings);
            mergedHoldings.addAll(targetHoldings);

            RecordEditation editation = recordEditationFactory
                    .withHoldingsWithoutCtx(new ArrayList<>(mergedHoldings), ctx)
                    .ofExisting(target)
                    .build(currentAuth);

            removeFieldsThatWillBeRewritten(editation, source);
            importFields(editation, source);
            removeFieldsThatWereNotOverwritten(editation, source);

            editation.saveIfModified(ctx, currentAuth);

        } catch (Exception e) {
            throw new RecordMergeException(target, source, e);
        }
    }

    /**
     * remove fields that will be rewritten
     */
    private void removeFieldsThatWillBeRewritten(RecordEditation editation, Record source) {
        editation.streamFields()
                .filter(topfield -> {
                    TransferType transferType = topfield.getType().getTransferType();
                    return transferType.equals(TransferType.OVERWRITE);
                })
                .forEach(overwritableTopfield -> {
                    List<Field<?>> sourceOverwrittableTopfields = source.getFields(overwritableTopfield.getCode());
                    if (sourceOverwrittableTopfields.stream().anyMatch(Field::hasAutonomousValue)) {
                        overwritableTopfield.removeAll(overwritableTopfield.getFields());
                    }
                });
    }

     /**
      * import fields from source to target according to transfer types
      */
    private void importFields(RecordEditation editation, Record source) {
        source.streamFields()
                .forEach(sourceTopfield -> {
                    TransferType transferType = sourceTopfield.getType().getTransferType(); // source and target should have same transfer types
                    switch (transferType) {
                        case OVERWRITE -> overwriteField(editation, sourceTopfield);
                        case ADD -> addField(editation, sourceTopfield);
                    }
                });
    }

    /**
     * if it is set, remove fields that are empty in source
     */
    private void removeFieldsThatWereNotOverwritten(RecordEditation editation, Record source) {
        editation.streamFields()
                .filter(targetTopfield -> {
                    TransferType transferType = targetTopfield.getType().getTransferType();
                    boolean shouldBeDeletedWhenSourceFieldIsEmpty = transferType.equals(TransferType.DELETE_WHEN_EMPTY);
                    if (shouldBeDeletedWhenSourceFieldIsEmpty) {
                        return true;
                    }
                    boolean allSourceFieldsAreEmpty = source.getDetail().streamFields(By.type(targetTopfield.getType())).allMatch(Field::isEmpty);
                    return allSourceFieldsAreEmpty;
                })
                .forEach(Field::deletePayload);
    }

    private void overwriteField(RecordEditation editation, Field<?> sourceTopfield) {
        List<Field<?>> sameNumberTargetTopfields = editation.getFields(By.type(sourceTopfield.getType())); // Topfields in target with same number

        Field<?> targetTopfield;
        if (sameNumberTargetTopfields.isEmpty()) { // Topfield with same number does not exist => create new Topfield
            targetTopfield = editation.createField(EmptyFieldCreation.toEnd(sourceTopfield.getType().getFieldTypeId()));
        } else { // Find field with same repetition or create it
            var fieldWithThisIndex = sameNumberTargetTopfields.stream()
                    .filter(sameNumberTargetTopfield -> sameNumberTargetTopfield.getRepetition() == sourceTopfield.getRepetition())
                    .findAny();
            if (fieldWithThisIndex.isPresent()) {
                targetTopfield = fieldWithThisIndex.get();
            } else {
                targetTopfield = editation.createField(EmptyFieldCreation.to(sourceTopfield.getType().getFieldTypeId(), sourceTopfield.getRepetition()));
            }
        }

        editation.copyTopfield(targetTopfield, sourceTopfield);
    }

    private void addField(RecordEditation editation, Field<?> sourceTopfield) {
        List<Field<?>> sameNumberTargetTopfields = editation.getFields(By.type(sourceTopfield.getType()));

        if (!FieldHelper.anyFieldContainsSameValue(sameNumberTargetTopfields, sourceTopfield)) {
            Field<?> targetTopfield = editation.createField(EmptyFieldCreation.toEnd(sourceTopfield.getType().getFieldTypeId()));
            editation.copyTopfield(targetTopfield, sourceTopfield);
        }
    }
}
