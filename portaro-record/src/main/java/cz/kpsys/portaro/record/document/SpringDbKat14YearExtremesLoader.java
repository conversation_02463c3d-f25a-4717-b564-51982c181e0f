package cz.kpsys.portaro.record.document;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.databasestructure.RecordDb.KAT1_4;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.Range;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.Calendar;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.RecordDb.RECORD;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class SpringDbKat14YearExtremesLoader implements Provider<Range<Integer>> {

    private static final String MIN_YEAR = "MIN_YEAR";
    private static final String MAX_YEAR = "MAX_YEAR";

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull RowMapper<Range<Integer>> rangeRowMapper = (rs, rowNum) -> Range.between(rs.getInt(MIN_YEAR), rs.getInt(MAX_YEAR));


    @Override
    public Range<Integer> get() {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(
                AS(MIN(TC(KAT1_4.TABLE, KAT1_4.ROK_OD)), MIN_YEAR),
                AS(MAX(TC(KAT1_4.TABLE, KAT1_4.ROK_DO)), MAX_YEAR)
        );
        sq.from(KAT1_4.TABLE);
        sq.joins().add(RECORD.TABLE, COLSEQ(TC(KAT1_4.TABLE, KAT1_4.RECORD_ID), TC(RECORD.TABLE, RECORD.ID)));
        sq.where()
                .and().ltEq(KAT1_4.ROK_DO, Calendar.getInstance().get(Calendar.YEAR))
                .and().isNull(TC(RECORD.TABLE, RECORD.DELETION_EVENT_ID))
                .and().isNotNull(TC(RECORD.TABLE, RECORD.ACTIVATION_EVENT_ID));

        return jdbcTemplate.queryForObject(sq.getSql(), sq.getParamMap(), rangeRowMapper);
    }


}
