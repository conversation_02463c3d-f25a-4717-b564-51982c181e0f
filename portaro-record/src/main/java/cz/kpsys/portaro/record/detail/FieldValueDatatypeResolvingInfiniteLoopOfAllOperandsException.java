package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.record.detail.fn.Formula;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.Map;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class FieldValueDatatypeResolvingInfiniteLoopOfAllOperandsException extends RuntimeException {

    public FieldValueDatatypeResolvingInfiniteLoopOfAllOperandsException(Map<? extends Formula<?>, FieldValueDatatypeResolvingInfiniteLoopException> infiniteLoops) {
        super("All operands of formula creates infinite loop while datatype resolving: " + generateLoopReport(infiniteLoops));
    }

    private static @NonNull String generateLoopReport(Map<? extends Formula<?>, FieldValueDatatypeResolvingInfiniteLoopException> infiniteLoops) {
        return infiniteLoops.entrySet().stream()
                .map(entry -> entry.getKey() + " -> " + entry.getValue().getMessage())
                .collect(Collectors.joining(", "));
    }
}
