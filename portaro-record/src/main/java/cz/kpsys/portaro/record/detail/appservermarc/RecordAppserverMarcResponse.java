package cz.kpsys.portaro.record.detail.appservermarc;

import lombok.NonNull;
import lombok.Value;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Value
public class RecordAppserverMarcResponse {

    @NonNull
    UUID id;

    @Nullable
    Integer fondId;

    @NonNull
    LeaderAppserverMarcResponse leader;

    @NonNull
    List<ControlfieldAppserverMarcResponse> controlfields;

    @NonNull
    List<DatafieldAppserverMarcResponse> datafields;

    @Nullable
    TocAppserverMarcResponse toc;

    public Optional<Integer> getFondId() {
        return Optional.ofNullable(fondId);
    }

    public Optional<TocAppserverMarcResponse> getToc() {
        return Optional.ofNullable(toc);
    }
}
