package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.detail.value.StringFieldValue;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import lombok.NonNull;

import java.util.List;

public class FieldFactory {

    public static @NonNull Field<StringFieldValue> scalar(@NonNull RecordFieldId recordFieldId, @NonNull EditableFieldType<?> fieldType, @NonNull StringFieldValue fieldValueHolder) {
        return new Field<>(
                UuidGenerator.forIdentifier(),
                recordFieldId,
                fieldType,
                Field.NO_LINK,
                Field.NO_SUFFIX,
                fieldValueHolder,
                Field.noSubfields()
        );
    }

    public static @NonNull Field<ScalarFieldValue<?>> subfielded(@NonNull RecordFieldId recordFieldId, @NonNull EditableFieldType<?> fieldType, @NonNull List<@NonNull Field<?>> fields) {
        return new Field<>(
                UuidGenerator.forIdentifier(),
                recordFieldId,
                fieldType,
                Field.NO_LINK,
                Field.NO_SUFFIX,
                Field.noValue(),
                fields
        );
    }

    public static @NonNull Field<ScalarFieldValue<?>> linked(@NonNull RecordFieldId recordFieldId, @NonNull EditableFieldType<?> fieldType, @NonNull RecordIdFondPair recordLink) {
        return new Field<>(
                UuidGenerator.forIdentifier(),
                recordFieldId,
                fieldType,
                recordLink,
                Field.NO_SUFFIX,
                Field.noValue(),
                Field.noSubfields()
        );
    }
}
