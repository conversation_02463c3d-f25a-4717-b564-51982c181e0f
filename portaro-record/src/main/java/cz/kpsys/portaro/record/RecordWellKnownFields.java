package cz.kpsys.portaro.record;

import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.FieldTypes;

public class RecordWellKnownFields {

    public static class DocumentControlNumber {
        public static final Integer NUMBER = 1;
        public static final String CODE = "d1";
        public static final String QUERY_CODE = CODE;
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);
    }

    public static class AuthorityControlNumber {
        public static final Integer NUMBER = 1;
        public static final String CODE = "a1";
        public static final String QUERY_CODE = CODE;
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);
    }

    public static class DocumentControlNumberIdentifier {
        public static final Integer NUMBER = 3;
        public static final String CODE = "d3";
        public static final String QUERY_CODE = CODE;
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);
    }

    public static class AuthorityControlNumberIdentifier {
        public static final Integer NUMBER = 3;
        public static final String CODE = "a3";
        public static final String QUERY_CODE = CODE;
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);
    }

    public static class DocumentPhysicalDescription {
        public static final String CODE = "d7";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);
    }

    public static class DocumentTitle {
        public static final String CODE = "d245";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Name {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentTitle.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentTitle.TYPE_ID, CODE);
            public static final FieldTypeId CONFIG_TYPE_ID = TYPE_ID;
        }

        public static class ResponsibilityStatement {
            public static final String CODE = "c";
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentTitle.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentTitle.TYPE_ID, CODE);
        }
    }

    public static class DocumentMainAuthor {
        public static final String CODE = "d100";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static final String ABC_QUERY_CODE = FieldTypeId.delim(CODE, "[a,b,c]");

        public static class Main {
            public static final String CODE = FieldTypes.VIRTUAL_GROUP_FIELD_CODE;
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentMainAuthor.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentMainAuthor.TYPE_ID, CODE);

            public static class Name {
                public static final String CODE = "a";
                public static final String QUERY_CODE = FieldTypeId.delim(Main.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Main.TYPE_ID, CODE);
            }

            public static class RomanNumber {
                public static final String CODE = "b";
                public static final String QUERY_CODE = FieldTypeId.delim(Main.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Main.TYPE_ID, CODE);
            }
        }
    }

    public static class DocumentOtherAuthor {
        public static final String CODE = "d700";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Main {
            public static final String CODE = FieldTypes.VIRTUAL_GROUP_FIELD_CODE;
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentOtherAuthor.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentOtherAuthor.TYPE_ID, CODE);

            public static class Name {
                public static final String CODE = "a";
                public static final String QUERY_CODE = FieldTypeId.delim(Main.CODE, CODE);
                public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Main.TYPE_ID, CODE);
            }
        }
    }

    public static class DocumentCcnb {
        public static final String CODE = "d15";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Value {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentCcnb.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentCcnb.TYPE_ID, CODE);
        }
    }

    public static class DocumentIsbn {
        public static final String CODE = "d20";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Value {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentIsbn.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentIsbn.TYPE_ID, CODE);
        }

        public static class CancelledValue {
            public static final String CODE = "z";
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentIsbn.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentIsbn.TYPE_ID, CODE);
        }
    }

    public static class DocumentIssn {
        public static final String CODE = "d22";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Value {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentIssn.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentIssn.TYPE_ID, CODE);
        }

        public static class CancelledValue {
            public static final String CODE = "z";
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentIssn.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentIssn.TYPE_ID, CODE);
        }
    }

    public static class DocumentEan {
        public static final String CODE = "d24";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Value {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentEan.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentEan.TYPE_ID, CODE);
        }

        public static class Source {
            public static final String CODE = "2";
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentEan.TYPE_ID, CODE);
        }
    }

    public static class DocumentSystemControlNumber {
        public static final String CODE = "d35";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Value {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentSystemControlNumber.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentSystemControlNumber.TYPE_ID, CODE);
        }
    }

    public static class DocumentCatalogingSource {
        public static final String CODE = "d40";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Value {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentCatalogingSource.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentCatalogingSource.TYPE_ID, CODE);
        }
    }

    public static class DocumentPublicationOld {
        public static final String CODE = "d260";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Place {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentPublicationOld.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentPublicationOld.TYPE_ID, CODE);
        }

        public static class Publisher {
            public static final String CODE = "b";
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentPublicationOld.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentPublicationOld.TYPE_ID, CODE);
        }

        public static class Year {
            public static final String CODE = "c";
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentPublicationOld.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentPublicationOld.TYPE_ID, CODE);
        }
    }

    public static class DocumentPublication {
        public static final String CODE = "d264";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Place {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentPublication.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentPublication.TYPE_ID, CODE);
        }

        public static class Publisher {
            public static final String CODE = "b";
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentPublication.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentPublication.TYPE_ID, CODE);
        }

        public static class Year {
            public static final String CODE = "c";
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentPublication.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentPublication.TYPE_ID, CODE);
        }
    }

    public static class DocumentEdition {
        public static final String CODE = "d250";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Edition {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(DocumentEdition.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(DocumentEdition.TYPE_ID, CODE);
        }
    }

    public static class PhysicalDescription {
        public static final String CODE = "d300";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class NumberOfPages {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(PhysicalDescription.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(PhysicalDescription.TYPE_ID, CODE);
        }
    }

    public static class OtherRelationship {
        public static final String CODE = "d787";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class RelatedParts {
            public static final String CODE = "g";
            public static final String QUERY_CODE = FieldTypeId.delim(OtherRelationship.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(OtherRelationship.TYPE_ID, CODE);
        }
    }

    public static class Links {
        public static final String CODE = "d856";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class UrlAddress {
            public static final String CODE = "u";
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Links.TYPE_ID, CODE);
        }

        public static class Description {
            public static final String CODE = "y";
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(Links.TYPE_ID, CODE);
        }
    }

    public static class OurExternalLoanLink {
        public static final String CODE = "d1856";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class UrlAddress {
            public static final String CODE = "u";
            public static final String QUERY_CODE = FieldTypeId.delim(OurExternalLoanLink.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(OurExternalLoanLink.TYPE_ID, CODE);
        }
    }

    public static class LocationStatements {
        public static final String CODE = "d910";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Sigla {
            public static final String CODE = "a";
        }

        public static class Signature {
            public static final String CODE = "b";
        }

        public static class SignatureOfNonLinkedPeriodicals {
            public static final String CODE = "c";
        }
    }

    public static class F911 {
        public static final String CODE = "d911";

        public static class UrlAddress {
            public static final String CODE = "u";
        }

        public static class Description {
            public static final String CODE = "y";
        }
    }

    public static class CpkExemplar {
        public static final String CODE = "d996";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);
    }

    public static class CpkRecordId {
        public static final String CODE = "d4001";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Value {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(CpkRecordId.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(CpkRecordId.TYPE_ID, CODE);
        }
    }

    public static class AuthorityPersonName {
        public static final String CODE = "a100";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static final String ABCD_QUERY_CODE = FieldTypeId.delim(CODE, "[a,b,c,d]");

        public static class Name {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(AuthorityPersonName.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(AuthorityPersonName.TYPE_ID, CODE);
            public static final FieldTypeId CONFIG_TYPE_ID = TYPE_ID;
        }
    }

    public static class AuthorityCorporationName {
        public static final String CODE = "a110";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Name {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(AuthorityPersonName.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(AuthorityPersonName.TYPE_ID, CODE);
            public static final FieldTypeId CONFIG_TYPE_ID = TYPE_ID;
        }
    }

    /// https://www.loc.gov/marc/authority/ad400.html
    public static class AuthorityPersonAlternativeName {
        public static final String CODE = "a400";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Value {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(AuthorityPersonAlternativeName.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(AuthorityPersonAlternativeName.TYPE_ID, CODE);
        }
    }

    /// https://www.loc.gov/marc/authority/ad450.html
    public static class AuthorityTopicAlternativeName {
        public static final String CODE = "a450";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Value {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(AuthorityTopicAlternativeName.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(AuthorityTopicAlternativeName.TYPE_ID, CODE);
        }
    }

    public static class AuthorityHeadingTopicalTerm {
        public static final String CODE = "a150";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Name {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(AuthorityHeadingTopicalTerm.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(AuthorityHeadingTopicalTerm.TYPE_ID, CODE);
        }
    }

    public static class ProvenioMaybeSpecialField670 {
        public static final String CODE = "d670";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Value {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(ProvenioMaybeSpecialField670.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ProvenioMaybeSpecialField670.TYPE_ID, CODE);
        }

        public static class UrlAddress {
            public static final String CODE = "u";
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(ProvenioMaybeSpecialField670.TYPE_ID, CODE);
        }
    }

    public static class MzluDocumentProjectImageName {
        public static final String CODE = "d1995";
        public static final FieldTypeId TYPE_ID = FieldTypeId.top(CODE);

        public static class Value {
            public static final String CODE = "a";
            public static final String QUERY_CODE = FieldTypeId.delim(MzluDocumentProjectImageName.CODE, CODE);
            public static final FieldTypeId TYPE_ID = FieldTypeId.subfield(MzluDocumentProjectImageName.TYPE_ID, CODE);
        }
    }

}
