package cz.kpsys.portaro.record.document;

import cz.kpsys.portaro.commons.web.Link;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.view.DownloadLinkContextualResolver;
import cz.kpsys.portaro.record.view.DownloadLinkResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ContextIgnoringDownloadLinkContextualResolver<CTX> implements DownloadLinkContextualResolver<CTX> {

    @NonNull DownloadLinkResolver resolver;

    @Override
    public List<Link> resolveLinks(Record record, CTX ignored) {
        return resolver.resolveLinks(record);
    }
}
