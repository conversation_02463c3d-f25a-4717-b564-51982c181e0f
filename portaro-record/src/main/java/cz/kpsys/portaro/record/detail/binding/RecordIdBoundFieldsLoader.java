package cz.kpsys.portaro.record.detail.binding;

import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.detail.value.StringFieldValue;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordIdBoundFieldsLoader implements Function<Set<RecordIdFondPair>, Map<RecordIdFondPair, ? extends ScalarFieldValue<?>>> {

    @Override
    public Map<RecordIdFondPair, StringFieldValue> apply(@NonNull Set<RecordIdFondPair> recordIdentifiers) {
        Map<RecordIdFondPair, StringFieldValue> recordIdFondPairBindingMap = new HashMap<>();
        for (RecordIdFondPair bindingToLoadRecord : recordIdentifiers) {
            recordIdFondPairBindingMap.put(bindingToLoadRecord, StringFieldValue.of(bindingToLoadRecord.id().id().toString(), Set.of(bindingToLoadRecord)));
        }
        return recordIdFondPairBindingMap;
    }
}
