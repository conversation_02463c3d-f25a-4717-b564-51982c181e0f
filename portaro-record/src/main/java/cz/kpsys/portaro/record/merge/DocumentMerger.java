package cz.kpsys.portaro.record.merge;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import lombok.NonNull;

import java.util.List;

public interface DocumentMerger {

    void merge(@NonNull Record target, @NonNull List<Record> sources, @NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth);

}
