package cz.kpsys.portaro.record.print;

import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.object.Labeled;
import cz.kpsys.portaro.commons.util.StringEscapeUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.query.ValFieldGroup;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

import java.util.Locale;
import java.util.UUID;

import static cz.kpsys.portaro.record.RecordWellKnownFields.ProvenioMaybeSpecialField670;

public class RecordDetailPlainHtmlPrinter extends RecordDetailAbstractPrinter {

    public static final Converter<UUID, String> NO_BASE_URL_CONVERTER = id -> "/#!/records/" + id;

    private Converter<UUID, String> recordIdToDetailUrlConverter = NO_BASE_URL_CONVERTER;

    public RecordDetailPlainHtmlPrinter(Translator<Department> translator, Department currentDepartment, Locale locale, @NonNull Converter<UUID, String> recordIdToDetailUrlConverter) {
        super(translator, currentDepartment, locale);
        this.recordIdToDetailUrlConverter = recordIdToDetailUrlConverter;
    }


    /**
     * Pro vypsani textu ktere nemame v planu lokalizovat.
     */
    public RecordDetailPlainHtmlPrinter() {
        super();
    }


    public static String printGroup(ValFieldGroup group) {
        return new RecordDetailPlainHtmlPrinter().group(group);
    }



    /**
     * Slouzi k uprave vypisovaneho textu podpoli.
     * Ve vychozim stavu vypisuje field.getText().
     * <br/>
     * Metoda je pro eventuelni prepsani.
     */
    protected String getFieldText(Labeled field) {
        return localize(field.getText());
    }




    /* IMPLEMENTACE HLAVNICH METOD */

    @Override
    public String writeRecordLinkSubfield(ViewableFieldable sf) {
        if (!sf.hasRecordLink()) {
            throw new IllegalStateException(String.format("Cannot write record subfield %s when subfield is not of record", sf));
        }
        UUID recordId = switch (sf) {
            case Field<?> field -> {
                if (field.hasRecordLink()) {
                    yield field.getExistingRecordLink().id().id();
                }
                yield null;
            }
            case ViewableField viewableField -> viewableField.getRecordId();
            case ValFieldGroup singleValFieldGroups -> singleValFieldGroups.getRecordId();
            default -> throw new UnsupportedOperationException(String.format("Fieldable %s is not Valuable nor ViewableField nor ValFieldGroup", sf));
        };
        if (recordId == null) {
            return getFieldText(sf);
        }
        String url = recordIdToDetailUrlConverter.convert(recordId);
        return writeLink(url, getFieldText(sf), false);
    }


    @Override
    public String writeUrlSubfield(@NonNull ViewableFieldable sf, @NonNull ViewableFieldContainer originalDetail) {
        String textOdkazu = getTextLinku(sf, originalDetail);
        return writeLink(sf.getRaw(), textOdkazu, true);
    }


    @Override
    public String writeNormalField(ViewableFieldable sf, @NonNull ViewableFieldContainer originalDetail) {
        if (isNormalFieldWritable(sf, originalDetail)) {
            return getFieldText(sf);
        }
        return "";
    }


    /**
     * Pripoji k builderu dane url jako odkaz.
     * @param newPage zda se ma odkaz otevrit na nove strance (pripoji target blank)
     */
    public String writeLink(String url, String name, boolean newPage) {
        String s = "<a href=\"" + url + "\"";
        if (newPage) {
            s += " target=\"_blank\"";
        }
        return s + ">" + escape(name) + "</a>";
    }







    /**
     * Vrati, zda se ma podpole vubec vypsat. <br/>
     * Napr. v situaci, kdy se vypise podpole 670u jako odkaz a jako text <br/>
     * odkazu si vezme 670a. Potom uz se 670a po druhe nema vypsat.
     */
    private boolean isNormalFieldWritable(ViewableFieldable field, @NonNull ViewableFieldContainer originalDetail) {
        if (field.getFieldId().isRoot()) {
            return true;
        }
        return !field.getFieldId().existingParent().getCode().equals(ProvenioMaybeSpecialField670.CODE)
                || !field.getFieldId().getCode().equals(ProvenioMaybeSpecialField670.Value.CODE)
                || originalDetail.getFields(By.siblingOfType(field.getFieldId(), ProvenioMaybeSpecialField670.UrlAddress.TYPE_ID)).isEmpty();
    }


    protected static String escape(String s) {
        return StringEscapeUtil.escapeHtml4(s);
    }


    /**
     * Pro dane podpole, ktere je url, vrati nazev odkazu, ktery se ma zobrazit. <br/>
     * Kdyz nenajde vhodne podpole pro odkaz, vrati primo url. Takze by ani nemel nikdy vracet null.
     */
    private String getTextLinku(ViewableFieldable podpoleSUrl, @NonNull ViewableFieldContainer viewableDetail) {
        FieldId parentFieldId = podpoleSUrl.getFieldId().existingParent();
        ViewableField parent = viewableDetail.getFirstField(By.fieldId(parentFieldId)).orElseThrow();

        Labeled podpoleProTextOdkazu = null;
        if (parent.getCode().equals(ProvenioMaybeSpecialField670.CODE)) { //pro pole 670 zobraz jako text podpole a (STT)
            podpoleProTextOdkazu = parent.getFirstFieldByCode(ProvenioMaybeSpecialField670.Value.CODE).orElse(null);
        }

        //porad jsme nenasli vhodne podpole pro text odkazu -> jako text zobrazime podpole $y (to je jakesi vychozi univerzalni podpole pro text jakychkoliv odkazu)
        if (podpoleProTextOdkazu == null) {
            podpoleProTextOdkazu = parent.getFirstFieldByCode("y").orElse(null);
        }

        //porad jsme nenasli vhodne podpole pro text odkazu -> jako text zobrazime primo url
        if (podpoleProTextOdkazu == null) {
            podpoleProTextOdkazu = podpoleSUrl;
        }

        return getFieldText(podpoleProTextOdkazu);
    }
}
