package cz.kpsys.portaro.record.document;

import cz.kpsys.portaro.record.Record;

import java.util.Optional;
import java.util.UUID;

public interface AuthoritySourceDocumentProvider {

    /**
     * Je-li autorita zastresujici zdrojovy dokument, vrati dany zdrojovy dokument. <br/>
     * <PERSON>ak vrati null
     * @param a Zdrojova autorita (autorita, ktera jakoby zastupuje zdrojovy dokument)
     */
    Optional<UUID> getAuthoritySourceDocumentId(Record a);
}
