package cz.kpsys.portaro.record.file.cover;

import cz.kpsys.portaro.commons.util.PeriodWaiter;
import cz.kpsys.portaro.commons.util.RegExpPatterns;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.LoadedFile;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.extract.RecordNormalizedIsbnExtractor;
import cz.kpsys.portaro.record.extract.RecordValueExtractor;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestOperations;

import java.time.Duration;
import java.util.StringTokenizer;

import static cz.kpsys.portaro.record.Record.TYPE_DOCUMENT;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class GoogleBooksCoverLoader implements NamedCoverLoader {

    public static final String GOOGLE_BOOKS_API_URL = "https://www.googleapis.com/books/v1/volumes?q=isbn:{ISBN}";
    public static final String SERVICE_NAME = "GoogleBooks";
    private static final Duration PERIODA_MEZI_NACTENIMI = Duration.ofSeconds(10);

    @NonNull RestOperations rest;
    @NonNull CoverDownloader coverDownloader;
    @NonNull PeriodWaiter periodWaiter = new PeriodWaiter(PERIODA_MEZI_NACTENIMI);
    @NonNull RecordValueExtractor recordNormalizedIsbnExtractor = new RecordNormalizedIsbnExtractor();
    @Getter @NonNull String serviceName = SERVICE_NAME;

    @Override
    public LoadedFile getCover(Record record, Department currentDepartment) throws CoverSearchException {
        if (!record.getType().equals(TYPE_DOCUMENT)) {
            throw new CoverSearchException(getServiceName(), CoverSearchException.NOT_A_DOCUMENT);
        }

        var isbns = recordNormalizedIsbnExtractor.extractValue(record).toList();
        if (isbns.isEmpty()) {
            throw new CoverSearchException(getServiceName(), CoverSearchException.CHYBEJICI_ISBN);
        }

        CoverSearchException thrownException = null;

        for (var isbn : isbns) {
            try {
                return tryGetCover(record, getCoverUrl(isbn));
            } catch (CoverSearchException e) {
                thrownException = e;
            }
        }

        throw thrownException;
    }

    private LoadedFile tryGetCover(Record record, String url) throws CoverSearchException {
        return coverDownloader.getCoverFromUrl(record, getServiceName(), url);
    }

    private synchronized String getCoverUrl(String isbn) throws CoverSearchException {
        periodWaiter.sleepUntilNextTick();

        String jsonString;

        try {
            jsonString = rest.getForObject(GOOGLE_BOOKS_API_URL, String.class, isbn);
        } catch (HttpClientErrorException e) {
            if (e.getStatusCode().value() == HttpStatus.FORBIDDEN.value()) {
                throw new CoverSearchException(getServiceName(), CoverSearchException.DOCASNE_ZABLOKOVANO, e);
            }
            throw e;
        }

        String url = findUrlInJson(jsonString);
        if (url != null) {
            return url;
        }

        if (jsonString != null && jsonString.contains("\"code\": 403")) {
            throw new CoverSearchException(getServiceName(), CoverSearchException.DOCASNE_ZABLOKOVANO);
        }

        throw new CoverSearchException(getServiceName(), CoverSearchException.CHYBEJICI_OBALKA);
    }

    private String findUrlInJson(String jsonString) {
        //vyhledani url obrazku v JSONu
        StringTokenizer tokenizer = new StringTokenizer(jsonString, "\"");
        while (tokenizer.hasMoreTokens()) {
            if (tokenizer.nextToken().equals("thumbnail")) {
                tokenizer.nextToken(); //dvojtecka mezi jmenem a hodnotou
                String thumbnail = tokenizer.nextToken();

                //posledni kontrola, zda jde o url
                if (thumbnail.matches(RegExpPatterns.URL)) {
                    return thumbnail;
                }
            }
        }
        return null;
    }
}
