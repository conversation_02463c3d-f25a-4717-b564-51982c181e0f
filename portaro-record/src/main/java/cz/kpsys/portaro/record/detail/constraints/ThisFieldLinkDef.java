package cz.kpsys.portaro.record.detail.constraints;

import lombok.NonNull;

public record ThisFieldLinkDef(

        @NonNull LinkedRecordAsRelatedRecordConstraintDef linkLookupDef

) implements MatchingRecordDef {

    public static ThisFieldLinkDef create(@NonNull LinkedRecordAsRelatedRecordConstraintDef linkLookupDef) {
        return new ThisFieldLinkDef(linkLookupDef);
    }

    @Override
    public String toString() {
        return linkLookupDef.toString();
    }
}
