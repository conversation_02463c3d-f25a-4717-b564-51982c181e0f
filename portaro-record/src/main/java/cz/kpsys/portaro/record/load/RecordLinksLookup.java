package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.spec.RecordDatableId;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.util.List;
import java.util.Objects;
import java.util.Set;

public record RecordLinksLookup(
        @NonNull List<RecordIdFondPair> recordLinks,
        @NonNull Set<? extends RecordDatableId> missingLinkFieldIds
) {

    public static RecordLinksLookup of(@NonNull RecordIdFondPair recordLink) {
        return of(List.of(recordLink));
    }

    public static RecordLinksLookup of(@NonNull List<RecordIdFondPair> recordLinks) {
        return new RecordLinksLookup(recordLinks, Set.of());
    }

    public static RecordLinksLookup ofMissingLinkField(@NonNull RecordDatableId missingLinkFieldId) {
        return new RecordLinksLookup(List.of(), Set.of(missingLinkFieldId));
    }

    public static RecordLinksLookup ofLinkMissingField(@NonNull RecordDatableId linkMissingFieldId) {
        return new RecordLinksLookup(List.of(), Set.of(linkMissingFieldId));
    }

    public static RecordLinksLookup ofLinkMissingFields(@NonNull @NotEmpty Set<? extends RecordDatableId> missingLinkFieldIds) {
        return new RecordLinksLookup(List.of(), missingLinkFieldIds);
    }

    public boolean isSuccess() {
        return !recordLinks.isEmpty();
    }

    public boolean isMissingLinkFields() {
        return !missingLinkFieldIds.isEmpty();
    }

    public @NonNull List<RecordIdFondPair> getAll() {
        return recordLinks;
    }

    public @NonNull RecordIdFondPair getSingle() {
        return DataUtils.requireSingle(recordLinks, "RecordIdFondPair", "single record link");
    }

    public @NonNull @NotEmpty Set<? extends RecordDatableId> existingMissingLinkFieldIds() {
        return Objects.requireNonNull(missingLinkFieldIds);
    }

}
