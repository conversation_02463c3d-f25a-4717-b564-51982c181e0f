package cz.kpsys.portaro.record.search.restriction;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.field.BasicSearchField;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Restriction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.EqWords;
import cz.kpsys.portaro.search.restriction.matcher.StartsWithWords;
import cz.kpsys.portaro.search.restriction.modifier.RestrictionModifier;

import java.util.List;
import java.util.stream.Collectors;

public class FieldedQAddingConjunctionModifier implements RestrictionModifier<MapBackedParams> {

    @Override
    public Conjunction<SearchField> modify(Conjunction<SearchField> rootConjunction, MapBackedParams p, Department ctx) {
        boolean rightHandExtension = ObjectUtil.isTrue(p.get(CoreSearchParams.RIGHT_HAND_EXTENSION));
        String q = p.get(CoreSearchParams.Q);

        if (q != null && p.hasNotNull(CoreSearchParams.FIELD)) {
            List<Restriction<? extends SearchField>> collect = p.get(CoreSearchParams.FIELD).stream()
                    .map(fieldName -> createTerm(fieldName, q, rightHandExtension))
                    .collect(Collectors.toUnmodifiableList());
            rootConjunction.add(new Conjunction<>(collect));
        }

        return rootConjunction;
    }

    private Term<BasicSearchField> createTerm(String fieldName, String q, boolean rightHandExtension) {
        return new Term<>(BasicSearchField.generic(fieldName), rightHandExtension ? new StartsWithWords(q) : new EqWords(q));
    }

}
