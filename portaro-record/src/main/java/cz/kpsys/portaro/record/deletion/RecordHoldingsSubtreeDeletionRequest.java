package cz.kpsys.portaro.record.deletion;

import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.record.Record;
import jakarta.validation.constraints.NotNull;
import lombok.With;


@Form(id = "holdingSubtreeDeletion")
@FormSubmit(path = "/api/records/delete-subtree-holdings")
@With
public record RecordHoldingsSubtreeDeletionRequest(
        @NotNull
        Record record
) {
}
