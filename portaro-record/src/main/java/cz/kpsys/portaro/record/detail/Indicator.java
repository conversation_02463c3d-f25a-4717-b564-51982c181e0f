package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.BasicNamedLabeledIdentified;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.io.Serializable;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class Indicator extends BasicNamedLabeledIdentified<String> implements Serializable {

    @NonNull FieldTypeId fieldTypeId;
    boolean isDefault;
    boolean numeric;

    public static Indicator createEmpty(@NonNull FieldTypeId fieldTypeId) {
        return new Indicator(IndicatorType.DEFAULT_NAME, IndicatorType.STANDARD_EMPTY_IND, fieldTypeId, true, false);
    }

    public static Indicator createUnknown(@NonNull FieldTypeId fieldTypeId, @NonNull @NotBlank String value) {
        Assert.hasText(value, "Uknown indicator value can't be empty");
        return new Indicator(IndicatorType.DEFAULT_NAME, value, fieldTypeId, true, false);
    }

    public static Indicator createStandard(String name, @NonNull @NotEmpty String value, @NonNull FieldTypeId fieldTypeId, boolean isDefault) {
        return new Indicator(name, value, fieldTypeId, isDefault, false);
    }

    public static Indicator createNumeric(String name, int value, @NonNull FieldTypeId fieldTypeId, boolean isDefault) {
        return new Indicator(name, String.valueOf(value), fieldTypeId, isDefault, true);
    }

    private Indicator(String name, @NonNull @NotEmpty String value, @NonNull FieldTypeId fieldTypeId, boolean isDefault, boolean numeric) {
        super(value, name);
        this.fieldTypeId = fieldTypeId;
        this.isDefault = isDefault;
        this.numeric = numeric;
    }

    @Override
    public Text getText() {
        if (numeric) {
            return Texts.ofNative(getName() + " - " + getId());
        }
        String table = fieldTypeId.isInAuthority() ? "DEF_DOKINDIK" : "DEF_AUTINDIK";
        String column = "NAZEV";
        return switch (fieldTypeId.getCode()) {
            case FieldTypes.IND_1_FIELD_CODE -> Texts.ofColumnMessageCodedOrNative(getName(), table, column, getId(), " ");
            case FieldTypes.IND_2_FIELD_CODE -> Texts.ofColumnMessageCodedOrNative(getName(), table, column, " ", getId());
            default -> throw new IllegalArgumentException("Indicator field code can be only %s or %s, but is %s".formatted(FieldTypes.IND_1_FIELD_CODE, FieldTypes.IND_2_FIELD_CODE, fieldTypeId.getCode()));
        };
    }

    public boolean isDefault() {
        return isDefault;
    }

}
