package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.commons.object.repo.DataAccessException;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.RecordIdentifier;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.SimpleFieldContainer;
import cz.kpsys.portaro.record.detail.spec.*;
import cz.kpsys.portaro.record.detail.value.AbsentDataFail;
import cz.kpsys.portaro.record.detail.value.FormulaResult;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

import static cz.kpsys.portaro.record.load.Multifields.Ordering.NUMERICALLY_COMPATIBLE_ORDER;

@Slf4j
public record LoadedRecords(

        @NonNull Map<RecordIdFondPair, Multifields> records,
        @NonNull Map<RecordIdentifier, List<Field<?>>> targetRecordFieldsMap,
        @NonNull FieldTypesByFondLoader fieldTypesByFondLoader,
        @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver

) {

    public static LoadedRecords empty(@NonNull FieldTypesByFondLoader fieldTypesByFondLoader,
                                      @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver) {
        return new LoadedRecords(new HashMap<>(), new HashMap<>(), fieldTypesByFondLoader, recordEntryFieldTypeIdResolver);
    }

    public void addPrimary(@NonNull RecordIdFondPair recordIdFondPair) {
        if (records.containsKey(recordIdFondPair)) {
            throw new IllegalStateException("Record %s is already present in loadedRecords".formatted(recordIdFondPair.id()));
        }
        List<EditableFieldType<?>> recordFieldTypes = fieldTypesByFondLoader.getTopfieldTypesByFond(recordIdFondPair.fond());
        records.put(recordIdFondPair, createMasterRecordIncludingMultifields(recordIdFondPair, recordFieldTypes));
    }

    public void addLoadedFields(@NonNull Collection<Field<?>> fields) {
        Map<RecordIdFondPair, List<Field<?>>> groupedFieldsByRecordId = fields.stream().collect(Collectors.groupingBy(Field::getRecordIdFondPair));
        for (Map.Entry<RecordIdFondPair, List<Field<?>>> recordIdentifierListEntry : groupedFieldsByRecordId.entrySet()) {

            RecordIdFondPair recordIdentifier = recordIdentifierListEntry.getKey();
            Multifields loadedRecord = findRecordOrCreateEmptySlave(recordIdentifier); // existujici recordy jsou "master", neexistujici vytvorime jako "slave"

            List<Field<?>> fieldsOfRecord = recordIdentifierListEntry.getValue();
            loadedRecord.addFields(fieldsOfRecord);
        }

        Map<RecordIdentifier, List<Field<? extends ScalarFieldValue<?>>>> currentTargetRecordFieldsMap = new SimpleFieldContainer(fields).deepStreamFields()
                .filter(Field::hasRecordLink)
                .collect(Collectors.groupingBy(field -> field.getExistingRecordLink().id()));
        targetRecordFieldsMap.putAll(currentTargetRecordFieldsMap);
    }

    private static Multifields createMasterRecordIncludingMultifields(@NonNull RecordIdFondPair recordIdFondPair,
                                                                      @NonNull List<EditableFieldType<?>> recordFieldTypes) {
        Multifields multifields = Multifields.createWithMultifields(recordIdFondPair, RecordRootId.of(recordIdFondPair), recordFieldTypes, NUMERICALLY_COMPATIBLE_ORDER);
        multifields.setRemainingExtraSpecs(RecordSpecSet.of(RecordSpec.ofWholeRecord(recordIdFondPair)));
        return multifields;
    }

    private static Multifields createSlaveRecordWithoutMultifields(@NonNull RecordIdFondPair recordIdFondPair) {
        return Multifields.create(recordIdFondPair, RecordRootId.of(recordIdFondPair), new ArrayList<>(), NUMERICALLY_COMPATIBLE_ORDER);
    }

    public RecordSpecSet updateLoadedAndGetNextSpecs(@NonNull RecordSpecSet searchedSpecs) {
        RecordSpecSet result = RecordSpecSet.ofEmptyModifiable();
        for (Multifields requiredRecord : List.copyOf(records.values())) {
            try {
                result.addAll(requiredRecord.updateLoadedAndGetNextSpecs(this, searchedSpecs));
            } catch (Exception e) {
                throw new DataAccessException("Error filling record " + requiredRecord.id().recordIdFondPair().id().id() + " (fond " + requiredRecord.id().recordIdFondPair().fond().getId() + "): " + e.getMessage(), requiredRecord.id().recordIdFondPair().id().id().toString(), e);
            }
        }
        return result;
    }

    public Optional<FieldSlot> findSingleByRecordFieldId(@NonNull RecordIdFondPair recordIdFondPair, @NonNull RecordFieldId recordFieldId) {
        Optional<Multifields> record = findRecord(recordIdFondPair);
        if (record.isEmpty()) {
            return Optional.empty();
        }
        return record.get().findSingleByFieldId(recordFieldId);
    }

    public List<FieldSlot> findAllByRecordMultifieldId(@NonNull RecordIdFondPair recordIdFondPair, @NonNull RecordMultifieldId recordMultifieldId) {
        Optional<Multifields> record = findRecord(recordIdFondPair);
        if (record.isEmpty()) {
            return List.of();
        }
        return record.get().findAllByMultifieldId(recordMultifieldId);
    }

    public List<Multifield> findMultifieldsByFieldTypeId(@NonNull RecordIdFondPair recordIdFondPair, @NonNull FieldTypeId fieldTypeId) {
        Optional<Multifields> record = findRecord(recordIdFondPair);
        if (record.isEmpty()) {
            return List.of();
        }
        return record.get().findMultifieldsByFieldTypeId(fieldTypeId);
    }

    public @NotEmpty List<Multifield> findMultifieldsByFieldTypeIdOrCreateStructure(@NonNull RecordIdFondPair recordIdFondPair, @NonNull FieldTypeId fieldTypeId) {
        List<Multifield> existingMultifields = findMultifieldsByFieldTypeId(recordIdFondPair, fieldTypeId);
        if (!existingMultifields.isEmpty()) {
            return existingMultifields;
        }

        Multifields multifields = findRecordOrCreateEmptySlave(recordIdFondPair);

        Multifield segmentMultifield = null;
        for (FieldTypeId segmentFieldTypeId : fieldTypeId.asSegments()) {
            Optional<Multifield> multifieldOpt = multifields.find(segmentFieldTypeId);
            if (multifieldOpt.isPresent()) {
                segmentMultifield = multifieldOpt.get();
            } else {
                EditableFieldType<?> segmentFieldType = fieldTypesByFondLoader.findByFondAndId(recordIdFondPair.fond(), segmentFieldTypeId, FieldTypesByFondLoader.WhenMissing.CREATE_UNKNOWN);
                segmentMultifield = multifields.getOrCreateMultifield(recordIdFondPair, segmentFieldType);
            }
            FieldSlot slot = segmentMultifield.findAnySlotOrCreate();
            multifields = slot.multifields();
        }

        return List.of(Objects.requireNonNull(segmentMultifield));
    }

    private Optional<Multifields> findRecord(@NonNull RecordIdFondPair recordIdFondPair) {
        return Optional.ofNullable(records.get(recordIdFondPair));
    }

    private Multifields findRecordOrCreateEmptySlave(@NonNull RecordIdFondPair recordIdFondPair) {
        return records.computeIfAbsent(recordIdFondPair, LoadedRecords::createSlaveRecordWithoutMultifields); // pokud pole generujeme, vytvarime vzdy slave
    }

    public Multifields getRecord(@NonNull RecordIdFondPair recordIdFondPair) {
        return findRecord(recordIdFondPair).orElseThrow(() -> new IllegalStateException("Record %s is not present in loadedRecords".formatted(recordIdFondPair)));
    }

    public List<Field<?>> getFieldsWithTarget(@NonNull RecordIdentifier rid) {
        return targetRecordFieldsMap.getOrDefault(rid, List.of());
    }

    public @NonNull RecordSpecSet resolveAndGetMissingSpecs(@NonNull RecordSpecSet searchedRecordSpecs, @NonNull ValuableFieldNode node) {
        FieldPayloadGenerator generator = new FieldPayloadGenerator(this, searchedRecordSpecs, recordEntryFieldTypeIdResolver);
        try {
            generator.resolveNodeLink(node);
        } catch (Exception e) {
            throw new DataAccessException("Error resolving link in " + node.id() + ": " + e.getMessage(), node.id().toString(), e);
        }
        try {
            FormulaResult formulaResult = generator.resolveNodeValue(node);
            return switch (formulaResult) {
                case AbsentDataFail absentDataFail -> absentDataFail.toSpec();
                default -> RecordSpecSet.ofEmptyUnmodifiable();
            };
        } catch (Exception e) {
            throw new DataAccessException("Error resolving value in " + node.id() + ": " + e.getMessage(), node.id().toString(), e);
        }
    }

}
