package cz.kpsys.portaro.record.detail.spec;

import cz.kpsys.portaro.commons.util.SealedComparator;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.Comparator;

public sealed interface RecordIndexedId permits RecordFieldId, RecordRootId {

    SealedComparator<RecordIndexedId> COMPARATOR = new SealedComparator<RecordIndexedId>()
            .registerSubtypeComparator(RecordRootId.class, 1, Comparator.comparing(RecordRootId::recordIdFondPair))
            .registerSubtypeComparator(RecordFieldId.class, 2, Comparator.comparing(RecordFieldId::recordIdFondPair).thenComparing(RecordFieldId::fieldId, FieldId.NUMERICALLY_COMPATIBLE_SORTER));

    @NonNull RecordIdFondPair recordIdFondPair();

    @Nullable FieldId fieldId();

    boolean hasParentIndexedId();

    default @NonNull RecordFieldId toSubfieldId(@NonNull FieldTypeId subTypeId, int index) {
        return toSubmultifield(subTypeId).toRecordFieldId(index);
    }

    @NonNull RecordMultifieldId toSubmultifield(@NonNull FieldTypeId subTypeId);

    @NonNull RecordIndexedId existingParentIndexedId();

    @NonNull RecordMultifieldId existingParentMultifieldId();

    int nestingLevel();

}
