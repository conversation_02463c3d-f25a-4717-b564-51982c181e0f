package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.IdentifiedFieldContainer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class TransactionalRecordFieldsLoader implements RecordFieldsLoader {

    @NonNull RecordFieldsLoader delegate;
    @NonNull TransactionTemplate readonlyTransactionTemplate;

    @Override
    public List<IdentifiedFieldContainer> load(@NonNull List<RecordIdFondPair> recordIdFondPairs) {
        return Objects.requireNonNull(readonlyTransactionTemplate.execute(_ -> delegate.load(recordIdFondPairs)));
    }

    @Override
    public IdentifiedFieldContainer refresh(@NonNull Record record) {
        return Objects.requireNonNull(readonlyTransactionTemplate.execute(_ -> delegate.refresh(record)));
    }
}
