package cz.kpsys.portaro.record.detail;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.function.Predicate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SubfieldedFieldFilter<E extends Field<?>> implements Predicate<E> {

    @Override
    public boolean test(E field) {
        return field != null && !field.getFields().isEmpty();
    }

}
