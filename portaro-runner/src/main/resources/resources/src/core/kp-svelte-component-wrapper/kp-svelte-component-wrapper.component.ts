import type {IAug<PERSON><PERSON><PERSON><PERSON>y, IOn<PERSON><PERSON>roy, IOnInit, IPostLink} from 'angular';
import type {Component, OnChanges, OnChangesObject} from 'typings/portaro.fe.types';
import type {SvelteComponentConstructor, SvelteContext} from '../types';
import type {LogService} from 'core/logging/log.service';
import type {SvelteComponent} from 'svelte';
import {exists} from 'shared/utils/custom-utils';

// This module (svelte/internal) actually exists, but it says it doesn't
import {
    detach,
    insert,
    noop
} from 'svelte/internal';

export class KpSvelteComponentWrapperComponent implements Component<KpSvelteComponentWrapperComponentBindings<Record<string, any>>> {
    public static readonly componentName = 'kpSvelteComponentWrapper';
    public controller = KpSvelteComponentWrapperComponentController;
    public bindings = {
        component: '<',
        props: '<?',
        getSvelteComponent: '&?',
        additionalContext: '<?'
    } as const;
}

interface KpSvelteComponentWrapperComponentBindings<T> {
    component: SvelteComponentConstructor<T>;
    props?: T;
    additionalContext?: SvelteContext;

    getSvelteComponent?(locals: {$svelteComponentInstant: SvelteComponent});
}

class KpSvelteComponentWrapperComponentController implements KpSvelteComponentWrapperComponentBindings<Record<string, any>>, IOnInit, IPostLink, IOnDestroy, OnChanges<KpSvelteComponentWrapperComponentBindings<Record<string, any>>> {
    // bindings:
    component: SvelteComponentConstructor;
    props?: Record<string, any>;
    additionalContext?: SvelteContext;

    getSvelteComponent?(locals: {$svelteComponentInstant: SvelteComponent});

    private readonly element: HTMLElement; // wrapper component element
    private readonly eventForwardersRemoveHandlers: (() => void)[]; // functions to remove svelte event listeners

    private componentInstance: SvelteComponent;
    private initialProps: Record<string, any>;
    private svelteContext: SvelteContext;

    /*@ngInject*/
    constructor($element: IAugmentedJQuery, private logService: LogService, private svelteDefaultContext: SvelteContext) {
        this.element = $element[0];
        this.eventForwardersRemoveHandlers = [];
    }

    $onInit(): void {
        this.svelteContext = new Map(exists(this.additionalContext) ? [...this.additionalContext, ...this.svelteDefaultContext] : this.svelteDefaultContext);
    }

    $postLink(): void {
        this.bootstrapSvelteComponent();
    }

    $onChanges(onChangesObj: OnChangesObject<KpSvelteComponentWrapperComponentBindings<Record<string, any>>>): void {
        if (exists(onChangesObj.props)) {
            const props = onChangesObj.props.currentValue;

            if (exists(this.componentInstance)) {
                const changedProps = this.filterOutUnchangedProps(onChangesObj.props.currentValue, onChangesObj.props.previousValue);
                this.componentInstance.$set(changedProps);
            } else {
                this.addInitialProps(props);
            }
        }

        if (exists(onChangesObj.component) && !onChangesObj.component.isFirstChange()) { // skip bootstrap for initial change (let $postLink try to bootstrap)
            this.bootstrapSvelteComponent();
        }
    }

    $onDestroy(): void {
        if (this.componentInstance) {
            this.eventForwardersRemoveHandlers.forEach((removeHandler) => removeHandler());
            this.componentInstance.$destroy();
        }
    }

    private bootstrapSvelteComponent() {
        if (!this.component) {
            return; // Don't bootstrap if constructor is missing
        }

        if (exists(this.componentInstance)) {
            this.logService.error(`${KpSvelteComponentWrapperComponent.componentName}: Svelte component is already instantiated (${this.element.getAttribute('component')})`);
            return;
        }

        let svelteComponentProps = {
            ...this.initialProps
        };

        const slottedElements = this.createSlottedElements(this.element);
        const anySlottedElementsExist = Object.keys(slottedElements).length > 0;
        this.element.innerHTML = ''; // Clear existing content before injecting a new Svelte component

        if (anySlottedElementsExist) {
            svelteComponentProps = {
                ...svelteComponentProps,
                $$scope: {}, // Needed for custom slots
                $$slots: slottedElements
            };
        }


        this.componentInstance = new this.component({
            target: this.element,
            context: this.svelteContext,
            props: svelteComponentProps
        });

        this.getListenedEventNames()
            .filter((eventName) => !NATIVE_DOM_EVENTS_NAMES.includes(eventName)) // native DOM events bubble up automatically
            .forEach((eventName) => this.attachEventForwarder(eventName)); // custom events from svelte need event forwarder

        if (this.getSvelteComponent) {
            this.getSvelteComponent({$svelteComponentInstant: this.componentInstance});
        }
    }

    private createSlottedElements(parentElement: Element) {
        // Find elements in the parent that have the 'data-slot' attribute
        const inSlotElements = Array.from(parentElement.children).filter((child) => child.hasAttribute('data-slot'));
        const slots: Record<string, any> = {};

        inSlotElements.forEach((slotElement) => {
            const slotName = slotElement.getAttribute('data-slot') || 'default';
            const innerHTML = slotElement.innerHTML;

            slots[slotName] = [this.createSlottedElementFromHTML(innerHTML)];
        });

        return slots;
    }

    private createSlottedElementFromHTML(innerHTML: string) {
        return function() {
            let fragment: DocumentFragment;
            let mounted = false; // Track if it's already mounted to avoid duplicate mounts

            return {
                c() {
                    // Convert the innerHTML string into actual DOM nodes
                    fragment = document.createRange().createContextualFragment(innerHTML);
                },
                // Mount - attaches the fragment to the DOM
                m(target: Node, anchor: Node | null) {
                    if (!mounted) {
                        // Insert the document fragment containing the slotted content
                        insert(target, fragment, anchor);
                        mounted = true; // Track the mount state
                    }
                },
                // Destroy - cleans up when the slot is removed
                d(detaching: boolean) {
                    if (detaching && mounted) {
                        detach(fragment);
                        mounted = false;
                    }
                },
                l: noop
            };
        };
    }

    // based on angularjs source code
    private getListenedEventNames(): string[] {
        const NG_ON_PREFIX = /^ng-on-([a-z].*)$/;
        return this.element
            .getAttributeNames()
            .map((attribute) => attribute.toLowerCase()) // angular attributes are case insensitive
            .filter((attribute) => exists(attribute.match(NG_ON_PREFIX))) // get ng-on-* attributes
            .map((attribute) => attribute.substring(6)) // remove ng-on- prefix (6 characters)
            .map((nonPrefixedAttribute) => nonPrefixedAttribute.replace(/_(.)/g, (_, letter: string) => letter.toUpperCase())); // normalize non-prefixed attribute name (_x -> X, e.g. test_lower_case -> testLowerCase)
    }

    private addInitialProps(extraProps: Record<string, any>): void {
        this.initialProps = {
            ...this.initialProps,
            ...extraProps
        };
    }

    private attachEventForwarder(eventName: string): void {
        const removeHandler = this.componentInstance.$on(eventName, (event: CustomEvent) => this.element.dispatchEvent(event));
        this.eventForwardersRemoveHandlers.push(removeHandler);
    }

    private filterOutUnchangedProps(currentProps: Record<string, any>, previousProps: Record<string, any>): Record<string, any> {
        const shallowEquals = (a, b) => a === b;
        return Object.fromEntries(Object.entries(currentProps).filter(([name, value]) => !shallowEquals(previousProps[name], value)));
    }
}

const NATIVE_DOM_EVENTS_NAMES = [
    'abort',
    'animationcancel',
    'animationend',
    'animationiteration',
    'animationstart',
    'auxclick',
    'beforeinput',
    'blur',
    'canplay',
    'canplaythrough',
    'change',
    'click',
    'close',
    'compositionend',
    'compositionstart',
    'compositionupdate',
    'contextmenu',
    'cuechange',
    'dblclick',
    'drag',
    'dragend',
    'dragenter',
    'dragleave',
    'dragover',
    'dragstart',
    'drop',
    'durationchange',
    'emptied',
    'ended',
    'error',
    'focus',
    'focusin',
    'focusout',
    'formdata',
    'gotpointercapture',
    'input',
    'invalid',
    'keydown',
    'keypress',
    'keyup',
    'load',
    'loadeddata',
    'loadedmetadata',
    'loadstart',
    'lostpointercapture',
    'mousedown',
    'mouseenter',
    'mouseleave',
    'mousemove',
    'mouseout',
    'mouseover',
    'mouseup',
    'pause',
    'play',
    'playing',
    'pointercancel',
    'pointerdown',
    'pointerenter',
    'pointerleave',
    'pointermove',
    'pointerout',
    'pointerover',
    'pointerup',
    'progress',
    'ratechange',
    'reset',
    'resize',
    'scroll',
    'securitypolicyviolation',
    'seeked',
    'seeking',
    'select',
    'selectionchange',
    'selectstart',
    'stalled',
    'submit',
    'suspend',
    'timeupdate',
    'toggle',
    'transitioncancel',
    'transitionend',
    'transitionrun',
    'transitionstart',
    'volumechange',
    'waiting',
    'webkitanimationend',
    'webkitanimationiteration',
    'webkitanimationstart',
    'webkittransitionend',
    'wheel',
    'fullscreenchange',
    'fullscreenerror',
    'copy',
    'cut',
    'paste'
];