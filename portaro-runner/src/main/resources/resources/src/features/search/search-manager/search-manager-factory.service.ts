import type {SearchManagerConfig} from './search-manager';
import type {Form, SearchParams, ViewableSearch} from 'typings/portaro.be.types';
import type {UrlStateManagerService} from 'shared/state-manager/url-state-manager.service';
import type {FormDataService} from 'shared/data-services/form.data-service';
import type {SearchService} from '../search.service';
import type {LogService} from 'core/logging/log.service';
import {SearchManager} from './search-manager';
import {MemoryStateManager} from 'shared/state-manager/memory-state-manager';
import {exists} from 'shared/utils/custom-utils';

export class SearchManagerBuilderFactoryService {
    public static serviceName = 'searchManagerBuilderFactoryService';

    /*@ngInject*/
    constructor(private searchService: SearchService,
                private urlStateManagerService: UrlStateManagerService<any>,
                private formDataService: FormDataService,
                private logService: LogService) {
    }

    public createBuilder<PARAMS extends SearchParams, ITEM>() {
        return new SearchManagerBuilder<PARAMS, ITEM>(
            this.logService,
            this.urlStateManagerService,
            this.searchService.search.bind(this.searchService),
            this.formDataService.getForms.bind(this.formDataService),
            {}
        );
    }
}

export type SearchFunction<PARAMS extends SearchParams, ITEM> = (params: Partial<PARAMS>) => Promise<ViewableSearch<PARAMS, ITEM>>;
export type LoadFormsFunction<PARAMS extends SearchParams> = (params: Partial<PARAMS>) => Promise<Form[]>;

export class SearchManagerBuilder<PARAMS extends SearchParams, ITEM> {
    private allowSimultaneousRequests = false;

    constructor(private logService: LogService,
                private urlStateManagerService: UrlStateManagerService<any>,
                private searchFunction: SearchFunction<PARAMS, ITEM>,
                private loadFormsFunction: LoadFormsFunction<PARAMS>,
                private staticParams: Partial<PARAMS>) {
    }

    public withSearchFunction(searchFunction: SearchFunction<PARAMS, ITEM>) {
        if (exists(searchFunction)) {
            this.searchFunction = searchFunction;
        }

        return this;
    }

    public withStaticParams(staticParams: Partial<PARAMS>) {
        if (exists(staticParams)) {
            this.staticParams = staticParams;
        }

        return this;
    }

    public withAllowedSimultaneousRequests() {
        this.allowSimultaneousRequests = true;
        return this;
    }

    public createLocalSearch(searchOnInit = false) {
        return new SearchManager<ITEM, PARAMS>(this.logService, new MemoryStateManager<PARAMS>({} as PARAMS, searchOnInit), this.createConfig());
    }

    public createGlobalSearch() {
        return new SearchManager<ITEM, PARAMS>(this.logService, this.urlStateManagerService, this.createConfig());
    }

    private createConfig(): SearchManagerConfig<ITEM, PARAMS> {
        return {
            staticParams: this.staticParams,
            allowSimultaneousRequests: this.allowSimultaneousRequests,
            searchFunction: this.searchFunction,
            loadFormsFunction: this.loadFormsFunction
        };
    }
}