<script lang="ts">
    import type {TabButton, TabId} from 'shared/ui-widgets/tabset/types';
    import type {CssSize} from 'shared/ui-widgets/types';
    import type {FondPermissions} from 'src/features/erp/services/fond-permissions.data-service';
    import {TabbedSubpagesUrlManagementService} from './tabbed-subpages-url-management.service';
    import {fly} from 'svelte/transition';
    import {exists} from 'shared/utils/custom-utils';
    import {getInjector} from 'core/svelte-context/context';
    import {createEventDispatcher, onMount} from 'svelte';
    import {ErpFondPermissionsContextService} from 'src/features/erp/services/erp-fond-permissions-context.service';
    import KpButtonTabs from 'shared/ui-widgets/tabset/KpButtonTabs.svelte';

    export let tabButtons: TabButton[];
    export let additionalClasses = '';
    export let gap: CssSize = '30px';
    export let withoutUrlManagement = false;

    const fondPermissionsService = getInjector().getByClass(ErpFondPermissionsContextService);
    const fondPermissions$ = fondPermissionsService.fondPermissions$;
    const dispatch = createEventDispatcher<{'tab-change': TabId}>();
    const service = getInjector().getByClass(TabbedSubpagesUrlManagementService);
    const flyInAnimParams = {y: -10, duration: 250};
    export let activeTab: TabId = tabButtons[0].id;
    export let tabToSelect: TabId | null = null;
    let urlStateInitialized = false;

    onMount(async () => {
        if (withoutUrlManagement) {
            return;
        }

        const currentUrlState = await service.getCurrentUrlState();

        if (exists(currentUrlState) && exists(currentUrlState.activeTab)) {
            activeTab = currentUrlState.activeTab;
            tabToSelect = activeTab;
        }

        urlStateInitialized = true;
    });

    const handleTabChange = (event: CustomEvent<TabId>) => {
        activeTab = event.detail;
        tabToSelect = null;

        dispatch('tab-change', activeTab);

        if (!withoutUrlManagement) {
            service.setActiveTab(activeTab);
        }
    };

    function hasTabPagePadding(tabId: TabId): boolean {
        const tabButton = tabButtons.find((tab) => tab.id === tabId);
        if (!exists(tabButton)) {
            return false;
        }

        return !tabButton.tabPageWithoutPadding;
    }

    function getPageContainerClass(tabId: TabId): string | null {
        const tabButton = tabButtons.find((tab) => tab.id === tabId);
        return tabButton?.tabPageContainerClass;
    }

    function filterTabButtonsByFondPermissions(buttons: TabButton[], permissions: FondPermissions): TabButton[] {
        return buttons.filter((button) => {
            return fondPermissionsService.hasFondPermissions(permissions, button.showPermissionFond, button.editPermissionFond);
        });
    }
</script>

{#if (!withoutUrlManagement && urlStateInitialized) || withoutUrlManagement}
    <div class="erp-tabbed-subpages-container {additionalClasses}">
        <div class="tab-menu-container">
            <KpButtonTabs {tabToSelect}
                          tabButtons="{filterTabButtonsByFondPermissions(tabButtons, $fondPermissions$)}"
                          on:tab-change={handleTabChange}
                          hrefGenerator="{withoutUrlManagement ? undefined : service.getTabHref.bind(service)}"/>

            {#if exists($$slots['additional-content'])}
                <slot name="additional-content" {activeTab}/>
            {/if}
        </div>

        <div class="subpage-container-wrapper {getPageContainerClass(activeTab) ?? ''}"
             class:with-padding={hasTabPagePadding(activeTab)}
             aria-live="polite">

            {#key activeTab}
                <div class="subpage-container"
                     style:--gap="{gap}"
                     in:fly={flyInAnimParams}>

                    <slot {activeTab}/>
                </div>
            {/key}
        </div>
    </div>
{/if}

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro-erp.less";

    .erp-tabbed-subpages-container {
        .flex-grow();

        .tab-menu-container {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: @spacing-xl;
            padding: @spacing-ml @spacing-xxl;
            border-bottom: 1px solid @themed-border-default;
        }

        .subpage-container-wrapper {
            .flex-grow();

            &.with-padding {
                padding: @spacing-xl @spacing-xxl;
            }

            .subpage-container {
                .flex-grow();

                gap: var(--gap);
            }
        }
    }
</style>
