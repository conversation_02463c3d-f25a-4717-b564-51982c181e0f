import type {SearchSelectionModalModel} from 'src/modals/search-selection-modal/types';
import type {RecordEditationModalModel} from 'src/modals/record-editation-modal/types';
import type {ModalDialogService} from 'shared/modal-dialogs/modal-dialog.service';
import type {Subkind} from 'shared/constants/portaro.constants';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type RecordDataService from './record.data-service';
import type ExemplarService from './exemplar.service';
import type WalkerService from 'shared/services/walker.service';
import type ReloaderService from 'shared/services/reloader.service';
import {ngAsync} from 'shared/utils/ng-@decorators';
import {isDocument} from 'shared/utils/types-utils';
import {catchManualInterruption, isInterruptedActionResponse} from 'src/modals/modal-utils';
import {getRecordPath, getRecordUrlId, transferify} from 'shared/utils/data-service-utils';
import {Kind, SearchType} from 'shared/constants/portaro.constants';
import {exists} from 'shared/utils/custom-utils';
import type {
    ActionResponse,
    Department,
    Document,
    Fond,
    HierarchyTree,
    Rec,
    RecordCreateParams,
    RecordHierarchyTreeNode,
    RecordHolding,
    RecordRevision,
    RecordSearchParams,
    UUID
} from 'typings/portaro.be.types';

export default class RecordService {
    public static readonly serviceName = 'recordService';

    /*@ngInject*/
    constructor(private recordDataService: RecordDataService,
                private exemplarService: ExemplarService,
                private currentDepartment: Department,
                private walker: WalkerService,
                private reloader: ReloaderService,
                private finishedResponseInteractionService: FinishedResponseInteractionService,
                private modalDialogService: ModalDialogService) {
    }

    public async getById(recordId: UUID): Promise<Rec | null> {
        try {
            return await this.recordDataService.getById(recordId);
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            return null;
        }
    }

    @ngAsync()
    public async findRecordOrCreateRecordWithHolding(staticSearchParams: RecordSearchParams, createParams: RecordCreateParams): Promise<Rec> {
        const modalModel: SearchSelectionModalModel = {
            staticSearchParams: transferify(staticSearchParams),
            createNewValueFn: () => this.createRecordEditation(createParams)
        };
        const record = await this.modalDialogService.openModalWindowAndGetPayload('searchSelection', modalModel) as Rec;
        await this.createRecordHoldingOnCurrentCtx(record);
        return record;
    }

    @ngAsync()
    public async createRecordEditation(createParams: RecordCreateParams, existingRecord?: Rec): Promise<Rec> {
        if (!createParams.kind && !createParams.subkind && !createParams.fond) {
            // TODO: await fond selection dialog
            throw new Error('Fond selection dialog is not implemented yet');
        }

        const recordModalModel: RecordEditationModalModel = {
            editationParams: {
                subkind: createParams.subkind,
                fond: createParams.fond,
                rootFond: createParams.rootFond,
                record: existingRecord,
                initialValues: createParams.initialValues
            },
            focusedFieldTypeId: createParams.focusedFieldTypeId
        };

        const recordLabeledIdentified = await this.modalDialogService.openModalWindowAndGetPayload('recordEditation', recordModalModel);
        try {
            return await this.recordDataService.getById(recordLabeledIdentified.id);
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async createRecordHoldingAndAddExemplarsThrowingToModal(subkind?: Subkind, fond?: Fond, recordPresearch: boolean = true): Promise<void> {
        if (!subkind) {
            // TODO: await fond selection dialog
            throw new Error('Fond selection dialog is not implemented yet');
        }

        let record: Rec;
        const createParams: RecordCreateParams = {
            kind: [Kind.KIND_RECORD],
            subkind,
            fond,
            rootFond: fond
        };

        if (recordPresearch) {
            const searchParams: RecordSearchParams = {
                type: SearchType.TYPE_SEARCH_SELECTION,
                kind: [Kind.KIND_RECORD],
                subkind
            };
            record = await this.findRecordOrCreateRecordWithHolding(searchParams, createParams);
        } else {
            record = await this.createRecordEditation(createParams);
        }

        try {
            await catchManualInterruption(() => this.createExemplarIfExemplarable(record));
            await this.walker.newPage(getRecordPath(getRecordUrlId(record)));

        } catch (exceptionResponse) {
            if (!isInterruptedActionResponse(exceptionResponse)) {
                await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            }
        }
    }

    @ngAsync()
    public async copyRecordToSpecifiedFondAndAddRecordHoldingThrowingToModal(source: Rec): Promise<void> {
        try {
            const {savedObject: record} = await this.recordDataService.copyRecord(source);
            await this.walker.newPage(getRecordPath(getRecordUrlId(record)));

        } catch (exceptionResponse) {
            if (!isInterruptedActionResponse(exceptionResponse)) {
                await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            }
        }
    }

    private async createRecordHoldingOnCurrentCtx(record: Rec): Promise<RecordHolding> {
        return this.createRecordHolding(record, this.currentDepartment);
    }

    public async createRecordHolding(record: Rec, targetDepartment?: Department): Promise<RecordHolding> {
        try {
            const response = await this.recordDataService.createRecordHolding(record, targetDepartment);
            return response.savedObject;
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    private async createExemplarIfExemplarable(record: Rec): Promise<ActionResponse | null> {
        if (isDocument(record) && record.fond.withExemplars) {
            return this.exemplarService.createEmptyExemplar(record, false);
        }
        return null;
    }

    @ngAsync()
    public async updateWithExternalRecordAndReloadThrowingToToast(record: Rec, modalModel: SearchSelectionModalModel): Promise<void> {
        try {
            const externalRecord: Rec = await this.modalDialogService.openModalWindowAndGetPayload('searchSelection', modalModel) as Rec;
            const actionResponse: ActionResponse = await this.recordDataService.mergeRecords(record, [externalRecord], true);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
            await this.reloader.reloadRoute();
        } catch (exceptionResponse) {
            if (!isInterruptedActionResponse(exceptionResponse)) {
                this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            }
        }
    }

    @ngAsync()
    public async mergeRecordsAndReload(target: Rec, sources: Rec[]): Promise<void> {
        try {
            const actionResponse: ActionResponse = await this.recordDataService.mergeRecords(target, sources);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
            this.reloader.reloadPage(10);
        } catch (exceptionResponse) {
            if (!isInterruptedActionResponse(exceptionResponse)) {
                throw exceptionResponse;
            }
        }
    }

    @ngAsync()
    public async getAllRecordRevisions(record: Rec): Promise<RecordRevision[]> {
        try {
            return await this.recordDataService.queryRecordRevisions(record);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async sendTemplatedMail(record: Rec): Promise<boolean> {
        try {
            const actionResponse = await this.recordDataService.sendTemplatedMail(record.id);
            await this.finishedResponseInteractionService.showSuccessResponseInToastIfPossible(actionResponse);
            return true;
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
        }

        return false;
    }

    @ngAsync()
    public async getAllRecordHoldings(record: Rec, department: Department): Promise<RecordHolding[]> {
        try {
            return await this.recordDataService.queryRecordHoldings({record, rootDepartment: department});
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async getAllRecordParts(record: Rec, pageNumber: number, pageSize: number): Promise<Document[]> {
        try {
            return await this.recordDataService.getRecordParts(record.id, pageNumber, pageSize);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async getAllRecordArticles(record: Rec, pageNumber: number, pageSize: number): Promise<Document[]> {
        try {
            return await this.recordDataService.getRecordArticles(record.id, pageNumber, pageSize);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async getRecordHierarchyTrees(record: Rec): Promise<HierarchyTree<UUID, RecordHierarchyTreeNode>[]> {
        try {
            return await this.recordDataService.getRecordHierarchyTrees(record.id);
        } catch (exceptionResponse) {
            this.finishedResponseInteractionService.showFailedResponseInToast(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async deleteRecordHolding(recordHolding: RecordHolding, goToHomepageInsteadOfReload: boolean | null = null): Promise<void> {
        try {
            await this.recordDataService.deleteRecordHolding(recordHolding);

            if (exists(goToHomepageInsteadOfReload)) {
                if (goToHomepageInsteadOfReload) {
                    await this.walker.newPage('/');
                } else {
                    await this.reloader.reloadRoute();
                }
            }
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }

    @ngAsync()
    public async discardRecordHolding(recordHolding: RecordHolding): Promise<void> {
        try {
            await this.recordDataService.discardRecordHolding(recordHolding);
            await this.walker.newPage('/');
        } catch (exceptionResponse) {
            await this.finishedResponseInteractionService.showFailedResponseInModalWindow(exceptionResponse);
            throw exceptionResponse;
        }
    }
}
