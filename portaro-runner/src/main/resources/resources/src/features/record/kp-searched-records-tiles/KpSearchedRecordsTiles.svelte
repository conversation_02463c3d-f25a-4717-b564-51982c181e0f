<script lang="ts">
    import type {CacheMode, Department} from 'typings/portaro.be.types';
    import {assertUnreachable, exists, isNullOrUndefined} from 'shared/utils/custom-utils';
    import {getInjector} from 'core/svelte-context/context';
    import KpSearchContext from '../../search/kp-search-context/KpSearchContext.svelte';
    import KpRecordsTiles from '../kp-records-tiles/KpRecordsTiles.svelte';
    import KpStaticSearchResults from '../../search/kp-search-results/KpStaticSearchResults.svelte';
    import KpPageableSearchResults from 'src/features/search/kp-search-results/KpPageableSearchResults.svelte';

    type GridSearchTypes = 'random' | 'newest' | 'custom' | 'custom-random';

    export let type: GridSearchTypes = 'random';
    export let cache: CacheMode = 'longterm';
    export let maxCount = 32;
    export let department = getInjector().getByToken<Department>('currentDepartment').id;
    export let searchQuery: string | null = null;
    export let orderBy: string | null = null;
    export let includePagination = false;

    const params = initParams();

    function initParams(): Record<string, any> {
        switch (type) {
            case 'newest':
                return getNewestSearchParams();
            case 'random':
                return getRandomSearchParams();
            case 'custom':
                return getCustomSearchParams(false);
            case 'custom-random':
                return getCustomSearchParams(true);
            default:
                assertUnreachable(type);
        }
    }

    function getRandomSearchParams() {
        const qt = '{"and": [{"field": "fond", "in": {"value": [1]}}, {"field": "documentStatus", "in": {"value": [3, 4, 5, 6]}}]}';

        return {
            type: 'random',
            qt,
            pageSize: maxCount,
            cache,
            ...exists(department) ? {department} : {}
        };
    }

    function getNewestSearchParams() {
        const sorting = '-REZS_DATVYT';
        const qt = '{"and": [{"field": "fond", "in": {"value": [1]}}, {"field": "recordCreationDate", "between": {"from": 2018, "to": null}}, {"field": "documentStatus", "in": {"value": [3, 4, 5, 6]}}]}';

        return {
            qt,
            pageSize: maxCount,
            cache,
            sorting,
            ...exists(department) ? {department} : {}
        };
    }

    function getCustomSearchParams(random: boolean) {
        if (isNullOrUndefined(searchQuery)) {
            throw new Error('search-query parameter is mandatory for custom search');
        }

        return {
            type: random ? 'random' : undefined,
            qt: searchQuery,
            pageSize: maxCount,
            cache,
            ...exists(orderBy) ? {sorting: orderBy} : {},
            ...exists(department) ? {department} : {}
        };
    }
</script>

<div class="kp-searched-records-tiles">
    <KpSearchContext staticParams={params} localSearch let:searchManager>
        {#if includePagination}
            <KpPageableSearchResults showPageButtons="{false}" loadingSize="lg" pagination={searchManager.getPagination()} let:paginationData>
                <KpRecordsTiles records="{paginationData.items}"/>
            </KpPageableSearchResults>
        {:else}
            <KpStaticSearchResults pagination={searchManager.getPagination()} loadingSize="lg" let:paginationData>
                <KpRecordsTiles records="{paginationData.items}"/>
            </KpStaticSearchResults>
        {/if}
    </KpSearchContext>
</div>