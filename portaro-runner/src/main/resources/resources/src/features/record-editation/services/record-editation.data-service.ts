import type {PromiseBasedRecordEditationDataService} from '../types';
import type {AllowIdentifiedStubs} from 'typings/portaro.fe.types';
import type {Subkind} from 'shared/constants/portaro.constants';
import type {AjaxService} from 'core/data-services/ajax.service';
import {uuid} from 'shared/utils/custom-utils';
import {transferify} from 'shared/utils/data-service-utils';
import type {
    FieldId,
    FieldTypeId,
    Fond,
    Identified,
    Rec,
    RecordEditation,
    SetFieldValueRequest,
    UUID
} from 'typings/portaro.be.types';

export interface NewRecordEditationParams {
    subkind: Subkind,
    record?: Rec,
    fond?: Fond | Identified<number>,
    rootFond?: Fond | Identified<number>,
    initialValues?: Record<FieldTypeId, SetFieldValueRequest>
}

export class RecordEditationDataService implements PromiseBasedRecordEditationDataService {

    public static readonly serviceName = 'recordEditationDataService';

    private static ROUTE = '/record-editations' as const;
    private static RECORD_EDITATION_V2_ROUTE = '/record-editations/v2' as const;

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    public async createNewEditation(params: AllowIdentifiedStubs<NewRecordEditationParams>): Promise<RecordEditation> {
        if (!params.subkind) {
            throw new Error('subkind param is not defined in new record editation params');
        }

        return this.ajaxService
            .createRequest(RecordEditationDataService.ROUTE)
            .post({...transferify(params), initialValues: params.initialValues});
    }

    public async getEditation(editationId: string): Promise<RecordEditation> {
        return this.ajaxService
            .createRequest(`${RecordEditationDataService.ROUTE}/${editationId}`)
            .get();
    }

    public createField(editationId: string, code: string) {
        return this.doPutTo(editationId, `/fields/${uuid()}?code=${code}`);
    }

    public createSubfield(editationId: string, parentId: UUID, code: string) {
        return this.doPutTo(editationId, `/fields/${encodeURIComponent(parentId)}/${uuid()}?code=${encodeURIComponent(code)}`);
    }

    public deleteField(editationId: string, fieldIdPath: string) {
        return this.doDeleteTo(editationId, `/fields/${fieldIdPath}`);
    }

    public moveField(editationId: string, fieldIdPath: string, isDown: boolean) {
        return this.doPostTo(editationId, `/fields/${fieldIdPath}/move/${isDown ? 'down' : 'up'}`);
    }

    public setFieldValue(editationId: string, fieldIdPath: string, setFieldValueRequest: SetFieldValueRequest) {
        return this.doPostTo(editationId, `/fields/${fieldIdPath}`, setFieldValueRequest);
    }

    public clearFieldValue(editationId: string, fieldIdPath: string) {
        return this.doPostTo(editationId, `/fields/${fieldIdPath}/clear`);
    }

    public setFond(editationId: string, fondId: number) {
        return this.doPostTo(editationId, `/fond/${encodeURIComponent(fondId)}`);
    }

    public save(editationId: string) {
        return this.doPostTo(editationId, '/save');
    }

    public publish(editationId: string) {
        return this.doPostTo(editationId, '/publish');
    }

    public remove(editationId: string) {
        return this.doDeleteTo(editationId, '/delete');
    }

    public setFieldValueByFieldId(editationId: string, fieldId: FieldId, setFieldValueRequest: SetFieldValueRequest): Promise<RecordEditation> {
        return this.ajaxService
            .createRequest(`${RecordEditationDataService.RECORD_EDITATION_V2_ROUTE}/${editationId}/fields/${encodeURIComponent(fieldId)}`)
            .post(setFieldValueRequest);
    }

    public deleteFieldByFieldId(editationId: string, fieldId: FieldId): Promise<RecordEditation> {
        return this.ajaxService
            .createRequest(`${RecordEditationDataService.RECORD_EDITATION_V2_ROUTE}/${editationId}/fields/${encodeURIComponent(fieldId)}`)
            .delete();
    }

    private async doPostTo(editationId: string, pathAndParams: string, data?: any): Promise<RecordEditation> {
        return this.ajaxService
            .createRequest(`${RecordEditationDataService.ROUTE}/${editationId}${pathAndParams}`)
            .post(data);
    }

    private async doPutTo(editationId: string, pathAndParams: string, data?: any): Promise<RecordEditation> {
        return this.ajaxService
            .createRequest(`${RecordEditationDataService.ROUTE}/${editationId}${pathAndParams}`)
            .put(data);
    }

    private async doDeleteTo(editationId: string, pathAndParams: string): Promise<RecordEditation> {
        return this.ajaxService
            .createRequest(`${RecordEditationDataService.ROUTE}/${editationId}${pathAndParams}`)
            .delete();
    }
}
