<script lang="ts">
    import type {RecordSearchParams} from 'typings/portaro.be.types';
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import type {YearMonth} from 'shared/components/kp-calendar/types';
    import type {SearchManager} from 'src/features/search/search-manager/search-manager';
    import {asRecordRow} from 'src/features/record-grid/lib/types-utils';
    import {Kind, SearchType} from 'shared/constants/portaro.constants';
    import {onDestroy, onMount} from 'svelte';
    import {getDataViewEventContext} from 'src/features/erp-sutor/pages/data-view/data-view.event-context';
    import {exists, isFunction} from 'shared/utils/custom-utils';
    import {FOND_MONTHLY_ATTENDANCE} from 'src/features/erp-sutor/sutor-fonds';
    import {REFRESH_DATA_VIEW_GRID_EVENT} from 'src/features/erp-sutor/pages/data-view/types';
    import KpPageableSearchResults from 'src/features/search/kp-search-results/KpPageableSearchResults.svelte';
    import KpSearchToolbar from 'src/features/search/kp-search-toolbar/KpSearchToolbar.svelte';
    import KpSearchContext from 'src/features/search/kp-search-context/KpSearchContext.svelte';
    import KpSearchFieldTypeFiltersContainer from 'src/features/search/kp-search-field-type-filter/KpSearchFieldTypeFiltersContainer.svelte';
    import KpBarebonesTable from 'shared/ui-widgets/table/barebones/KpBarebonesTable.svelte';
    import AttendanceEmployeeItem from 'src/features/erp-sutor/pages/data-view/custom-tab-components/employees/AttendanceEmployeeItem.svelte';
    import KpSearchFieldTypeFilter from 'src/features/search/kp-search-field-type-filter/KpSearchFieldTypeFilter.svelte';
    import MonthSelector from 'src/features/erp-sutor/components/attendance-calendar/MonthSelector.svelte';

    const eventContext = getDataViewEventContext();
    let getSearchManager: () => SearchManager<RecordRow>;
    let yearMonth: YearMonth | null = null;

    onMount(() => {
        eventContext.addEventListener(REFRESH_DATA_VIEW_GRID_EVENT, handleRefresh);
    });

    onDestroy(() => {
        eventContext.removeEventListener(REFRESH_DATA_VIEW_GRID_EVENT, handleRefresh);
    });

    function createStaticParams(yearMonthParam: YearMonth): RecordSearchParams {
        return {
            kind: [Kind.KIND_RECORD],
            type: SearchType.TYPE_TABLE,
            rootFond: [{id: FOND_MONTHLY_ATTENDANCE.fond}],
            pageSize: 50,
            [FOND_MONTHLY_ATTENDANCE.yearMonthFieldFilterSearchParam]: yearMonthParam
        };
    }

    const handleRefresh = () => {
        if (!isFunction(getSearchManager)) {
            return;
        }

        const searchManager = getSearchManager();
        searchManager.refreshSearch();
    };

    const handleMonthSelected = (event: CustomEvent<YearMonth>) => {
        if (!exists(yearMonth)) {
            yearMonth = event.detail;
            return;
        }

        yearMonth = event.detail;

        if (!isFunction(getSearchManager)) {
            return;
        }

        const searchManager = getSearchManager();

        searchManager.newSearchWithPartialParams({
            [FOND_MONTHLY_ATTENDANCE.yearMonthFieldFilterSearchParam]: event.detail
        });
    };
</script>

<MonthSelector on:month-selected={handleMonthSelected}/>

{#if exists(yearMonth)}
    <KpSearchContext localSearch staticParams="{createStaticParams(yearMonth)}" let:searchManager bind:getSearchManager>
        <KpSearchFieldTypeFiltersContainer fondId="{FOND_MONTHLY_ATTENDANCE.fond}">
            <div class="sutor-employees-attendance-container">
                <KpSearchToolbar/>

                <KpPageableSearchResults noRefreshLoading pagination="{searchManager.getPagination()}" let:paginationData>
                    <KpBarebonesTable fontSize="13px" stickyHeader>
                        <tr slot="header">
                            <th>
                                <KpSearchFieldTypeFilter fieldTypeId="{FOND_MONTHLY_ATTENDANCE.employeeFieldId}">
                                    Osobní číslo
                                </KpSearchFieldTypeFilter>
                            </th>

                            <th>
                                <KpSearchFieldTypeFilter fieldTypeId="{FOND_MONTHLY_ATTENDANCE.employeeFieldId}">
                                    Pracovník
                                </KpSearchFieldTypeFilter>
                            </th>

                            <th>
                                <KpSearchFieldTypeFilter fieldTypeId="{FOND_MONTHLY_ATTENDANCE.contractTypeFieldId}">
                                    Typ smlouvy
                                </KpSearchFieldTypeFilter>
                            </th>

                            <th>
                                <KpSearchFieldTypeFilter fieldTypeId="{FOND_MONTHLY_ATTENDANCE.divisionFieldId}">
                                    Divize
                                </KpSearchFieldTypeFilter>
                            </th>

                            <th>
                                <KpSearchFieldTypeFilter fieldTypeId="{FOND_MONTHLY_ATTENDANCE.superiorFieldId}">
                                    Nadřízený
                                </KpSearchFieldTypeFilter>
                            </th>

                            <th>
                                <KpSearchFieldTypeFilter fieldTypeId="{FOND_MONTHLY_ATTENDANCE.stateFieldId}">
                                    Stav schválení
                                </KpSearchFieldTypeFilter>
                            </th>

                            <th>Období</th>
                        </tr>

                        <svelte:fragment slot="body">
                            {#each paginationData.items as itemRow(itemRow.id)}
                                {@const record = asRecordRow(itemRow)}

                                <AttendanceEmployeeItem {record} {yearMonth}/>
                            {/each}
                        </svelte:fragment>
                    </KpBarebonesTable>
                </KpPageableSearchResults>
            </div>
        </KpSearchFieldTypeFiltersContainer>
    </KpSearchContext>
{/if}

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    :global {
        .sutor-employees-attendance-container .kp-pageable-search-results {
            margin-top: @spacing-m;
        }
    }
</style>