import {getContext, setContext} from 'svelte';

const employeesAttendanceContextKey = 'employees-attendance-ctx';

export interface EmployeesAttendanceContext {
    test: string;
}

export function createEmployeesAttendanceContext(): EmployeesAttendanceContext {
    return setContext<EmployeesAttendanceContext>(employeesAttendanceContextKey, {
        test: 'WIP'
    });
}

export function getEmployeesAttendanceContext(): EmployeesAttendanceContext {
    return getContext<EmployeesAttendanceContext>(employeesAttendanceContextKey);
}