<script lang="ts">
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import type {YearMonth} from 'shared/components/kp-calendar/types';
    import {getFirstFieldByFieldTypeIdRecursive} from 'src/features/record-grid/lib/grid-fields';
    import {getTextFromGridFieldValue} from 'src/features/erp-sutor/sutor-utils';
    import {FOND_MONTHLY_ATTENDANCE} from 'src/features/erp-sutor/sutor-fonds';
    import GridFieldValue from 'shared/ui-widgets/grid/GridFieldValue.svelte';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';
    import SutorAttendanceCalendar from 'src/features/erp-sutor/components/attendance-calendar/SutorAttendanceCalendar.svelte';

    export let record: RecordRow;
    export let yearMonth: YearMonth;

    const personNumberField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.personNumberFieldId);
    const employeeField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.employeeFieldId);
    const contractTypeField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.contractTypeFieldId);
    const divisionField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.divisionFieldId);
    const superiorField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.superiorFieldId);
    const stateField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.stateFieldId);
    const yearMonthField = getFirstFieldByFieldTypeIdRecursive(record, FOND_MONTHLY_ATTENDANCE.yearMonthFieldId);
</script>

<tr>
    <td>
        <GridFieldValue field="{personNumberField}"/>
    </td>

    <td>
        <GridFieldValue field="{employeeField}">
            <KpUserAvatar userRecordId="{employeeField?.recordReference?.id}" sizePx="{32}"/>
            {getTextFromGridFieldValue(employeeField)}
        </GridFieldValue>
    </td>

    <td>
        <GridFieldValue field="{contractTypeField}"/>
    </td>

    <td>
        <GridFieldValue field="{divisionField}"/>
    </td>

    <td>
        <GridFieldValue field="{superiorField}">
            <KpUserAvatar userRecordId="{superiorField?.recordReference?.id}" sizePx="{32}"/>
            {getTextFromGridFieldValue(superiorField)}
        </GridFieldValue>
    </td>

    <td>
        <GridFieldValue field="{stateField}"/>
    </td>

    <td>
        <GridFieldValue field="{yearMonthField}"/>
    </td>
</tr>

<tr>
    <td colspan="7">
        <div class="attendance-calendar-container">
            <SutorAttendanceCalendar userRecordId="{record.id}"
                                     type="month-with-summary"
                                     withoutLoadAnimations
                                     {yearMonth}/>
        </div>
    </td>
</tr>

<style lang="less">
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro.variables.less";

    .attendance-calendar-container {
        width: 100%;
        padding-bottom: @spacing-ml;
        border-bottom: 1px solid @themed-border-default;
        margin-bottom: @spacing-ml;
    }
</style>