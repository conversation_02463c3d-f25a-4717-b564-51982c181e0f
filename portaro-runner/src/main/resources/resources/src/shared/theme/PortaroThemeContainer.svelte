<script lang="ts">
    import type {PortaroTheme} from './portaro-theme';
    import type {Writable} from 'svelte/store';
    import {createThemeContext, themeLocalStorageKey} from './portaro-theme';
    import {writable} from 'svelte/store';
    import {onDestroy} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';

    export let overrideThemeValue: PortaroTheme | null = null;

    const initialValue: PortaroTheme = overrideThemeValue ?? (exists(window.localStorage.getItem(themeLocalStorageKey)) ? window.localStorage.getItem(themeLocalStorageKey) as PortaroTheme : 'light');
    const portaroTheme: Writable<PortaroTheme> = writable(initialValue);
    let changeThemeTimeoutAction: number | undefined;
    let changeThemeTimeoutEnable: number | undefined;

    createThemeContext({
        currentTheme: portaroTheme,
        changeTheme: setPortaroTheme
    });

    onDestroy(() => {
        themeUnsubscribe();
    });

    const themeUnsubscribe = portaroTheme.subscribe((themeValue) => {
        if (exists(overrideThemeValue)) {  // Don't save theme value into localstorage and cookies when theme is overridden
            return;
        }

        document.cookie = `portaroTheme=${themeValue};path=/;expires=Fri, 31 Dec 9999 23:59:59 GMT`

        if (document?.body) {
            document.body.classList.remove(themeValue === 'light' ? 'portaro-theme-dark' : 'portaro-theme-light');
            document.body.classList.add(`portaro-theme-${themeValue}`);
        }

        window.localStorage.setItem(themeLocalStorageKey, themeValue);
    });

    export function setPortaroTheme(themeValue: PortaroTheme) {
        if (changeThemeTimeoutAction || changeThemeTimeoutEnable) return;

        window.clearTimeout(changeThemeTimeoutAction);
        window.clearTimeout(changeThemeTimeoutEnable);

        const style = document.createElement('style')
        const css = document.createTextNode(`*, *::before, *::after {
            transition: none !important;
        }`);
        style.appendChild(css);

        const changeThemeAction = () => portaroTheme.set(themeValue);
        const disable = () => document.head.appendChild(style);
        const enable = () => document.head.removeChild(style);

        // getComputedStyle forces browser to repaint
        if (typeof window.getComputedStyle !== 'undefined') {
            disable();
            changeThemeAction();
            // Request the computed CSS of the style element we created
            // forcing the browser to evaluate and paint it which in turn disables transitions
            // You need to access any property of the result like `opacity` for it to work
            // eslint-disable-next-line @typescript-eslint/no-unused-expressions, no-unused-expressions
            window.getComputedStyle(style).opacity;
            enable();
            return;
        }

        // requestAnimationFrame processes function before next repaint
        if (typeof window.requestAnimationFrame !== 'undefined') {
            disable();
            changeThemeAction();
            window.requestAnimationFrame(enable);
            return;
        }

        // Fallback, just in case
        disable();
        changeThemeTimeoutAction = window.setTimeout(() => {
            changeThemeAction();
            changeThemeTimeoutAction = undefined;

            changeThemeTimeoutEnable = window.setTimeout(() => {
                enable();
                changeThemeTimeoutEnable = undefined;
            }, 75);
        }, 75);
    }
</script>

<slot/>