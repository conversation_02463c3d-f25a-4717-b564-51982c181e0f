import type {SvelteComponentConstructor, SvelteContext} from 'core/types';
import type {SvelteComponent} from 'svelte';
import {exists} from '../utils/custom-utils';
import {camelCaseToKebabCase, kebabCaseToCamelCase} from 'shared/utils/string-utils';

export function registerCustomElement(name: string,
                                      alternativeNames: string[] | undefined,
                                      componentConstructor: SvelteComponentConstructor,
                                      context: SvelteContext,
                                      propsNames: string[] = []) {

    customElements.define(name, createCustomElement(componentConstructor, context, propsNames));

    if (exists(alternativeNames)) {
        alternativeNames.forEach((alternativeName) => {
            customElements.define(alternativeName, createCustomElement(componentConstructor, context, propsNames));
        });
    }
}

function createCustomElement(componentConstructor: SvelteComponentConstructor, context: SvelteContext, propsNames: string[]) {

    // return anonymous class for each custom element
    return class extends HTMLElement {
        #componentInstance: SvelteComponent;
        #props: Record<string, any> = {};
        #slotContent: string;

        // declare props names used by custom element (attributes not listed here are ignored)
        static get observedAttributes() {
            return propsNames.map(camelCaseToKebabCase);
        }

        // required constructor with mandatory super() call
        constructor() {
            super();
        }

        // like onMount
        connectedCallback() {
            // extract slotted html content
            this.#slotContent = this.innerHTML;
            this.innerHTML = '';

            this.#componentInstance = new componentConstructor({
                target: this as HTMLElement,
                props: {...this.#props, ...this.injectSlot()},
                context
            });
        }

        // like onChange
        attributeChangedCallback(propName: string, oldValue: string | null, newValue: string) {
            const parsedPropName = kebabCaseToCamelCase(propName);
            const parsedValue = JSON.parse(newValue);
            this.#props[parsedPropName] = parsedValue;
            // props are initialized before `connectedCallback`
            if (exists(this.#componentInstance)) {
                this.#componentInstance.$set({[parsedPropName]: parsedValue});
            }
        }

        // like onDestroy
        disconnectedCallback() {
            if (exists(this.#componentInstance)) {
                this.#componentInstance.$destroy();
                this.#componentInstance = null;
            }
            this.innerHTML = this.#slotContent;
        }

        public get slotContent(): string {
            return this.#slotContent;
        }

        public get props(): Record<string, any> {
            return this.#props;
        }

        private create_default_slot() {
            let nodes: Node[];
            const slotHtml = this.#slotContent;
            return {
                c() {
                    nodes = htmlToNodes(slotHtml);
                },
                m(target: Node, anchor: Node) {
                    nodes.forEach((node) => insert(target, node, anchor));
                },
                d(detaching: boolean) {
                    nodes.forEach((node) => {
                        if (detaching) detach(node);
                    });
                }
            };
        }

        private hasSlot(): boolean {
            return exists(this.#slotContent) && this.#slotContent.length > 0;
        }

        private injectSlot(): Record<string, any> {
            if (this.hasSlot()) {
                return {
                    $$slots: {default: [() => this.create_default_slot()]},
                    $$scope: {ctx: []}
                };
            } else {
                return {};
            }
        }
    };
}

function htmlToNodes(html: string): Node[] {
    const div = document.createElement('div');
    div.innerHTML = html.trim();
    return [...div.childNodes];
}

// stolen from svelte/runtime
function insert(target: Node, node: Node, anchor?: Node) {
    target.insertBefore(node, anchor || null);
}

// stolen from svelte/runtime
function detach(node: Node) {
    if (node.parentNode) {
        node.parentNode.removeChild(node);
    }
}
