import {DateTime} from 'luxon';
import {parseIntervalStringToObject, parseObjectToIntervalString, type IntervalString} from './date-range-util';

describe('date-range-util', () => {
    describe('parseIntervalStringToObject', () => {
        it('should handle empty interval', () => {
            const result = parseIntervalStringToObject('empty');
            expect(result).toEqual({});
        });

        it('should parse inclusive interval with dates', () => {
            const interval: IntervalString = '[2023-01-01T00:00:00.000Z,2023-01-02T00:00:00.000Z]';
            const result = parseIntervalStringToObject(interval);

            expect(result.from).toEqual(DateTime.fromISO('2023-01-01T00:00:00.000Z'));
            expect(result.to).toEqual(DateTime.fromISO('2023-01-02T00:00:00.000Z'));
        });

        it('should parse exclusive start interval', () => {
            const interval: IntervalString = '(2023-01-01T00:00:00.000Z,2023-01-02T00:00:00.000Z]';
            const result = parseIntervalStringToObject(interval);

            expect(result.from).toEqual(DateTime.fromISO('2023-01-01T00:00:01.000Z'));
            expect(result.to).toEqual(DateTime.fromISO('2023-01-02T00:00:00.000Z'));
        });

        it('should parse exclusive end interval', () => {
            const interval: IntervalString = '[2023-01-01T00:00:00.000Z,2023-01-02T00:00:00.000Z)';
            const result = parseIntervalStringToObject(interval);

            expect(result.from).toEqual(DateTime.fromISO('2023-01-01T00:00:00.000Z'));
            expect(result.to).toEqual(DateTime.fromISO('2023-01-02T00:00:01.000Z'));
        });

        it('should parse fully exclusive interval', () => {
            const interval: IntervalString = '(2023-01-01T00:00:00.000Z,2023-01-02T00:00:00.000Z)';
            const result = parseIntervalStringToObject(interval);

            expect(result.from).toEqual(DateTime.fromISO('2023-01-01T00:00:01.000Z'));
            expect(result.to).toEqual(DateTime.fromISO('2023-01-02T00:00:01.000Z'));
        });

        it('should handle -infinity start', () => {
            const interval: IntervalString = '[-infinity,2023-01-02T00:00:00.000Z]';
            const result = parseIntervalStringToObject(interval);

            expect(result.from).toBe('-infinity');
            expect(result.to).toEqual(DateTime.fromISO('2023-01-02T00:00:00.000Z'));
        });

        it('should handle infinity end', () => {
            const interval: IntervalString = '[2023-01-01T00:00:00.000Z,infinity]';
            const result = parseIntervalStringToObject(interval);

            expect(result.from).toEqual(DateTime.fromISO('2023-01-01T00:00:00.000Z'));
            expect(result.to).toBe('infinity');
        });

        it('should handle quoted dates', () => {
            const interval: IntervalString = '["2023-01-01T00:00:00.000Z","2023-01-02T00:00:00.000Z"]';
            const result = parseIntervalStringToObject(interval);

            expect(result.from).toEqual(DateTime.fromISO('2023-01-01T00:00:00.000Z'));
            expect(result.to).toEqual(DateTime.fromISO('2023-01-02T00:00:00.000Z'));
        });

        it('should throw error for invalid format', () => {
            expect(() => parseIntervalStringToObject('invalid' as IntervalString)).toThrow('Invalid interval string (too short)');
        });

        it('should throw error for missing comma', () => {
            expect(() => parseIntervalStringToObject('[2023-01-01T00:00:00.000Z 2023-01-02T00:00:00.000Z]' as IntervalString)).toThrow('Invalid interval string (missing comma)');
        });

        it('should throw error for invalid brackets', () => {
            expect(() => parseIntervalStringToObject('{2023-01-01T00:00:00.000Z,2023-01-02T00:00:00.000Z}' as IntervalString)).toThrow('Invalid start bracket');
        });
    });