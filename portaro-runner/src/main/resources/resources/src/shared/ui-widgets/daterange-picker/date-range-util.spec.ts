import {DateTime} from 'luxon';
import {
    parseIntervalStringToObject,
    parseObjectToIntervalString,
    type IntervalString,
    type DateRangeInterval
} from './date-range-util';

describe('date-range-util', () => {
    describe('parseIntervalStringToObject', () => {
        describe('empty intervals', () => {
            it('should handle empty interval', () => {
                const result = parseIntervalStringToObject('empty');
                expect(result).toEqual({});
            });
        });

        describe('inclusive intervals', () => {
            it('should parse fully inclusive interval with dates', () => {
                const interval: IntervalString = '[2023-01-01T00:00:00.000Z,2023-01-02T00:00:00.000Z]';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toEqual(DateTime.fromISO('2023-01-01T00:00:00.000Z'));
                expect(result.to).toEqual(DateTime.fromISO('2023-01-02T00:00:00.000Z'));
            });

            it('should parse inclusive interval with timezone information', () => {
                const interval: IntervalString = '[2023-06-15T14:30:45.123+02:00,2023-06-16T09:15:30.456-05:00]';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toEqual(DateTime.fromISO('2023-06-15T14:30:45.123+02:00'));
                expect(result.to).toEqual(DateTime.fromISO('2023-06-16T09:15:30.456-05:00'));
            });

            it('should parse inclusive interval with millisecond precision', () => {
                const interval: IntervalString = '[2023-12-31T23:59:59.999Z,2024-01-01T00:00:00.001Z]';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toEqual(DateTime.fromISO('2023-12-31T23:59:59.999Z'));
                expect(result.to).toEqual(DateTime.fromISO('2024-01-01T00:00:00.001Z'));
            });
        });

        describe('exclusive intervals', () => {
            it('should parse exclusive start interval by adding 1 second', () => {
                const interval: IntervalString = '(2023-01-01T00:00:00.000Z,2023-01-02T00:00:00.000Z]';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toEqual(DateTime.fromISO('2023-01-01T00:00:01.000Z'));
                expect(result.to).toEqual(DateTime.fromISO('2023-01-02T00:00:00.000Z'));
            });

            it('should parse exclusive end interval by adding 1 second', () => {
                const interval: IntervalString = '[2023-01-01T00:00:00.000Z,2023-01-02T00:00:00.000Z)';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toEqual(DateTime.fromISO('2023-01-01T00:00:00.000Z'));
                expect(result.to).toEqual(DateTime.fromISO('2023-01-02T00:00:01.000Z'));
            });

            it('should parse fully exclusive interval by adding 1 second to both bounds', () => {
                const interval: IntervalString = '(2023-01-01T00:00:00.000Z,2023-01-02T00:00:00.000Z)';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toEqual(DateTime.fromISO('2023-01-01T00:00:01.000Z'));
                expect(result.to).toEqual(DateTime.fromISO('2023-01-02T00:00:01.000Z'));
            });

            it('should handle exclusive bounds with high precision timestamps', () => {
                const interval: IntervalString = '(2023-06-15T12:30:45.123Z,2023-06-15T12:30:45.456Z)';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toEqual(DateTime.fromISO('2023-06-15T12:30:46.123Z'));
                expect(result.to).toEqual(DateTime.fromISO('2023-06-15T12:30:46.456Z'));
            });
        });

        describe('infinity bounds', () => {
            it('should handle -infinity start with inclusive bracket', () => {
                const interval: IntervalString = '[-infinity,2023-01-02T00:00:00.000Z]';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toBe('-infinity');
                expect(result.to).toEqual(DateTime.fromISO('2023-01-02T00:00:00.000Z'));
            });

            it('should handle -infinity start with exclusive bracket', () => {
                const interval: IntervalString = '(-infinity,2023-01-02T00:00:00.000Z]';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toBe('-infinity');
                expect(result.to).toEqual(DateTime.fromISO('2023-01-02T00:00:00.000Z'));
            });

            it('should handle infinity end with inclusive bracket', () => {
                const interval: IntervalString = '[2023-01-01T00:00:00.000Z,infinity]';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toEqual(DateTime.fromISO('2023-01-01T00:00:00.000Z'));
                expect(result.to).toBe('infinity');
            });

            it('should handle infinity end with exclusive bracket', () => {
                const interval: IntervalString = '[2023-01-01T00:00:00.000Z,infinity)';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toEqual(DateTime.fromISO('2023-01-01T00:00:00.000Z'));
                expect(result.to).toBe('infinity');
            });

            it('should handle both infinity bounds', () => {
                const interval: IntervalString = '[-infinity,infinity]';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toBe('-infinity');
                expect(result.to).toBe('infinity');
            });
        });

        describe('quoted dates', () => {
            it('should handle quoted dates correctly', () => {
                const interval: IntervalString = '["2023-01-01T00:00:00.000Z","2023-01-02T00:00:00.000Z"]';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toEqual(DateTime.fromISO('2023-01-01T00:00:00.000Z'));
                expect(result.to).toEqual(DateTime.fromISO('2023-01-02T00:00:00.000Z'));
            });

            it('should handle mixed quoted and unquoted dates', () => {
                const interval: IntervalString = '["2023-01-01T00:00:00.000Z",2023-01-02T00:00:00.000Z]';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toEqual(DateTime.fromISO('2023-01-01T00:00:00.000Z'));
                expect(result.to).toEqual(DateTime.fromISO('2023-01-02T00:00:00.000Z'));
            });

            it('should handle quoted infinity values', () => {
                const interval: IntervalString = '["-infinity","infinity"]';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toBe('-infinity');
                expect(result.to).toBe('infinity');
            });
        });

        describe('whitespace handling', () => {
            it('should handle whitespace around dates', () => {
                const interval: IntervalString = '[ 2023-01-01T00:00:00.000Z , 2023-01-02T00:00:00.000Z ]';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toEqual(DateTime.fromISO('2023-01-01T00:00:00.000Z'));
                expect(result.to).toEqual(DateTime.fromISO('2023-01-02T00:00:00.000Z'));
            });

            it('should handle whitespace with infinity values', () => {
                const interval: IntervalString = '[ -infinity , infinity ]';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toBe('-infinity');
                expect(result.to).toBe('infinity');
            });
        });

        describe('error handling', () => {
            it('should throw error for invalid format (too short)', () => {
                expect(() => parseIntervalStringToObject('x' as IntervalString))
                    .toThrow('Invalid interval string (too short): x');
            });

            it('should throw error for missing comma', () => {
                expect(() => parseIntervalStringToObject('[2023-01-01T00:00:00.000Z 2023-01-02T00:00:00.000Z]' as IntervalString))
                    .toThrow('Invalid interval string (missing comma)');
            });

            it('should throw error for invalid start bracket', () => {
                expect(() => parseIntervalStringToObject('{2023-01-01T00:00:00.000Z,2023-01-02T00:00:00.000Z]' as IntervalString))
                    .toThrow('Invalid start bracket: {');
            });

            it('should throw error for invalid end bracket', () => {
                expect(() => parseIntervalStringToObject('[2023-01-01T00:00:00.000Z,2023-01-02T00:00:00.000Z}' as IntervalString))
                    .toThrow('Invalid end bracket: }');
            });

            it('should throw error for invalid date format', () => {
                expect(() => parseIntervalStringToObject('[invalid-date,2023-01-02T00:00:00.000Z]' as IntervalString))
                    .toThrow('Invalid date format: invalid-date');
            });

            it('should throw error for malformed ISO date', () => {
                expect(() => parseIntervalStringToObject('[2023-13-01T00:00:00.000Z,2023-01-02T00:00:00.000Z]' as IntervalString))
                    .toThrow('Invalid date format: 2023-13-01T00:00:00.000Z');
            });

            it('should throw error for incomplete date', () => {
                expect(() => parseIntervalStringToObject('[2023-01-01,2023-01-02T00:00:00.000Z]' as IntervalString))
                    .toThrow('Invalid date format: 2023-01-01');
            });

            it('should handle empty string bounds gracefully', () => {
                expect(() => parseIntervalStringToObject('[,2023-01-02T00:00:00.000Z]' as IntervalString))
                    .not.toThrow();

                const result = parseIntervalStringToObject('[,2023-01-02T00:00:00.000Z]' as IntervalString);
                expect(result.from).toBe('-infinity');
                expect(result.to).toEqual(DateTime.fromISO('2023-01-02T00:00:00.000Z'));
            });
        });

        describe('edge cases', () => {
            it('should handle same start and end dates', () => {
                const interval: IntervalString = '[2023-01-01T12:00:00.000Z,2023-01-01T12:00:00.000Z]';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toEqual(DateTime.fromISO('2023-01-01T12:00:00.000Z'));
                expect(result.to).toEqual(DateTime.fromISO('2023-01-01T12:00:00.000Z'));
            });

            it('should handle leap year dates', () => {
                const interval: IntervalString = '[2024-02-29T00:00:00.000Z,2024-03-01T00:00:00.000Z]';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toEqual(DateTime.fromISO('2024-02-29T00:00:00.000Z'));
                expect(result.to).toEqual(DateTime.fromISO('2024-03-01T00:00:00.000Z'));
            });

            it('should handle year boundaries', () => {
                const interval: IntervalString = '[2023-12-31T23:59:59.999Z,2024-01-01T00:00:00.000Z]';
                const result = parseIntervalStringToObject(interval);

                expect(result.from).toEqual(DateTime.fromISO('2023-12-31T23:59:59.999Z'));
                expect(result.to).toEqual(DateTime.fromISO('2024-01-01T00:00:00.000Z'));
            });
        });
    });

    describe('parseObjectToIntervalString', () => {
        describe('empty intervals', () => {
            it('should return empty for null interval', () => {
                expect(parseObjectToIntervalString(null as any)).toBe('empty');
            });

            it('should return empty for undefined interval', () => {
                expect(parseObjectToIntervalString(undefined as any)).toBe('empty');
            });

            it('should return empty for empty object', () => {
                expect(parseObjectToIntervalString({})).toBe('empty');
            });

            it('should return empty when both from and to are null', () => {
                expect(parseObjectToIntervalString({from: null as any, to: null as any})).toBe('empty');
            });

            it('should return empty when both from and to are undefined', () => {
                expect(parseObjectToIntervalString({from: undefined, to: undefined})).toBe('empty');
            });
        });

        describe('DateTime objects', () => {
            it('should convert DateTime objects to inclusive interval string', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2023-01-01T00:00:00.000Z'),
                    to: DateTime.fromISO('2023-01-02T00:00:00.000Z')
                };

                const result = parseObjectToIntervalString(interval);
                expect(result).toBe('[2023-01-01T00:00:00.000Z,2023-01-02T00:00:00.000Z]');
            });

            it('should handle DateTime objects with timezone information', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2023-06-15T14:30:45.123+02:00'),
                    to: DateTime.fromISO('2023-06-16T09:15:30.456-05:00')
                };

                const result = parseObjectToIntervalString(interval);
                expect(result).toBe('[2023-06-15T12:30:45.123Z,2023-06-16T14:15:30.456Z]');
            });

            it('should handle DateTime objects with millisecond precision', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2023-12-31T23:59:59.999Z'),
                    to: DateTime.fromISO('2024-01-01T00:00:00.001Z')
                };

                const result = parseObjectToIntervalString(interval);
                expect(result).toBe('[2023-12-31T23:59:59.999Z,2024-01-01T00:00:00.001Z]');
            });

            it('should handle same start and end DateTime', () => {
                const dateTime = DateTime.fromISO('2023-06-15T12:00:00.000Z');
                const interval: DateRangeInterval = {
                    from: dateTime,
                    to: dateTime
                };

                const result = parseObjectToIntervalString(interval);
                expect(result).toBe('[2023-06-15T12:00:00.000Z,2023-06-15T12:00:00.000Z]');
            });
        });

        describe('infinity values', () => {
            it('should handle -infinity start', () => {
                const interval: DateRangeInterval = {
                    from: '-infinity',
                    to: DateTime.fromISO('2023-01-02T00:00:00.000Z')
                };

                const result = parseObjectToIntervalString(interval);
                expect(result).toBe('[-infinity,2023-01-02T00:00:00.000Z]');
            });

            it('should handle infinity end', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2023-01-01T00:00:00.000Z'),
                    to: 'infinity'
                };

                const result = parseObjectToIntervalString(interval);
                expect(result).toBe('[2023-01-01T00:00:00.000Z,infinity]');
            });

            it('should handle both infinity bounds', () => {
                const interval: DateRangeInterval = {
                    from: '-infinity',
                    to: 'infinity'
                };

                const result = parseObjectToIntervalString(interval);
                expect(result).toBe('[-infinity,infinity]');
            });
        });

        describe('missing bounds', () => {
            it('should handle missing from (treated as -infinity)', () => {
                const interval: DateRangeInterval = {
                    to: DateTime.fromISO('2023-01-02T00:00:00.000Z')
                };

                const result = parseObjectToIntervalString(interval);
                expect(result).toBe('[-infinity,2023-01-02T00:00:00.000Z]');
            });

            it('should handle missing to (treated as infinity)', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2023-01-01T00:00:00.000Z')
                };

                const result = parseObjectToIntervalString(interval);
                expect(result).toBe('[2023-01-01T00:00:00.000Z,infinity]');
            });

            it('should handle null from (treated as -infinity)', () => {
                const interval: DateRangeInterval = {
                    from: null as any,
                    to: DateTime.fromISO('2023-01-02T00:00:00.000Z')
                };

                const result = parseObjectToIntervalString(interval);
                expect(result).toBe('[-infinity,2023-01-02T00:00:00.000Z]');
            });

            it('should handle null to (treated as infinity)', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2023-01-01T00:00:00.000Z'),
                    to: null as any
                };

                const result = parseObjectToIntervalString(interval);
                expect(result).toBe('[2023-01-01T00:00:00.000Z,infinity]');
            });
        });

        describe('edge cases', () => {
            it('should handle leap year dates', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2024-02-29T00:00:00.000Z'),
                    to: DateTime.fromISO('2024-03-01T00:00:00.000Z')
                };

                const result = parseObjectToIntervalString(interval);
                expect(result).toBe('[2024-02-29T00:00:00.000Z,2024-03-01T00:00:00.000Z]');
            });

            it('should handle year boundaries', () => {
                const interval: DateRangeInterval = {
                    from: DateTime.fromISO('2023-12-31T23:59:59.999Z'),
                    to: DateTime.fromISO('2024-01-01T00:00:00.000Z')
                };

                const result = parseObjectToIntervalString(interval);
                expect(result).toBe('[2023-12-31T23:59:59.999Z,2024-01-01T00:00:00.000Z]');
            });
        });
    });

    describe('round-trip conversion', () => {
        describe('DateTime preservation', () => {
            it('should maintain data integrity through round-trip conversion', () => {
                const originalInterval: DateRangeInterval = {
                    from: DateTime.fromISO('2023-01-01T12:30:45.123Z'),
                    to: DateTime.fromISO('2023-12-31T23:59:59.999Z')
                };

                const intervalString = parseObjectToIntervalString(originalInterval);
                const parsedBack = parseIntervalStringToObject(intervalString);

                expect(parsedBack.from?.toISO()).toBe(originalInterval.from.toISO());
                expect(parsedBack.to?.toISO()).toBe(originalInterval.to.toISO());
            });

            it('should handle timezone information in round-trip', () => {
                const originalInterval: DateRangeInterval = {
                    from: DateTime.fromISO('2023-06-15T14:30:45.123+02:00'),
                    to: DateTime.fromISO('2023-06-16T09:15:30.456-05:00')
                };

                const intervalString = parseObjectToIntervalString(originalInterval);
                const parsedBack = parseIntervalStringToObject(intervalString);

                // Should preserve the UTC equivalent times
                expect(parsedBack.from?.toUTC().toISO()).toBe(originalInterval.from.toUTC().toISO());
                expect(parsedBack.to?.toUTC().toISO()).toBe(originalInterval.to.toUTC().toISO());
            });

            it('should handle millisecond precision in round-trip', () => {
                const originalInterval: DateRangeInterval = {
                    from: DateTime.fromISO('2023-01-01T00:00:00.001Z'),
                    to: DateTime.fromISO('2023-01-01T00:00:00.999Z')
                };

                const intervalString = parseObjectToIntervalString(originalInterval);
                const parsedBack = parseIntervalStringToObject(intervalString);

                expect(parsedBack.from?.toISO()).toBe(originalInterval.from.toISO());
                expect(parsedBack.to?.toISO()).toBe(originalInterval.to.toISO());
            });
        });

        describe('infinity values preservation', () => {
            it('should handle infinity values in round-trip', () => {
                const originalInterval: DateRangeInterval = {
                    from: '-infinity',
                    to: 'infinity'
                };

                const intervalString = parseObjectToIntervalString(originalInterval);
                const parsedBack = parseIntervalStringToObject(intervalString);

                expect(parsedBack.from).toBe('-infinity');
                expect(parsedBack.to).toBe('infinity');
            });

            it('should handle mixed infinity and DateTime in round-trip', () => {
                const originalInterval: DateRangeInterval = {
                    from: '-infinity',
                    to: DateTime.fromISO('2023-06-15T12:00:00.000Z')
                };

                const intervalString = parseObjectToIntervalString(originalInterval);
                const parsedBack = parseIntervalStringToObject(intervalString);

                expect(parsedBack.from).toBe('-infinity');
                expect(parsedBack.to?.toISO()).toBe(originalInterval.to.toISO());
            });

            it('should handle missing bounds in round-trip', () => {
                const originalInterval: DateRangeInterval = {
                    from: DateTime.fromISO('2023-01-01T00:00:00.000Z')
                    // to is missing, should be treated as infinity
                };

                const intervalString = parseObjectToIntervalString(originalInterval);
                const parsedBack = parseIntervalStringToObject(intervalString);

                expect(parsedBack.from?.toISO()).toBe(originalInterval.from.toISO());
                expect(parsedBack.to).toBe('infinity');
            });
        });

        describe('empty interval preservation', () => {
            it('should handle empty interval in round-trip', () => {
                const originalInterval: DateRangeInterval = {};

                const intervalString = parseObjectToIntervalString(originalInterval);
                expect(intervalString).toBe('empty');

                const parsedBack = parseIntervalStringToObject(intervalString);
                expect(parsedBack).toEqual({});
            });
        });

        describe('exclusive bounds handling', () => {
            it('should not preserve exclusive bounds in round-trip (converts to inclusive)', () => {
                // Start with exclusive interval
                const exclusiveInterval: IntervalString = '(2023-01-01T00:00:00.000Z,2023-01-02T00:00:00.000Z)';
                const parsed = parseIntervalStringToObject(exclusiveInterval);

                // Convert back to string (should be inclusive)
                const backToString = parseObjectToIntervalString(parsed);

                // Should be inclusive with adjusted dates
                expect(backToString).toBe('[2023-01-01T00:00:01.000Z,2023-01-02T00:00:01.000Z]');
            });
        });
    });

    describe('integration tests', () => {
        describe('real-world scenarios', () => {
            it('should handle business hours interval', () => {
                const businessHours: DateRangeInterval = {
                    from: DateTime.fromISO('2023-06-15T09:00:00.000Z'),
                    to: DateTime.fromISO('2023-06-15T17:00:00.000Z')
                };

                const intervalString = parseObjectToIntervalString(businessHours);
                expect(intervalString).toBe('[2023-06-15T09:00:00.000Z,2023-06-15T17:00:00.000Z]');

                const parsed = parseIntervalStringToObject(intervalString);
                expect(parsed.from?.toISO()).toBe(businessHours.from.toISO());
                expect(parsed.to?.toISO()).toBe(businessHours.to.toISO());
            });

            it('should handle multi-day event interval', () => {
                const event: DateRangeInterval = {
                    from: DateTime.fromISO('2023-07-01T00:00:00.000Z'),
                    to: DateTime.fromISO('2023-07-03T23:59:59.999Z')
                };

                const intervalString = parseObjectToIntervalString(event);
                const parsed = parseIntervalStringToObject(intervalString);

                expect(parsed.from?.toISO()).toBe(event.from.toISO());
                expect(parsed.to?.toISO()).toBe(event.to.toISO());
            });

            it('should handle historical data range (with -infinity)', () => {
                const historicalData: DateRangeInterval = {
                    from: '-infinity',
                    to: DateTime.fromISO('2023-01-01T00:00:00.000Z')
                };

                const intervalString = parseObjectToIntervalString(historicalData);
                expect(intervalString).toBe('[-infinity,2023-01-01T00:00:00.000Z]');

                const parsed = parseIntervalStringToObject(intervalString);
                expect(parsed.from).toBe('-infinity');
                expect(parsed.to?.toISO()).toBe(historicalData.to.toISO());
            });

            it('should handle ongoing process (with infinity)', () => {
                const ongoingProcess: DateRangeInterval = {
                    from: DateTime.fromISO('2023-01-01T00:00:00.000Z'),
                    to: 'infinity'
                };

                const intervalString = parseObjectToIntervalString(ongoingProcess);
                expect(intervalString).toBe('[2023-01-01T00:00:00.000Z,infinity]');

                const parsed = parseIntervalStringToObject(intervalString);
                expect(parsed.from?.toISO()).toBe(ongoingProcess.from.toISO());
                expect(parsed.to).toBe('infinity');
            });
        });

        describe('PostgreSQL compatibility', () => {
            it('should parse PostgreSQL-style interval strings', () => {
                // These are typical formats that might come from PostgreSQL
                const pgIntervals = [
                    '[2023-01-01 00:00:00,2023-01-02 00:00:00)',
                    '(2023-06-15 12:30:45.123,2023-06-15 18:45:30.456]',
                    '[-infinity,2023-12-31 23:59:59.999]',
                    '[2023-01-01 00:00:00,infinity)'
                ];

                pgIntervals.forEach(intervalStr => {
                    expect(() => parseIntervalStringToObject(intervalStr as IntervalString)).not.toThrow();
                });
            });

            it('should handle PostgreSQL timestamp format variations', () => {
                const variations = [
                    '[2023-01-01T00:00:00,2023-01-02T00:00:00]',
                    '[2023-01-01T00:00:00.000,2023-01-02T00:00:00.000]',
                    '[2023-01-01T00:00:00.000Z,2023-01-02T00:00:00.000Z]'
                ];

                variations.forEach(intervalStr => {
                    const result = parseIntervalStringToObject(intervalStr as IntervalString);
                    expect(result.from).toBeDefined();
                    expect(result.to).toBeDefined();
                });
            });
        });
    });
});