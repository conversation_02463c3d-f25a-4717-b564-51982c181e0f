<script lang="ts">
    import type {GridFieldValue} from 'src/features/record-grid/lib/types';
    import type {ChipSize} from 'shared/ui-widgets/chip/types';
    import {tooltip} from 'shared/ui-widgets/tooltip/use.tooltip';
    import {exists, isDefined} from 'shared/utils/custom-utils';
    import {getTextFromGridFieldValue} from 'src/features/erp-sutor/sutor-utils';
    import {hasRecordReference} from 'src/features/record-grid/lib/types-utils';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import SutorFondChip from 'src/features/erp-sutor/pages/record-detail/components/SutorFondChip.svelte';

    export let value: string | number | null = null;
    export let field: GridFieldValue | null = null;
    export let unitField: GridFieldValue | null = null;
    export let href: string | null = null;
    export let lengthLimit = 35;
    export let fondId: number | null = null;
    export let chipSize: ChipSize = 'xs';
    export let linkButtonPosition: 'left' | 'right' = 'right';

    $: link = href ?? (exists(field) && hasRecordReference(field) ? `/#!/records/${field.recordReference.id}` : null);
    $: stringValue = isDefined($$props.field) ? getTextFromGridFieldValue(field) : (typeof value === 'number' ? value.toString(10) : value);
    $: displayedText = formatDisplayedText(stringValue, unitField, lengthLimit);
    $: wasShortened = (stringValue ?? '').length > lengthLimit;

    function formatDisplayedText(fullText: string | null, unit: GridFieldValue | null, characterLimit: number): string {
        if (!exists(fullText)) {
            return '';
        }

        if (exists(unit)) {
            return `${fullText.substring(0, characterLimit)} ${getTextFromGridFieldValue(unit)}`;
        }

        return fullText.substring(0, characterLimit);
    }
</script>

<span class="barebones-table-value"
      use:tooltip={{enabled: wasShortened && !$$slots.default, content: stringValue, role: 'tooltip'}}>

    {#if $$slots.default}
        <div class="row-container">
            <slot/>
        </div>
    {:else}
        {#if exists(field?.recordReference?.fond) || exists(fondId)}
            {#if displayedText !== '-'}
                <SutorFondChip fondId="{exists(field?.recordReference?.fond) ? field.recordReference.fond.id : fondId}"
                               fondName="{exists(field?.recordReference?.fond) ? field.recordReference.fond.text : null}" {chipSize}>
                    {displayedText}

                    {#if wasShortened}
                        <span class="ellipsis-icon-container" aria-hidden="true">…</span>
                    {/if}
                </SutorFondChip>
            {:else}
                {displayedText}
            {/if}
        {:else}
            {displayedText}

            {#if wasShortened}
                <span class="ellipsis-icon-container" aria-hidden="true">…</span>
            {/if}
        {/if}
    {/if}

    {#if exists(link)}
        <div class="link-button-container" class:position-left={linkButtonPosition === 'left'}>
            <KpIconButton buttonSize="xs" href="{link}" anchorTarget="_self" icon="link" accented/>
        </div>
    {/if}
</span>

<style lang="less">
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro.variables.less";

    .barebones-table-value {
        position: relative;
        display: inline-flex;
        align-items: center;

        &:hover {
            .link-button-container {
                opacity: 1;
                visibility: visible;
                transform: translate(0, -50%);

                &.position-left {
                    transform: translate(0, -50%);
                }
            }
        }

        .link-button-container {
            position: absolute;
            right: calc(@spacing-s * -1);
            top: 50%;
            opacity: 0;
            transform: translate(@spacing-xs, -50%);
            visibility: hidden;
            transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out, transform 0.2s ease-in-out;

            &.position-left {
                right: unset;
                left: 0;
                transform: translate(@spacing-xs, -50%);
            }
        }

        .ellipsis-icon-container {
            color: var(--accent-blue-new);
        }

        .row-container {
            display: flex;
            align-items: center;
            width: min-content;
            white-space: nowrap;
            gap: @spacing-s;
        }
    }
</style>