import {DateTime} from 'luxon';
import {exists} from 'shared/utils/custom-utils';

const INCLUSIVE_START_BRACKET = '[' as const;
const EXCLUSIVE_START_BRACKET = '(' as const;
const INCLUSIVE_END_BRACKET = ']' as const;
const EXCLUSIVE_END_BRACKET = ')' as const;
const NEGATIVE_INFINITY = '-infinity' as const;
const POSITIVE_INFINITY = 'infinity' as const;
const EMPTY_INTERVAL = 'empty' as const;

type InclusiveStartBracket = typeof INCLUSIVE_START_BRACKET;
type ExclusiveStartBracket = typeof EXCLUSIVE_START_BRACKET;
type InclusiveEndBracket = typeof INCLUSIVE_END_BRACKET;
type ExclusiveEndBracket = typeof EXCLUSIVE_END_BRACKET;
type IntervalStartBracket = InclusiveStartBracket | ExclusiveStartBracket;
type IntervalEndBracket = InclusiveEndBracket | ExclusiveEndBracket;

type BoundPosition = 'start' | 'end';

type DateTimeWithZoneString = string & {readonly __brand: 'DateTimeWithZoneString'};
type IntervalStartDate = DateTimeWithZoneString | typeof NEGATIVE_INFINITY;
type IntervalEndDate = DateTimeWithZoneString | typeof POSITIVE_INFINITY;

type EmptyIntervalString = typeof EMPTY_INTERVAL;

export type IntervalString =
    EmptyIntervalString
    | `${IntervalStartBracket}${IntervalStartDate},${IntervalEndDate}${IntervalEndBracket}`;

export interface DateRangeInterval {
    from?: DateTime | typeof NEGATIVE_INFINITY; // Null = -infinity
    to?: DateTime | typeof POSITIVE_INFINITY; // Null = infinity
}

export function parseIntervalStringToObject(interval: IntervalString): DateRangeInterval {
    if (interval === EMPTY_INTERVAL) {
        return {};
    }

    const startBracket = interval[0] as IntervalStartBracket;
    const endBracket = interval[interval.length - 1] as IntervalEndBracket;

    // Validate brackets
    if (![INCLUSIVE_START_BRACKET, EXCLUSIVE_START_BRACKET].includes(startBracket)) {
        throw new Error(`Invalid start bracket: ${startBracket}`);
    }
    if (![INCLUSIVE_END_BRACKET, EXCLUSIVE_END_BRACKET].includes(endBracket)) {
        throw new Error(`Invalid end bracket: ${endBracket}`);
    }

    const inside = interval.substring(1, interval.length - 1);
    const commaIndex = inside.indexOf(',');

    if (commaIndex < 0) {
        throw new Error(`Invalid interval string (missing comma): ${interval}`);
    }

    const leftPart = inside.substring(0, commaIndex).trim();
    const rightPart = inside.substring(commaIndex + 1).trim();

    const startDate = parseIntervalBound(leftPart, 'start');
    const endDate = parseIntervalBound(rightPart, 'end');

    let adjustedStartDate = startDate;
    let adjustedEndDate = endDate;

    // Handle start-exclusive bound by adding by 1 second
    if (startBracket === EXCLUSIVE_START_BRACKET && startDate !== NEGATIVE_INFINITY && startDate !== null) {
        adjustedStartDate = startDate.plus({seconds: 1});
    }

    // Handle end-exclusive bound by subtracting 1 second
    if (endBracket === EXCLUSIVE_END_BRACKET && endDate !== POSITIVE_INFINITY && endDate !== null) {
        adjustedEndDate = endDate.minus({seconds: 1});
    }

    return {
        from: adjustedStartDate,
        to: adjustedEndDate
    };
}

export function parseObjectToIntervalString(interval: DateRangeInterval): IntervalString {
    if (!exists(interval) || (!exists(interval.from) && !exists(interval.to))) {
        return EMPTY_INTERVAL;
    }

    const startBracket: InclusiveStartBracket = INCLUSIVE_START_BRACKET;
    const endBracket: InclusiveEndBracket = INCLUSIVE_END_BRACKET;

    let startString: string;
    if (!exists(interval.from) || interval.from === NEGATIVE_INFINITY) {
        startString = NEGATIVE_INFINITY;
    } else {
        startString = interval.from.toISO() as DateTimeWithZoneString;
    }

    let endString: string;
    if (!exists(interval.to) || interval.to === POSITIVE_INFINITY) {
        endString = POSITIVE_INFINITY;
    } else {
        endString = interval.to.toISO() as DateTimeWithZoneString;
    }

    return `${startBracket}${startString},${endString}${endBracket}` as IntervalString;
}

function parseIntervalBound(boundString: string, position: 'start'): DateTime | typeof NEGATIVE_INFINITY | null;
function parseIntervalBound(boundString: string, position: 'end'): DateTime | typeof POSITIVE_INFINITY | null;

function parseIntervalBound(boundString: string, position: BoundPosition): DateTime | typeof NEGATIVE_INFINITY | typeof POSITIVE_INFINITY | null {
    if (!boundString || boundString === NEGATIVE_INFINITY) {
        return position === 'start' ? NEGATIVE_INFINITY : null;
    }

    if (boundString === POSITIVE_INFINITY) {
        return position === 'start' ? null : POSITIVE_INFINITY;
    }

    const unquoted = unquoteString(boundString);
    const parsed = DateTime.fromISO(unquoted);

    if (!parsed.isValid) {
        throw new Error(`Invalid date format: ${unquoted}`);
    }

    return parsed;
}

function unquoteString(str: string): string {
    if (str.length >= 2 && str.charAt(0) === '"' && str.charAt(str.length - 1) === '"') {
        return str.substring(1, str.length - 1);
    }

    return str;
}