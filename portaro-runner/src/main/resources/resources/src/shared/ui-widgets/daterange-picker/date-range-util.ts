import {DateTime} from 'luxon';
import {exists} from 'shared/utils/custom-utils';

type InclusiveStartBracket = '[';
type ExclusiveStartBracket = '(';
type InclusiveEndBracket = ']';
type ExclusiveEndBracket = ')';

type IntervalStartBracket = InclusiveStartBracket | ExclusiveStartBracket;
type IntervalEndBracket = InclusiveEndBracket | ExclusiveEndBracket;

type DateTimeWithZoneString = string & {readonly __brand: 'DateTimeWithZoneString'};
type IntervalStartDate = DateTimeWithZoneString | '-infinity';
type IntervalEndDate = DateTimeWithZoneString | 'infinity';

type EmptyIntervalString = 'empty';

export type IntervalString =
    EmptyIntervalString
    | `${IntervalStartBracket}${IntervalStartDate},${IntervalEndDate}${IntervalEndBracket}`;

interface IntervalObject {
    from?: DateTime | '-infinity'; // Null = -infinity
    to?: DateTime | 'infinity'; // Null = infinity
}

// TODO: This function should take an IntervalString and return an IntervalObject
//  handle excluded by subtracting/adding 1s
//  handle empty
//  start can ONLY be datetime or -infinity, end can ONLY be datetime or infinity
//  throw errors for invalid input
export function parseIntervalStringToObject(interval: IntervalString): IntervalObject {
    if (interval === 'empty') {
        return {};
    }

    if (interval.length < 2) {
        throw new Error(`Invalid interval string (too short): ${interval}`);
    }

    const startBracket = interval[0] as IntervalStartBracket;
    const endBracket = interval[interval.length - 1] as IntervalEndBracket;

    // Validate brackets
    if (![('[' as InclusiveStartBracket), ('(' as ExclusiveStartBracket)].includes(startBracket)) {
        throw new Error(`Invalid start bracket: ${startBracket}`);
    }
    if (![(']' as InclusiveEndBracket), (')' as ExclusiveEndBracket)].includes(endBracket)) {
        throw new Error(`Invalid end bracket: ${endBracket}`);
    }

    const inside = interval.substring(1, interval.length - 1);
    const commaIndex = inside.indexOf(',');

    if (commaIndex < 0) {
        throw new Error(`Invalid interval string (missing comma): ${interval}`);
    }

    const leftPart = inside.substring(0, commaIndex).trim();
    const rightPart = inside.substring(commaIndex + 1).trim();

    const startDate = parseIntervalBound(leftPart, 'start');
    const endDate = parseIntervalBound(rightPart, 'end');

    // Handle exclusive bounds by adjusting by 1 second
    let adjustedStartDate = startDate;
    let adjustedEndDate = endDate;

    if (startBracket === '(' && startDate !== '-infinity' && startDate !== null) {
        adjustedStartDate = (startDate as DateTime).plus({seconds: 1});
    }

    if (endBracket === ']' && endDate !== 'infinity' && endDate !== null) {
        adjustedEndDate = (endDate as DateTime).plus({seconds: 1});
    }

    return {
        from: adjustedStartDate,
        to: adjustedEndDate
    };
}

// TODO: Both from and date should return as inclusive, when both from and to are null return empty
export function parseObjectToIntervalString(interval: IntervalObject): IntervalString {
    if (!exists(interval) || (!exists(interval.from) && !exists(interval.to))) {
        return 'empty';
    }

    const startBracket: InclusiveStartBracket = '[';
    const endBracket: InclusiveEndBracket = ']';

    let startString: string;
    if (!exists(interval.from) || interval.from === '-infinity') {
        startString = '-infinity';
    } else {
        startString = (interval.from as DateTime).toISO() as DateTimeWithZoneString;
    }

    let endString: string;
    if (!exists(interval.to) || interval.to === 'infinity') {
        endString = 'infinity';
    } else {
        endString = (interval.to as DateTime).toISO() as DateTimeWithZoneString;
    }

    return `${startBracket}${startString},${endString}${endBracket}` as IntervalString;
}

type BoundPosition = 'start' | 'end';

function parseIntervalBound(boundString: string, position: 'start'): DateTime | '-infinity' | null;
function parseIntervalBound(boundString: string, position: 'end'): DateTime | 'infinity' | null;

function parseIntervalBound(boundString: string, position: BoundPosition): DateTime | '-infinity' | 'infinity' | null {
    if (!boundString || boundString === '-infinity') {
        return position === 'start' ? '-infinity' : null;
    }

    if (boundString === 'infinity') {
        return position === 'start' ? null : 'infinity';
    }

    // Remove quotes if present
    const unquoted = unquoteString(boundString);

    try {
        const parsed = DateTime.fromISO(unquoted);

        if (!parsed.isValid) {
            throw new Error(`Invalid date format: ${unquoted}`);
        }
        return parsed;
    } catch (error) {
        throw new Error(`Failed to parse date bound "${unquoted}": ${error}`);
    }
}

function unquoteString(str: string): string {
    if (str.length >= 2 && str.charAt(0) === '"' && str.charAt(str.length - 1) === '"') {
        return str.substring(1, str.length - 1);
    }
    return str;
}