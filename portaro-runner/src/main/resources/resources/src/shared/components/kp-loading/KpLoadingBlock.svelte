<script lang="ts">
    import type {AvailableSize} from './types';
    import KpLoadingImg from './KpLoadingImg.svelte';

    export let showWhen = true;
    export let color: string | null = null;
    export let fillAvailableSpace = false;

    export let size: AvailableSize = 'md';
</script>

<div class="kp-loading-wrapper"
     class:hidden={!showWhen}
     class:fill-page={fillAvailableSpace}
     {...$$restProps}>

    <KpLoadingImg {size} {color}/>
    <slot/>
</div>

<style lang="less">
    @import (reference) "styles/portaro-erp.less";

    .hidden {
        display: none;
    }

    .kp-loading-wrapper {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 3px;

        &.fill-page {
            .flex-grow();
        }
    }
</style>