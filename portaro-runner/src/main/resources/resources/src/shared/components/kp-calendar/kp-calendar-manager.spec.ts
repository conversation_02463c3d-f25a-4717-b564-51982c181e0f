import {firstValueFrom} from 'rxjs';
import {KpCalendarManager} from './kp-calendar-manager';
import {constructJsDate} from 'shared/utils/date-utils';
import {getCalendarDay, getCalendarMonth, getCalendarYear} from 'shared/components/kp-calendar/utils';

describe('KpCalendarManager', () => {
    describe('data', () => {
        describe('generateDays', () => {
            it('should generate a list of CalendarDay objects for the specified date range', () => {
                const days = KpCalendarManager.createCalendarData({
                    granularity: 'day',
                    from: getCalendarDay(1, 1, 2025),
                    to: getCalendarDay(3, 1, 2025)
                });

                expect(days.length).toBe(3);
                expect(days[0]).toEqual({
                    day: 1,
                    month: 1,
                    year: 2025,
                    week: 1,
                    weekDay: 3 // Wednesday
                });
                expect(days[1]).toEqual({
                    day: 2,
                    month: 1,
                    year: 2025,
                    week: 1,
                    weekDay: 4 // Thursday
                });
                expect(days[2]).toEqual({
                    day: 3,
                    month: 1,
                    year: 2025,
                    week: 1,
                    weekDay: 5 // Friday
                });
            });

            it('should generate correct number of days on a leap year', () => {
                const days = KpCalendarManager.createCalendarData({
                    granularity: 'day',
                    from: getCalendarYear(2024).start,
                    to: getCalendarYear(2024).end
                });

                expect(days.length).toBe(366);
            });

            it('should generate a list of CalendarDay objects for the specified date range between multiple years', () => {
                const days = KpCalendarManager.createCalendarData({
                    granularity: 'day',
                    from: getCalendarDay(10, 10, 2022),
                    to: getCalendarDay(30, 6, 2025)
                });

                const fromDate = constructJsDate(10, 10, 2022);
                const toDate = constructJsDate(30, 6, 2025);
                const totalDays = Math.floor((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

                expect(days.length).toBe(totalDays);

                // First day
                expect(days[0]).toEqual({
                    day: 10,
                    month: 10,
                    year: 2022,
                    week: 41,
                    weekDay: 1 // Monday
                });

                // Last day
                expect(days[days.length - 1]).toEqual({
                    day: 30,
                    month: 6,
                    year: 2025,
                    week: 27,
                    weekDay: 1 // Monday
                });

                // Start + 1 year
                expect(days[365]).toEqual({
                    day: 10,
                    month: 10,
                    year: 2023,
                    week: 41,
                    weekDay: 2 // Tuesday
                });

                // Start + 2 years (2024 was a leap year, so it is 1 day behind)
                expect(days[365 * 2]).toEqual({
                    day: 9,
                    month: 10,
                    year: 2024,
                    week: 41,
                    weekDay: 3 // Wednesday
                });
            });

            it('should handle ranges that span across months', () => {
                const days = KpCalendarManager.createCalendarData({
                    granularity: 'day',
                    from: getCalendarDay(30, 12, 2024),
                    to: getCalendarDay(2, 1, 2025)
                });

                expect(days.length).toBe(4);
                expect(days[0]).toEqual({
                    day: 30,
                    month: 12,
                    year: 2024,
                    week: 1,
                    weekDay: 1 // Monday
                });
                expect(days[1]).toEqual({
                    day: 31,
                    month: 12,
                    year: 2024,
                    week: 1,
                    weekDay: 2 // Tuesday
                });
                expect(days[2]).toEqual({
                    day: 1,
                    month: 1,
                    year: 2025,
                    week: 1,
                    weekDay: 3 // Wednesday
                });
                expect(days[3]).toEqual({
                    day: 2,
                    month: 1,
                    year: 2025,
                    week: 1,
                    weekDay: 4 // Thursday
                });
            });
        });

        describe('generateWeeks', () => {
            it('should generate a list of CalendarWeek objects for the specified date range', () => {
                const weeks = KpCalendarManager.createCalendarData({
                    granularity: 'week',
                    from: getCalendarDay(1, 1, 2025),
                    to: getCalendarDay(14, 1, 2025)
                });

                expect(weeks.length).toBe(3);
                expect(weeks[0].days.length).toBe(5); // 2025 started on Wednesday - total of 5 days remaining
                expect(weeks[1].days.length).toBe(7);
                expect(weeks[2].days.length).toBe(2); // Remaining days - 2
                expect(weeks[0].days[0]).toEqual({
                    day: 1,
                    month: 1,
                    year: 2025,
                    week: 1,
                    weekDay: 3 // Wednesday
                });
                expect(weeks[1].days[6]).toEqual({
                    day: 12,
                    month: 1,
                    year: 2025,
                    week: 2,
                    weekDay: 7 // Sunday
                });
                expect(weeks[weeks.length - 1].days[weeks[weeks.length - 1].days.length - 1]).toEqual({
                    day: 14,
                    month: 1,
                    year: 2025,
                    week: 3,
                    weekDay: 2 // Tuesday
                })
            });

            it('should handle partial weeks at the start or end of the range', () => {
                const weeks = KpCalendarManager.createCalendarData({
                    granularity: 'week',
                    from: getCalendarDay(3, 1, 2025),
                    to: getCalendarDay(9, 1, 2025)
                });

                expect(weeks.length).toBe(2);
                expect(weeks[0].days[0]).toEqual({
                    day: 3,
                    month: 1,
                    year: 2025,
                    week: 1,
                    weekDay: 5 // Friday
                });
                expect(weeks[1].days[weeks[1].days.length - 1]).toEqual({
                    day: 9,
                    month: 1,
                    year: 2025,
                    week: 2,
                    weekDay: 4 // Thursday
                });
            });
        });

        describe('generateMonths', () => {
            it('should generate a list of CalendarMonth objects for the specified year', () => {
                const months = KpCalendarManager.createCalendarData({
                    granularity: 'month',
                    from: getCalendarYear(2025).start,
                    to: getCalendarYear(2025).end
                });

                expect(months.length).toBe(12); // Total months in a year

                // Check January
                expect(months[0].year).toEqual(2025)
                expect(months[0].month).toEqual(1);
                expect(months[0].days.length).toBe(31);
                expect(months[0].weeks.length).toBe(5); // January 2025 has total of 5 weeks in it

                // Check February
                expect(months[1].year).toBe(2025);
                expect(months[1].month).toBe(2);
                expect(months[1].days.length).toBe(28); // 2025 is not a leap year

                // Check December
                expect(months[11].year).toBe(2025);
                expect(months[11].month).toBe(12);
                expect(months[11].days.length).toBe(31);
            });

            it('should handle a date range spanning across multiple years', () => {
                const months = KpCalendarManager.createCalendarData({
                    granularity: 'month',
                    from: getCalendarMonth(6, 2024).start,
                    to: getCalendarMonth(5, 2025).end
                });

                expect(months.length).toBe(12); // Total months in the range

                // First month (June 2024)
                expect(months[0].year).toBe(2024);
                expect(months[0].month).toBe(6);
                expect(months[0].days.length).toBe(30);

                // Last month (May 2025)
                expect(months[months.length - 1].year).toBe(2025);
                expect(months[months.length - 1].month).toBe(5);
                expect(months[months.length - 1].days.length).toBe(31);
            });

            it('should handle partial months at the start and end of the range', () => {
                const months = KpCalendarManager.createCalendarData({
                    granularity: 'month',
                    from: getCalendarDay(15, 6, 2025),
                    to: getCalendarDay(15, 7, 2025)
                });

                expect(months.length).toBe(2); // Two months in the range

                // June 2025
                expect(months[0].year).toBe(2025);
                expect(months[0].month).toBe(6);
                // First day of the range
                expect(months[0].days[0]).toEqual({
                    day: 15,
                    month: 6,
                    year: 2025,
                    week: 24,
                    weekDay: 7
                });
                // 16 days from June 15 to June 30
                expect(months[0].days.length).toBe(16);

                // July 2025
                expect(months[1].year).toBe(2025);
                expect(months[1].month).toBe(7);
                // Last day of the range
                expect(months[1].days[months[1].days.length - 1]).toEqual({
                    day: 15,
                    month: 7,
                    year: 2025,
                    week: 29,
                    weekDay: 2
                });
                // 15 days from July 1 to July 15
                expect(months[1].days.length).toBe(15);
            });

            it('should handle single month ranges', () => {
                const months = KpCalendarManager.createCalendarData({
                    granularity: 'month',
                    from: getCalendarMonth(3, 2025).start,
                    to: getCalendarMonth(3, 2025).end
                });

                expect(months.length).toBe(1); // Only one month in the range
                expect(months[0].year).toBe(2025);
                expect(months[0].month).toBe(3);
                expect(months[0].days.length).toBe(31);
            });

            it('should handle leap years correctly', () => {
                const months = KpCalendarManager.createCalendarData({
                    granularity: 'month',
                    from: getCalendarMonth(2, 2024).start,
                    to: getCalendarMonth(2, 2024).end
                });

                expect(months.length).toBe(1); // Only February
                expect(months[0].year).toBe(2024);
                expect(months[0].month).toBe(2);
                expect(months[0].days.length).toBe(29); // Leap year February
            });
        });
    });

    describe('view calendar', () => {
        beforeEach(() => {
            jasmine.clock().install();
            jasmine.clock().mockDate(constructJsDate(15, 6, 2025));
        });

        afterEach(() => {
            jasmine.clock().uninstall();
        });

        describe('weekView', () => {
            it('should generate the initial view correctly', async () => {
                const calendar = KpCalendarManager.createNavigableCalendar({
                    type: 'weekView',
                });

                const view = calendar.getView$();
                const calendarView = await firstValueFrom(view);
                const days = calendarView.days;

                const weekDays = [
                    {day: 9, month: 6, year: 2025}, // Monday
                    {day: 10, month: 6, year: 2025}, // Tuesday
                    {day: 11, month: 6, year: 2025}, // Wednesday
                    {day: 12, month: 6, year: 2025}, // Thursday
                    {day: 13, month: 6, year: 2025}, // Friday
                    {day: 14, month: 6, year: 2025}, // Saturday
                    {day: 15, month: 6, year: 2025}, // Sunday
                ];

                expect(days.length).toBe(7);

                days.forEach((day, index) => {
                    expect(day.day).toBe(weekDays[index].day);
                    expect(day.month).toBe(weekDays[index].month);
                    expect(day.year).toBe(weekDays[index].year);
                    expect(day.current).toBe(day.day === 15); // June 15, 2025, should be marked as "current"
                });
            });

            it('should navigate to the next week', async () => {
                const calendar = KpCalendarManager.createNavigableCalendar({
                    type: 'weekView',
                });

                const view = calendar.getView$();

                // Initial view
                let calendarView = await firstValueFrom(view);
                let days = calendarView.days;

                expect(days[0].day).toBe(9); // June 9, 2025
                expect(days[6].day).toBe(15); // June 15, 2025
                expect(calendarView.navigationOffset).toBe(0);

                // Navigate forward by one week
                calendar.navigate(1);

                // Updated view
                calendarView = await firstValueFrom(view);
                days = calendarView.days;

                expect(days[0].day).toBe(16); // June 16, 2025
                expect(days[6].day).toBe(22); // June 22, 2025
                expect(calendarView.navigationOffset).toBe(1);
            });

            it('should navigate to the previous week', async () => {
                const calendar = KpCalendarManager.createNavigableCalendar({
                    type: 'weekView',
                });

                const view = calendar.getView$();

                // Initial view
                let calendarView = await firstValueFrom(view);
                let days = calendarView.days;

                expect(days[0].day).toBe(9); // June 9, 2025
                expect(days[6].day).toBe(15); // June 15, 2025
                expect(calendarView.navigationOffset).toBe(0);

                // Navigate backward by one week
                calendar.navigate(-1);

                // Updated view
                calendarView = await firstValueFrom(view);
                days = calendarView.days;

                expect(days[0].day).toBe(2); // June 2, 2025
                expect(days[6].day).toBe(8); // June 8, 2025
                expect(calendarView.navigationOffset).toBe(-1);
            });
        });

        describe('monthView', () => {
            it('should generate the initial view correctly', async () => {
                const calendar = KpCalendarManager.createNavigableCalendar({
                    type: 'monthView',
                });

                const view = calendar.getView$();
                const calendarView = await firstValueFrom(view);
                const days = calendarView.days;

                expect(days.length).toBeGreaterThanOrEqual(28); // Minimum number of days in a month
                expect(days.length).toBeLessThanOrEqual(31); // Maximum number of days in a month

                // Check the first and last days of June
                expect(days[0].day).toBe(1);
                expect(days[0].month).toBe(6);
                expect(days[0].year).toBe(2025);

                expect(days[days.length - 1].day).toBe(30);
                expect(days[days.length - 1].month).toBe(6);
                expect(days[days.length - 1].year).toBe(2025);

                // Verify that June 15 is marked as "current"
                const currentDay = days.find((day) => day.current);
                expect(currentDay).toBeDefined();
                expect(currentDay?.day).toBe(15);
                expect(currentDay?.month).toBe(6);
                expect(currentDay?.year).toBe(2025);
            });

            it('should navigate to the next month', async () => {
                const calendar = KpCalendarManager.createNavigableCalendar({
                    type: 'monthView',
                });

                const view = calendar.getView$();

                // Initial view
                let calendarView = await firstValueFrom(view);
                let days = calendarView.days;

                expect(days[0].month).toBe(6); // June 2025
                expect(calendarView.navigationOffset).toBe(0);

                // Navigate forward by one month
                calendar.navigate(1);

                // Updated view
                calendarView = await firstValueFrom(view);
                days = calendarView.days;

                expect(days[0].month).toBe(7); // July 2025
                expect(calendarView.navigationOffset).toBe(1);
            });

            it('should navigate to the previous month', async () => {
                const calendar = KpCalendarManager.createNavigableCalendar({
                    type: 'monthView',
                });

                const view = calendar.getView$();

                // Initial view
                let calendarView = await firstValueFrom(view);
                let days = calendarView.days;

                expect(days[0].month).toBe(6); // June 2025
                expect(calendarView.navigationOffset).toBe(0);

                // Navigate backward by one month
                calendar.navigate(-1);

                // Updated view
                calendarView = await firstValueFrom(view);
                days = calendarView.days;

                expect(days[0].month).toBe(5); // May 2025
                expect(calendarView.navigationOffset).toBe(-1);
            });
        });
    });
});