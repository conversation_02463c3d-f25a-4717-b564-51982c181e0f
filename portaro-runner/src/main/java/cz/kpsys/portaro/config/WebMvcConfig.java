package cz.kpsys.portaro.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.BasicUserAuthentication;
import cz.kpsys.portaro.auth.current.ActiveUserWebArgumentResolver;
import cz.kpsys.portaro.auth.current.CurrentAuthWebResolver;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.io.BytesRange;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.localization.UserFriendlyExceptionTextResolver;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.web.WebResolver;
import cz.kpsys.portaro.config.modules.AppserverProxyConfig;
import cz.kpsys.portaro.config.modules.DbConsoleConfig;
import cz.kpsys.portaro.config.web.RequestMappingSupportingWebMvcConfigurationSupport;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.validation.AdhocValidator;
import cz.kpsys.portaro.form.validation.ValidFormObjectControllerAdvice;
import cz.kpsys.portaro.search.MapToMapSearchParamsConverter;
import cz.kpsys.portaro.search.view.MapSearchParamsMethodArgumentResolver;
import cz.kpsys.portaro.search.view.SearchTextResolver;
import cz.kpsys.portaro.setting.CoreSettingKeys;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.util.logging.ClientRequestCreatingInterceptor;
import cz.kpsys.portaro.util.logging.ClientSession;
import cz.kpsys.portaro.util.logging.ClientSessionRepository;
import cz.kpsys.portaro.util.logging.ClientSessionWebArgumentResolver;
import cz.kpsys.portaro.view.web.currentdepartment.CurrentDepartmentWebArgumentResolver;
import cz.kpsys.portaro.view.web.customfile.CustomFileResourceResolver;
import cz.kpsys.portaro.view.web.ratelimit.RateLimitInterceptor;
import cz.kpsys.portaro.view.web.rss.RssSearchHttpMessageConverter;
import cz.kpsys.portaro.web.*;
import cz.kpsys.portaro.web.client.WebClientRequest;
import cz.kpsys.portaro.web.exception.ExceptionHttpStatusResolver;
import cz.kpsys.portaro.web.exception.ExceptionObjectResolver;
import cz.kpsys.portaro.web.exception.ExceptionResponseLogger;
import cz.kpsys.portaro.web.mediatype.RequestMatchingContentNegotiationStrategy;
import cz.kpsys.portaro.web.page.CurrentPageFactory;
import cz.kpsys.portaro.web.page.ExceptionModelAndViewResolver;
import cz.kpsys.portaro.web.page.SettableModelAndPageViewFactory;
import cz.kpsys.portaro.web.page.SimpleExceptionModelAndViewResolver;
import cz.kpsys.portaro.web.server.ServerUrlWebArgumentResolver;
import cz.kpsys.portaro.web.swagger.SwaggerDeprecatedPropertyCustomizer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springdoc.core.converters.SchemaPropertyDeprecatingConverter;
import org.springdoc.webmvc.ui.SwaggerWebMvcConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.format.support.FormattingConversionService;
import org.springframework.http.CacheControl;
import org.springframework.http.MediaType;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.converter.xml.MappingJackson2XmlHttpMessageConverter;
import org.springframework.security.web.servlet.util.matcher.PathPatternRequestMatcher;
import org.springframework.web.accept.ContentNegotiationManager;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.ViewResolver;
import org.springframework.web.servlet.config.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.ServletWebArgumentResolverAdapter;

import java.time.Duration;
import java.util.List;

import static java.nio.charset.StandardCharsets.UTF_8;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class WebMvcConfig extends RequestMappingSupportingWebMvcConfigurationSupport {

    @NonNull Provider<@NonNull String> publicContextPath;
    @NonNull ObjectMapper objectMapper;
    @NonNull SettingLoader settingLoader;
    @NonNull WebResolver<Department> currentDepartmentWebResolver;
    @NonNull Provider<Department> rootDepartmentProvider;
    @NonNull CurrentAuthWebResolver currentAuthWebResolver;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull Translator<Department> translator;
    @NonNull ContextualProvider<Department, @NullableNotBlank String> serverUrlProvider;
    @NonNull UrlWebResolver serverUrlWebResolver;
    @NonNull SearchTextResolver searchTitleResolver;
    @NonNull WebResolver<WebClientRequest> webClientRequestWebResolver;
    @NonNull Saver<WebClientRequest, ClientSession> clientRequestSaver;
    @NonNull MapToMapSearchParamsConverter mapToMapSearchParamsConverter;
    @NonNull ClientSessionRepository clientSessionRepository;
    @NonNull FormattingConversionService conversionService;
    @NonNull LocaleResolver portaroLocaleResolver;
    @NonNull Provider<Department> currentDepartmentProvider;
    @NonNull ObjectMapper xmlMapper;
    @NonNull ExceptionHttpStatusResolver exceptionHttpStatusResolver;
    @NonNull ExceptionObjectResolver<Throwable> exceptionResponseObjectResolver;
    @NonNull ExceptionResponseLogger exceptionResponseLogger;
    @NonNull ViewResolver freeMarkerAngularTemplatesViewResolver;
    @NonNull UserFriendlyExceptionTextResolver userFriendlyExceptionTextResolver;
    @NonNull AdhocValidator adhocValidator;
    @NonNull SwaggerWebMvcConfigurer swaggerWebMvcConfigurer;
    @NonNull WebResolver<BytesRange> bytesRangeWebResolver;

    @Bean
    @NonNull
    @Override
    public LocaleResolver localeResolver() {
        return portaroLocaleResolver;
    }

    @Bean
    @NonNull
    @Override
    public FormattingConversionService mvcConversionService() {
        return conversionService;
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/**")
                .allowedMethods("*")
                .allowCredentials(false);
    }

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        // "/db" -> "/db/"
        registry.addRedirectViewController(
                DbConsoleConfig.DB_CONSOLE_PROXY_SERVLET_PATH,
                        publicContextPath.get() + DbConsoleConfig.DB_CONSOLE_PROXY_SERVLET_PATH + "/")
                .setContextRelative(false);

        // "/appserver/manager" -> "/appserver/manager/"
        registry.addRedirectViewController(
                AppserverProxyConfig.APPSERVER_MANAGER_PATH,
                        publicContextPath.get() + AppserverProxyConfig.APPSERVER_MANAGER_PATH + "/")
                .setContextRelative(false);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/favicon.png")
                .addResourceLocations("custom:/design", "classpath:/resources/img/")
                .setOptimizeLocations(true)
                .setCacheControl(CacheControl.maxAge(Duration.ofDays(1)))
                .resourceChain(true)
                .addResolver(new CustomFileResourceResolver());
        registry.addResourceHandler("/resources/**")
                .addResourceLocations("classpath:/resources/")
                .setOptimizeLocations(true)
                .setCacheControl(CacheControl.maxAge(Duration.ofDays(1)))
                .resourceChain(true);
        registry.addResourceHandler("/robots.txt")
                .addResourceLocations("classpath:/resources/")
                .setOptimizeLocations(true)
                .setCacheControl(CacheControl.maxAge(Duration.ofDays(1)))
                .resourceChain(true);
        registry.addResourceHandler("/db-scheduler-ui/**")
                .addResourceLocations("classpath:/static/db-scheduler-ui/")
                .setOptimizeLocations(true)
                .setCacheControl(CacheControl.maxAge(Duration.ofDays(1)))
                .resourceChain(true);
        swaggerWebMvcConfigurer.addResourceHandlers(registry);
    }


    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(new ServletWebArgumentResolverAdapter(new CurrentDepartmentWebArgumentResolver(currentDepartmentWebResolver)));
        resolvers.add(new WebResolverAdaptingHandlerMethodArgumentResolver<>(BasicUserAuthentication.class, currentAuthWebResolver));
        resolvers.add(new ServletWebArgumentResolverAdapter(new ActiveUserWebArgumentResolver(authenticationHolder)));
        resolvers.add(new ServletWebArgumentResolverAdapter(new ServerUrlWebArgumentResolver(serverUrlWebResolver)));
        resolvers.add(new MapSearchParamsMethodArgumentResolver(mapToMapSearchParamsConverter));
        resolvers.add(new ClientSessionWebArgumentResolver(clientSessionRepository));
        resolvers.add(new WebResolverAdaptingHandlerMethodArgumentResolver<>(WebClientRequest.class, webClientRequestWebResolver));
        resolvers.add(new NullableWebResolverAdaptingHandlerMethodArgumentResolver<>(BytesRange.class, bytesRangeWebResolver));
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new ClientRequestCreatingInterceptor(webClientRequestWebResolver, clientRequestSaver));
        registry.addInterceptor(new RateLimitInterceptor(webClientRequestWebResolver));
    }

    @Bean
    @Primary
    @NonNull
    @Override
    public ContentNegotiationManager mvcContentNegotiationManager() {
        return super.mvcContentNegotiationManager();
    }

    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        configurer
                .defaultContentTypeStrategy(requestMatchingContentNegotiationStrategy())
                .favorParameter(true)
                .parameterName(ResponseHelper.FORMAT_PARAMETER_NAME)
                .mediaType("rss20", new MediaType("application", "rss+xml"));
    }

    @Bean
    public RequestMatchingContentNegotiationStrategy requestMatchingContentNegotiationStrategy() {
        return new RequestMatchingContentNegotiationStrategy()
                .add(PathPatternRequestMatcher.withDefaults().matcher("/api/**"), List.of(MediaType.APPLICATION_JSON, MediaType.APPLICATION_XML));
    }

    @Override
    public void extendMessageConverters(@NonNull List<HttpMessageConverter<?>> converters) {
        //primary return format is always json -> add jackson json converter to start of list (but after ByteArray converter - for compatibility with swagger-ui)
        MessageConverterListModifier.removeConvertersOfType(converters, MappingJackson2HttpMessageConverter.class);
        MessageConverterListModifier.addAfterConverterOfType(converters, ByteArrayHttpMessageConverter.class, new MappingJackson2HttpMessageConverter(objectMapper));

        //must set to be before MappingJackson2XmlHttpMessageConverter (String MVC return value should be handled by StringHttpMessageConverter, not MappingJackson2XmlHttpMessageConverter)
        MessageConverterListModifier.replaceConvertersOfType(converters, StringHttpMessageConverter.class, new StringHttpMessageConverter(UTF_8));

        converters.add(2,
                new RssSearchHttpMessageConverter(
                        translator,
                        settingLoader.getDepartmentedProvider(CoreSettingKeys.DEFAULT_LOCALE),
                        serverUrlProvider.throwingWhenNull(),
                        currentDepartmentProvider,
                        searchTitleResolver));

        //remove charset from xml converter to avoid higher specificity than json converter (sorted in
        //MessageConverterListModifier.removeConvertersOfType(converters, MappingJackson2XmlHttpMessageConverter.class); // ... but maybe we cal completely remove xml?? (it is not used for oai and rss)
        MessageConverterListModifier.modifyConvertersOfType(converters, MappingJackson2XmlHttpMessageConverter.class, httpMessageConverter -> httpMessageConverter.setSupportedMediaTypes(List.of(new MediaType("application", "xml"))));
    }

    @Bean
    public ValidFormObjectControllerAdvice requestBodyValidFormObjectControllerAdvice() {
        return new ValidFormObjectControllerAdvice(currentDepartmentProvider, adhocValidator);
    }

    @Bean
    public SettableModelAndPageViewFactory modelAndPageViewFactory() {
        return new SettableModelAndPageViewFactory(
                new CurrentPageFactory.NotInitializedYetCurrentPageFactory(),
                objectMapper,
                translator,
                localeResolver(),
                freeMarkerAngularTemplatesViewResolver
        );
    }

    @Bean
    public ExceptionModelAndViewResolver exceptionModelAndViewResolver() {
        return new SimpleExceptionModelAndViewResolver(
                translator,
                objectMapper,
                xmlMapper,
                exceptionHttpStatusResolver,
                exceptionResponseObjectResolver,
                exceptionResponseLogger,
                userFriendlyExceptionTextResolver,
                modelAndPageViewFactory(),
                mvcContentNegotiationManager(),
                currentDepartmentWebResolver,
                rootDepartmentProvider,
                localeResolver()
        );
    }

    @Bean
    public SchemaPropertyDeprecatingConverter deprecatedPropertyCustomizer() {
        return new SwaggerDeprecatedPropertyCustomizer();
    }

}
