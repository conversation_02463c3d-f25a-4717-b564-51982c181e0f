package cz.kpsys.portaro.config;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.app.PortaroUrls;
import cz.kpsys.portaro.appserver.dml.TableWriteGenerator;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.auth.context.AuthenticatedContextualProvider;
import cz.kpsys.portaro.commons.cache.*;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.convert.IdToObjectConverter;
import cz.kpsys.portaro.commons.convert.ListToModifiedListConverter;
import cz.kpsys.portaro.commons.date.StringToDateRangeConverter;
import cz.kpsys.portaro.commons.date.StringToDatetimeRangeConverter;
import cz.kpsys.portaro.commons.date.StringToInstantConverter;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.*;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.contextual.ContextualVisibleDepartmentsLoader;
import cz.kpsys.portaro.database.JpaAllValuesProvider;
import cz.kpsys.portaro.database.ScalarValueDatabaseLoader;
import cz.kpsys.portaro.databasestructure.RecordDb;
import cz.kpsys.portaro.datatype.DatatypedAcceptableValuesRegistry;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.erp.workattendance.SearchLoadingRecordDayIdLoader;
import cz.kpsys.portaro.file.CacheCleaningIdentifiedFileDeleter;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.directory.AllIdsByDirectoryProvider;
import cz.kpsys.portaro.file.security.FileSecurityActions;
import cz.kpsys.portaro.file.security.IdentifiedFileDeletionCommand;
import cz.kpsys.portaro.file.security.IdentifiedFileSaveCommand;
import cz.kpsys.portaro.filter.FilterLoader;
import cz.kpsys.portaro.filter.FilterLoaderByRowLoader;
import cz.kpsys.portaro.filter.LazyFilter;
import cz.kpsys.portaro.formconfig.valueeditor.AuthenticatedAcceptableValuesResolver;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.id.IDdFile;
import cz.kpsys.portaro.prop.CompositeObjectPropertiesGenerator;
import cz.kpsys.portaro.prop.ObjectPropertiesGenerator;
import cz.kpsys.portaro.record.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.authority.RecordHierarchyLoader;
import cz.kpsys.portaro.record.authority.SpringDbAllAuthorityIdsByDirectoryLoader;
import cz.kpsys.portaro.record.authority.SpringDbAuthorityHierarchyLoader;
import cz.kpsys.portaro.record.authority.SpringDbAuthoritySearchLoader;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.detail.appservermarc.MarcXmlToDetailConverterImpl;
import cz.kpsys.portaro.record.detail.constraints.LinkConstraintsResolver;
import cz.kpsys.portaro.record.detail.spec.RecordIdFieldTypeId;
import cz.kpsys.portaro.record.document.*;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import cz.kpsys.portaro.record.edit.RecordValidator;
import cz.kpsys.portaro.record.edit.authorityfield008.AuthorityField008Definitions;
import cz.kpsys.portaro.record.edit.authorityfield008.AuthorityField008DefinitionsLoader;
import cz.kpsys.portaro.record.edit.authorityfield008.AuthorityField008EditFormData;
import cz.kpsys.portaro.record.edit.field007.*;
import cz.kpsys.portaro.record.edit.field008.Field008CodeEntity;
import cz.kpsys.portaro.record.edit.field008.Field008Definitions;
import cz.kpsys.portaro.record.edit.field008.Field008DefinitionsLoader;
import cz.kpsys.portaro.record.edit.field008.Field008EditFormData;
import cz.kpsys.portaro.record.evaluation.*;
import cz.kpsys.portaro.record.file.IdentifiedFileSaverRecordPrimaryImageSavingDecorator;
import cz.kpsys.portaro.record.file.PrimaryImageToRecordsSaveCommand;
import cz.kpsys.portaro.record.file.PrimaryImageToRecordsSaver;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.holding.RecordHoldingLoader;
import cz.kpsys.portaro.record.link.RecordLinkLoader;
import cz.kpsys.portaro.record.link.SearchElsewhereItemsRecordLinkLoader;
import cz.kpsys.portaro.record.link.SpringDbObalkyKnihRecordLinkLoader;
import cz.kpsys.portaro.record.list.RecordIdStorage;
import cz.kpsys.portaro.record.list.RecordIdStorageImpl;
import cz.kpsys.portaro.record.load.RecordDayIdLoader;
import cz.kpsys.portaro.record.operation.*;
import cz.kpsys.portaro.record.prop.DetailDelegatingRecordPropertiesGenerator;
import cz.kpsys.portaro.record.prop.DetailRecordPropertiesGenerator;
import cz.kpsys.portaro.record.prop.ReusingRecordPropertiesGenerator;
import cz.kpsys.portaro.record.prop.SimpleDetailRecordPropertiesGenerator;
import cz.kpsys.portaro.record.revision.ConvertingRecordRevisionLoader;
import cz.kpsys.portaro.record.revision.EntityToRecordRevisionConverter;
import cz.kpsys.portaro.record.revision.RecordRevisionLoader;
import cz.kpsys.portaro.record.revision.SpringDbRecordRevisionEntityLoader;
import cz.kpsys.portaro.record.view.RecordShowListener;
import cz.kpsys.portaro.record.viewcount.SpringDbRecordShowCountSaver;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.security.PermissionFactory;
import cz.kpsys.portaro.security.PermissionRegistry;
import cz.kpsys.portaro.security.PermissionResult;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.Where;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.matcher.WebAccess;
import cz.kpsys.portaro.view.web.rest.record.AssignableRecordDepartmentsAuthenticatedContextualProvider;
import cz.kpsys.portaro.view.web.rest.record.RecordHoldingCreationRequest;
import jakarta.persistence.EntityManager;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.web.context.WebApplicationContext;

import java.time.Duration;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;
import java.util.function.Function;

import static cz.kpsys.portaro.security.PermissionResolver.and;
import static cz.kpsys.portaro.security.PermissionResolver.withoutSubject;
import static cz.kpsys.portaro.user.BasicUser.ROLE_ADMIN;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RecordConfig {

    @NonNull ContextualProvider<Department, @NullableNotBlank String> serverUrlProvider;
    @NonNull Codebook<RecordStatus, Integer> recordStatusLoader;
    @NonNull Codebook<Fond, Integer> fondLoader;
    @NonNull AllValuesProvider<Fond> enabledFondsProvider;
    @NonNull CacheService cacheService;
    @NonNull CacheDeletableById recordCache;
    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull NamedParameterJdbcOperations notCriticalJdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull EntityManager entityManager;
    @NonNull TransactionTemplateFactory readonlyTransactionTemplateFactory;
    @NonNull MappingAppserverService mappingAppserver;
    @NonNull SettingLoader settingLoader;
    @NonNull ConverterRegisterer converterRegisterer;
    @NonNull PermissionRegistry permissionRegistry;
    @NonNull SecurityManager securityManager;
    @NonNull PermissionFactory permissionFactory;
    @NonNull FilterLoader filterLoader;
    @NonNull HttpServletRequest httpServletRequest;
    @NonNull ByIdLoadable<@Nullable AllValuesProvider<? extends LabeledIdentified<?>>, ScalarDatatype> acceptableValuesProviderLoader;
    @NonNull Saver<IdentifiedFileSaveCommand, IdentifiedFile> loadedIdentifiedFileSaver;
    @NonNull Saver<IdentifiedFile, IdentifiedFile> identifiedFileSaver;
    @NonNull Deleter<IDdFile> pureIdentifiedFileDeleter;
    @NonNull ModelBeanBuilder modelBeanBuilder;
    @NonNull SaverBuilderFactory saverBuilderFactory;
    @NonNull AuthenticatedContextualProvider<Department, List<Department>> editableDepartmentsAuthenticatedContextualProvider;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull RecordHoldingLoader recordHoldingLoader;
    @NonNull ExecutorService executorService;
    @NonNull DepartmentAccessor departmentAccessor;
    @NonNull Function<@NonNull IDdFile, IdentifiedFile> fullIdentifiedFileConverter;
    @NonNull Function<String, List<Fond>> subkindToEnabledFondsExpander;
    @NonNull Function<Fond, List<Fond>> enabledLoadableFondsExpander;
    @NonNull DatatypedAcceptableValuesRegistry allowedDatatypeToAllValuesProviderMap;
    @NonNull DatatypedAcceptableValuesRegistry allDatatypeToAllValuesProviderMap;
    @NonNull Codebook<IndicatorType, FieldTypeId> indicatorTypeLoader;
    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;
    @NonNull IdAndIdsLoadable<Record, UUID> richRecordLoader;
    @NonNull ByIdLoadable<RecordIdFondPair, UUID> recordFondPairsLoader;
    @NonNull FieldTypeLoader fieldTypeLoader;
    @NonNull InternalRecordLoader internalRecordAllByIdsLoader;
    @NonNull StringToInstantConverter recordFieldStringToInstantConverter;
    @NonNull StringToDateRangeConverter recordFieldStringToDateRangeConverter;
    @NonNull StringToDatetimeRangeConverter recordFieldStringToDatetimeRangeConverter;
    @NonNull IdAndIdsLoadable<Record, Integer> authorityByKindedIdLoader;
    @NonNull IdAndIdsLoadable<Record, Integer> documentByKindedIdLoader;
    @NonNull IdAndIdsLoadable<Record, Integer> nonDetailedDocumentByKindedIdLoader;
    @NonNull IdAndIdsLoadable<Record, Integer> nonDetailedAuthorityByKindedIdLoader;
    @NonNull IdAndIdsLoadable<Record, UUID> nonDetailedRichRecordLoader;
    @NonNull Codebook<RecordOperationType, Integer> recordOperationTypeLoader;
    @NonNull FieldTypesByFondLoader fieldTypesByFondLoader;
    @NonNull Provider<@NonNull Fond> dayFondProvider;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull Runnable saveTransactionAuthenticator;

    // FIXME: Pozor, zatím neumí ukládat nové Recordy, ale pouze reuložit ty z databáze!
    @Bean
    public Saver<Record, RecordEntity> recordHeaderSaver() {
        var saver = saverBuilderFactory.jpaSaver(Record.class)
                .intermediateConverting(new RecordToEntityConverter(), RecordEntity.class)
                .build();
        saver.addPostSuccessHook(entity -> {
            recordCache.deleteFromCacheById(entity.getId());
        });
        return saver;
    }


    @Bean
    public RatingSaver ratingSaver() {
        return new RatingSaverEvaluatedCheckingProxy(ratingLoader(), byUserEvaluatedDocuments());
    }


    @Bean
    public SpringDbRatingLoaderAndSaver ratingLoader() {
        return new SpringDbRatingLoaderAndSaver(jdbcTemplate, queryFactory, recordCache);
    }


    @Bean
    public RecordNameResolver recordNameResolver() {
        return new RecordNameResolver(recordEntryFieldTypeIdResolver);
    }

    /**
     * TODO: now there is missing functionality in loading and showing rating!
     */
    @Bean
    public RowMapper<DocumentRating> documentRatingRowMapper() {
        return new DocumentRatingRowMapper();
    }


    @Bean
    @Scope(value = WebApplicationContext.SCOPE_SESSION, proxyMode = ScopedProxyMode.INTERFACES)
    public RecordIdStorage byUserEvaluatedDocuments() {
        return new RecordIdStorageImpl();
    }


    @Bean
    public RecordShowListener recordShowCountSaver() {
        return new SpringDbRecordShowCountSaver(jdbcTemplate, queryFactory, executorService);
    }

    @Bean
    public SimilarRecordsLoader similarRecordsLoader() {
        return new SpringDbSimilarRecordsLoader(
                notAutoCommittingJdbcTemplate,
                queryFactory,
                readonlyTransactionTemplateFactory.get(),
                settingLoader.getOnRootProvidedList(RecordSettingKeys.FORBIDDEN_RECORDS),
                enabledFondsProvider,
                richRecordLoader,
                departmentAccessor
        );
    }

    @Bean
    public ObjectPropertiesGenerator<Record> recordPropertiesGenerator() {
        return CompositeObjectPropertiesGenerator.of(List.of(
                new ReusingRecordPropertiesGenerator(),
                new DetailDelegatingRecordPropertiesGenerator(detailRecordPropertiesGenerator())
        ));
    }

    @Bean
    public DetailRecordPropertiesGenerator detailRecordPropertiesGenerator() {
        return new SimpleDetailRecordPropertiesGenerator();
    }

    @Bean
    public RecordHierarchyLoader recordHierarchyLoader() {
        return new SpringDbAuthorityHierarchyLoader(jdbcTemplate, queryFactory);
    }

    @Bean
    public ByIdsLoadableRepository<Record, UUID> externalRecordRepository() {
        return CacheBackedRepository.of(new GuavaTimedDynamicCache<>(Record::getId, Duration.ofMinutes(5), false), Record::getId);
    }

    @Bean
    public IdAndIdsLoadable<Record, UUID> recordLoader() {
        return CompositeByIdLoader.of(
                richRecordLoader,
                externalRecordRepository()
        );
    }

    @Bean
    public ByIdLoadable<FieldType<?>, FieldTypeId> subfieldTypeLoader() {
        return fieldTypeLoader::getSubfieldTypeById;
    }

    @Bean
    public RecordValidator recordValidator() {
        return new RecordValidator(
                recordEntryFieldTypeIdResolver,
                fieldTypesByFondLoader
        );
    }

    @Bean
    public DynamicCache<Record> exportedRecordCache() {
        return new GuavaTimedDynamicCache<>(Record::getId, Duration.ofSeconds(10), false).throwWhenCacheIsFull();
    }

    @Bean
    public IdAndIdsLoadable<Record, UUID> exportedRecordLoader() {
        return new DelegatingRichRecordsByIdsLoader(
                exportedRecordCache(),
                exportedRecordDetailLoader(),
                internalRecordAllByIdsLoader,
                true
        );
    }

    @Bean
    public AllByIdsLoadable<IdentifiedFieldContainer, Record> exportedRecordDetailLoader() {
        MarcXmlToDetailConverterImpl detailFromMarcXmlConstructor = new MarcXmlToDetailConverterImpl(
                false,
                fieldTypesByFondLoader,
                recordFieldStringToInstantConverter,
                recordFieldStringToDateRangeConverter,
                recordFieldStringToDatetimeRangeConverter,
                fondLoader
        );
        AppserverRecordDetailLoader bean = new AppserverRecordDetailLoader(
                mappingAppserver,
                true, //export mode
                detailFromMarcXmlConstructor,
                fondLoader
        );
        return ChunkingAllByIdsLoader.of(bean, Identified::getId, Record::getId).withChunkSize(50);
    }

    @Bean
    public Converter<List<String>, List<Record>> stringIdsToRecordsConverter() {
        return new IdsToRecordsConverter()
                .withAuthoritySupport(authorityByKindedIdLoader)
                .withDocumentSupport(documentByKindedIdLoader)
                .withRecordSupport(richRecordLoader);
    }

    @Bean
    public Converter<List<UUID>, List<? extends Record>> idsToRecordsConverter() {
        return richRecordLoader::getAllByIds;
    }

    @Bean
    public ByIdLoadable<@NonNull Record, String> recordStringIdPrefixDispatchingLoader() {
        return new DispatchingByStringIdLoader<Record>(RecordConstants.IdPrefixes.DELIMITER)
                .addUuid(recordLoader())
                .addPrefixedIntegerId(RecordConstants.IdPrefixes.DOCUMENT_KINDED_ID, documentByKindedIdLoader)
                .addPrefixedStringId(RecordConstants.IdPrefixes.RECORD_FIELD1_ID, kpwIdZaz -> {
                    UUID recordId = recordIdByField1ValueLoader().getById(kpwIdZaz);
                    return recordLoader().getById(recordId);
                });
    }

    /**
     * Currently loads only documents, but it could be improved to support also authorities (after record_field feature will be finished)
     */
    @Bean
    public ByIdLoadable<@NonNull UUID, String> recordIdByField1ValueLoader() {
        return new TransactionalByIdLoadable<>(
                field1Value -> {
                    StringUtil.assertHasTrimmedLength(field1Value, () -> "Given field 001 value cannot be empty.");
                    ScalarValueDatabaseLoader<UUID> loader = ScalarValueDatabaseLoader.ofQuery(UUID.class, notAutoCommittingJdbcTemplate, queryFactory, RecordDb.KAT1_1.TABLE, RecordDb.KAT1_1.RECORD_ID, new Consumer<>() {
                        @Override
                        public void accept(Where where) {
                            where.eq(RecordDb.KAT1_1.CIS_POL, RecordWellKnownFields.DocumentControlNumber.NUMBER)
                                    .and()
                                    .eq(RecordDb.KAT1_1.OBSAH, field1Value);
                        }

                        @Override
                        public String toString() {
                            return "WhereCondition[%s=%s,%s=%s]".formatted(RecordDb.KAT1_1.CIS_POL, RecordWellKnownFields.DocumentControlNumber.NUMBER, RecordDb.KAT1_1.OBSAH, field1Value);
                        }
                    });
                    return loader.get();
                },
                readonlyTransactionTemplateFactory.get()
        );
    }

    @Bean
    public AuthoritySourceDocumentProvider authoritySourceDocumentProvider() {
        return new SpringDbAuthoritySourceDocumentProvider(jdbcTemplate, queryFactory);
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, Record> relatedRecordsSearchLoader() {
        var idSearchLoader = modelBeanBuilder.idSearchLoader(MapBackedParams::createEmpty, new SpringDbRelatedRecordsIdSearchLoader(jdbcTemplate, queryFactory)).build();
        return modelBeanBuilder.modelSearchLoaderByIdSearchLoader(MapBackedParams::createEmpty, idSearchLoader, recordLoader()).build();
    }

    @Bean
    public TableWriteGenerator<RecordOperation> recordOperationTableWriteGenerator() {
        return modelBeanBuilder.modelTableWriteGenerator(new RecordOperationToEntityConverter());
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, UUID> recordOperationRecordIdSearchLoader() {
        SpringDbRecordOperationEntitySearchLoader pureSearchLoader = new SpringDbRecordOperationEntitySearchLoader(jdbcTemplate, queryFactory);

        ParametersConvertingPageSearchLoader<MapBackedParams, RecordOperationEntity, RangePaging> paramsConvertingSearchLoader = new ParametersConvertingPageSearchLoader<>(pureSearchLoader)
                .withExpandingParam(RecordConstants.SearchParams.OPERATED_SUBKIND, RecordConstants.SearchParams.OPERATED_FOND, subkindToEnabledFondsExpander)
                .withExpandingParam(RecordConstants.SearchParams.OPERATED_ROOT_FOND, RecordConstants.SearchParams.OPERATED_FOND, enabledLoadableFondsExpander);

        return new ParameterizedSearchLoaderImpl<>(
                MapBackedParams::createEmpty,
                new ResultConvertingPageSearchLoader<>(
                        paramsConvertingSearchLoader,
                        entitiesToRecordOperationRecordIdsConverter()
                )
        );
    }

    @Bean
    public Converter<List<RecordOperationEntity>, List<? extends UUID>> entitiesToRecordOperationRecordIdsConverter() {
        return new EntitiesToRecordOperationRecordIdsConverter();
    }


    @Bean
    public RecordRevisionLoader recordRevisionLoader() {
        return new ConvertingRecordRevisionLoader(
                new SpringDbRecordRevisionEntityLoader(jdbcTemplate, queryFactory),
                new ListToModifiedListConverter<>(new EntityToRecordRevisionConverter(nonDetailedDocumentByKindedIdLoader, nonDetailedAuthorityByKindedIdLoader))
        );
    }


    @Bean
    public DocumentContentTypeResolver documentContentTypeResolver() {
        return new CompositeDocumentContentTypeResolver();
    }


    @Bean
    public RecordLinkLoader externalDetailRecordLinkLoader() {
        return new SpringDbObalkyKnihRecordLinkLoader(jdbcTemplate, queryFactory);
    }

    @Bean
    public RecordLinkLoader searchElsewhereRecordLinkLoader() {
        return new SearchElsewhereItemsRecordLinkLoader(
                settingLoader.getOnRootProvidedList(SettingKeys.SEARCH_ELSEWHERE_ITEMS));
    }


    @Bean
    public Saver<IdentifiedFileSaveCommand, IdentifiedFile> primaryCoverSavingLoadedIdentifiedFileSaver() {
        return new IdentifiedFileSaverRecordPrimaryImageSavingDecorator(
                loadedIdentifiedFileSaver,
                identifiedFileSaver,
                allRecordIdsByDirectoryProvider(),
                allAuthorityIdsByDirectoryProvider(),
                nonDetailedRichRecordLoader,
                recordHeaderSaver(),
                primaryImageToRecordsSaver()
        );
    }

    @Bean
    public Saver<PrimaryImageToRecordsSaveCommand, List<Record>> primaryImageToRecordsSaver() {
        return new GenericHookableSaver<>(
                new PrimaryImageToRecordsSaver(
                        notAutoCommittingJdbcTemplate,
                        queryFactory
                )
        ).addPreHook(saveTransactionAuthenticator);
    }

    @Bean
    public Deleter<IDdFile> identifiedFileDeleter() {
        var saver = new GenericHookableDeleter<>(
                new TransactionalDeleter<>(
                        new CacheCleaningIdentifiedFileDeleter(
                                pureIdentifiedFileDeleter,
                                fullIdentifiedFileConverter,
                                recordCache,
                                allRecordIdsByDirectoryProvider(),
                                allAuthorityIdsByDirectoryProvider()
                        ),
                        defaultTransactionTemplateFactory.get()
                )
        );
        saver.addPreHook(saveTransactionAuthenticator);
        return saver;
    }

    @Bean
    public Deleter<IdentifiedFileDeletionCommand> securedIdentifiedFileDeleter() {
        Deleter<IDdFile> delegate = identifiedFileDeleter();
        return cmd -> {
            securityManager.throwIfCannot(FileSecurityActions.FILES_MANAGE, cmd.currentAuth(), cmd.ctx());
            delegate.delete(cmd.file());
        };
    }

    @Bean
    public AllIdsByDirectoryProvider allRecordIdsByDirectoryProvider() {
        return new CachedAllRecordIdsByDirectoryLoader(
                jdbcTemplate,
                queryFactory,
                settingLoader.getOnRoot(RecordSettingKeys.FORBIDDEN_RECORDS),
                settingLoader.getOnRoot(RecordSettingKeys.FORBIDDEN_RECORD_STATUSES)
        );
    }

    @Bean
    public SpringDbAllAuthorityIdsByDirectoryLoader allAuthorityIdsByDirectoryProvider() {
        return new SpringDbAllAuthorityIdsByDirectoryLoader(jdbcTemplate, queryFactory);
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, UUID> notCriticalAuthorityIdSearchSqlLoader() {
        SpringDbAuthoritySearchLoader pureSearchLoader = new SpringDbAuthoritySearchLoader(notCriticalJdbcTemplate, queryFactory);

        ParametersConvertingPageSearchLoader<MapBackedParams, UUID, RangePaging> paramsConvertingSearchLoader = new ParametersConvertingPageSearchLoader<>(pureSearchLoader)
                .withExpandingParam(CoreSearchParams.SUBKIND, RecordConstants.SearchParams.FOND, subkindToEnabledFondsExpander)
                .withExpandingParam(RecordConstants.SearchParams.ROOT_FOND, RecordConstants.SearchParams.FOND, enabledLoadableFondsExpander);

        return new ParameterizedSearchLoaderImpl<>(
                MapBackedParams::createEmpty,
                paramsConvertingSearchLoader
        );
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, Record> notCriticalDetailedAuthoritySearchSqlLoader() {
        return new ParameterizedSearchLoaderImpl<>(
                MapBackedParams::createEmpty,
                new ResultConvertingPageSearchLoader<>(
                        notCriticalAuthorityIdSearchSqlLoader(),
                        idsToRecordsConverter()
                )
        );
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, UUID> documentIdSearchSqlLoader() {
        SpringDbDocumentIdSearchLoader pureSearchLoader = new SpringDbDocumentIdSearchLoader(jdbcTemplate, queryFactory, departmentAccessor);

        ParametersConvertingPageSearchLoader<MapBackedParams, UUID, RangePaging> paramsConvertingSearchLoader = new ParametersConvertingPageSearchLoader<>(pureSearchLoader)
                .withExpandingParam(CoreSearchParams.SUBKIND, RecordConstants.SearchParams.FOND, subkindToEnabledFondsExpander)
                .withExpandingParam(RecordConstants.SearchParams.ROOT_FOND, RecordConstants.SearchParams.FOND, enabledLoadableFondsExpander);

        return new ParameterizedSearchLoaderImpl<>(
                MapBackedParams::createEmpty,
                paramsConvertingSearchLoader
        );
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, Record> nonDetailedRecordSearchSqlLoader() {
        return new ParameterizedSearchLoaderImpl<>(
                MapBackedParams::createEmpty,
                new ResultConvertingPageSearchLoader<>(
                        documentIdSearchSqlLoader(),
                        nonDetailedRichRecordLoader::getAllByIds
                )
        );
    }

    @Bean
    public ParameterizedSearchLoader<MapBackedParams, Record> detailedRecordSearchSqlLoader() {
        return new ParameterizedSearchLoaderImpl<>(
                MapBackedParams::createEmpty,
                new ResultConvertingPageSearchLoader<>(
                        documentIdSearchSqlLoader(),
                        idsToRecordsConverter()
                )
        );
    }

    @Bean
    public LinkConstraintsResolver linkConstraintsResolver() {
        return new LinkConstraintsResolver(
                nonDetailedRichRecordLoader,
                richRecordLoader,
                detailedRecordSearchSqlLoader(),
                fieldTypesByFondLoader,
                contextHierarchyLoader
        );
    }

    @Bean
    public RecordDayIdLoader recordDayIdLoader() {
        return new SearchLoadingRecordDayIdLoader(
                documentIdSearchSqlLoader(),
                dayFondProvider
        );
    }

    @Bean
    public Field007DefinitionsLoader field007DefinitionsLoader() {
        Field007DefinitionsLoader bean = new Field007DefinitionsLoader(
                new JpaAllValuesProvider<>(new SimpleJpaRepository<>(Field007DocumentCategoryEntity.class, entityManager)),
                new JpaAllValuesProvider<>(new SimpleJpaRepository<>(Field007CodeEntity.class, entityManager)),
                new JpaAllValuesProvider<>(new SimpleJpaRepository<>(Field007LabelEntity.class, entityManager))
        );
        cacheService.registerSpringCacheCleaner(Field007Definitions.class.getSimpleName(), Field007DefinitionsLoader.CACHE_NAME);
        return bean;
    }

    @Bean
    public Field008DefinitionsLoader field008DefinitionsLoader() {
        Field008DefinitionsLoader bean = new Field008DefinitionsLoader(
                acceptableValuesProviderLoader,
                new JpaAllValuesProvider<>(new SimpleJpaRepository<>(Field008CodeEntity.class, entityManager)),
                StaticAllValuesProvider.of(Field008EditFormData.getLabelEntities()),
                StaticAllValuesProvider.of(Field008EditFormData.getDocumentTypeEntities()),
                StaticProvider.of(Field008EditFormData.getPublicationStatus()),
                StaticProvider.of(Field008EditFormData.getModifiedRecord()),
                StaticProvider.of(Field008EditFormData.getCatalogingSource())
        );
        cacheService.registerSpringCacheCleaner(Field008Definitions.class.getSimpleName(), Field008DefinitionsLoader.CACHE_NAME);
        return bean;
    }


    @Bean
    public AuthorityField008DefinitionsLoader authorityField008DefinitionsLoader() {
        AuthorityField008DefinitionsLoader bean = new AuthorityField008DefinitionsLoader(
                StaticProvider.of(AuthorityField008EditFormData.getDirectOrIndirectGeographicSubdivision()),
                StaticProvider.of(AuthorityField008EditFormData.getRomanizationScheme()),
                StaticProvider.of(AuthorityField008EditFormData.getLanguageOfCatalog()),
                StaticProvider.of(AuthorityField008EditFormData.getKindOfRecord()),
                StaticProvider.of(AuthorityField008EditFormData.getDescriptiveCatalogingRules()),
                StaticProvider.of(AuthorityField008EditFormData.getSubjectHeadingSystemThesaurus()),
                StaticProvider.of(AuthorityField008EditFormData.getTypeOfSeries()),
                StaticProvider.of(AuthorityField008EditFormData.getNumberedOrUnnumberedSeries()),
                StaticProvider.of(AuthorityField008EditFormData.getHeadingUseMainOrAddedEntry()),
                StaticProvider.of(AuthorityField008EditFormData.getHeadingUseSubjectAddedEntry()),
                StaticProvider.of(AuthorityField008EditFormData.getHeadingUseSeriesAddedEntry()),
                StaticProvider.of(AuthorityField008EditFormData.getTypeOfSubjectSubdivision()),
                StaticProvider.of(AuthorityField008EditFormData.getTypeOfGovernmentAgency()),
                StaticProvider.of(AuthorityField008EditFormData.getReferenceEvaluation()),
                StaticProvider.of(AuthorityField008EditFormData.getRecordUpdateInProcess()),
                StaticProvider.of(AuthorityField008EditFormData.getUndifferentiatedPersonalName()),
                StaticProvider.of(AuthorityField008EditFormData.getLevelOfEstablishment()),
                StaticProvider.of(AuthorityField008EditFormData.getModifiedRecord()),
                StaticProvider.of(AuthorityField008EditFormData.getCatalogingSource())
        );
        cacheService.registerSpringCacheCleaner(AuthorityField008Definitions.class.getSimpleName(), AuthorityField008DefinitionsLoader.CACHE_NAME);
        return bean;
    }

    @Bean
    public ContextualProvider<Department, Converter<UUID, @NonNull String>> recordIdToRecordDetailUrlConverterProvider() {
        return serverUrlProvider.andThen(PortaroUrls::getUuidToRecordDetailUrlConverter);
    }

    @Bean
    public AuthenticatedAcceptableValuesResolver<RecordHoldingCreationRequest, Department> assignableDepartmentsAuthenticatedContextualProvider() {
        return new RecordHoldingCreationRequest.AssignableDepartmentsAuthenticatedContextualProvider(
                assignableRecordDepartmentsAuthenticatedContextualProvider()
        );
    }

    @Bean
    public AuthenticatedAcceptableValuesResolver<Record, Department> assignableRecordDepartmentsAuthenticatedContextualProvider() {
        return new AssignableRecordDepartmentsAuthenticatedContextualProvider(
                recordHoldingLoader,
                ContextualVisibleDepartmentsLoader.ofSettingsKey(contextHierarchyLoader, settingLoader, RecordSettingKeys.HOLDING_ASSIGNABLE_FROM_CONTEXTS, editableDepartmentsAuthenticatedContextualProvider)
        );
    }


    @EventListener(ApplicationReadyEvent.class)
    public void registerModule() {
        Function<String, AllValuesProvider<? extends LabeledIdentified<?>>> acceptableIndicatorsByTypeIdLoader = indicatorTypeIdString -> {
            FieldTypeId fieldTypeId = FieldTypeId.parse(indicatorTypeIdString);
            IndicatorType indicatorType = indicatorTypeLoader.getById(fieldTypeId);
            return indicatorType.toFallbackingCodebook();
        };
        allowedDatatypeToAllValuesProviderMap.registerPrefixedPrefixRemoving(CoreConstants.Datatype.DATATYPE_PREFIX_INDICATOR, acceptableIndicatorsByTypeIdLoader);
        allDatatypeToAllValuesProviderMap.registerPrefixedPrefixRemoving(CoreConstants.Datatype.DATATYPE_PREFIX_INDICATOR, acceptableIndicatorsByTypeIdLoader);

        converterRegisterer
                .registerForStringId(FieldTypeId.class, FieldTypeId::parse)
                .registerForStringId(FieldType.class, new FieldTypeLoaderByStringIdAdapter(fieldTypeLoader))
                .registerForStringId(FieldId.class, FieldId::parse)
                .registerForStringId(RecordIdFieldTypeId.class, RecordIdFieldTypeId::parse)
                .registerForIntegerId(RecordOperationType.class, recordOperationTypeLoader)
                .registerForIntegerId(RecordStatus.class, recordStatusLoader)
                .registerForUuidId(RecordIdFondPair.class, recordFondPairsLoader);

        converterRegisterer.registerForUuidIdWithCustomFromStringConversion(Record.class, recordLoader(), new IdToObjectConverter<>(recordStringIdPrefixDispatchingLoader()));
        converterRegisterer.registerForUuidIdWithCustomFromStringConversion(RecordDescriptor.class, recordLoader(), new IdToObjectConverter<>(recordStringIdPrefixDispatchingLoader()));

        registerPermissions();
    }

    private void registerPermissions() {
        LazyFilter<WebAccess<Record>> recordAccessFilter = new LazyFilter<>(FilterLoaderByRowLoader.RECORD_SHOW_FILTER, filterLoader);


        permissionRegistry.add(RecordSecurityActions.RECORDS_SHOW, (auth, ctx, record) -> PermissionResult.allow());

        permissionRegistry.add(RecordSecurityActions.RECORD_SHOW, permissionFactory.webAccessFilter(recordAccessFilter, httpServletRequest));



        permissionRegistry.add(RecordSecurityActions.FILES_MANAGE_OF_RECORD, and(
                withoutSubject(permissionRegistry.getLazy(FileSecurityActions.FILES_MANAGE)),
                (auth, ctx, record) -> !record.isDeleted() ? PermissionResult.allow() : PermissionResult.pointless(Texts.ofNative("Record is already deleted"))
        ));

        permissionRegistry.add(RecordSecurityActions.RECORD_OPERATION_SEARCH, permissionFactory.edit());

        permissionRegistry.add(RecordSecurityActions.RECORD_REQUEST_SEARCH, permissionFactory.currentEvidedAuthenticActiveWithAnyOfRoles(ROLE_ADMIN)); // TODO: after finishing record request feature, add access for librarians

        permissionRegistry.add(RecordSecurityActions.RECORD_OPERATIONS_SHOW_OF_RECORD, permissionFactory.edit());

        permissionRegistry.add(RecordSecurityActions.RECORD_REVISIONS_SHOW_OF_RECORD, permissionFactory.currentEvidedAuthenticActiveWithAnyOfRoles(ROLE_ADMIN));

        permissionRegistry.add(RecordSecurityActions.RECORD_INFORMATION_SHOW_OF_RECORD, permissionFactory.currentEvidedAuthenticActiveWithAnyOfRoles(ROLE_ADMIN));
    }

}
