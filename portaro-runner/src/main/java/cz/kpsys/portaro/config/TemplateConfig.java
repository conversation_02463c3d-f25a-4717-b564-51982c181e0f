package cz.kpsys.portaro.config;

import com.fasterxml.jackson.databind.module.SimpleModule;
import cz.kpsys.portaro.app.CatalogConstants;
import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.json.FromStringConvertingJsonDeserializer;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.CompositeAllValuesProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.ByIdOptLoadable;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.SystemInstitution;
import cz.kpsys.portaro.file.custom.CustomFile;
import cz.kpsys.portaro.file.custom.ResourceLoaderByCustomFileLoader;
import cz.kpsys.portaro.search.factory.VelocitySearcherFactory;
import cz.kpsys.portaro.setting.CoreSettingKeys;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.template.*;
import cz.kpsys.portaro.template.velocity.VelocityResourceLoaderByTemplateLoader;
import cz.kpsys.portaro.template.velocity.VelocityTemplateEngine;
import cz.kpsys.portaro.view.web.rest.TemplateApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.format.FormatterRegistry;

import java.util.List;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class TemplateConfig {

    @NonNull Provider<@NonNull String> publicContextPath;
    @NonNull CacheService cacheService;
    @NonNull SettingLoader settingLoader;
    @NonNull ContextualProvider<Department, @NullableNotBlank String> serverUrlProvider;
    @NonNull ContextualProvider<Department, @NonNull SystemInstitution> systemInstitutionProvider;
    @NonNull Translator<Department> translator;
    @NonNull ByIdOptLoadable<CustomFile, String> hierarchyTraversingCustomFileLoader;
    @NonNull Codebook<CustomFile, String> customFileLoader;
    @NonNull FileDataStreamer cachedUnsecuredFileDataStreamer;
    @NonNull VelocitySearcherFactory velocitySearcherFactory;
    @NonNull SimpleModule objectMapperModule;
    @NonNull FormatterRegistry conversionService;



    @Bean
    public TemplateApiController templateController() {
        return new TemplateApiController(templateDescriptorLoader());
    }


    @Bean
    public VelocityEngine velocityEngine() {
        ByIdLoadable<org.springframework.core.io.Resource, String> templateLoader = new MaybeDepartmentedTemplateResourceLoader(
                new ResourceLoaderByCustomFileLoader(hierarchyTraversingCustomFileLoader, cachedUnsecuredFileDataStreamer),
                new ClasspathFileTemplateResourceLoader(List.of(CatalogConstants.VELOCITY_TEMPLATES_CLASSPATH_DIRECTORY_NAME), true)
        );

        VelocityEngine engine = new VelocityEngine();
        engine.setProperty(RuntimeConstants.RESOURCE_LOADERS, "templateloader");
        engine.setProperty("resource.loader.templateloader.class", VelocityResourceLoaderByTemplateLoader.class.getName());
        engine.setProperty("resource.loader.templateloader.templateLoader", templateLoader);
        engine.setProperty("resource.loader.templateloader.cache", true);
        engine.setProperty("resource.loader.templateloader.modification_check_interval", 3L); //3 sekundy cache (to nam staci - custom files se cachuji sami)
        engine.setProperty("parser.space_gobbling", "bc");
        engine.setProperty("directive.if.empty_check", "false");
        engine.setProperty(RuntimeConstants.VM_LIBRARY, "/html/macros.vtl");
        engine.setProperty(RuntimeConstants.PARSER_POOL_SIZE, 20); //20 je i tak defaultni
        engine.setProperty(RuntimeConstants.VM_PERM_ALLOW_INLINE, true);
        engine.setProperty(RuntimeConstants.VM_PERM_ALLOW_INLINE_REPLACE_GLOBAL, true);
        return engine;
    }


    @Bean
    public TemplateEngine templateEngine() {
        return new VelocityTemplateEngine(
                velocityEngine(),
                translator,
                serverUrlProvider.throwingWhenNull(),
                publicContextPath,
                settingLoader.getDepartmentedProvider(CoreSettingKeys.DEFAULT_LOCALE),
                systemInstitutionProvider,
                velocitySearcherFactory);
    }


    @Bean
    public AllValuesProvider<TemplateDescriptor> allTemplateDescriptorsProvider() {
        CompositeAllValuesProvider<TemplateDescriptor> bean = CompositeAllValuesProvider.of(
                new AllTemplateDescriptorsProviderByCustomFileLoader(customFileLoader, List.of("cert")),
                new AllTemplateDescriptorsProviderClasspath(
                        "classpath:" + CatalogConstants.VELOCITY_TEMPLATES_CLASSPATH_DIRECTORY_NAME + "/*" + CatalogConstants.VELOCITY_TEMPLATES_FILE_EXTENSION_WITH_DOT,
                        CatalogConstants.VELOCITY_MACROS_FILENAME,
                        CatalogConstants.CUSTOM_FOLDER_HTML
                )
        );
        CachingAllTemplateDescriptorsProvider cached = new CachingAllTemplateDescriptorsProvider(bean);
        cacheService.registerSpringCacheCleaner(TemplateDescriptor.class.getSimpleName(), CachingAllTemplateDescriptorsProvider.CACHE_NAME);
        cacheService.registerSpringCacheCleaner(CustomFile.class.getSimpleName(), CachingAllTemplateDescriptorsProvider.CACHE_NAME);
        return cached;
    }


    @Bean
    public TemplateDescriptorLoader templateDescriptorLoader() {
        return new TemplateDescriptorLoaderByAllValuesProvider(allTemplateDescriptorsProvider());
    }


    @EventListener(ApplicationReadyEvent.class)
    public void registerConverters() {
        objectMapperModule
                .addDeserializer(TemplateDescriptor.class, new FromStringConvertingJsonDeserializer<>(TemplateDescriptor.class, BasicTemplateDescriptor::parse));
        conversionService
                .addConverter(String.class, TemplateDescriptor.class, BasicTemplateDescriptor::parse);
    }

}
