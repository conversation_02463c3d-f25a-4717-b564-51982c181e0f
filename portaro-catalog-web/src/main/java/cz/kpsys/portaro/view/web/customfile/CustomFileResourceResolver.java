package cz.kpsys.portaro.view.web.customfile;

import jakarta.servlet.http.HttpServletRequest;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.lang.Nullable;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.resource.AbstractResourceResolver;
import org.springframework.web.servlet.resource.ResourceResolverChain;

import java.io.IOException;
import java.util.List;

@Slf4j
public class CustomFileResourceResolver extends AbstractResourceResolver {

    @Override
    protected Resource resolveResourceInternal(@Nullable HttpServletRequest request,
                                               @NonNull String requestPath,
                                               @NonNull List<? extends Resource> locations,
                                               @NonNull ResourceResolverChain chain) {
        return getResource(requestPath, locations);
    }

    @Override
    protected String resolveUrlPathInternal(@NonNull String resourcePath,
                                            @NonNull List<? extends Resource> locations,
                                            @NonNull ResourceResolverChain chain) {
        return (StringUtils.hasText(resourcePath) && getResource(resourcePath, locations) != null ? resourcePath : null);
    }

    private Resource getResource(String resourcePath, List<? extends Resource> locations) {
        for (Resource location : locations) {
            try {
                log.trace("Checking location: {}", location);
                Resource resource = getResource(resourcePath, location);
                if (resource != null) {
                    log.trace("Found match: {}", resource);
                    return resource;
                } else {
                    log.trace("No match for location: {}", location);
                }
            } catch (IOException ex) {
                log.trace("Failure checking for relative resource - trying next location", ex);
            }
        }
        return null;
    }

    @Nullable
    protected Resource getResource(String resourcePath, Resource location) throws IOException {
        Resource resource = location.createRelative(resourcePath);
        if (resource.isReadable()) {
            return resource;
        }
        return null;
    }
}
