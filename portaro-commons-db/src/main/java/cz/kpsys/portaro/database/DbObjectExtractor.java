package cz.kpsys.portaro.database;

import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.date.DatetimeRange;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.sql.ResultSet;
import java.sql.SQLException;

public interface DbObjectExtractor {

    DbObjectExtractor UNSUPPORTING = new UnsupportingDbObjectExtractor();

    @NonNull
    DateRange getDateRange(ResultSet rs, String column) throws SQLException;

    @Nullable
    DateRange getDateRangeNullable(ResultSet rs, String column) throws SQLException;

    @NonNull
    DatetimeRange getDatetimeRange(ResultSet rs, String column) throws SQLException;

    @Nullable
    DatetimeRange getDatetimeRangeNullable(ResultSet rs, String column) throws SQLException;

}
