package cz.kpsys.portaro.database;

import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.date.DatetimeRange;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.sql.ResultSet;
import java.sql.SQLException;

public class UnsupportingDbObjectExtractor implements DbObjectExtractor {

    @Override
    public @NonNull DateRange getDateRange(ResultSet rs, String column) {
        throw new UnsupportedOperationException("DateRange (on column " + column + ") is not supported");
    }

    @Override
    public @Nullable DateRange getDateRangeNullable(ResultSet rs, String column) throws SQLException {
        Object obj = rs.getObject(column);
        if (obj == null) {
            return null;
        }
        throw new UnsupportedOperationException("DateRange (on column " + column + ") is not supported");
    }

    @Override
    public @NonNull DatetimeRange getDatetimeRange(ResultSet rs, String column) {
        throw new UnsupportedOperationException("DatetimeRange (on column " + column + ") is not supported");
    }

    @Override
    public @Nullable DatetimeRange getDatetimeRangeNullable(ResultSet rs, String column) throws SQLException {
        Object obj = rs.getObject(column);
        if (obj == null) {
            return null;
        }
        throw new UnsupportedOperationException("DatetimeRange (on column " + column + ") is not supported");
    }
}
