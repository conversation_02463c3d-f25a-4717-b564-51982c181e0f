dependencies {
    testCompileOnly("org.projectlombok:lombok:+")
    testAnnotationProcessor("org.projectlombok:lombok:+")

    testImplementation(project(":portaro-user"))
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.mockito:mockito-core:+")
    testImplementation("org.springframework:spring-context:6.+")


    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-auth"))
    implementation(project(":portaro-commons"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-form"))
    implementation(project(":portaro-form-annotation"))


    implementation("org.springframework:spring-beans:6.+")
    implementation("jakarta.validation:jakarta.validation-api:3.+")

}
