package cz.kpsys.portaro.record.export;

import cz.kpsys.portaro.appserver.dml.DmlAppserverService;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.commons.cache.CacheDeletableById;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.convert.IdToObjectConverter;
import cz.kpsys.portaro.commons.convert.StringToAnyListConverter;
import cz.kpsys.portaro.commons.convert.StringToIntegerToAnyConverter;
import cz.kpsys.portaro.commons.convert.StringToUuidConverter;
import cz.kpsys.portaro.commons.date.DateRangeToStringConverter;
import cz.kpsys.portaro.commons.date.DatetimeRangeToStringConverter;
import cz.kpsys.portaro.commons.date.InstantToStringConverter;
import cz.kpsys.portaro.commons.localization.ContextualLocaleLocalizer;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.IdSettable;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.ProviderByIdProvider;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.commons.object.filter.ConjunctionObjectFilter;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.config.CodebookLoaderBuilderFactory;
import cz.kpsys.portaro.config.SaverBuilderFactory;
import cz.kpsys.portaro.database.FlushingJpaSaver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.Resource;
import cz.kpsys.portaro.file.filecategory.FileCategory;
import cz.kpsys.portaro.file.filecategory.FileCategoryBySystemTypeLoader;
import cz.kpsys.portaro.file.matcher.FileCategoryMatcher;
import cz.kpsys.portaro.file.web.FileDownloadUrlGenerator;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.marcxml.MarcConstants;
import cz.kpsys.portaro.matcher.Matcher;
import cz.kpsys.portaro.matcher.NegatingMatcher;
import cz.kpsys.portaro.record.RecordStatus;
import cz.kpsys.portaro.record.RecordWellKnownFields.Links;
import cz.kpsys.portaro.record.detail.LessThan1000RecordFieldFilter;
import cz.kpsys.portaro.record.export.batch.*;
import cz.kpsys.portaro.record.export.listener.*;
import cz.kpsys.portaro.record.export.marc.*;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.operation.RecordOperationType;
import cz.kpsys.portaro.record.view.RecordMediaViewerUrlGenerator;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.PageSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.security.PermissionFactory;
import cz.kpsys.portaro.security.PermissionRegistry;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.data.repository.core.support.RepositoryFactorySupport;

import java.time.ZoneId;
import java.util.List;
import java.util.UUID;

@Configuration
@Lazy
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RecordExportConfig {

    public static String KINDED_ID_PLACEHOLDER = "kindedId";
    public static String RECORD_ID_PLACEHOLDER = "recordId";

    @NonNull SettingLoader settingLoader;
    @NonNull ContextualProvider<Department, @NullableNotBlank String> serverUrlProvider;
    @NonNull Provider<@NonNull ZoneId> databaseColumnsTimeZoneProvider;
    @NonNull ContextualProvider<Department, Converter<UUID, @NonNull String>> recordIdToRecordDetailUrlConverterProvider;
    @NonNull DmlAppserverService dmlAppserverService;
    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull DepartmentAccessor departmentAccessor;
    @NonNull ByIdLoadable<RecordOperationType, Integer> recordOperationTypeLoader;
    @NonNull ByIdLoadable<RecordStatus, Integer> recordStatusLoader;
    @NonNull ByIdLoadable<Fond, Integer> fondLoader;
    @NonNull EntityManager entityManager;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull RepositoryFactorySupport jpaRepositoryFactory;
    @NonNull CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;
    @NonNull SaverBuilderFactory saverBuilderFactory;
    @NonNull CacheDeletableById exportedRecordCache;
    @NonNull PageSearchLoader<MapBackedParams, IdentifiedFile, RangePaging> fileSearchLoader;
    @NonNull Provider<Department> rootDepartmentProvider;
    @NonNull PermissionRegistry permissionRegistry;
    @NonNull PermissionFactory permissionFactory;
    @NonNull ContextualLocaleLocalizer<Department> localizer;
    @NonNull FileCategoryBySystemTypeLoader fileCategoryBySystemTypeLoader;

    @Bean
    public Provider<@NonNull RecordExport> defaultRecordExportProvider() {
        return ProviderByIdProvider.ofStaticId(recordExportByOaiSetIdLoader(), RecordExportFilterEntity.DEFAULT_ID);
    }

    @Bean
    public RecordsToMarcRecordsMapper recordsToMarcRecordsMapper() {
        BasicRecordsToMarcDtosConverter basicConverter = new BasicRecordsToMarcDtosConverter(
                new InstantToStringConverter(databaseColumnsTimeZoneProvider, MarcConstants.MARCXML_INSTANT_FORMATTER),
                DateRangeToStringConverter.ofInternalFormat(),
                DatetimeRangeToStringConverter.ofInternalFormat(databaseColumnsTimeZoneProvider),
                new ConjunctionObjectFilter<>(
                        new LessThan1000RecordFieldFilter<>(),
                        new ExportableFieldRecordFieldFilter()
                ));
        SimpleConverterRecordsToMarcRecordsMapper simpleMapper = new SimpleConverterRecordsToMarcRecordsMapper(basicConverter);
        return new EnhancingRecordsToMarcRecordsMapper(simpleMapper, compositeMarcRecordsEnhancer());
    }

    @Bean
    public CompositeMarcRecordsEnhancer compositeMarcRecordsEnhancer() {
        return new CompositeMarcRecordsEnhancer();
    }

    @Bean
    public Converter<RecordExportFilterEntity, RecordExport> entityToRecordExportConverter() {
        return new EntityToRecordExportConverter(
                StringToAnyListConverter.create(StringToIntegerToAnyConverter.nullConvertingToNull(new IdToObjectConverter<>(recordOperationTypeLoader)).throwWhenNull(), ",").throwWhenNull(), // nullConvertingToNull because we want null list when null column value
                StringToAnyListConverter.create(StringToIntegerToAnyConverter.nullConvertingToNull(new IdToObjectConverter<>(fondLoader)).throwWhenNull(), ",").throwWhenNull(), // nullConvertingToNull because we want null list when null column value
                StringToAnyListConverter.create(StringToIntegerToAnyConverter.nullConvertingToNull(new IdToObjectConverter<>(recordStatusLoader)).throwWhenNull(), ",").throwWhenNull(), // nullConvertingToNull because we want null list when null column value
                StringToAnyListConverter.create(StringToIntegerToAnyConverter.nullConvertingToNull(new IdToObjectConverter<>(departmentLoader)).throwWhenNull(), ",").throwWhenNull() // nullConvertingToNull because we want null list when null column value
        );
    }

    @Bean
    public Codebook<RecordExport, UUID> recordExportLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedByJpa(RecordExportFilterEntity.class)
                .convertedEachBy(entityToRecordExportConverter())
                .staticCached(RecordExport.class.getSimpleName())
                .build();
    }

    @Bean
    public ByIdLoadable<RecordExport, String> recordExportByOaiSetIdLoader() {
        return new CustomGetterAllByIdsBrowsingByIdLoadable<>(RecordExport.class, RecordExport::getOaiSetId, recordExportLoader());
    }

    @Bean
    public Saver<RecordExport, RecordExport> recordExportSaver() {
        return saverBuilderFactory.<RecordExport, UUID>saver()
                .idSetting(RecordExportFilterEntity.class, StringToUuidConverter.INSTANCE, IdSettable::setId)
                .intermediateConverting(new RecordExportToSavedEntitiesConverter(rootDepartmentProvider))
                .withClearedCacheName(RecordExport.class.getSimpleName())
                .build();
    }

    @Bean
    public ExportedDepartmentSearchHelper exportedDepartmentSearchHelper() {
        return new ExportedDepartmentSearchHelper(contextHierarchyLoader, departmentAccessor);
    }

    @Bean
    public RecordExportApiController recordExportApiController() {
        return new RecordExportApiController(recordExportSaver(), recordExportLoader());
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<RecordExportRequest> recordExportRequestDefaulter() {
        return new RecordExportRequestDefaulter();
    }

    @Bean
    public RecordExportListener recordExportListener() {
        return new CompositeRecordExportListener(List.of(
                new ExceptionCatchingRecordExportListener(new TableWriteLoggingRecordExportListener(dmlAppserverService)),
                new CacheDeletingRecordExportListener(exportedRecordCache)
        ));
    }

    @Bean
    public MarcRecordsEnhancer filesAddingMarcRecordsEnhancer() {
        return new FilesMarcRecordsEnhancer(fileSearchLoader, fileDatafieldsMarcGenerator());
    }

    private DatafieldsMarcGenerator<Provider<List<IdentifiedFile>>> fileDatafieldsMarcGenerator() {
        return new CompositeMultipleDatafieldsMarcGenerator<Provider<List<IdentifiedFile>>>()
                .withGenerator(mediaViewerLinkGroupingFileDatafieldsMarcGenerator())
                .withGenerator(particularFileDatafieldsMarcGenerator());
    }

    private DatafieldsMarcGenerator<Provider<List<IdentifiedFile>>> mediaViewerLinkGroupingFileDatafieldsMarcGenerator() {
        Matcher<Resource> allowedFileCategoriesMatcher = new NegatingMatcher<>(new FileCategoryMatcher(fileCategoryBySystemTypeLoader.allIdsByList(FileCategory.MEDIA_VIEWER_LINK_EXCLUDED_FILE_CATEGORY_SYSTEM_TYPES)));
        GroupingToSingleMultipleDatafieldsMarcGenerator<IdentifiedFile> generator = new GroupingToSingleMultipleDatafieldsMarcGenerator<IdentifiedFile>(allowedFileCategoriesMatcher::matches, StaticProvider.of(Links.TYPE_ID))
                .withNullableSubfieldGenerator(Links.UrlAddress.CODE, new RecordMediaViewerUrlGenerator<>(serverUrlProvider.throwingWhenNull(), recordIdToRecordDetailUrlConverterProvider))
                .withNullableSubfieldGenerator(Links.Description.CODE, (unused, ctx) -> localizer.localize(Texts.ofMessageCoded("commons.PlnyText"), ctx));
        return new ConditionalMultipleDatafieldsMarcGenerator<>(settingLoader.getDepartmentedProvider(RecordExportSettingKeys.MEDIA_VIEWER_LINK_IN_MARC_EXPORT_ENABLED), generator);
    }

    private DatafieldsMarcGenerator<Provider<List<IdentifiedFile>>> particularFileDatafieldsMarcGenerator() {
        StaticMultipleDatafieldsMarcGenerator<IdentifiedFile> generator = new StaticMultipleDatafieldsMarcGenerator<IdentifiedFile>(StaticProvider.of(Links.TYPE_ID))
                .withNullableSubfieldGenerator(Links.UrlAddress.CODE, new FileDownloadUrlGenerator<>(serverUrlProvider.throwingWhenNull()))
                .withNullableSubfieldGenerator(Links.Description.CODE, (file, ctx) -> localizer.localize(file.getCategory().getText(), ctx));
        return new ConditionalMultipleDatafieldsMarcGenerator<>(settingLoader.getDepartmentedProvider(RecordExportSettingKeys.ATTACHMENTS_IN_MARC_EXPORT_ENABLED), generator);
    }

    @Bean
    public Saver<TransferredBatch, TransferredBatchEntity> isolatedTransferredBatchSaver() {
        return new TransactionalSaver<>(
                new PreConvertingSaver<>(
                        new TransferredBatchToEntityConverter(),
                        new FlushingJpaSaver<>(new SimpleJpaRepository<>(TransferredBatchEntity.class, entityManager))
                ),
                defaultTransactionTemplateFactory.withRequiresNewPropagation().get()
        );
    }

    @Bean
    public TransferredBatchLoader transferredBatchLoader() {
        TransferredBatchEntityLoader entityLoader = jpaRepositoryFactory.getRepository(TransferredBatchEntityLoader.class);
        return new EntityLoaderDelegatingTransferredBatchTransferLoader(entityLoader, new EntityToTransferredBatchConverter());
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerPermissions() {
        permissionRegistry.add(RecordExportSecurityActions.SHOW, permissionFactory.adminOrEditWithServicePrivileges());
        permissionRegistry.add(RecordExportSecurityActions.EDIT, permissionFactory.adminOrEditWithServicePrivileges());
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerMarcRecordsEnhancer() {
        compositeMarcRecordsEnhancer().add(filesAddingMarcRecordsEnhancer());
    }
}
