package cz.kpsys.portaro.record.export.marc;

import cz.kpsys.portaro.bool.BooleanToBoolStringConverter;
import cz.kpsys.portaro.commons.date.DateRangeToStringConverter;
import cz.kpsys.portaro.commons.date.DatetimeRangeToStringConverter;
import cz.kpsys.portaro.commons.date.InstantToStringConverter;
import cz.kpsys.portaro.commons.date.LocalDateToStringConverter;
import cz.kpsys.portaro.commons.object.IdentifiedValue;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.marcxml.MarcConstants;
import cz.kpsys.portaro.marcxml.model.StrictControlfieldMarcDto;
import cz.kpsys.portaro.marcxml.model.StrictDatafieldMarcDto;
import cz.kpsys.portaro.marcxml.model.StrictRecordMarcDto;
import cz.kpsys.portaro.marcxml.model.StrictSubfieldMarcDto;
import cz.kpsys.portaro.number.BigDecimalToStringConverter;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.detail.value.*;
import cz.kpsys.portaro.record.fond.FondTypeResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Predicate;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class BasicRecordsToMarcDtosConverter implements RecordsToMarcDtosConverter {

    @NonNull BooleanToBoolStringConverter booleanToMarcXmlSubfieldStringConverter = new BooleanToBoolStringConverter();
    @NonNull BigDecimalToStringConverter numberToMarcXmlSubfieldStringConverter = new BigDecimalToStringConverter();
    @NonNull LocalDateToStringConverter localDateToMarcXmlSubfieldStringConverter = new LocalDateToStringConverter(MarcConstants.MARCXML_LOCAL_DATE_FORMATTER);
    @NonNull InstantToStringConverter instantToMarcXmlSubfieldStringConverter;
    @NonNull DateRangeToStringConverter dateRangeToMarcXmlSubfieldStringConverter;
    @NonNull DatetimeRangeToStringConverter datetimeRangeToMarcXmlSubfieldStringConverter;
    @NonNull Predicate<Field<?>> fieldFilter;

    @Override
    public List<IdentifiedValue<UUID, StrictRecordMarcDto>> convert(@NonNull List<? extends Record> records) {
        return records.stream()
                .peek(this::validateRecord)
                .map(r -> {
                    StrictRecordMarcDto recordMarcDto = new StrictRecordMarcDto(
                            getLeader(r),
                            getControlfields(r),
                            getDatafields(r)
                    );
                    return IdentifiedValue.of(r.getId(), recordMarcDto);
                })
                .toList();
    }

    private void validateRecord(Record record) {
        Objects.requireNonNull(record.getDetail(), () -> "Record %s has null detail".formatted(record));
    }

    @NonNull
    private String getLeader(Record record) {
        Predicate<Field<?>> isLeader = By.code(FondTypeResolver.isAuthorityFond(record) ? FieldTypes.AUTHORITY_LEADER_FIELD_CODE : FieldTypes.DOCUMENT_LEADER_FIELD_CODE);
        return record.getDetail()
                .getFirstField(fieldFilter.and(isLeader))
                .map(this::createMarcLeader)
                .orElseThrow(() -> new IllegalStateException("Record detail %s does not contain leader".formatted(record)));
    }

    @NonNull
    private List<StrictControlfieldMarcDto> getControlfields(Record record) {
        Predicate<Field<?>> isControlfield = By.controlfield();
        Predicate<Field<?>> isNotLeader = By.<Field<?>>code(FondTypeResolver.isAuthorityFond(record) ? FieldTypes.AUTHORITY_LEADER_FIELD_CODE : FieldTypes.DOCUMENT_LEADER_FIELD_CODE).negate();
        return record.getDetail().streamFields(fieldFilter.and(isControlfield).and(isNotLeader))
                .map(this::createMarcControlfield)
                .toList();
    }

    @NonNull
    private List<StrictDatafieldMarcDto> getDatafields(Record record) {
        Predicate<Field<?>> isDatafield = By.datafield();
        return record.getDetail().streamFields(fieldFilter.and(isDatafield))
                .map(this::createMarcDatafield)
                .toList();
    }

    private String createMarcLeader(Field<?> f) {
        return convertFieldValueToStandardMarcFormat(f.getRaw());
    }

    private StrictControlfieldMarcDto createMarcControlfield(Field<?> f) {
        return new StrictControlfieldMarcDto(
                FieldHelper.fieldCodeToMarc21Tag(f.getCode()),
                convertFieldValueToStandardMarcFormat(f.getRaw())
        );
    }

    private StrictDatafieldMarcDto createMarcDatafield(Field<?> f) {
        List<StrictSubfieldMarcDto> subfields = f.streamFields(fieldFilter)
                .filter(By.anyCode(FieldTypes.INDICATORS_FIELD_CODES).negate())
                .flatMap(this::mapSubfieldToXmlObjectStream)
                .toList();
        return new StrictDatafieldMarcDto(
                FieldHelper.fieldCodeToMarc21Tag(f.getCode()),
                toMarcDtoIndicator(f, FieldTypes.IND_1_FIELD_CODE),
                toMarcDtoIndicator(f, FieldTypes.IND_2_FIELD_CODE),
                subfields
        );
    }

    private Stream<StrictSubfieldMarcDto> mapSubfieldToXmlObjectStream(Field<?> sf) {
        if (sf.isEmpty()) {
            return Stream.empty();
        }

        if (sf.getType().isVirtualGroup()) {
            return sf.streamFields().flatMap(this::mapSubfieldToXmlObjectStream);
        }

        String value = switch (sf.getExistingValueHolder()) {
            case BooleanFieldValue val -> booleanToMarcXmlSubfieldStringConverter.convert(val.value());
            case NumberFieldValue val -> numberToMarcXmlSubfieldStringConverter.convert(val.value());
            case LocalDateFieldValue val -> localDateToMarcXmlSubfieldStringConverter.convert(val.value());
            case InstantFieldValue val -> instantToMarcXmlSubfieldStringConverter.convert(val.value());
            case DateRangeFieldValue val -> dateRangeToMarcXmlSubfieldStringConverter.convert(val.value());
            case DatetimeRangeFieldValue val -> datetimeRangeToMarcXmlSubfieldStringConverter.convert(val.value());
            case AcceptableValueFieldValue<?> val -> convertFieldValueToStandardMarcFormat(String.valueOf(val.value().getId()));
            case StringFieldValue val -> convertFieldValueToStandardMarcFormat(val.value());
        };
        return Stream.of(StrictSubfieldMarcDto.ofWithoutRecord(sf.getCode(), value));
    }

    private static String toMarcDtoIndicator(Field<?> parentField, @NonNull String fieldCode) {
        Field<?> indField = parentField.getFirstField(By.code(fieldCode)).orElse(null);
        return IndicatorType.toMarcXmlIndicatorValue(indField, false);
    }

    private String convertFieldValueToStandardMarcFormat(String value) {
        return StringUtil.notNullString(value)
                .replace("\n", " ")
                .replace("\t", " ");
    }

}
