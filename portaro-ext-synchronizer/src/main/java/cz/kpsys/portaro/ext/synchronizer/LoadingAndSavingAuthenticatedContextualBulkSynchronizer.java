package cz.kpsys.portaro.ext.synchronizer;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.TimeMeter;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.synchronizer.filters.ForeignSystemUserFilter;
import cz.kpsys.portaro.ext.synchronizer.result.BulkSyncResult;
import cz.kpsys.portaro.ext.synchronizer.result.ItemSyncResult;
import cz.kpsys.portaro.ext.synchronizer.result.SyncResolveMode;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.edit.UserEditationSaveResult;
import cz.kpsys.portaro.user.edit.command.PersonEditationCommand;
import cz.kpsys.portaro.user.edit.command.UserRecordEditationCommand;
import jakarta.validation.ConstraintViolationException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.List;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class LoadingAndSavingAuthenticatedContextualBulkSynchronizer implements AuthenticatedContextualBulkSynchronizer<User> {

    @NonNull ForeignSystemUserLoader foreignSystemUserLoader;
    @NonNull ForeignSystemUserSaver foreignSystemUserSaver;
    @NonNull ForeignSystemUserFilter foreignSystemUserFilter;

    @Override
    public BulkSyncResult<User> synchronizeAllOn(Department ctx, UserAuthentication currentAuth) {
        List<UserRecordEditationCommand<PersonEditationCommand>> loadedUsers = TimeMeter.measureAndLog(
                () -> foreignSystemUserLoader.getForeignUsersFromServer(ctx, currentAuth), log,
                () -> "Foreign user loading", Duration.ofMinutes(30));
        List<ItemSyncResult<User, ?>> syncResults = ListUtil.convert(loadedUsers,
                userEditationRequest -> TimeMeter.measureAndLog(
                        () -> syncSingle(ctx, currentAuth, userEditationRequest), log,
                        () -> "User %s sync".formatted(ObjectUtil.elvis(userEditationRequest.recordEditation(), RecordEditation::getRecord)), Duration.ofSeconds(3)
        ));
        return new BulkSyncResult<>(syncResults);
    }

    private ItemSyncResult<User, UserRecordEditationCommand<PersonEditationCommand>> syncSingle(Department ctx, UserAuthentication currentAuth, UserRecordEditationCommand<PersonEditationCommand> userEditationRequest) {
        Optional<UserRecordEditationCommand<PersonEditationCommand>> filtered = foreignSystemUserFilter.filter(userEditationRequest);
        if (filtered.isEmpty()) {
            return ItemSyncResult.skipped(ctx, userEditationRequest);
        }
        try {
            UserEditationSaveResult<? extends User> saveResult = foreignSystemUserSaver.saveForeignSystemUser(userEditationRequest, ctx, currentAuth, true, true);
            return createSyncResult(ctx, saveResult);
        } catch (ConstraintViolationException e) {
            log.error("Cannot sync {} due to validation exception", userEditationRequest, e);
            return ItemSyncResult.failed(ctx, userEditationRequest, e);
        }
    }

    private ItemSyncResult<User, UserRecordEditationCommand<PersonEditationCommand>> createSyncResult(Department ctx, UserEditationSaveResult<? extends User> saveResult) {
        SyncResolveMode resolveMode = saveResult.creation() ? (saveResult.user().isActive() ? SyncResolveMode.CREATED : SyncResolveMode.DRAFT_CREATED) : SyncResolveMode.UPDATED;
        return ItemSyncResult.success(ctx, saveResult.user(), resolveMode, null);
    }
}
