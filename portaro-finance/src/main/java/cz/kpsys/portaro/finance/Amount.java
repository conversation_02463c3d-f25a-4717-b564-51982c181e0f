package cz.kpsys.portaro.finance;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.util.NumberUtil;
import lombok.NonNull;

import java.math.BigDecimal;
import java.util.function.Predicate;

public interface Amount {

    BigDecimal getSum();

    @JsonIgnore
    default boolean isPositive() {
        return NumberUtil.isPositive(getSum());
    }

    @JsonIgnore
    default boolean isPositiveOrZero() {
        return NumberUtil.isPositiveOrZero(getSum());
    }

    @JsonIgnore
    default boolean isNegative() {
        return NumberUtil.isNegative(getSum());
    }

    @JsonIgnore
    default boolean isNotZero() {
        return !isZero();
    }

    @JsonIgnore
    default boolean isZero() {
        return NumberUtil.isZero(getSum());
    }

    default boolean isLowerThan(@NonNull Amount other) {
        return isLowerThan(other.getSum());
    }

    default boolean isLowerThan(@NonNull BigDecimal other) {
        return NumberUtil.isLt(getSum(), other);
    }

    default boolean isGreaterThan(@NonNull BigDecimal other) {
        return NumberUtil.isGt(getSum(), other);
    }

    default BigDecimal getTaxBase(@NonNull Double vatRate) {
        return getTaxBase(getSum(), vatRate);
    }

    static BigDecimal getTaxBase(@NonNull BigDecimal inclVat, @NonNull Double vatRate) {
        BigDecimal vat = getVat(inclVat, vatRate);
        return inclVat.subtract(vat);
    }

    static BigDecimal getVat(@NonNull BigDecimal inclVat, @NonNull Double vatRate) {
        final int VAT_ROUNDING_DECIMAL_NUMS = 2;
        double vatCoeficient = vatRate / 100d;
        double base = inclVat.doubleValue() / (1 + vatCoeficient);
        double vat = inclVat.doubleValue() - base;
        double roundedVat = Math.round(vat * Math.pow(10, VAT_ROUNDING_DECIMAL_NUMS)) / Math.pow(10, VAT_ROUNDING_DECIMAL_NUMS);
        return BigDecimal.valueOf(roundedVat);
    }

    class NotZeroFilter<E extends Amount> implements Predicate<E> {
        @Override
        public boolean test(E c) {
            return NumberUtil.isNotZero(c.getSum());
        }
    }

    class PositiveFilter<E extends Amount> implements Predicate<E> {
        @Override
        public boolean test(E c) {
            return c.isPositive();
        }
    }

    class NotPositiveFilter<E extends Amount> implements Predicate<E> {
        @Override
        public boolean test(E c) {
            return !c.isPositive();
        }
    }

    class NegativeFilter<E extends Amount> implements Predicate<E> {
        @Override
        public boolean test(E c) {
            return c.isNegative();
        }
    }

    class NotNegativeFilter<E extends Amount> implements Predicate<E> {
        @Override
        public boolean test(E c) {
            return !c.isNegative();
        }
    }
}
