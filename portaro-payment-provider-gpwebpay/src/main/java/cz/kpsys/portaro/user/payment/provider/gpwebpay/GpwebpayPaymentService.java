package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.web.Link;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.payment.*;
import cz.kpsys.portaro.user.payment.provider.PaymentGatewayRedirectResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class GpwebpayPaymentService implements PaymentService {

    @NonNull GpwebpayTemplate gpwebpayTemplate;
    @NonNull Saver<PaymentSaveCommand<GpwebpayPayment>, GpwebpayPayment> gopayPaymentSaver;
    @NonNull ContextualProvider<Department, ? extends Link> nullableTermsAndConditionLinkProvider;
    @NonNull TransactionTemplate transactionTemplate;
    @NonNull PaymentItemsCreator paymentItemsCreator;
    @NonNull GpwebpayPaymentCreator gpwebpayPaymentCreator;
    @NonNull PaymentBasicCreator paymentCreator;

    @Override
    public ActionResponse pay(@NonNull PayCommand command) {
        return transactionTemplate.execute(_ -> {
            Payment payment = paymentCreator.createInstance(command, null);

            GpwebpayPaymentOrder order = gpwebpayTemplate.createPaymentOrder(command, String.valueOf(payment.getId()));

            String gatewayUrl = gpwebpayTemplate.createPaymentGatewayUrl(order, command.department());

            GpwebpayPayment gpwebpayPayment = gpwebpayPaymentCreator.create(command, payment, order.getOrderNumber(), gatewayUrl);
            paymentItemsCreator.create(gpwebpayPayment.getPayment());

            PaymentGatewayRedirectResponse response = new PaymentGatewayRedirectResponse(command, gatewayUrl, Texts.ofNative("Budete přesměrováni na platební bránu, na které můžete zaplatit platební kartou, peněženkou Masterpass nebo přes mobilní platbu MasterCard Mobile."));

            Link termsAndConditionLink = nullableTermsAndConditionLinkProvider.getOn(command.department());
            if (termsAndConditionLink != null) {
                response = response.withTermsAndConditionsLink(termsAndConditionLink);
            }

            return response;
        });
    }

}
