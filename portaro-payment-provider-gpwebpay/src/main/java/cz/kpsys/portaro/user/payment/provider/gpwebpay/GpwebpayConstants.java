package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import java.util.List;

public class GpwebpayConstants {

    public static final String PROVIDER_NAME = "gateway-gpwebpay";

    public static final String PROD_API_URL = "https://3dsecure.gpwebpay.com/pgw/order.do";
    public static final String PROD_SOAP_URL = "https://3dsecure.gpwebpay.com/pay-ws/v1/PaymentService";
    public static final String PROD_PRIVATE_KEY_KEYSTORE_FILENAME = "gpwebpay-pvk.jks";
    public static final String PROD_PRIVATE_KEY_KEYSTORE_AND_KEY_PASS = "Gpwebpay456";
    public static final String PROD_CERTIFICATE_FILENAME = "gpe.signing_prod.cer";

    public static final String TEST_API_URL = "https://test.3dsecure.gpwebpay.com/pgw/order.do";
    public static final String TEST_SOAP_URL = "https://test.3dsecure.gpwebpay.com/pay-ws/v1/PaymentService";
    public static final String TEST_CLIENT_ID = "**********";
    public static final String TEST_CLIENT_SECRET = "BypyRLhH";
    public static final String TEST_ESHOP_ID = "**********";
    public static final String TEST_PRIVATE_KEY_KEYSTORE_FILENAME = "testing-gpwebpay-pvk.jks";
    public static final String TEST_PRIVATE_KEY_KEYSTORE_AND_KEY_PASS = "Jw0ef4skd0fkSDsasVgarhggzlwpr5x";
    public static final String TEST_CERTIFICATE_FILENAME = "testing-gpe.signing_test.cer";


    public static final String PARAM_MERCHANTNUMBER = "MERCHANTNUMBER";
    public static final String PARAM_OPERATION = "OPERATION";
    public static final String PARAM_ORDERNUMBER = "ORDERNUMBER";
    public static final String PARAM_MERORDERNUM = "MERORDERNUM";
    public static final String PARAM_AMOUNT = "AMOUNT";
    public static final String PARAM_CURRENCY = "CURRENCY";
    public static final String PARAM_DEPOSITFLAG = "DEPOSITFLAG";
    public static final String PARAM_URL = "URL";
    public static final String PARAM_PAYMETHOD = "PAYMETHOD";
    public static final String PARAM_DIGEST = "DIGEST";
    public static final String PARAM_DIGEST1 = "DIGEST1";
    public static final String PARAM_PRCODE = "PRCODE";
    public static final String PARAM_SRCODE = "SRCODE";
    public static final String PARAM_RESULTTEXT = "RESULTTEXT";

    public static List<String> ORDERED_REQUEST_DIGEST_PARAMS = List.of(
            PARAM_MERCHANTNUMBER,
            PARAM_OPERATION,
            PARAM_ORDERNUMBER,
            PARAM_AMOUNT,
            PARAM_CURRENCY,
            PARAM_DEPOSITFLAG,
            PARAM_MERORDERNUM,
            PARAM_URL,
            PARAM_PAYMETHOD
    );

    public static List<String> ORDERED_RESULT_DIGEST_PARAMS = List.of(
            PARAM_OPERATION,
            PARAM_ORDERNUMBER,
            PARAM_MERORDERNUM,
            PARAM_PRCODE,
            PARAM_SRCODE,
            PARAM_RESULTTEXT
    );
}
