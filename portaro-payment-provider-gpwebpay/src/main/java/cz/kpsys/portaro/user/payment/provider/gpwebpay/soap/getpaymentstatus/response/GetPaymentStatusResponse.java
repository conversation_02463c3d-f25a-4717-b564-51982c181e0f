package cz.kpsys.portaro.user.payment.provider.gpwebpay.soap.getpaymentstatus.response;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

public class GetPaymentStatusResponse {

    @JacksonXmlProperty(namespace = "http://gpe.cz/pay/pay-ws/proc/v1", localName = "paymentStatusResponse")
    private PaymentStatusResponse paymentStatusResponse;

    public PaymentStatusResponse getPaymentStatusResponse() {
        return paymentStatusResponse;
    }

    public void setPaymentStatusResponse(PaymentStatusResponse paymentStatusResponse) {
        this.paymentStatusResponse = paymentStatusResponse;
    }

    @Override
    public String toString() {
        return "GetPaymentStatusResponse{" +
                "paymentStatusResponse=" + paymentStatusResponse +
                '}';
    }
}
