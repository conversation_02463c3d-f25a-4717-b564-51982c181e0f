package cz.kpsys.portaro.user.payment.provider.gpwebpay.soap;

import cz.kpsys.portaro.commons.object.Provider;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestOperations;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SoapCaller {

    @NonNull Provider<String> urlProvider;
    @NonNull RestOperations rest;

    public <E> E call(Object requestObject, Class<E> responseType) {
        try {

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.TEXT_XML);
            HttpEntity<?> request = new HttpEntity<>(requestObject, headers);
            ResponseEntity<E> responseEntity = rest.postForEntity(urlProvider.get(), request, responseType);

            E body = responseEntity.getBody();
            log.info("Successful SOAP request with response:\n" + body);
            return body;

        } catch (HttpServerErrorException e) {
            log.error(String.format("Failed SOAP request with status %s %s, response:\n%s", e.getStatusCode(), e.getStatusText(), e.getResponseBodyAsString()), e);
            throw e;
        }
    }

}
