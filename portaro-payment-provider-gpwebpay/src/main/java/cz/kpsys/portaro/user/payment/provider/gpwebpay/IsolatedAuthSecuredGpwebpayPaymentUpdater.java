package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.process.StaticSuccessAuthentication;
import cz.kpsys.portaro.business.update.SubjectUpdater;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.payment.PaymentSecurityActions;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class IsolatedAuthSecuredGpwebpayPaymentUpdater implements SubjectUpdater<GpwebpayPayment, Department> {

    @NonNull GpwebpayPaymentUpdater delegate;
    @NonNull SecurityManager securityManager;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull Provider<@NonNull User> authUserProvider;
    @NonNull ExecutorService executorService;

    public synchronized GpwebpayPayment refreshState(GpwebpayPayment payment, GpwebpayPaymentOrderResult orderResult, Department ctx) {
        StaticSuccessAuthentication authentication = createAuthentication(ctx);
        CompletableFuture<GpwebpayPayment> future = authenticationHolder.asyncAuthenticated(authentication, executorService, currentAuth -> {
            securityManager.throwIfCannot(PaymentSecurityActions.PAYMENT_CONFIRM, currentAuth, ctx, payment);
            return delegate.refreshState(payment, orderResult, ctx, currentAuth);
        });
        return future.join();
    }

    @Override
    public GpwebpayPayment update(GpwebpayPayment payment, Department ctx) {
        StaticSuccessAuthentication authentication = createAuthentication(ctx);
        CompletableFuture<GpwebpayPayment> future = authenticationHolder.asyncAuthenticated(authentication, executorService, currentAuth -> {
            securityManager.throwIfCannot(PaymentSecurityActions.PAYMENT_CONFIRM, currentAuth, ctx, payment);
            return delegate.update(payment, ctx, currentAuth);
        });
        return future.join();
    }

    @NonNull
    private StaticSuccessAuthentication createAuthentication(Department ctx) {
        return StaticSuccessAuthentication.authenticatedForBackgroundJobs(authUserProvider.get(), ctx);
    }
}
