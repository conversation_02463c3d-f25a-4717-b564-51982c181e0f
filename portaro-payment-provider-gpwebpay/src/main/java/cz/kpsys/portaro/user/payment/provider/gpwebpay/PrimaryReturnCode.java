package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.util.ListUtil;

import java.util.Arrays;

public enum PrimaryReturnCode implements Identified<Integer> {

    OK(0, "OK", "OK"),
    FIELD_TOO_LONG(1, "Pole příliš dlouhé", "Field too long"),
    FIELD_TOO_SHORT(2, "Pole příliš krátké", "Field too short"),
    INCORRECT_FIELD_CONTENT(3, "Chybný obsah pole", "Incorrect content of field"),
    NULL_FIELD(4, "Pole je prázdné", "Field is null"),
    MISSING_REQUIRED_FIELD(5, "Chyb<PERSON> povinné pole", "Missing required field"),
    UNKNOWN_MERCHANT(11, "Neznámý obchodník", "Unknown merchant"),
    DUPLICATE_ORDER_NUMBER(14, "Duplikátní číslo platby", "Duplicate order number"),
    OBJECT_NOT_FOUND(15, "Objekt nenalezen", "Object not found"),
    AMOUNT_TO_DEPOSIT_EXCEEDS_APPROVED(17, "Částka k zaplacení překročila povolenou (autorizovanou) částku", "Amount to deposit exceeds approved amount"),
    CREDITED_AMOUNT_EXCEEDS_DEPOSIT(18, "Součet vracených částek překročil zaplacenou částku", "Total sum of credited amounts exceeded deposited amount"),
    OBJECT_STATE_NOT_VALID_FOR_OPERATION(20, "Objekt není ve stavu odpovídajícím této operaci (Info: Pokud v případě vytváření objednávky (CREATE_ORDER) obdrží obchodník tento návratový kód, vytvoření objednávky již proběhlo a objednávka je v určitém stavu – tento návratový kód je zapříčiněn aktivitou držitele karty (například pokusem o přechod zpět, použití refresh…)).", "Object not in valid state for operation"),
    OPERATION_NOT_ALLOWED(25, "Uživatel není oprávněn k provedení operace", "Operation not allowed for user"),
    AUTHORIZATION_CENTER_PROBLEM(26, "Technický problém při spojení s autorizačním centrem", "Technical problem in connection to authorization center"),
    INCORRECT_ORDER_TYPE(27, "Chybný typ objednávky", "Incorrect order type"),
    DECLINED_IN_3D(28, "Zamítnuto v 3D (Info: důvod zamítnutí udává SRCODE)", "Declined in 3D"),
    DECLINED_IN_ac(30, "Zamítnuto v autorizačním centru (Info: Důvod zamítnutí udává SRCODE)", "Declined in AC"),
    WRONG_DIGEST(31, "Chybný podpis", "Wrong digest"),
    SESSION_EXPIRED(35, "Expirovaná session (Nastává při vypršení webové session při zadávání karty)", "Session expired"),
    CARDHOLDER_CANCELLED_PAYMENT(50, "Držitel karty zrušil platbu", "The cardholder canceled the payment"),
    ADDITIONAL_INFO_REQUEST(200, "Žádost o doplňující informace", "Additional info request");

    public static PrimaryReturnCode getByNumber(int number) {
        return ListUtil.getById(Arrays.asList(values()), number);
    }

    private int number;
    private Text text;

    PrimaryReturnCode(int number, String czechDesc, String engDesc) {
        this.number = number;
        this.text = Texts.ofNative(czechDesc);
    }

    @Override
    public Integer getId() {
        return getNumber();
    }

    public int getNumber() {
        return number;
    }

    public Text getText() {
        return text;
    }
}
