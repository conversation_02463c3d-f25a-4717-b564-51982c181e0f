package cz.kpsys.portaro.user.payment.provider.gpwebpay.soap.getpaymentstatus.request;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.NonNull;

public record PaymentStatusRequest(

        @JacksonXmlProperty(namespace = "http://gpe.cz/pay/pay-ws/proc/v1/type")
        @NonNull
        String messageId,

        @JacksonXmlProperty(namespace = "http://gpe.cz/pay/pay-ws/proc/v1/type")
        @NonNull
        String provider,

        @JacksonXmlProperty(namespace = "http://gpe.cz/pay/pay-ws/proc/v1/type")
        @NonNull
        String merchantNumber,

        @JacksonXmlProperty(namespace = "http://gpe.cz/pay/pay-ws/proc/v1/type")
        @NonNull
        String paymentNumber,

        @JacksonXmlProperty(namespace = "http://gpe.cz/pay/pay-ws/proc/v1/type")
        @NonNull
        String signature

) {}
