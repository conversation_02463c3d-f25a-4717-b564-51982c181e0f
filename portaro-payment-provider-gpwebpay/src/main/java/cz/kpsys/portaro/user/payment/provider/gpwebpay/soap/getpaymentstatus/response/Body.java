package cz.kpsys.portaro.user.payment.provider.gpwebpay.soap.getpaymentstatus.response;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.NonNull;

public record Body(

        @JacksonXmlProperty(namespace = "http://gpe.cz/pay/pay-ws/proc/v1", localName = "getPaymentStatusResponse")
        @NonNull
        GetPaymentStatusResponse getPaymentStatusResponse

) {}
