package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.commons.contextual.AndBooleanContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.crypto.KeystoreResourcePrivateKeyProvider;
import cz.kpsys.portaro.commons.crypto.MessageSigner;
import cz.kpsys.portaro.commons.crypto.MessageVerifierWithCertificate;
import cz.kpsys.portaro.commons.crypto.ResourceCertificateProvider;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.web.StaticLink;
import cz.kpsys.portaro.database.FlushingJpaSaver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.licence.FeatureEnabledProvider;
import cz.kpsys.portaro.licence.FeatureManager;
import cz.kpsys.portaro.mail.MailService;
import cz.kpsys.portaro.payment.*;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.template.TemplateEngine;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.contact.ContactManager;
import cz.kpsys.portaro.user.locale.UserLocaleResolver;
import cz.kpsys.portaro.user.payment.provider.*;
import cz.kpsys.portaro.user.payment.provider.gpwebpay.soap.SoapCaller;
import cz.kpsys.portaro.web.page.ModelAndPageViewFactory;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.web.client.RestOperations;

import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.text.NumberFormat;
import java.util.concurrent.ExecutorService;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class PaymentGpwebpayConfig {

    @NonNull Provider<@NonNull String> publicContextPath;
    @NonNull Translator<Department> translator;
    @NonNull QueryFactory queryFactory;
    @NonNull TemplateEngine templateEngine;
    @NonNull SecurityManager securityManager;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull Provider<@NonNull User> portaroUserProvider;
    @NonNull ExecutorService executorService;
    @NonNull NumberFormat currencyFormat;
    @NonNull FeatureManager featureManager;
    @NonNull SettingLoader settingLoader;
    @NonNull ContextualProvider<Department, @NullableNotBlank String> serverUrlProvider;
    @NonNull ContactManager contactManager;
    @NonNull UserLocaleResolver userLocaleResolver;
    @NonNull MailService mailService;
    @NonNull RestOperations rest;
    @NonNull ModelAndPageViewFactory modelAndPageViewFactory;
    @NonNull RowMapper<Payment> paymentRowMapper;
    @NonNull TransactionLoader transactionLoader;
    @NonNull PaymentServiceRegistrar paymentServiceRegistrar;
    @NonNull PaymentSplitterService paymentSplitterService;
    @NonNull TransactionCreator transactionsCreator;
    @NonNull PaymentBasicCreator paymentCreator;
    @NonNull EntityManager entityManager;
    @NonNull ByIdLoadable<Payment, Integer> concretePaymentLoader;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull Saver<Payment, Payment> paymentSaver;
    @NonNull PaymentItemsCreator paymentItemsCreator;
    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull TransactionsRefunder transactionsRefunder;


    @Bean
    public GpwebpayEndpointsController gpwebpayEndpointsController() {
        return new GpwebpayEndpointsController(
                gpwebpayPaymentLoader(),
                gpwebpayPaymentUpdater(),
                translator,
                publicContextPath,
                currencyFormat,
                modelAndPageViewFactory
        );
    }

    @Bean
    public PaymentProvider gpwebpayPaymentProvider() {
        return new PaymentProvider(GpwebpayConstants.PROVIDER_NAME);
    }

    @Bean
    public PaymentService gpwebpayPaymentService() {
        return new GpwebpayPaymentService(
                gpwebpayTemplate(),
                gpwebpayPaymentSaver(),
                settingLoader.getDepartmentedProvider(PaymentProviderSettingKeys.TERMS_AND_CONDITIONS_URL).andThenFastReturningNull(url -> new StaticLink(url, Texts.ofMessageCoded("payment.TermsAndConditionsLinkText"))),
                defaultTransactionTemplateFactory.get(),
                paymentItemsCreator,
                gpwebpayPaymentCreator(),
                paymentCreator
        );
    }

    @Bean
    public GpwebpayTemplate gpwebpayTemplate() {
        final Provider<Boolean> prodEnvironmentEnabled = () -> settingLoader.getOnRoot(PaymentProviderSettingKeys.GPWEBPAY_ORDER_URL).equals(GpwebpayConstants.PROD_API_URL);
        Provider<String> soapUrlProvider = () -> prodEnvironmentEnabled.get() ? GpwebpayConstants.PROD_SOAP_URL : GpwebpayConstants.TEST_SOAP_URL;
        Provider<PrivateKey> privateKeyProvider = () -> {
            String keyPath = prodEnvironmentEnabled.get() ? GpwebpayConstants.PROD_PRIVATE_KEY_KEYSTORE_FILENAME : GpwebpayConstants.TEST_PRIVATE_KEY_KEYSTORE_FILENAME;
            String keystoreAndKeyPass = prodEnvironmentEnabled.get() ? GpwebpayConstants.PROD_PRIVATE_KEY_KEYSTORE_AND_KEY_PASS : GpwebpayConstants.TEST_PRIVATE_KEY_KEYSTORE_AND_KEY_PASS;
            return KeystoreResourcePrivateKeyProvider.ofJceksWithSamePasswordOfKeystoreAndEntry(new ClassPathResource(keyPath), keystoreAndKeyPass, "alias").get();
        };
        Provider<X509Certificate> certificateProvider = () -> {
            String certPath = prodEnvironmentEnabled.get() ? GpwebpayConstants.PROD_CERTIFICATE_FILENAME : GpwebpayConstants.TEST_CERTIFICATE_FILENAME;
            return new ResourceCertificateProvider(new ClassPathResource(certPath)).get();
        };
        return new GpwebpayTemplate(
                settingLoader.getDepartmentedProvider(PaymentProviderSettingKeys.GPWEBPAY_ORDER_URL).throwingWhenNull(),
                serverUrlProvider.andThen(serverUrl -> serverUrl.concat("/payment/gpwebpay/return")),
                settingLoader.getDepartmentedProvider(PaymentProviderSettingKeys.GPWEBPAY_MERCHANT_NUMBER).throwingWhenNull(),
                ContextIgnoringContextualProvider.<Department, String>of(GpwebpayTemplate.BANK_CODE_KB).throwingWhenNull(),
                MessageSigner.createWithSha1(privateKeyProvider),
                new MessageVerifierWithCertificate(certificateProvider),
                new SoapCaller(soapUrlProvider, rest)
        );
    }

    @Bean
    public ContextualProvider<Department, Boolean> gpwebpayPaymentEnabled() {
        return AndBooleanContextualProvider.of(
                new ContextIgnoringContextualProvider<>(new FeatureEnabledProvider(featureManager, FeatureManager.FEATURE_PAYMENT_GATEWAY)),
                settingLoader.getDepartmentedProvider(PaymentProviderSettingKeys.GPWEBPAY_ENABLED)
        );
    }

    @Bean
    public IsolatedAuthSecuredGpwebpayPaymentUpdater gpwebpayPaymentUpdater() {
        GpwebpayPaymentUpdater pure = new GpwebpayPaymentUpdater(
                gpwebpayPaymentSaver(),
                gpwebpayTemplate(),
                defaultTransactionTemplateFactory.get());
        return new IsolatedAuthSecuredGpwebpayPaymentUpdater(
                pure,
                securityManager,
                authenticationHolder,
                portaroUserProvider,
                executorService
        );
    }

    @Bean
    public GpwebpayPaymentLoader gpwebpayPaymentLoader() {
        return new SpringDbGpwebpayPaymentLoader(
                notAutoCommittingJdbcTemplate,
                queryFactory,
                gpwebpayPaymentRowMapper()
        );
    }

    @Bean
    public RowMapper<GpwebpayPayment> gpwebpayPaymentRowMapper() {
        return new GpwebpayPaymentRowMapper(paymentRowMapper);
    }

    @Bean
    public Saver<PaymentSaveCommand<GpwebpayPayment>, GpwebpayPayment> gpwebpayPaymentSaver() {
        return new MailSendingReceiptPaymentSaver<>(
                new PaymentWithTransactionsSaver<>(
                        transactionLoader,
                        paymentSplitterService,
                        transactionsCreator,
                        transactionsRefunder,
                        new CombinedGpwebpayPaymentSaver(
                                paymentSaver,
                                new GenericHookableSaver<>(
                                        new PostConvertingSaver<>(
                                                new PreConvertingSaver<>(
                                                        new GpwebpayPaymentToEntityConverter(),
                                                        new FlushingJpaSaver<>(
                                                                new SimpleJpaRepository<>(GpwebpayPaymentEntity.class, entityManager)
                                                        )
                                                ),
                                                new GpwebpayPaymentFromEntityConverter(concretePaymentLoader)
                                        )
                                )
                        )
                ),
                contactManager,
                templateEngine,
                userLocaleResolver,
                mailService,
                defaultTransactionTemplateFactory.withRequiresNewPropagation().get()
        );
    }

    @Bean
    public GpwebpayPaymentCreator gpwebpayPaymentCreator() {
        return new GpwebpayPaymentCreator(gpwebpayPaymentSaver(), defaultTransactionTemplateFactory.get());
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerProviders() {
        paymentServiceRegistrar.register(
                GpwebpayConstants.PROVIDER_NAME,
                1200,
                GpwebpayPayment.class,
                gpwebpayPaymentEnabled(),
                gpwebpayPaymentProvider(),
                gpwebpayPaymentLoader(),
                gpwebpayPaymentService(),
                gpwebpayPaymentUpdater()
        );
    }

}
