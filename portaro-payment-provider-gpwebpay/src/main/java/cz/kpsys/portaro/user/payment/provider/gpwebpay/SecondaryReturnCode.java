package cz.kpsys.portaro.user.payment.provider.gpwebpay;

public enum SecondaryReturnCode {

    NOTHING(0, null, null, null),

    ORDERNUMBER(1, "ORDERNUMBER", null, null),

    MERCHANTNUMBER(2, "MERCHANTNUMBER", null, null),

    AMOUNT(6, "AMOUNT", null, null),

    CURRENCY(7, "CURRENCY", null, null),

    DEPOSITFLAG(8, "DEPOSITFLAG", null, null),

    MERORDERNUM(10, "MERORDERNUM", null, null),

    CREDITNUMBER(11, "CREDITNUMBER", null, null),

    OPERATION(12, "OPERATION", null, null),

    BATCH(18, "BATCH", null, null),

    ORDER(22, "ORDER", null, null),

    URL(24, "URL", null, null),

    MD(25, "MD", null, null),

    DESC(26, "DESC", null, null),

    DIGEST(34, "DIGEST", null, null),

    /**
     * Info: O<PERSON><PERSON><PERSON><PERSON><PERSON> dr<PERSON>le karty bylo <PERSON> (ne<PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON><PERSON><PERSON>, uza<PERSON><PERSON><PERSON><PERSON> okna pro autentikaci držitele karty se zpětnou vazbou…).
     * V transakci se nesmí pokračovat
     */
    CODE_3000(3000, null, "Neověřeno v 3D. Vydavatel karty není zapojen do 3D nebo karta nebyla aktivována", "Declined in 3D. Cardholder not authenticated in 3D"),

    /**
     * Ověření držitele karty v 3D systémech proběhlo úspěšně.
     * Pokračuje se autorizací objednávky.
     */
    CODE_3001(3001, null, "Držitel karty ověřen", "Authenticated"),

    /**
     * V 3D systémech nebylo možné ověřit držitele karty – karta, nebo její vydavatel, není zapojen do 3D.
     * V transakci se pokračuje
     */
    CODE_3002(3002, null, "Neověřeno v 3D. Vydavatel karty nebo karta není zapojena do 3D.", "Not Authenticated in 3D. Issuer or Cardholder not participating in 3D."),

    /**
     * V 3D systémech nebylo možné ověřit držitele karty – karta není aktivována, nebo její vydavatel, není zapojen do 3D.
     * V transakci je možné pokračovat.
     */
    CODE_3004(3004, null, "Neověřeno v 3D. Vydavatel karty není zapojen do 3D nebo karta nebyla aktivována", "Not Authenticated in 3D. Issuer not participating or Cardholder not enrolled"),

    /**
     * V 3D systémech nebylo možné ověřit držitele karty – vydavatel karty nepodporuje 3D, nebo technický problém v komunikaci s 3D systémy finančních asociací, či vydavatele karty.
     * V transakci není možné pokračovat, povoleno z důvodu zabezpečení obchodníka před případnou reklamací transakce držitelem karty
     */
    CODE_3005(3005, null, "Zamítnuto v 3D.Technický problém při ověření držitele karty", "Declined in 3D. Technical problem during Cardholder authentication"),

    /**
     * V 3D systémech nebylo možné ověřit držitele karty – technický problém ověření obchodníka v 3D systémech, anebo v komunikaci s 3D systémy finančních asociací, či vydavatele karty.
     * V transakci není možné pokračovat
     */
    CODE_3006(3006, null, "Zamítnuto v 3D. Technický problém při ověření držitele karty", "Declined in 3D. Technical problem during Cardholder authentication"),

    /**
     * V 3D systémech nebylo možné ověřit držitele karty – technický problém v 3D systémech.
     * V transakci není možné pokračovat
     */
    CODE_3007(3007, null, "Zamítnuto v 3D. Technický problém v systému zúčtující banky. Kontaktujte obchodníka", "Declined in 3D. Acquirer technical problem. Contact the merchant"),

    /**
     * Byla použita karta, která není v 3D systémech podporována.
     * V transakci není možné pokračovat.
     */
    CODE_3008(3008, null, "Zamítnuto v 3D. Použit nepodporovaný karetní produkt", "Declined in 3D. Unsupported card product"),

    /**
     * Zahrnuje důvody, které naznačují zneužití platební karty – kradená karta, podezření na podvod, ztracená karta apod.
     * - Karta je označena jako:
     * - Ztracená
     * - K zadržení
     * - K zadržení (speciální důvody)
     * - Ukradená
     * - Většinou pokus o podvodnou transakci
     */
    CODE_1001(1001, null, "Zamitnuto v autorizacnim centru, karta blokovana", "Declined in AC, Card blocked"),

    /**
     * Z autorizace se vrátil důvod zamítnutí “Do not honor“.
     * Vydavatel, nebo finanční asociace zamítla autorizaci BEZ udání důvodu
     */
    CODE_1002(1002, null, "Zamitnuto v autorizacnim centru, autorizace zamítnuta", "Declined in AC, Declined"),

    /**
     * Zahrnuje důvody:
     * expirovaná karta, chybné číslo karty, nastavení karty - pro kartu není povoleno použití na internetu, nepovolená karta, expirovaná karta, neplatná karta, neplatné číslo karty, částka přesahuje maximální limit karty, neplatné CVC/CVV, neplatná délka čísla karty, neplatná expirační doba, pro kartu je požadována kontrola PIN.
     */
    CODE_1003(1003, null, "Zamitnuto v autorizacnim centru, problem karty", "Declined in AC, Card problem"),

    /**
     * Autorizaci není možné provést z technických důvodů – technické problémy v systému vydavatele karty, nebo finančních asociací a finančních procesorů
     */
    CODE_1004(1004, null, "Zamitnuto v autorizacnim centru, technicky problem", "Declined in AC, Technical problem in authorization process"),

    /**
     * Důvody: nedostatek prostředků na účtu, překročeny limity, překročen max. povolený počet použití…
     */
    CODE_1005(1005, null, "Zamitnuto v autorizacnim centru, Problem uctu", "Declined in AC, Account problem");




    private int number;
    private String subject;
    private String czechDesc;
    private String engDesc;

    SecondaryReturnCode(int number, String subject, String czechDesc, String engDesc) {
        this.number = number;
        this.subject = subject;
        this.czechDesc = czechDesc;
        this.engDesc = engDesc;
    }

    public int getNumber() {
        return number;
    }

    public String getSubject() {
        return subject;
    }

    public String getCzechDesc() {
        return czechDesc;
    }

    public String getEngDesc() {
        return engDesc;
    }
}
