package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import cz.kpsys.portaro.bool.BooleanToBinaryNumberStringConverter;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.crypto.MessageSigner;
import cz.kpsys.portaro.commons.crypto.MessageVerifierWithCertificate;
import cz.kpsys.portaro.commons.object.Sequence;
import cz.kpsys.portaro.commons.util.PriceUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.web.UrlCreator;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.payment.PayCommand;
import cz.kpsys.portaro.soap.HeadlessXmlsoapEnvelope;
import cz.kpsys.portaro.user.payment.provider.gpwebpay.soap.SoapCaller;
import cz.kpsys.portaro.user.payment.provider.gpwebpay.soap.getpaymentstatus.request.GetPaymentStatus;
import cz.kpsys.portaro.user.payment.provider.gpwebpay.soap.getpaymentstatus.request.GetPaymentStatusBody;
import cz.kpsys.portaro.user.payment.provider.gpwebpay.soap.getpaymentstatus.request.PaymentStatusRequest;
import cz.kpsys.portaro.user.payment.provider.gpwebpay.soap.getpaymentstatus.response.PaymentStatusResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cz.kpsys.portaro.user.payment.provider.gpwebpay.GpwebpayConstants.*;

/**
 * Postup pro standardni nastaveni: <br/>
 * 1) vygenerovani soukromeho klice (s heslem) na portalu gpwebpay -> soubor gpwebpay-pvk.key. Pokud jiz mame soukromy klic svuj, napr. testing-gpwebpay-pvk.key (openssl format, zasifrovany heslem), muzeme ho pouzit <br/>
 * 2) nastaveni soukromeho klice k danemu merhantovi (na portalu gpwebpay) <br/>
 * 3) konverze soukromeho klice do JKS formatu pomoci GP webpay Keystore Manager -> soubor gpwebpay-pvk.jks <br/>
 * 4) stazeni Produkčního verejneho klice GPE na portalu gpwebpay -> soubor gpe.signing_prod.cer <br/>
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class GpwebpayTemplate {

    private static final String DELIM = "|";
    public static final String BANK_CODE_KB = "0100";

    @NonNull ContextualProvider<Department, @NonNull String> gpwebpayUrlProvider;
    @NonNull ContextualProvider<Department, @NonNull String> merchantReturnUrlProvider;
    @NonNull ContextualProvider<Department, @NonNull String> merchantNumberProvider;
    @NonNull ContextualProvider<Department, @NonNull String> bankCodeProvider;
    @NonNull MessageSigner messageSigner;
    @NonNull MessageVerifierWithCertificate messageVerifier;
    @NonNull SoapCaller soapCaller;
    @NonNull Function<@NonNull Boolean, @NonNull String> booleanToStringConverter = new BooleanToBinaryNumberStringConverter()::convert;


    public GpwebpayPaymentOrder createPaymentOrder(PayCommand request, String merchantOrderNumber) {
        return new GpwebpayPaymentOrder(
                merchantNumberProvider.getOn(request.department()),
                GpwebpayPaymentOrder.OPERATION_CREATE_ORDER,
                PriceUtil.toMinorUnitsLong(request.items().getSum()),
                createOrderNumber(),
                merchantOrderNumber,
                GpwebpayPaymentOrder.CURRENCY_ISO4217_NUMERIC_CZK,
                true,
                merchantReturnUrlProvider.getOn(request.department()),
                GpwebpayPaymentOrder.PAY_METHOD_CARD
        );
    }


    public String createPaymentGatewayUrl(GpwebpayPaymentOrder order, Department payRequestDepartment) {
        UrlCreator url = new UrlCreator()
                .serverBaseUrl(gpwebpayUrlProvider.getOn(payRequestDepartment));

        createDataMapOfOrder(order).forEach(url::addParameter);

        url.addParameter(PARAM_DIGEST, createSignature(order));

        return url.build();
    }


    public boolean verifyOrderResult(GpwebpayPaymentOrderResult orderResult) {
        Map<String, String> dataMap = createDataMapOfResult(orderResult);
        String stringToVerify = createJoinedDataString(dataMap, ORDERED_RESULT_DIGEST_PARAMS);

        String basedSignature = orderResult.getDigest();
        byte[] signatureBytes = StringUtil.base64ToBytes(basedSignature);

        return messageVerifier.verify(stringToVerify, signatureBytes);
    }


    public PaymentStatusResponse getPaymentStatus(String orderNumber, Department payRequestDepartment) {
        String messageId = UuidGenerator.forIdentifierWithoutDashes();
        String merchantNumber = merchantNumberProvider.getOn(payRequestDepartment);
        String bankCode = bankCodeProvider.getOn(payRequestDepartment);

        List<String> valuesForSignature = List.of(messageId, bankCode, merchantNumber, orderNumber);
        String stringToSign = StringUtil.listToString(valuesForSignature, DELIM);
        String signature = createBased64Signature(stringToSign);

        HeadlessXmlsoapEnvelope<GetPaymentStatusBody> envelope = new HeadlessXmlsoapEnvelope<>(
                new GetPaymentStatusBody(
                        new GetPaymentStatus(
                                new PaymentStatusRequest(
                                        messageId,
                                        bankCode,
                                        merchantNumber,
                                        orderNumber,
                                        signature
                                )
                        )));

        log.info("Calling SOAP request for {}", envelope.body());

        cz.kpsys.portaro.user.payment.provider.gpwebpay.soap.getpaymentstatus.response.Envelope response = soapCaller.call(envelope, cz.kpsys.portaro.user.payment.provider.gpwebpay.soap.getpaymentstatus.response.Envelope.class);
        return response.getBody().getPaymentStatusResponse().getPaymentStatusResponse();
    }


    /**
     * Parametr ORDERNUMBER, Typ numerický, Délka 15, Povinný ano
     */
    private String createOrderNumber() {
        String timePart = StringUtil.limitCharsLeftTrim(String.valueOf(System.currentTimeMillis()), 10); // last 10 chars of unix timestamp
        String randomPart = StringUtil.fillToSize(String.valueOf(Math.round(Math.random() * 1000)), '0', 3, true); // 3 random chars
        String sequencePart = StringUtil.limitCharsLeftTrim(StringUtil.fillToSize(String.valueOf(Sequence.getNextNumber()), '0', 2, true), 2); // 2 sequence chars
        return timePart + randomPart + sequencePart;
    }


    private Map<String, String> createDataMapOfOrder(GpwebpayPaymentOrder order) {
        HashMap<String, String> map = new HashMap<>();
        map.put(PARAM_MERCHANTNUMBER, order.getMerchantNumber());
        map.put(PARAM_OPERATION, order.getOperation());
        map.put(PARAM_ORDERNUMBER, order.getOrderNumber());
        map.put(PARAM_AMOUNT, String.valueOf(order.getAmount()));
        map.put(PARAM_CURRENCY, String.valueOf(order.getCurrency()));
        map.put(PARAM_DEPOSITFLAG, booleanToStringConverter.apply(order.getDeposit()));
        if (order.getMerchantOrderNumber() != null) {
            map.put(PARAM_MERORDERNUM, order.getMerchantOrderNumber());
        }
        map.put(PARAM_URL, order.getMerchantUrl());
        map.put(PARAM_PAYMETHOD, order.getPayMethod());
        return map;
    }


    private Map<String, String> createDataMapOfResult(GpwebpayPaymentOrderResult result) {
        HashMap<String, String> map = new HashMap<>();
        map.put(PARAM_OPERATION, result.getOperation());
        map.put(PARAM_ORDERNUMBER, result.getOrderNumber());
        if (result.getMerchantOrderNumber() != null) {
            map.put(PARAM_MERORDERNUM, result.getMerchantOrderNumber());
        }
        map.put(PARAM_PRCODE, String.valueOf(result.getPrimaryReturnCode()));
        map.put(PARAM_SRCODE, String.valueOf(result.getSecondaryReturnCode()));
        map.put(PARAM_RESULTTEXT, result.getResultString());
        return map;
    }


    private String createSignature(GpwebpayPaymentOrder dto) {
        Map<String, String> dataMap = createDataMapOfOrder(dto);
        String stringToSign = createJoinedDataString(dataMap, ORDERED_REQUEST_DIGEST_PARAMS);
        return createBased64Signature(stringToSign);
    }


    private String createBased64Signature(String stringToSign) {
        byte[] signatureBytes = messageSigner.sign(stringToSign);
        String basedSignature = StringUtil.bytesToBase64(signatureBytes);
        basedSignature = basedSignature
                .replaceAll("\r", "")
                .replaceAll("\n", "");
        return basedSignature;
    }


    private static String createJoinedDataString(Map<String, String> dataMap, List<String> orderRule) {
        return orderRule.stream()
                .filter(dataMap::containsKey)
                .map(dataMap::get)
                .collect(Collectors.joining(DELIM));
    }

}
