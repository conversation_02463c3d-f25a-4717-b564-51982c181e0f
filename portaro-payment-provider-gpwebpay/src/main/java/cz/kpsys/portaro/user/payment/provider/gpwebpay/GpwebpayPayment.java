package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.payment.Payment;
import cz.kpsys.portaro.payment.PaymentProxy;
import lombok.Getter;
import lombok.NonNull;
import org.springframework.lang.Nullable;

@SuppressWarnings("checkstyle:EmptyLineSeparator")
public class GpwebpayPayment extends PaymentProxy {

    public static final int PROPERTY_GATEWAY_URL_MAX_LENGTH = 2000;
    public static final int PROPERTY_RESULT_TEXT_MAX_LENGTH = 250;
    public static final int PROPERTY_SOAP_RESULT_STATUS_MAX_LENGTH = 121;
    public static final int PROPERTY_SOAP_RESULT_SUBSTATUS_MAX_LENGTH = 121;

    public GpwebpayPayment(@NonNull Payment target,
                           @Nullable String gpwebpayOrderNumber,
                           @Nullable String gatewayUrl) {
        super(target);
        this.gpwebpayOrderNumber = gpwebpayOrderNumber;
        this.gatewayUrl = gatewayUrl;
    }

    public GpwebpayPayment withResult(Integer primaryReturnCode, Integer secondaryReturnCode, String resultText) {
        this.resultPrimaryReturnCode = primaryReturnCode;
        this.resultSecondaryReturnCode = secondaryReturnCode;
        this.resultText = StringUtil.notBlankTrimmedString(resultText);
        return this;
    }

    public GpwebpayPayment withSoapResult(Integer soapResultState, String soapResultStatus, String soapResultSubstatus) {
        this.soapResultState = soapResultState;
        this.soapResultStatus = soapResultStatus;
        this.soapResultSubstatus = soapResultSubstatus;
        return this;
    }

    public GpwebpayPayment(@NonNull Payment target,
                           @NonNull String gpwebpayOrderNumber,
                           @Nullable String gatewayUrl,
                           @Nullable Integer resultPrimaryReturnCode,
                           @Nullable Integer resultSecondaryReturnCode,
                           @Nullable String resultText,
                           @Nullable Integer soapResultState,
                           @Nullable String soapResultStatus,
                           @Nullable String soapResultSubstatus) {
        super(target);
        this.gpwebpayOrderNumber = gpwebpayOrderNumber;
        this.gatewayUrl = gatewayUrl;
        this.resultPrimaryReturnCode = resultPrimaryReturnCode;
        this.resultSecondaryReturnCode = resultSecondaryReturnCode;
        this.resultText = resultText;
        this.soapResultState = soapResultState;
        this.soapResultStatus = soapResultStatus;
        this.soapResultSubstatus = soapResultSubstatus;
    }

    @Getter
    @Nullable
    private final String gpwebpayOrderNumber;

    @Getter
    @Nullable
    private final String gatewayUrl;

    @Getter
    @Nullable
    private Integer resultPrimaryReturnCode;

    @Getter
    @Nullable
    private Integer resultSecondaryReturnCode;

    @Getter
    @Nullable
    private String resultText;

    @Getter
    @Nullable
    private Integer soapResultState;

    @Getter
    @Nullable
    private String soapResultStatus;

    @Getter
    @Nullable
    private String soapResultSubstatus;

    @Nullable
    @JsonIgnore
    @Override
    public String getProviderPaymentId() {
        return this.gpwebpayOrderNumber;
    }
}
