package cz.kpsys.portaro.user.payment.provider.gpwebpay.soap.getpaymentstatus.response;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

public class PaymentStatusResponse {

    @JacksonXmlProperty(namespace = "http://gpe.cz/pay/pay-ws/proc/v1/type")
    private String messageId;

    @JacksonXmlProperty(namespace = "http://gpe.cz/pay/pay-ws/proc/v1/type")
    private Integer state;

    @JacksonXmlProperty(namespace = "http://gpe.cz/pay/pay-ws/proc/v1/type")
    private String status;

    @JacksonXmlProperty(namespace = "http://gpe.cz/pay/pay-ws/proc/v1/type")
    private String subStatus;

    @JacksonXmlProperty(namespace = "http://gpe.cz/pay/pay-ws/proc/v1/type")
    private String signature;


    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSubStatus() {
        return subStatus;
    }

    public void setSubStatus(String subStatus) {
        this.subStatus = subStatus;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }


    @Override
    public String toString() {
        return "PaymentStatusResponse{" +
                "messageId='" + messageId + '\'' +
                ", state=" + state +
                ", status='" + status + '\'' +
                ", subStatus='" + subStatus + '\'' +
                ", signature='" + signature + '\'' +
                '}';
    }
}
