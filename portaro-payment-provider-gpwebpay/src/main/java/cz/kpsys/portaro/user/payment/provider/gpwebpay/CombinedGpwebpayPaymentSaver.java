package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.payment.Payment;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CombinedGpwebpayPaymentSaver implements Saver<GpwebpayPayment, GpwebpayPayment> {

    @NonNull Saver<Payment, Payment> paymentSaver;
    @NonNull Saver<GpwebpayPayment, GpwebpayPayment> gpwebpayPaymentSaver;


    @Override
    public @NonNull GpwebpayPayment save(@NonNull GpwebpayPayment gpwebpayPayment) {
        Payment payment = gpwebpayPayment.getPayment();
        paymentSaver.save(payment);
        return gpwebpayPaymentSaver.save(gpwebpayPayment);
    }

}
