package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.payment.Payment;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class GpwebpayPaymentFromEntityConverter implements Converter<GpwebpayPaymentEntity, GpwebpayPayment> {

    @NonNull ByIdLoadable<Payment, Integer> concretePaymentLoader;

    @Override
    public GpwebpayPayment convert(@NonNull GpwebpayPaymentEntity source) {
        return new GpwebpayPayment(
                concretePaymentLoader.getById(source.getId()),
                source.getGpwebpayOrderNumber(),
                source.getGatewayUrl(),
                source.getResultPrimaryReturnCode(),
                source.getResultSecondaryReturnCode(),
                source.getResultText(),
                source.getSoapResultState(),
                source.getSoapResultStatus(),
                source.getSoapResultSubstatus()
        );
    }
}
