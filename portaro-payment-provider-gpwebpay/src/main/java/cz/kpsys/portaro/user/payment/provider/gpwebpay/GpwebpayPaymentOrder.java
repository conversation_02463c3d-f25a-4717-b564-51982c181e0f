package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import lombok.NonNull;
import lombok.Value;
import org.springframework.lang.Nullable;

@Value
public class GpwebpayPaymentOrder {

    public static final String OPERATION_CREATE_ORDER = "CREATE_ORDER";

    public static final String CURRENCY_ISO4217_CZK = "CZK";
    public static final int CURRENCY_ISO4217_NUMERIC_CZK = 203;

    public static final String PAY_METHOD_CARD = "CRD";
    public static final String PAY_METHOD_MASTER_CARD_MOBILE = "MCM";
    public static final String PAY_METHOD_MASTER_PASS = "MPS";

    /**
     * Přidělené číslo obchodníka
     */
    @NonNull
    String merchantNumber;

    /**
     * Typ operace
     */
    @NonNull
    String operation;

    /**
     * Částka v nejmenších jednotkách dané měny pro Kč = v ha<PERSON>ř<PERSON><PERSON>, pro EUR = v centech
     */
    @NonNull
    Long amount;

    /**
     * <PERSON><PERSON>lo platby; Č<PERSON>lo musí být v každém požadavku od obchodníka unikátní.
     */
    @NonNull
    String orderNumber;

    /**
     * Číslo platby.
     * V případě, že není zadáno, použije se hodnota ORDERNUMBER
     * Zobrazí se na výpisu z banky.
     * Každá banka má své řešení/limit.
     * DO SYSTÉMU POSKYTOVATELE SE, AKTUÁLNĚ, PROPAGUJE MAX 16 ČÍSLIC. KOLIK JE NÁSLEDNĚ ZOBRAZENO NA VÝPISE UVÁDÍ TABULKA NA KONCI DOKUMENTU
     */
    @Nullable
    String merchantOrderNumber;

    /**
     * Identifikátor měny dle ISO 4217
     */
    @NonNull
    Integer currency;

    /**
     * Udává, zda má být platba uhrazena
     * automaticky.
     * Povolené hodnoty:
     * 0 = není požadována okamžitá úhrada
     * 1 = je požadována úhrada
     */
    @NonNull
    Boolean deposit;

    /**
     * Plná URL adresa obchodníka. <br/>
     * Na tuto adresu bude odeslán výsledek <br/>
     * požadavku. Výsledek je přeposlán přes <br/>
     * prohlížeč zákazníka – tj. je použit redirect <br/>
     * (metoda GET). <br/>
     * (včetně specifikace protokolu – např. https://) <br/>
     * Z bezpečnostních důvodů může dojít <br/>
     * k zamezení některých tvarů URL adresy – <br/>
     * např. použití parametrů v adrese. Tuto <br/>
     * kontrolu nelze vypnout a je nutné odzkoušet <br/>
     * reálný tvar návratové adresy v testovacím <br/>
     * prostředí. <br/>
     */
    @NonNull
    String merchantUrl;

    /**
     * Hodnota určující preferovanou platební metodu.
     * Podporované hodnoty:
     * CRD – platební karta
     * MCM – MasterCard Mobile
     * MPS – MasterPass
     */
    @NonNull
    String payMethod;

}
