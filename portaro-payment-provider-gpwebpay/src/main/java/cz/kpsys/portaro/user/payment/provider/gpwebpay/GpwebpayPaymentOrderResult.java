package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import lombok.NonNull;
import lombok.Value;
import org.springframework.lang.Nullable;

@Value
public class GpwebpayPaymentOrderResult {

    @NonNull
    String operation;

    @NonNull
    String orderNumber;

    @Nullable
    String merchantOrderNumber;

    @NonNull
    Integer primaryReturnCode;

    @NonNull
    Integer secondaryReturnCode;

    @NonNull
    String resultString;

    @NonNull
    String digest;

    @NonNull
    String digest1;

}
