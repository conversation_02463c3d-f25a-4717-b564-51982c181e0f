package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.web.GenericPageController;
import cz.kpsys.portaro.web.page.ModelAndPageViewFactory;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import java.text.NumberFormat;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static cz.kpsys.portaro.user.payment.provider.gpwebpay.GpwebpayConstants.*;

@RequestMapping
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class GpwebpayEndpointsController extends GenericPageController {

    @NonNull GpwebpayPaymentLoader gpwebpayPaymentLoader;
    @NonNull IsolatedAuthSecuredGpwebpayPaymentUpdater gpwebpayPaymentUpdater;
    @NonNull Translator<Department> translator;
    @NonNull Provider<@NonNull String> publicContextPath;
    @NonNull NumberFormat currencyFormat;
    @NonNull ModelAndPageViewFactory modelAndPageViewFactory;

    @RequestMapping("/payment/gpwebpay/return")
    public ModelAndView paymentReturn(@RequestParam(value = PARAM_OPERATION) String operation,
                                      @RequestParam(value = PARAM_ORDERNUMBER) String orderNumber,
                                      @RequestParam(value = PARAM_MERORDERNUM, required = false) String merchantOrderNumber,
                                      @RequestParam(value = PARAM_PRCODE) Integer primaryReturnCode,
                                      @RequestParam(value = PARAM_SRCODE) Integer secondaryReturnCode,
                                      @RequestParam(value = PARAM_RESULTTEXT, required = false) String resultText,
                                      @RequestParam(value = PARAM_DIGEST) String digest,
                                      @RequestParam(value = PARAM_DIGEST1) String digest1,
                                      Locale locale,
                                      @CurrentDepartment Department currentDepartment,
                                      HttpServletRequest request) {
        GpwebpayPaymentOrderResult orderResult = new GpwebpayPaymentOrderResult(operation, orderNumber, merchantOrderNumber, primaryReturnCode, secondaryReturnCode, resultText, digest, digest1);
        log.info("Returned from GP webpay (gpwebpay order number {}})", orderResult.getOrderNumber());

        GpwebpayPayment payment = gpwebpayPaymentLoader.getByGpwebpayOrderNumber(orderResult.getOrderNumber());
        payment = gpwebpayPaymentUpdater.refreshState(payment, orderResult, currentDepartment);
        Map<String, Object> model = Map.of(
                "message", createResultMessage(payment, currentDepartment, locale),
                "origin", String.format("%s/#!/users/%s", publicContextPath.get(), payment.getPayer().getId())
        );
        return modelAndPageViewFactory.pageView("message-page", currentDepartment, request, model, List.of());
    }


    private String createResultMessage(GpwebpayPayment payment, Department currentDepartment, Locale locale) {
        return switch (payment.getState()) {
            case CREATED -> String.format("Platba %s byla vytvořena, čeká se na její přijetí", currencyFormat.format(payment.getSumToPay()));
            case PAID -> String.format("Platba %s byla úspěšně provedena", currencyFormat.format(payment.getSumToPay()));
            case CANCELED -> String.format("Platba %s byla zrušena", currencyFormat.format(payment.getSumToPay()));
            default -> String.format("Platba %s je nyní ve stavu %s", currencyFormat.format(payment.getSumToPay()), payment.getState().getText().localize(translator, currentDepartment, locale));
        };
    }

}
