package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

public class GpwebpayPaymentToEntityConverter implements Converter<GpwebpayPayment, GpwebpayPaymentEntity> {

    @Override
    public GpwebpayPaymentEntity convert(@NonNull GpwebpayPayment source) {
        return new GpwebpayPaymentEntity(
                source.getId(),
                source.getGpwebpayOrderNumber(),
                source.getGatewayUrl(),
                source.getResultPrimaryReturnCode(),
                source.getResultSecondaryReturnCode(),
                source.getResultText(),
                source.getSoapResultState(),
                source.getSoapResultStatus(),
                source.getSoapResultSubstatus()
        );
    }
}
