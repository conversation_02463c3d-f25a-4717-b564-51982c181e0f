package cz.kpsys.portaro.user.payment.provider.gpwebpay.soap.getpaymentstatus.request;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.NonNull;

public record GetPaymentStatus(

        @JacksonXmlProperty(namespace = "http://gpe.cz/pay/pay-ws/proc/v1", localName = "paymentStatusRequest")
        @NonNull
        PaymentStatusRequest paymentStatusRequest

) {}
