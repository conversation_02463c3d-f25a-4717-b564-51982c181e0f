package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.context.AuthenticatedSubjectUpdater;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.payment.PaymentSaveCommand;
import cz.kpsys.portaro.payment.PaymentState;
import cz.kpsys.portaro.security.AccessDeniedException;
import cz.kpsys.portaro.user.payment.provider.gpwebpay.soap.getpaymentstatus.response.PaymentStatusResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionTemplate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class GpwebpayPaymentUpdater implements AuthenticatedSubjectUpdater<GpwebpayPayment, Department> {

    @NonNull Saver<PaymentSaveCommand<GpwebpayPayment>, GpwebpayPayment> gpwebpayPaymentSaver;
    @NonNull GpwebpayTemplate template;
    @NonNull TransactionTemplate transactionTemplate;


    //musime synchronizovat - pokud by se refresh state volaly 2 ve stejny okamzik, do PLATBY by se ulozilo 2x
    public synchronized GpwebpayPayment refreshState(@NonNull GpwebpayPayment payment, @NonNull GpwebpayPaymentOrderResult orderResult, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        return transactionTemplate.execute(status -> {
            boolean resultVerified = template.verifyOrderResult(orderResult);
            AccessDeniedException.throwIfNot(resultVerified, "Returned result was not verified", Texts.ofNative("Returned result was not verified"));

            final PaymentState oldState = payment.getState();
            final PaymentState newState = mapOrderResultToState(orderResult);
            final boolean stateChanged = payment.setNewState(newState);

            GpwebpayPayment updatedPayment = payment;
            if (stateChanged) {
                updatedPayment = payment.withResult(orderResult.getPrimaryReturnCode(), orderResult.getSecondaryReturnCode(), orderResult.getResultString());
                updatedPayment = gpwebpayPaymentSaver.save(new PaymentSaveCommand<>(updatedPayment, ctx, currentAuth));
                log.info("Payment {} state {} changed to {}", updatedPayment.getId(), oldState, updatedPayment.getState());
            } else {
                log.info("Payment {} state {} unchanged", updatedPayment.getId(), updatedPayment.getState());
            }

            return updatedPayment;
        });
    }


    private PaymentState mapOrderResultToState(GpwebpayPaymentOrderResult orderResult) {
        Integer primaryCodeNumber = orderResult.getPrimaryReturnCode();
        PrimaryReturnCode primaryCode = PrimaryReturnCode.getByNumber(primaryCodeNumber);

        return switch (primaryCode) {
            case OK -> PaymentState.PAID;
            case SESSION_EXPIRED -> PaymentState.TIMEOUTED;
            case CARDHOLDER_CANCELLED_PAYMENT -> PaymentState.CANCELED;
            default -> PaymentState.CANCELED;
        };
    }


    /**
     * musime synchronizovat - pokud by se refresh state volaly 2 ve stejny okamzik, do PLATBY by se ulozilo 2x
     */
    @Override
    public synchronized @NonNull GpwebpayPayment update(@NonNull GpwebpayPayment payment, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        PaymentStatusResponse paymentStatus = template.getPaymentStatus(payment.getGpwebpayOrderNumber(), payment.getDepartment());

        PaymentState newState = mapStatusResponseToState(paymentStatus);
        PaymentState oldState = payment.getState();
        boolean stateChanged = payment.setNewState(newState);

        if (stateChanged) {
            payment.withSoapResult(paymentStatus.getState(), paymentStatus.getStatus(), paymentStatus.getSubStatus());
            payment = gpwebpayPaymentSaver.save(new PaymentSaveCommand<>(payment, ctx, currentAuth));
            log.info("Payment {} state {} changed to {}", payment.getId(), oldState, payment.getState());
        } else {
            log.info("Payment {} state {} unchanged", payment.getId(), payment.getState());
        }

        return payment;
    }


    private PaymentState mapStatusResponseToState(PaymentStatusResponse paymentStatus) {
        Integer state = paymentStatus.getState();
        String status = paymentStatus.getStatus();
        String subStatus = paymentStatus.getSubStatus();

        return switch (status) {
            case "CAPTURED" -> PaymentState.PAID;
            case "UNPAID" -> switch (subStatus) {
                case "CANCELED", "TECHNICAL_PROBLEM", "FRAUD", "DECLINED" -> PaymentState.CANCELED;
                case "PGW_PAGE" ->
                        PaymentState.CREATED; //payment gateway page was showed to user, but still not paid (or user could close browser)
                default -> PaymentState.CREATED;
            };
            default ->
                    throw new UnsupportedOperationException("Unknown gpwebpay soap payment state %s, status %s, substatus %s".formatted(state, status, subStatus));
        };
    }

}
