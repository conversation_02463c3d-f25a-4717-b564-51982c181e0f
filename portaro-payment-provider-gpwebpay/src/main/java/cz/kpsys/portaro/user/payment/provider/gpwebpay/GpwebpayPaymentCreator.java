package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.payment.PayCommand;
import cz.kpsys.portaro.payment.Payment;
import cz.kpsys.portaro.payment.PaymentSaveCommand;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class GpwebpayPaymentCreator {

    @NonNull Saver<PaymentSaveCommand<GpwebpayPayment>, GpwebpayPayment> gpwebpayPaymentSaver;
    @NonNull TransactionTemplate transactionTemplate;

    public GpwebpayPayment create(@NonNull PayCommand command, @NonNull Payment payment, @NonNull String gpwebpayCreatedOrderNumber, @NonNull String gpwebpayGatewayUrl) {
        return transactionTemplate.execute(_ -> {
            GpwebpayPayment gpwebpayPayment = new GpwebpayPayment(payment, gpwebpayCreatedOrderNumber, gpwebpayGatewayUrl);
            gpwebpayPaymentSaver.save(new PaymentSaveCommand<>(gpwebpayPayment, command.ctx(), command.currentAuth()));

            return gpwebpayPayment;
        });
    }

}
