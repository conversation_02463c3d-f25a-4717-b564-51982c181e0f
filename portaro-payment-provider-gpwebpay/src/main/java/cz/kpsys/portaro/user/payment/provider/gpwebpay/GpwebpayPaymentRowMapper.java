package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.payment.Payment;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.transaction.annotation.Transactional;

import java.sql.ResultSet;
import java.sql.SQLException;

import static cz.kpsys.portaro.databasestructure.PaymentDb.PLACENI_GPWEBPAY.*;
import static cz.kpsys.portaro.databasestructure.PaymentDb.PLACENI_GPWEBPAY.SOAP_RESULT_SUBSTATUS;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class GpwebpayPaymentRowMapper implements RowMapper<GpwebpayPayment> {
    @NonNull RowMapper<Payment> paymentRowMapper;

    @Override
    @Transactional(readOnly = true)
    public GpwebpayPayment mapRow(@NonNull ResultSet rs, int rowNum) throws SQLException {
        Payment payment = paymentRowMapper.mapRow(rs, rowNum);

        String orderNumber = rs.getString(GPWEBPAY_ORDER_NUMBER);
        String gatewayUrl = rs.getString(GATEWAY_URL);
        Integer resultPrimaryReturnCode = DbUtils.getInteger(rs, RESULT_PRCODE);
        Integer resultSecondaryReturnCode = DbUtils.getInteger(rs, RESULT_SRCODE);
        String resultText = rs.getString(RESULT_TEXT);
        Integer soapResultState = DbUtils.getInteger(rs, SOAP_RESULT_STATE);
        String soapResultStatus = rs.getString(SOAP_RESULT_STATUS);
        String soapResultSubstatus = rs.getString(SOAP_RESULT_SUBSTATUS);

        return new GpwebpayPayment(payment, orderNumber, gatewayUrl)
                .withResult(resultPrimaryReturnCode, resultSecondaryReturnCode, resultText)
                .withSoapResult(soapResultState, soapResultStatus, soapResultSubstatus);
    }

}
