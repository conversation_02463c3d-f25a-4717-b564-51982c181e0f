package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import cz.kpsys.portaro.commons.object.Identified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import static cz.kpsys.portaro.databasestructure.PaymentDb.PLACENI_GPWEBPAY.*;

@Entity
@Table(name = PLACENI_GPWEBPAY)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class GpwebpayPaymentEntity implements Identified<Integer> {

    @Id
    @Column(name = FK_PLACENI)
    @EqualsAndHashCode.Include
    @NonNull
    Integer id;

    @Column(name = GPWEBPAY_ORDER_NUMBER)
    @NonNull
    String gpwebpayOrderNumber;

    @Column(name = GATEWAY_URL)
    @Nullable
    String gatewayUrl;

    @Column(name = RESULT_PRCODE)
    @Nullable
    Integer resultPrimaryReturnCode;

    @Column(name = RESULT_SRCODE)
    @Nullable
    Integer resultSecondaryReturnCode;

    @Column(name = RESULT_TEXT)
    @Nullable
    String resultText;

    @Column(name = SOAP_RESULT_STATE)
    @Nullable
    Integer soapResultState;

    @Column(name = SOAP_RESULT_STATUS)
    @Nullable
    String soapResultStatus;

    @Column(name = SOAP_RESULT_SUBSTATUS)
    @Nullable
    String soapResultSubstatus;
}
