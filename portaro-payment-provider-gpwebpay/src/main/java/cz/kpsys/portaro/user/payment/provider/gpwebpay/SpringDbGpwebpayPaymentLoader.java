package cz.kpsys.portaro.user.payment.provider.gpwebpay;

import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.transaction.annotation.Transactional;

import java.util.function.Consumer;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.PaymentDb.PLACENI.ID_PLACENI;
import static cz.kpsys.portaro.databasestructure.PaymentDb.PLACENI.TABLE;
import static cz.kpsys.portaro.databasestructure.PaymentDb.PLACENI_GPWEBPAY.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbGpwebpayPaymentLoader implements GpwebpayPaymentLoader {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull RowMapper<GpwebpayPayment> gpwebpayPaymentRowMapper;

    @Override
    @Transactional(readOnly = true)
    public GpwebpayPayment getById(final @NonNull Integer id) {
        return findOne(sq -> sq.where().eq(FK_PLACENI, id));
    }

    @Override
    @Transactional(readOnly = true)
    public GpwebpayPayment getByGpwebpayOrderNumber(final @NonNull String gpwebpayOrderNumber) {
        return findOne(sq -> sq.where().eq(GPWEBPAY_ORDER_NUMBER, gpwebpayOrderNumber));
    }

    private GpwebpayPayment findOne(Consumer<SelectQuery> whereClauseModifier) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(WHOLE(TABLE), WHOLE(PLACENI_GPWEBPAY));
        sq.from(TABLE);
        sq.joins().add(PLACENI_GPWEBPAY, COLSEQ(TC(TABLE, ID_PLACENI), TC(PLACENI_GPWEBPAY, FK_PLACENI)));
        whereClauseModifier.accept(sq);
        return jdbcTemplate.queryForObject(sq.getSql(), sq.getParamMap(), gpwebpayPaymentRowMapper);
    }


}
