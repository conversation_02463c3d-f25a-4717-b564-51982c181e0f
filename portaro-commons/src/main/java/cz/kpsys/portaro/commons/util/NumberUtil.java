package cz.kpsys.portaro.commons.util;

import lombok.NonNull;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.function.Supplier;

public class NumberUtil {

    public static <N extends Number> N requirePositive(N n, Supplier<String> errorMessage) {
        if (!isPositive(n)) {
            throw new IllegalArgumentException(errorMessage.get());
        }
        return n;
    }

    public static <N extends Number> N requirePositiveOrZero(@NonNull N n, Supplier<String> errorMessage) {
        if (!isPositiveOrZero(n)) {
            throw new IllegalArgumentException(errorMessage.get());
        }
        return n;
    }

    public static <N extends Number> N requireNegative(N n, Supplier<String> errorMessage) {
        if (!isNegative(n)) {
            throw new IllegalArgumentException(errorMessage.get());
        }
        return n;
    }

    public static <N extends Number> N requireZero(N n, String variableName) {
        if (n instanceof BigDecimal bd) {
            if (isZero(bd)) {
                return n;
            } else {
                throw new IllegalArgumentException("Number '%s' should be zero, but is %s".formatted(variableName, n));
            }
        }
        throw new IllegalArgumentException("Unsupported number type: %s".formatted(n.getClass().getName()));
    }

    public static boolean isZero(@NonNull Number n) {
        return switch (n) {
            case BigDecimal bd -> bd.compareTo(BigDecimal.ZERO) == 0;
            case Integer i -> i == 0;
            case Long l -> l == 0L;
            case Double d -> d == 0d;
            case Float f -> f == 0f;
            default -> throw new IllegalArgumentException("Unsupported number type: %s".formatted(n.getClass().getName()));
        };
    }

    public static boolean isEqual(@NonNull BigDecimal n1, @NonNull BigDecimal n2) {
        return n1.compareTo(n2) == 0;
    }

    public static boolean isZero(@NonNull BigDecimal n) {
        return n.compareTo(BigDecimal.ZERO) == 0;
    }

    public static boolean isNotZero(@NonNull BigDecimal n) {
        return !isZero(n);
    }

    public static boolean isNullOrZero(Integer n) {
        return n == null || n == 0;
    }

    public static boolean isNullOrZeroOrNegative(Integer n) {
        return isNullOrZero(n) || n < 0;
    }

    public static boolean isPositive(Number n) {
        return switch (n) {
            case null -> false;
            case BigDecimal bd -> bd.compareTo(BigDecimal.ZERO) > 0;
            case Integer i -> i > 0;
            case Long l -> l > 0;
            case Double d -> d > 0d;
            case Float f -> f > 0f;
            default -> throw new IllegalArgumentException("Unsupported number type: %s".formatted(n.getClass().getName()));
        };
    }

    public static boolean isNegative(Number n) {
        return switch (n) {
            case null -> false;
            case BigDecimal bd -> bd.compareTo(BigDecimal.ZERO) < 0;
            case Integer i -> i < 0;
            case Long l -> l < 0;
            case Double d -> d < 0d;
            case Float f -> f < 0f;
            default -> throw new IllegalArgumentException("Unsupported number type: %s".formatted(n.getClass().getName()));
        };
    }

    public static boolean isPositiveOrZero(Number n) {
        if (n == null) {
            return false;
        }
        return isPositive(n) || isZero(n);
    }

    public static boolean isLt(BigDecimal n, BigDecimal compared) {
        return n.compareTo(compared) < 0;
    }

    public static boolean isGt(BigDecimal n, BigDecimal compared) {
        return n.compareTo(compared) > 0;
    }

    public static boolean isLtEq(BigDecimal n, BigDecimal compared) {
        return n.compareTo(compared) <= 0;
    }

    public static boolean isGtEq(BigDecimal n, BigDecimal compared) {
        return n.compareTo(compared) >= 0;
    }

    public static int zeroIfNull(Integer n) {
        if (isNullOrZero(n)) {
            return 0;
        }
        return n;
    }

    public static Optional<Integer> emptyIfNullOrZero(Integer n) {
        if (isNullOrZero(n)) {
            return Optional.empty();
        }
        return Optional.of(n);
    }

    public static <NUMBER extends Number> NUMBER nullIfZero(NUMBER n) {
        if (isNullOrZero(n)) {
            return null;
        }
        return n;
    }

    public static boolean isNullOrZero(Number n) {
        return n == null || n.longValue() == 0;
    }

    public static int defaultIfNegative(int n, int dflt) {
        return n < 0 ? dflt : n;
    }

    public static int defaultIfNegativeOrZero(int n, int dflt) {
        return n <= 0 ? dflt : n;
    }

    public static int leastSignificantDigits(int number, int digits) {
        return number % (int) Math.pow(10, digits);
    }

}
