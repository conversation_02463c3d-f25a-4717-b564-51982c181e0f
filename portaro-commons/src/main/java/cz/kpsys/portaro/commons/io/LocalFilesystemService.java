package cz.kpsys.portaro.commons.io;

import cz.kpsys.portaro.commons.licence.FeatureNotEnabledException;
import cz.kpsys.portaro.commons.object.Asserter;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.id.UuidGenerator;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.util.Assert;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.file.*;
import java.util.*;
import java.util.stream.Stream;

/**
 * <ul>
 * <li>Všechny vstupní relativní cesty jsou zkonvertovány z zpětných lomítek na dopředné.</li>
 * <li>Všechny výstupní relativní cesty obsahují dopředná lomítka.</li>
 * </ul>
 */
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class LocalFilesystemService implements FilesystemService, TempFileService<LocalFilesystemService.FsTmpFile> {

    public static final String DATA_DIR = "data";
    public static final String TEMP_DIR = "tmp";
    public static final String TO_DELETE_DIR = "to_delete";

    private static String pathFixup(String path) {
        return path.replace('\\', '/');
    }

    @NonNull Provider<@NonNull BlobDir> blobDirProvider;
    @NonNull Provider<@NonNull Boolean> blobDirModificationEnabledProvider;
    @NonNull Asserter<? extends FeatureNotEnabledException> blobDirModificationEnabledAsserter;

    public LocalFilesystemService(@NonNull Provider<@NonNull BlobDir> blobDirProvider,
                                  @NonNull Provider<@NonNull Boolean> blobDirModificationEnabledProvider) {
        this.blobDirProvider = blobDirProvider;
        this.blobDirModificationEnabledProvider = blobDirModificationEnabledProvider;
        this.blobDirModificationEnabledAsserter = blobDirModificationEnabledProvider.toEnabledAsserter(enabled -> enabled, "Blob storage modification", null);
    }

    private Path getBlobDir() {
        return blobDirProvider.get().path();
    }

    public boolean isModifiable() {
        return blobDirModificationEnabledProvider.get();
    }

    /**
     * Vrací soubor na souborovém systému vzhledem k blobDiru.
     *
     * @param relativeFilePath relativní cesta k blobDiru (typicky z databáze)
     * @return soubor s cestou na lokálním souborovém systému.
     * @throws IllegalStateException pokud nastavený blobDir neexistuje nebo není absolutní cesta
     */
    public @NonNull LocalFsFile getFile(@NonNull String relativeFilePath) throws IllegalStateException {
        var blobDir = getBlobDir();
        Path res = blobDir.resolve(pathFixup(relativeFilePath));
        return new LocalFsFile(res, blobDir);
    }

    /**
     * Vrací soubor na souborovém systému vzhledem k blobDiru.
     *
     * @param relativeFilePath relativní cesta k blobDiru (typicky z databáze)
     * @return soubor s cestou na lokálním souborovém systému.
     * @throws IllegalStateException pokud nastavený blobDir neexistuje nebo není absolutní cesta
     * @throws ItemNotFoundException pokud soubor s danou cestou neexistuje
     */
    public @NonNull LocalFilesystemService.LocalValidFsFile getValidatedFile(@NonNull String relativeFilePath) throws IllegalStateException, ItemNotFoundException {
        var blobDir = getBlobDir();
        Path res = blobDir.resolve(pathFixup(relativeFilePath));
        return new LocalValidFsFile(res, blobDir);
    }

    public @NonNull FsTmpFile getTmpFile(@NonNull String relativeFilePath) throws IllegalStateException, ItemNotFoundException {
        var blobDir = getBlobDir();
        Path res = blobDir.resolve(pathFixup(relativeFilePath));
        return new FsTmpFile(res, blobDir);
    }

    private static Path addUuidSubdirectories(Path blobDir, String uuid) {
        Path relPath = blobDir.getFileSystem().getPath(DATA_DIR, uuid.substring(0, 2), uuid.substring(2, 4), uuid.substring(4, 6));
        return blobDir.resolve(relPath);
    }

    /**
     * Uloží data z blobu a vrátí relativní cestu vzhledem k blobDiru.
     *
     * Zatim se (pravdepodobne docasne) pouziva v appserveru
     *
     * @param is   vstupní stream dat k uložení
     * @param uuid uuid identifikátor souboru
     * @return relativní cesta vzhledem k blobDiru s dopřednými lomítky.
     * @throws IllegalStateException      pokud nastavený blobDir neexistuje nebo není absolutní cesta
     * @throws FeatureNotEnabledException pokud je vypnutá modifikace blobů
     * @throws FileAlreadyExistsException v případě, že soubor s daným UUID už existuje
     * @throws IOException                v případě, že se nepodaří soubor uložit
     */
    @Deprecated
    public @NonNull String storeBlob(final @NonNull InputStream is, @NonNull final String uuid) throws IllegalStateException, FeatureNotEnabledException, IOException {
        return save(is, uuid).getBlobDirRelativeFilePath();
    }

    public @NonNull LocalValidFsFile save(final @NonNull InputStream is) throws IllegalStateException, FeatureNotEnabledException, IOException {
        return save(is, UuidGenerator.forRandomString());
    }

    public @NonNull LocalValidFsFile save(final @NonNull InputStream is, @NonNull final String uuid) throws IllegalStateException, FeatureNotEnabledException, IOException {
        blobDirModificationEnabledAsserter.run();

        Path blobDir = getBlobDir();

        // TODO: když se smaže soubor, co dělat s adresáři, které jsou prázdné a k ničemu?
        Path targetDirPath = addUuidSubdirectories(blobDir, uuid);
        Path absNewFilePath = targetDirPath.resolve(uuid);
        if (Files.exists(absNewFilePath)) {
            // Aplikáč nenechá zapsat nový soubor přes existující
            throw new FileAlreadyExistsException(absNewFilePath.toString());
        }

        // Nahrát soubor nejprve do dočasného adresáře
        Path tmpDir = mkTempDir(blobDir);
        Path tmpFilePath = tmpDir.resolve(uuid);
        try (OutputStream os = new BufferedOutputStream(Files.newOutputStream(tmpFilePath))) {
            IOUtils.copyLarge(is, os);

            Files.createDirectories(targetDirPath);
            Files.move(tmpFilePath, absNewFilePath);
        } catch (Exception e) {
            try {
                Files.delete(tmpFilePath);
            } catch (Exception ex) {
                // Ignorace
            }
            throw e;
        }

        return new LocalValidFsFile(absNewFilePath, blobDir);
    }

    /**
     * Získání dat z relativní cesty vzhledem k blobDiru.
     *
     * @param file  soubor z FS
     * @param range rozsah dat k načtení, pokud je potřeba vše, lze předat {@code null}
     * @return data soboru ke čtení.
     * @throws IOException v případě, že se nepodaří otevřít soubor ke čtení
     */
    public @NonNull InputStream getInputStream(@NonNull LocalFilesystemService.LocalValidFsFile file, @Nullable AbsoluteRangeBytes range) throws IOException {
        return new BufferedInputStream(PartialInputStream.wrap(Files.newInputStream(file.path), range));
    }

    public @NonNull InputStream getInputStream(@NonNull LocalFilesystemService.LocalValidFsFile file) throws IOException {
        return new BufferedInputStream(Files.newInputStream(file.path));
    }

    /**
     * @param file soubor z FS
     * @throws FeatureNotEnabledException pokud je vypnutá modifikace blobů
     * @throws NoSuchFileException        pokud soubor s danou cestou neexistuje
     * @throws IOException                pokud dojde k chybě při mazání (např. neexistuje soubor)
     */
    public void deleteBlob(@NonNull LocalFsFile file) throws FeatureNotEnabledException, NoSuchFileException, IOException {
        blobDirModificationEnabledAsserter.run();
        Files.delete(file.path);
    }

    /**
     * Vrací true, pokud se podařilo blob smazat (resp. true pokud soubor po
     * skončení funkce neexistuje (bez ohledu, zda existoval předtím)).
     * Ignoruje chyby IOException na rozdíl od {@link #deleteBlob(LocalFsFile)}.
     * <p>
     * cz.kpsys.kpwin2.services.FilesystemBlobStorageService#deleteBlob
     *
     * @param file soubor z FS
     * @return {@code true}, pokud soubor neexistuje, jinak {@code false}.
     * @throws FeatureNotEnabledException pokud je vypnutá modifikace blobů
     */
    public boolean deleteBlobQuietly(@NonNull LocalFsFile file) throws FeatureNotEnabledException {
        try {
            deleteBlob(file);
            return true;
        } catch (NoSuchFileException e) {
            return true;
        } catch (IOException e) {
            return !Files.exists(file.path);
        }
    }

    public void moveToToDeleteDir(@NonNull LocalValidFsFile file) throws IOException, FeatureNotEnabledException {
        blobDirModificationEnabledAsserter.run();

        Path toDeleteDir = getBlobDir().resolve(TO_DELETE_DIR);
        Files.createDirectories(toDeleteDir);

        Path targetPath = toDeleteDir.resolve(getBlobDir().resolve(DATA_DIR).relativize(file.path));
        Files.createDirectories(targetPath.getParent());
        Files.move(file.path, targetPath);
    }

    public void cleanupEmptyDirs() throws IOException {
        Path rootDir = getBlobDir().resolve(DATA_DIR);
        try (Stream<Path> paths = Files.walk(rootDir)) {
            List<Path> dirs = paths
                    .filter(Files::isDirectory)
                    .sorted(Comparator.reverseOrder())
                    .toList();

            for (Path dir : dirs) {
                try (Stream<Path> children = Files.list(dir)) {
                    if (children.findAny().isEmpty() && !dir.equals(rootDir) && dir.startsWith(rootDir)) {
                        Files.delete(dir);
                    }
                }
            }
        }
    }

    public void deleteFilesFromToDeleteDir() throws IOException {
        Path toDeleteDir = getBlobDir().resolve(TO_DELETE_DIR);
        if (!Files.exists(toDeleteDir) || !Files.isDirectory(toDeleteDir)) {
            throw new FileNotFoundException("No to_delete directory found: " + toDeleteDir);
        }

        try (Stream<Path> paths = Files.walk(toDeleteDir)) {
            paths.sorted(Comparator.reverseOrder())
                    .filter(path -> !path.equals(toDeleteDir))
                    .forEach(path -> {
                        Assert.state(path.startsWith(toDeleteDir), () -> "File path is not within the blob directory: " + path);
                        try {
                            Files.deleteIfExists(path);
                            log.info("Deleted: {}", path);
                        } catch (IOException e) {
                            throw new RuntimeException("Failed to delete: " + path, e);
                        }
                    });
        }

    }

    /**
     * Vraci velikost BLOBu
     *
     * @param file soubor z FS
     * @return velikost souboru.
     * @throws IOException v případě chyby nebo neexistujícího souboru
     */
    public long blobSize(@NonNull LocalFilesystemService.LocalValidFsFile file) throws IOException {
        return Files.size(file.path);
    }

    /// Creates temporary file in blobDir space.
    ///
    /// @param id uuid of blobDir file. String must be exactly 36 chars long.
    public @NonNull FsTmpFile createTmpFile(@NonNull UUID id, long size, @NonNull String suffix) throws IOException {
        blobDirModificationEnabledAsserter.run();

        var uuid = id.toString();
        Assert.isTrue(uuid.length() == UUID_LENGTH, () -> "We always suppose that UUID has 36 characters! %s".formatted(uuid));
        Path blobDir = getBlobDir();
        Path targetDirPath = addUuidSubdirectories(blobDir, uuid);
        Path absNewFilePath = targetDirPath.resolve(uuid);
        if (Files.exists(absNewFilePath)) {
            // Nenecháme potenciálně přepsat existující soubor
            throw new FileAlreadyExistsException(absNewFilePath.toString());
        }

        Path tmpDir = mkTempDir(blobDir);
        Path tmpFilePath = tmpDir.resolve(id + suffix);

        try (var file = Files.newByteChannel(tmpFilePath, Set.of(StandardOpenOption.CREATE_NEW, StandardOpenOption.WRITE, StandardOpenOption.SPARSE))) {
            if (size > 0) {
                // Expand file to requested size (maybe not necessary, but should store size information)
                file.position(size - 1).write(ByteBuffer.wrap(new byte[]{0}));
            }
        }

        return new FsTmpFile(tmpFilePath, blobDir);
    }

    /// Finds temporary file in blobDir space based on its UUID.
    ///
    /// @param uuid of blobDir file. String must be exactly 36 chars long.
    ///
    /// @throws IllegalArgumentException if uuid string is not exactly 36 chars long.
    public Optional<FsTmpFile> findTmpFile(@NonNull String uuid) throws IOException {
        // TODO: What if there is long upload and someone disables blob dir?
        Assert.isTrue(uuid.length() == UUID_LENGTH, () -> "We always suppose that UUID has 36 characters! %s".formatted(uuid));
        var blobDir = getBlobDir();
        Path tmpDir = resolveTempDir(blobDir);
        try (var files = Files.list(tmpDir)) {
            var list = files.toList();
            return list.stream()
                    .filter(f -> f.getFileName().toString().startsWith(uuid))
                    .findFirst()
                    .map(f -> new FsTmpFile(f, blobDir));
        }
    }

    /// Finds temporary file in blobDir space based on its UUID.
    ///
    /// @param uuid of blobDir file. String must be exactly 36 chars long.
    ///
    /// @throws IllegalArgumentException if uuid string is not exactly 36 chars long.
    public void cleanUpTmpFile(@NonNull String uuid) {
        try {
            findTmpFile(uuid)
                    .map(TmpFile::getPath)
                    .ifPresent(f -> {
                        try {
                            Files.deleteIfExists(f);
                        } catch (IOException e) {
                            // ignore
                        }
                    });
        } catch (IOException e) {
            // Ignore
        }
    }

    private static Path resolveTempDir(Path blobDir) {
        return blobDir.resolve(TEMP_DIR);
    }

    private static Path mkTempDir(Path blobDir) throws IOException {
        return Files.createDirectories(resolveTempDir(blobDir));
    }


    public record BlobDir(
            @NonNull Path path
    ) {
        public BlobDir {
            Assert.state(path.isAbsolute(), "Ini file.blobDir must be absolute path, but is %s".formatted(path));
            Assert.state(Files.isDirectory(path), "Directory specified in ini file.blobDir must exist and must be a directory!");
        }

        public BlobDir(String path) {
            this(Path.of(path));
        }
    }

    @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
    @FieldDefaults(level = AccessLevel.PROTECTED, makeFinal = true)
    public sealed class LocalFsFile implements FsFile permits LocalValidFsFile, FsTmpFile {

        @NonNull Path path;
        @NonNull Path blobDir;

        private LocalValidFsFile validate() throws ItemNotFoundException {
            return new LocalValidFsFile(path, blobDir);
        }

        @Override
        public String getBlobDirRelativeFilePath() {
            return pathFixup(blobDir.relativize(path).toString());
        }

        @Override
        public long size() throws IOException {
            return Files.size(path);
        }

        @Override
        public String getFilename() {
            return path.getFileName().toString();
        }
    }

    public final class LocalValidFsFile extends LocalFsFile {

        public static LocalValidFsFile from(LocalFsFile file) throws ItemNotFoundException {
            return file.validate();
        }

        private static Path validate(Path path) {
            if (!Files.exists(path)) {
                throw new ItemNotFoundException(File.class, path.toString());
            }
            return path;
        }

        private LocalValidFsFile(@NonNull Path path, @NonNull Path blobDir) throws ItemNotFoundException {
            super(validate(path), blobDir);
        }

        public InputStream getInputStream(@Nullable AbsoluteRangeBytes range) throws IOException {
            return LocalFilesystemService.this.getInputStream(this, range);
        }

        public InputStream getInputStream() throws IOException {
            return LocalFilesystemService.this.getInputStream(this);
        }

        public long size() throws IOException {
            return blobSize(this);
        }

        /// Získá UUID jméno v blob storu
        public String getFilename() {
            return path.getFileName().toString();
        }

        ///  Získání relativní cesty vzhledem k blob storu (stejný formát jako v DB)
        public String getBlobDirRelativeFilePath() {
            return pathFixup(blobDir.relativize(path).toString());
        }
    }

    public final class FsTmpFile extends LocalFsFile implements TmpFile {

        private FsTmpFile(@NonNull Path path, @NonNull Path blobDir) {
            super(path, blobDir);
        }

        public Path getPath() {
            return path;
        }

        public String getFilename() {
            return path.getFileName().toString();
        }

        public String getUuidPart() {
            return getFilename().substring(0, UUID_LENGTH);
        }

        public String getSuffixPart() {
            return getFilename().substring(UUID_LENGTH);
        }

        public long getSize() throws IOException {
            return Files.size(path);
        }

        public LocalValidFsFile persistIntoBlobDir() throws IOException {
            String uuid = getUuidPart();
            Path targetDirPath = addUuidSubdirectories(blobDir, uuid);
            Path absNewFilePath = targetDirPath.resolve(uuid);
            Files.createDirectories(targetDirPath);
            return new LocalValidFsFile(Files.move(path, absNewFilePath), blobDir);
        }

    }

}
