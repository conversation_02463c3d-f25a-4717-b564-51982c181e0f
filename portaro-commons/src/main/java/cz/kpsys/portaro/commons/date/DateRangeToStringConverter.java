package cz.kpsys.portaro.commons.date;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DateRangeToStringConverter implements Converter<@NonNull DateRange, @NonNull String> {

    public static DateRangeToStringConverter ofInternalFormat() {
        return ofPostgresFormat();
    }

    public static DateRangeToStringConverter ofPostgresFormat() {
        return new DateRangeToStringConverter();
    }

    @Override
    public @NonNull String convert(@NonNull DateRange range) {
        if (range.empty()) {
            return StringToDateRangeConverter.EMPTY_RANGE;
        }
        char leftBracket = range.lowerInclusive() ? StringToDateRangeConverter.LOWER_INCLUSIVE_SYMBOL : StringToDateRangeConverter.LOWER_EXCLUSIVE_SYMBOL;
        char rightBracket = range.upperInclusive() ? StringToDateRangeConverter.UPPER_INCLUSIVE_SYMBOL : StringToDateRangeConverter.UPPER_EXCLUSIVE_SYMBOL;
        String lo = range.lower() == null ? StringToDateRangeConverter.NEGATIVE_INFINITY : range.lower().toString();
        String hi = range.upper() == null ? StringToDateRangeConverter.POSITIVE_INFINITY : range.upper().toString();
        return leftBracket + lo + ',' + hi + rightBracket;
    }

}