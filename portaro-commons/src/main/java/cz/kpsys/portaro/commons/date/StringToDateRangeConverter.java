package cz.kpsys.portaro.commons.date;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.time.LocalDate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class StringToDateRangeConverter implements Converter<@NonNull String, @NonNull DateRange> {

    public static final String EMPTY_RANGE = "empty";
    public static final String NEGATIVE_INFINITY = "-infinity";
    public static final String POSITIVE_INFINITY = "infinity";
    public static final char LOWER_INCLUSIVE_SYMBOL = '[';
    public static final char UPPER_INCLUSIVE_SYMBOL = ']';
    public static final char LOWER_EXCLUSIVE_SYMBOL = '(';
    public static final char UPPER_EXCLUSIVE_SYMBOL = ')';

    public static StringToDateRangeConverter ofInternalFormat() {
        return ofPostgresFormat();
    }

    public static StringToDateRangeConverter ofPostgresFormat() {
        return new StringToDateRangeConverter();
    }

    @Override
    public @NonNull DateRange convert(@NonNull String txt) {
        if (txt.equals(EMPTY_RANGE)) {
            return DateRange.ofEmpty();
        }

        char l = txt.charAt(0);
        char r = txt.charAt(txt.length() - 1);
        boolean li = l == LOWER_INCLUSIVE_SYMBOL;
        boolean ui = r == UPPER_INCLUSIVE_SYMBOL;

        String inside = txt.substring(1, txt.length() - 1);
        int comma = inside.indexOf(',');
        String left = inside.substring(0, comma).trim();
        String right = inside.substring(comma + 1).trim();

        LocalDate lo = parseBound(left);
        LocalDate hi = parseBound(right);

        return DateRange.of(lo, hi, li, ui);
    }

    private static LocalDate parseBound(String s) {
        if (s.equals(NEGATIVE_INFINITY) || s.isEmpty()) {
            return null;
        }
        if (s.equals(POSITIVE_INFINITY)) {
            return null;
        }
        return LocalDate.parse(s); // yyyy-MM-dd
    }

}
