package cz.kpsys.portaro.commons.util;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.slf4j.Logger;
import org.slf4j.event.Level;

import java.time.Duration;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class TimeMeter {
    
    long startTime;
    
    public static TimeMeter start() {
        return new TimeMeter(System.nanoTime());
    }
    
    private long elapsedNanos() {
        return System.nanoTime() - startTime;
    }

    public Duration elapsedDuration() {
        return Duration.ofNanos(elapsedNanos());
    }

    public boolean isGreaterThan(Duration duration) {
        Duration minused = elapsedDuration().minus(duration);
        return !minused.isZero() && !minused.isNegative();
    }

    public String elapsedTimeString() {
        return DateUtils.toHumanReadableDuration(elapsedDuration());
    }

    /**
     * resetuje stopky, ale pokracuje v mereni.
     * Tzn. pouze se vynuluje cas.
     */
    public void reset() {
        this.startTime = System.nanoTime();
    }

    public String toCurrentRateString(long itemsProcessed) {
        double rowsPerNs = itemsProcessed / (double) elapsedNanos();
        long roundedRowsPerSecond = Math.round(rowsPerNs * TimeUnit.SECONDS.toNanos(1));
        return roundedRowsPerSecond + " items/s";
    }

    public static void measureAndLog(@NonNull Runnable fn, @NonNull Logger log, @NonNull Supplier<@NonNull String> actionDescription, @NonNull Duration infoThreshold, @NonNull Duration warnThreshold) {
        Supplier<Object> supplier = () -> {
            fn.run();
            return null;
        };
        measureAndLog(supplier, log, actionDescription, infoThreshold, warnThreshold);
    }

    public static <RES> RES measureAndLog(@NonNull Supplier<RES> fn, @NonNull Logger log, @NonNull Supplier<@NonNull String> actionDescription, @NonNull Duration infoThreshold, @NonNull Duration warnThreshold) {
        TimeMeter tm = TimeMeter.start();

        RES res = fn.get();

        Level logLevel = getLogLevel(infoThreshold, warnThreshold, tm);
        if (log.isEnabledForLevel(logLevel)) {
            log.atLevel(logLevel).log("Action took {}: {}", tm.elapsedTimeString(), actionDescription.get());
        }

        return res;
    }

    public static <RES> RES measureAndLog(@NonNull Supplier<RES> fn, @NonNull Logger log, @NonNull Supplier<@NonNull String> actionDescription, @NonNull Duration warnThreshold) {
        return measureAndLog(fn, log, actionDescription, Duration.ZERO, warnThreshold);
    }

    private static @NonNull Level getLogLevel(@NonNull Duration infoThreshold, @NonNull Duration warnThreshold, TimeMeter tm) {
        if (tm.isGreaterThan(warnThreshold)) {
            return Level.WARN;
        }
        if (tm.isGreaterThan(infoThreshold)) {
            return Level.INFO;
        }
        return Level.DEBUG;
    }

}
