package cz.kpsys.portaro.commons.localization;

import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.date.DateRangeToStringConverter;
import lombok.NonNull;

import java.util.Locale;
import java.util.Optional;

public record DateRangeText(

        @NonNull
        DateRange value

) implements Text {

    public static DateRangeText create(@NonNull DateRange value) {
        return new DateRangeText(value);
    }

    @Override
    public String toString() {
        return "DateRangeText %s".formatted(value);
    }

    @Override
    public boolean isEmpty() {
        return false;
    }

    @Override
    public <CTX> Optional<String> tryLocalize(Translator<CTX> translator, CTX ctx, Locale locale) {
        String formattedDate = DateRangeToStringConverter.ofInternalFormat().convert(value);
        return Optional.of(formattedDate);
    }

}
