package cz.kpsys.portaro.commons.json;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.io.IOException;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ToStringConvertingJsonSerializer<S> extends StdSerializer<S> {

    @NonNull Converter<S, ? extends String> converter;

    public ToStringConvertingJsonSerializer(@NonNull Class<S> clazz, @NonNull Converter<S, ? extends String> converter) {
        super(clazz);
        this.converter = converter;
    }

    @Override
    public void serialize(S value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeString(this.converter.convert(value));
    }
}
