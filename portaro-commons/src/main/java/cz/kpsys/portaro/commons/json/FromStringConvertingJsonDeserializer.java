package cz.kpsys.portaro.commons.json;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.deser.std.StdScalarDeserializer;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.io.IOException;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class FromStringConvertingJsonDeserializer<E> extends StdScalarDeserializer<E> {

    @NonNull Converter<String, E> converter;
    
    public FromStringConvertingJsonDeserializer(Class<?> vc, @NonNull Converter<String, E> converter) {
        super(vc);
        this.converter = converter;
    }
    
    @Override
    public E deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
        String id = null;
        if (jp.getCurrentToken() == JsonToken.START_OBJECT) { //hodnota neni jako id ale jako cely objekt -> najdeme v objektu id
            ObjectNode node = jp.getCodec().readTree(jp);
            JsonNode idNode = node.path("id");
            if (idNode != null) {
                id = idNode.isMissingNode() || idNode.isNull() ? null : idNode.asText();
            }
        } else { //standardni id jako string
            id = jp.getValueAsString();
        }
        return converter.convert(id);
    }
}
