package cz.kpsys.portaro.commons.date;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import cz.kpsys.portaro.commons.util.DateUtils;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import lombok.NonNull;
import lombok.With;
import org.jspecify.annotations.Nullable;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Objects;
import java.util.stream.Stream;

@JsonIgnoreProperties
public record DateRange(

        @JsonProperty("fromDate")
        @With
        @Nullable
        Instant inclusiveFromDate,

        @JsonProperty("toDate")
        @With
        @Nullable
        Instant exclusiveToDate,

        @JsonIgnore
        boolean empty

) {

    @JsonCreator
    public DateRange(@JsonProperty("fromDate") Instant inclusiveFromDate,
                     @JsonProperty("toDate") Instant exclusiveToDate) {
        this(inclusiveFromDate, exclusiveToDate, false);
    }

    public DateRange(@NonNull LocalDateTime inclusiveFromDate,
                     @NonNull LocalDateTime exclusiveToDate,
                     @NonNull ZoneId zoneId) {
        this(inclusiveFromDate.atZone(zoneId).toInstant(), exclusiveToDate.atZone(zoneId).toInstant());
    }

    public DateRange(@NonNull ZonedDateTime inclusiveFromDate,
                     @NonNull ZonedDateTime exclusiveToDate) {
        this(inclusiveFromDate.toInstant(), exclusiveToDate.toInstant());
    }

    public DateRange {
        if (inclusiveFromDate != null && exclusiveToDate != null && !inclusiveFromDate.isBefore(exclusiveToDate)) {
            inclusiveFromDate = null;
            exclusiveToDate = null;
            empty = true;
        }
    }

    public static DateRange ofEmpty() {
        return new DateRange(null, null, true);
    }

    public static DateRange ofDay(@NonNull LocalDate date, @NonNull ZoneId timeZoneId) {
        return ofStartOfDays(date, date.plusDays(1), timeZoneId);
    }

    public static DateRange ofStartOfDays(LocalDate inclusiveFromDate, LocalDate exclusiveToDate, ZoneId zoneId) {
        Instant inclusiveFromDateAtStartOfDay = inclusiveFromDate.atStartOfDay(zoneId).toInstant();
        Instant exclusiveToDateAtStartOfDay = exclusiveToDate.atStartOfDay(zoneId).toInstant();
        return new DateRange(inclusiveFromDateAtStartOfDay, exclusiveToDateAtStartOfDay);
    }

    public Instant fromDate() {
        return inclusiveFromDate;
    }

    public Instant toDate() {
        return exclusiveToDate;
    }

    public static DateRange ofMonth(YearMonth yearMonth, ZoneId timeZone) {
        ZonedDateTime zonedFromDate = yearMonth.atDay(1).atStartOfDay(timeZone);
        ZonedDateTime zonedToDate = zonedFromDate.plusMonths(1);
        return new DateRange(zonedFromDate, zonedToDate);
    }

    public static DateRange ofMonth(Instant fromDate, boolean truncateDate, ZoneId timeZone) {
        ZonedDateTime zonedFromDate = fromDate.atZone(timeZone);
        if (truncateDate) {
            zonedFromDate = zonedFromDate.with(TemporalAdjusters.firstDayOfMonth()).truncatedTo(ChronoUnit.DAYS);
        }
        ZonedDateTime zonedToDate = zonedFromDate.plusMonths(1);
        return new DateRange(zonedFromDate, zonedToDate);
    }

    public static DateRange ofYear(Instant fromDate, boolean truncateDate, ZoneId timeZone) {
        ZonedDateTime zonedFromDate = fromDate.atZone(timeZone);
        if (truncateDate) {
            zonedFromDate = zonedFromDate.with(TemporalAdjusters.firstDayOfYear()).truncatedTo(ChronoUnit.DAYS);
        }
        ZonedDateTime zonedToDate = zonedFromDate.plusYears(1);
        return new DateRange(zonedFromDate, zonedToDate);
    }

    public LocalTimeRange toLocalTimeRange(ZoneId timeZone) {
        Objects.requireNonNull(inclusiveFromDate, "Cannot convert date range to local time range, because inclusiveFromDate is null");
        Objects.requireNonNull(exclusiveToDate, "Cannot convert date range to local time range, because exclusiveToDate is null");
        LocalTime fromLocalTime = inclusiveFromDate.atZone(timeZone).toLocalTime();
        LocalTime toLocalTime = exclusiveToDate.atZone(timeZone).toLocalTime();
        return new LocalTimeRange(fromLocalTime, toLocalTime);
    }

    public boolean touches(DateRange other) {
        if (empty || other.empty) {
            return false;
        }
        return (inclusiveFromDate == null || other.exclusiveToDate == null || inclusiveFromDate.isBefore(other.exclusiveToDate)) &&
               (exclusiveToDate == null || other.inclusiveFromDate == null || exclusiveToDate.isAfter(other.inclusiveFromDate));
    }

    public Stream<LocalDate> streamLocalDates(@NonNull ZoneId zoneId) {
        LocalDate from = DateUtils.instantToLocalDate(inclusiveFromDate, zoneId);
        LocalDate exclusiveTo = DateUtils.instantToLocalDate(exclusiveToDate, zoneId);
        return from.datesUntil(exclusiveTo);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DateRange dateRange)) {
            return false;
        }
        return empty == dateRange.empty && Objects.equals(inclusiveFromDate, dateRange.inclusiveFromDate) && Objects.equals(exclusiveToDate, dateRange.exclusiveToDate);
    }

    @Override
    public int hashCode() {
        int result = Objects.hashCode(inclusiveFromDate);
        result = 31 * result + Objects.hashCode(exclusiveToDate);
        return result;
    }

    @Override
    public String toString() {
        if (empty) {
            return "empty";
        }
        return "[" + ObjectUtil.firstNotNull(inclusiveFromDate, "-inf") + ", " + ObjectUtil.firstNotNull(exclusiveToDate, "inf") + ")";
    }

}
