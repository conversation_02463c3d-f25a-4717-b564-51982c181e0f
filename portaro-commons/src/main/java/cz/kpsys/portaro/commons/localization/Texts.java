package cz.kpsys.portaro.commons.localization;

import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.date.DatetimeRange;
import cz.kpsys.portaro.commons.util.DateUtils;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.web.Link;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;

@Slf4j
public class Texts {

    public static Text ofMessageCodedWithLocalizedArgs(String messageCode, Text... arguments) {
        return LocalizedArgsText.create(messageCode, arguments);
    }

    public static Text ofDefaulted(Text primary, Text dflt) {
        return DefaultedText.create(primary, dflt);
    }

    /**
     * Creates text from format "{message.code}:param1,param2,..."
     */
    public static Text ofCurlyBracesFormat(String curlyBracedString) {
        if (curlyBracedString.startsWith("{")) {
            Assert.isTrue(curlyBracedString.endsWith("}"), "Curly-braces formatted string does not contain '}'");
            String messageCodeAndArgs = curlyBracedString.substring(1, curlyBracedString.length() - 1);
            String[] split = messageCodeAndArgs.split(":");
            if (split.length == 1) {
                return ofMessageCoded(split[0]);
            }
            String[] args = split[1].split(",");
            return ofArgumentedMessageCoded(split[0], (Object[]) args);
        }
        return ofNative(curlyBracedString);
    }

    public static Text ofMessageCodedOrNative(String messageCodeOrNative) {
        return ofMessageCodedOrNative(messageCodeOrNative, messageCodeOrNative);
    }

    public static Text ofMessageCodedOrNative(String messageCode, String natif) {
        return ofDefaulted(ofMessageCoded(messageCode), ofNative(natif));
    }

    public static Text ofMessageCodedOrNativeOrEmptyNative(String messageCode, String natif) {
        return ofDefaulted(ofMessageCoded(messageCode), ofNativeOrEmpty(natif));
    }

    public static Text ofColumnMessageCoded(String table, String column, Object... ids) {
        return ofMessageCoded(createMessageCodeWithArrayedIds(table, column, ids));
    }

    public static Text ofColumnMessageCodedOrNative(String nativeString, String table, String column, Object... ids) {
        return ofMessageCodedOrNativeOrEmptyNative(createMessageCodeWithArrayedIds(table, column, ids), nativeString);
    }

    public static Text ofColumnMessageCodedOrNativeOrEmptyNative(String nativeString, String table, String column, Object... ids) {
        return ofDefaulted(ofMessageCoded(createMessageCodeWithArrayedIds(table, column, ids)), StringUtil.hasLength(nativeString) ? ofNative(nativeString) : ofEmpty());
    }

    public static Text ofNativeOrEmpty(String nativeString) {
        return ofNativeOrEmptyDefault(nativeString, Texts.ofEmpty());
    }

    public static Text ofNativeOrEmptyDefault(@Nullable String nullableEmptyableNativeString, @NonNull Text dflt) {
        if (StringUtil.isNullOrEmpty(nullableEmptyableNativeString)) {
            return dflt;
        }
        return NativeText.create(nullableEmptyableNativeString);
    }

    public static Text ofNative(@Nullable String nativeString) {
        if (nativeString == null || nativeString.isEmpty()) {
            log.warn("Calling Texts.ofNative(<{} string>), use rather Texts.ofEmpty() or Texts.ofNativeOrEmpty()", nativeString == null ? "null" : "empty", new Exception("Exception for generating stacktrace"));
            return Texts.ofEmpty();
        }
        return NativeText.create(nativeString);
    }

    public static Text ofNativeObject(@Nullable Number nativeNumber) {
        return Texts.ofNative(String.valueOf(nativeNumber));
    }

    public static StaticallyLocalizedText ofStaticallyLocalized(@NotEmpty @NonNull String englishTranslation) {
        return StaticallyLocalizedText.ofEnglish(englishTranslation);
    }

    public static Text ofNumber(@NonNull Number number) {
        return NativeText.create(String.valueOf(number));
    }

    // TODO: při malých hodnotách (1h 1m) mate v docházce tím, že zahodí údaj o 1m a vypíše pouze 1h
    public static Text ofDuration(@NonNull Duration duration) {
        return ofNative(DateUtils.toHumanReadableDuration(duration));
    }

    public static Text ofBoolean(boolean bool) {
        return bool ? ofMessageCoded("commons.ANO")
                    : ofMessageCoded("commons.NE");
    }

    public static Text ofMessageCoded(String messageCode) {
        return MessageCodedText.create(messageCode);
    }

    public static Text ofObjectPropertyMessageCode(String objectName, String fieldName) {
        return MessageCodedText.create(objectName + "." + fieldName);
    }

    public static Text ofEmpty() {
        return new EmptyText();
    }

    public static Text ofDateWithoutTime(@NonNull Instant date) {
        return DateText.createWithoutTime(date);
    }

    public static Text ofDateWithoutTime(@NonNull LocalDate date) {
        return LocalDateText.createWithoutTime(date);
    }

    public static Text ofDateWithTime(@NonNull Instant date) {
        return DateText.createWithTime(date);
    }

    public static Text ofDateRange(@NonNull DateRange dateRange) {
        return DateRangeText.create(dateRange);
    }

    public static Text ofDatetimeRange(@NonNull DatetimeRange datetimeRange) {
        return DatetimeRangeText.create(datetimeRange);
    }

    public static Text ofLink(@NonNull Link link) {
        return LinkText.create(link);
    }

    public static Text ofArgumentedMessageCoded(@NonNull String messageCode, @NonNull Object... args) {
        return ArgumentedText.create(messageCode, args);
    }

    /**
     * Vytvori message code podle nazvu databazove tabulky, sloupce a idecka. <br/>
     * Jednotliva idecka zprava otrimuje, mezery nahradi podtrzitky a spoji teckovou notaci
     */
    public static String createColumnMessageCode(String table, String column, Object... ids) {
        return createMessageCodeWithArrayedIds(table, column, ids);
    }

    private static String createMessageCodeWithArrayedIds(String table, String column, Object[] ids) {
        String delim = ".";
        String spaceReplacement = "_";
        StringBuilder idString = new StringBuilder();
        for (Object id : ids) {
            if (!idString.isEmpty()) {
                idString.append(delim);
            }
            idString.append(id == null ? "" : StringUtil.rtrimOrLetNull(id.toString()).replace(" ", spaceReplacement));
        }
        return table + delim + column + delim + idString;
    }
}
