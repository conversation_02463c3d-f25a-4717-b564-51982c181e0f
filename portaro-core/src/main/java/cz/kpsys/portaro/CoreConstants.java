package cz.kpsys.portaro;

import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.date.DatetimeRange;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.department.Department;
import lombok.NonNull;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

import static cz.kpsys.portaro.datatype.Datatype.scalar;

public class CoreConstants {

    public static final Integer ROOT_DEPARTMENT = 0;
    public static final String CZECH_TIME_TONE_ID_VALUE = "Europe/Prague";
    public static final ZoneId CZECH_TIME_ZONE_ID = ZoneId.of(CZECH_TIME_TONE_ID_VALUE);
    public static final TimeZone CZECH_TIME_ZONE = TimeZone.getTimeZone(CoreConstants.CZECH_TIME_ZONE_ID);
    public static final Instant MIN_INSTANT = Instant.parse("0000-01-01T00:00:00Z");

    public static class Locales {
        public static final Locale CS = new Locale("cs");
        public static final Locale SK = new Locale("sk");
        public static final Locale EN = new Locale("en");
        public static final Locale DE = new Locale("de");
        public static final Locale FR = new Locale("fr");
        public static final Locale ES = new Locale("es");
        public static final Locale IT = new Locale("it");
        public static final Locale HU = new Locale("hu");
        public static final Locale PL = new Locale("pl");
        public static final Locale BG = new Locale("bg");
        public static final Locale XX = new Locale("xx");
    }

    public static class Web {
        public static final String TRACE_ID_HTTP_HEADER_NAME = "X-Verbis-Trace-Id";

        public static class Defaults {
            public static final String HEADER_TEXT_COLOR = "#FFF";
            public static final String HEADER_BACKGROUND_COLOR = "#FF6633";
            public static final String HEADER_LINK_COLOR = "#FFE6D6";
            public static final String MAIN_MENU_BACKGROUND_COLOR = "#FF6633";
            public static final String MAIN_MENU_TEXT_COLOR = "#FFF";
            public static final String GLOBAL_SEARCH_BUTTON = "#337AB7";
            public static final String TABLE_HEADER_ACCENT_COLOR = "#FF5712";
            public static final String DEFAULT_THEME = "light";
            public static final String SELECTED_TAB_HIGHLIGHT_COLOR = "#337ab7";
        }

        public static class Cookies {
            public static final String THEME_COOKIE_NAME = "portaroTheme";
        }
    }

    public static class Resource {
        public static final String CLASSPATH_FILE_PROTOCOL = "classpath";
        public static final String CLASSPATH_FILE_PREFIX = CLASSPATH_FILE_PROTOCOL + ":";
        public static final String CUSTOM_FILE_PROTOCOL = "custom";
        public static final String CUSTOM_FILE_PREFIX = CUSTOM_FILE_PROTOCOL + ":";
        public static final String DB_FILE_PROTOCOL = "db";
        public static final String DB_FILE_PREFIX = DB_FILE_PROTOCOL + ":";
        public static final String FILE_PROTOCOL = "file";
        public static final String FILE_PREFIX = FILE_PROTOCOL + ":";
    }

    public static class Datatype {
        public static final String DATATYPE_TEXT = "TEXT";
        public static final String DATATYPE_PREFIX_PHRASE = "FRZ"; //FRZxxx = vyber z DEF_FRAZE
        public static final String DATATYPE_PREFIX_VAL = "VAL"; //VALxxx = validace z validačního slovníku, bez možnosti zadání hodnoty která v něm není
        public static final String DATATYPE_PREFIX_EDV = "EDV"; //EDVxxx = validace z validačního slovníku, s možnosti zadání libovolné hodnoty
        public static final String DATATYPE_PREFIX_INIVAL = "INIVAL"; //INIVALxxx = vyber z INI_VAL
        public static final String DATATYPE_PREFIX_RECORD = "RECORD_";
        public static final String DATATYPE_PREFIX_INDICATOR = "Indicator_";

        public static final String BOOLEAN_NAME = "BOOLEAN";
        public static final String NUMBER_NAME = "NUMBER";
        public static final String NUMBER_DECIMAL_2_NAME = "NUMBER_DECIMAL_2";
        public static final String NUMBER_DECIMAL_4_NAME = "NUMBER_DECIMAL_4";
        public static final String NUMBER_DECIMAL_6_NAME = "NUMBER_DECIMAL_6";
        public static final String DATE_NAME = "DATE";
        public static final String DATETIME_NAME = "DATETIME";
        public static final String DATE_RANGE_NAME = "DATE_RANGE";
        public static final String DATETIME_RANGE_NAME = "DATETIME_RANGE";
        public static final String MONEY_NAME = "MONEY";

        public static final int NUMBER_SCALE = 0;
        public static final int NUMBER_DECIMAL_2_SCALE = 2;
        public static final int NUMBER_DECIMAL_4_SCALE = 4;
        public static final int NUMBER_DECIMAL_6_SCALE = 6;
        public static final int MONEY_SCALE = NUMBER_DECIMAL_4_SCALE;

        public static final ScalarDatatype TEXT = scalar(DATATYPE_TEXT, String.class);
        public static final ScalarDatatype BOOLEAN = scalar(BOOLEAN_NAME, Boolean.class);
        public static final ScalarDatatype NUMBER = scalar(NUMBER_NAME, Integer.class);
        public static final ScalarDatatype NUMBER_DECIMAL_2 = scalar(NUMBER_DECIMAL_2_NAME, BigDecimal.class);
        public static final ScalarDatatype NUMBER_DECIMAL_4 = scalar(NUMBER_DECIMAL_4_NAME, BigDecimal.class);
        public static final ScalarDatatype NUMBER_DECIMAL_6 = scalar(NUMBER_DECIMAL_6_NAME, BigDecimal.class);
        public static final ScalarDatatype UUID = scalar("UUID", UUID.class);
        public static final ScalarDatatype URL = scalar("URL", String.class);
        public static final ScalarDatatype BAR_CODE = scalar("BAR_CODE", String.class);
        public static final ScalarDatatype DATE = scalar(DATE_NAME, LocalDate.class);
        public static final ScalarDatatype DATETIME = scalar(DATETIME_NAME, Instant.class);
        public static final ScalarDatatype DATE_RANGE = scalar(DATE_RANGE_NAME, DateRange.class);
        public static final ScalarDatatype DATETIME_RANGE = scalar(DATETIME_RANGE_NAME, DatetimeRange.class);
        public static final ScalarDatatype RANGE = scalar("RANGE");
        public static final ScalarDatatype TIME_RANGE_MINUTES = scalar("TIME_RANGE_MINUTES");
        public static final ScalarDatatype RANGE_YEAR = scalar("ROZSAH_ROK");
        public static final ScalarDatatype YEAR = scalar("YEAR", Integer.class);
        public static final ScalarDatatype VELOCITY_TEMPLATE = scalar("VELOCITY_TEMPLATE", String.class);
        public static final ScalarDatatype HTML = scalar("HTML", String.class);
        public static final ScalarDatatype DEPARTMENT = scalar("PUJCOVNA", Department.class, Integer.class);
        public static final ScalarDatatype DATABASE = scalar("DATABAZE");
        public static final ScalarDatatype DATASOURCE = scalar("DATASOURCE");
        public static final ScalarDatatype VALHOD = scalar("VALHOD", String.class);
        public static final ScalarDatatype FACET_VALUE = scalar("FACET_VALUE");
        public static final ScalarDatatype USER = scalar("USER");
        public static final ScalarDatatype TIME_GRANULARITY = scalar("TIME_GRANULARITY");
        public static final ScalarDatatype SEARCH_FIELD = scalar("SEARCH_FIELD");
        public static final ScalarDatatype LOCAL_DATASET = scalar("LOCAL_DATASET");

        private static final Map<ScalarDatatype, Integer> NUMERIC_DATATYPE_SCALES = Map.of(
                NUMBER, 0,
                NUMBER_DECIMAL_2, 2,
                NUMBER_DECIMAL_4, 4,
                NUMBER_DECIMAL_6, 6
        );
        public static final Set<ScalarDatatype> NUMERIC_DATATYPES = NUMERIC_DATATYPE_SCALES.keySet();
        public static final Comparator<ScalarDatatype> NUMERIC_DATATYPES_BY_SCALE_SORTER = Comparator.comparing(Datatype::datatypeToScale);

        public static @NonNull Integer datatypeToScale(ScalarDatatype datatype) {
            Integer scale = NUMERIC_DATATYPE_SCALES.get(datatype);
            return Objects.requireNonNull(scale, () -> "Only numeric datatypes are supported, but there is %s (numerics are %s)".formatted(datatype, NUMERIC_DATATYPES));
        }

        public static @NonNull ScalarDatatype scaleToDatatype(@NonNull Integer scale) {
            if (scale == NUMBER_SCALE) {
                return NUMBER;
            }
            if (scale <= NUMBER_DECIMAL_2_SCALE) {
                return NUMBER_DECIMAL_2;
            }
            if (scale <= NUMBER_DECIMAL_4_SCALE) {
                return NUMBER_DECIMAL_4;
            }
            if (scale <= NUMBER_DECIMAL_6_SCALE) {
                return NUMBER_DECIMAL_6;
            }
            throw new IllegalStateException("Max %s decimal places precision is currently supported, but given precision is %s".formatted(NUMBER_DECIMAL_6_SCALE, scale));
        }
    }

}
