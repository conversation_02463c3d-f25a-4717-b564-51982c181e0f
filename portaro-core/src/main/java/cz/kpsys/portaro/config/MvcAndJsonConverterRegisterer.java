package cz.kpsys.portaro.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import cz.kpsys.portaro.commons.convert.*;
import cz.kpsys.portaro.commons.json.ByIntConverterJsonDeserializer;
import cz.kpsys.portaro.commons.json.ByLongConverterJsonDeserializer;
import cz.kpsys.portaro.commons.json.FromStringConvertingJsonDeserializer;
import cz.kpsys.portaro.commons.json.ToStringConvertingJsonSerializer;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.format.FormatterRegistry;

import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MvcAndJsonConverterRegisterer implements ConverterRegisterer {

    @NonNull FormatterRegistry conversionService;
    @NonNull SimpleModule objectMapperModule;
    @NonNull ObjectMapper objectMapper;

    @Override
    public <MODEL> MvcAndJsonConverterRegisterer registerForIntegerId(Class<MODEL> modelClass, ByIdLoadable<? extends MODEL, Integer> byIdLoadable) {
        IdToObjectConverter<Integer, MODEL> idToModelConverter = new IdToObjectConverter<>(byIdLoadable);
        conversionService.addConverter(Integer.class, modelClass, idToModelConverter);
        conversionService.addConverter(String.class, modelClass, StringToIntegerToAnyConverter.nullConvertingToNull(idToModelConverter).throwWhenNull());
        objectMapperModule.addDeserializer(modelClass, new ByIntConverterJsonDeserializer<>(modelClass, idToModelConverter));
        return this;
    }

    @Override
    public <MODEL> MvcAndJsonConverterRegisterer registerForIntegerIdWithCustomFromStringConversion(Class<MODEL> modelClass, ByIdLoadable<? extends MODEL, Integer> byIdLoadable, Converter<String, MODEL> customFromStringConverter) {
        IdToObjectConverter<Integer, MODEL> idToModelConverter = new IdToObjectConverter<>(byIdLoadable);
        conversionService.addConverter(Integer.class, modelClass, idToModelConverter);
        conversionService.addConverter(String.class, modelClass, customFromStringConverter);
        objectMapperModule.addDeserializer(modelClass, new FromStringConvertingJsonDeserializer<>(modelClass, customFromStringConverter));
        return this;
    }

    @Override
    public <MODEL> ConverterRegisterer registerForLongId(Class<MODEL> modelClass, ByIdLoadable<? extends MODEL, Long> byLongIdLoader) {
        IdToObjectConverter<Long, MODEL> idToModelConverter = new IdToObjectConverter<>(byLongIdLoader);
        conversionService.addConverter(Long.class, modelClass, idToModelConverter);
        conversionService.addConverter(String.class, modelClass, new StringToLongToAnyConverter<>(idToModelConverter).throwWhenNull());
        objectMapperModule.addDeserializer(modelClass, new ByLongConverterJsonDeserializer<>(modelClass, idToModelConverter));
        return this;
    }

    @Override
    public <MODEL> MvcAndJsonConverterRegisterer registerForStringId(Class<MODEL> modelClass, ByIdLoadable<? extends MODEL, String> byStringIdLoader) {
        IdToObjectConverter<String, MODEL> stringToModelConverter = new IdToObjectConverter<>(byStringIdLoader);
        conversionService.addConverter(String.class, modelClass, stringToModelConverter);
        objectMapperModule.addDeserializer(modelClass, new FromStringConvertingJsonDeserializer<>(modelClass, stringToModelConverter));
        return this;
    }

    @Override
    public <MODEL> ConverterRegisterer registerForUuidId(Class<MODEL> modelClass, ByIdLoadable<? extends MODEL, UUID> byUuidIdLoader) {
        IdToObjectConverter<UUID, MODEL> idToModelConverter = new IdToObjectConverter<>(byUuidIdLoader);
        Converter<String, MODEL> standardFromStringConverter = new ChainingConverter<>(StringToUuidConverter.INSTANCE, idToModelConverter).throwWhenNull();
        return registerForUuidIdWithCustomFromStringConversion(modelClass, byUuidIdLoader, standardFromStringConverter);
    }

    @Override
    public <MODEL> ConverterRegisterer registerForUuidIdWithCustomFromStringConversion(Class<MODEL> modelClass, ByIdLoadable<? extends MODEL, UUID> byUuidIdLoader, Converter<String, MODEL> customFromStringConverter) {
        IdToObjectConverter<UUID, MODEL> idToModelConverter = new IdToObjectConverter<>(byUuidIdLoader);
        conversionService.addConverter(UUID.class, modelClass, idToModelConverter);
        conversionService.addConverter(String.class, modelClass, customFromStringConverter);
        objectMapperModule.addDeserializer(modelClass, new FromStringConvertingJsonDeserializer<>(modelClass, customFromStringConverter));
        return this;
    }

    @Override
    public <MODEL> ConverterRegisterer registerForValue(Class<MODEL> modelClass) {
        conversionService.addConverter(String.class, modelClass, source -> {
            try {
                return objectMapper.readValue(source, modelClass);
            } catch (JsonProcessingException e) {
                throw new ConversionException(String.format("Cannot convert value (%s) to value class (%s)", source,  modelClass.getSimpleName()), e);
            }
        });
        return this;
    }

    @Override
    public <MODEL> MvcAndJsonConverterRegisterer registerForStringSerializedObject(Class<MODEL> modelClass, Converter<MODEL, String> serializer, Converter<String, MODEL> deserializer) {
        conversionService.addConverter(String.class, modelClass, deserializer);
        objectMapperModule.addDeserializer(modelClass, new FromStringConvertingJsonDeserializer<>(modelClass, deserializer));
        objectMapperModule.addSerializer(modelClass, new ToStringConvertingJsonSerializer<>(modelClass, serializer));
        return this;
    }
}
