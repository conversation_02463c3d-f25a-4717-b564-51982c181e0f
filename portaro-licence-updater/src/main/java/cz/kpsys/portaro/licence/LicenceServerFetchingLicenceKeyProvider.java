package cz.kpsys.portaro.licence;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.DataAccessException;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.web.HttpHeaderConstants.Authorization;
import cz.kpsys.portaro.commons.web.UrlCreator;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestOperations;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class LicenceServerFetchingLicenceKeyProvider implements Provider<@NonNull String> {

    public static final String LICENCE_SERVER_URL = "https://verbis-licencer.kpsys.cz/oauth/token";
    public static final String LICENCE_SERVER_BASE_URL = "https://verbis-licencer.kpsys.cz";
    public static final String LICENCE_SERVER_TOKEN_PATH = "/oauth/token";

    @NonNull RestOperations rest;
    @NonNull Provider<@NonNull String> serialCodeProvider;
    @NonNull Provider<@NonNull String> kpsysApiKeyProvider;
    @NonNull Map<String, @NonNull Provider<@NullableNotBlank String>> extraDataPassingProvider = new HashMap<>();

    public LicenceServerFetchingLicenceKeyProvider withExtraData(@NonNull String extraDataName, @NonNull Provider<@NullableNotBlank String> extraDataValueProvider) {
        this.extraDataPassingProvider.put(extraDataName, extraDataValueProvider);
        return this;
    }

    @NonNull
    @Override
    public String get() {
        String serialCode = serialCodeProvider.get();
        String kpsysApiKey = kpsysApiKeyProvider.get();
        HttpEntity<?> entity = new HttpEntity<>(getHeaders(serialCode, kpsysApiKey));


        UrlCreator url = new UrlCreator()
                .serverBaseUrl(LICENCE_SERVER_BASE_URL)
                .path(LICENCE_SERVER_TOKEN_PATH)
                .addParameter("grant_type", "client_credentials");

        extraDataPassingProvider.forEach((name, valueProvider) -> {
            @NullableNotBlank String value = valueProvider.get();
            if (value != null) {
                url.addParameter(name, value);
            }
        });

        try {
            URI uri = new URI(url.build()); // kdyz toto neudelame, spatne (dvojite) se pak enkoduji url parametry
            ResponseEntity<AccessTokenResponse> licenceKeyEntity = rest.exchange(uri, HttpMethod.GET, entity, AccessTokenResponse.class);
            String licenceKey = Objects.requireNonNull(licenceKeyEntity.getBody()).getAccessToken();
            log.info("Fetched licence key {}", licenceKey);
            return licenceKey;

        } catch (Exception e) {
            throw new DataAccessException("Cannot load licence key from licence server for serial code \"%s\"".formatted(serialCode), AccessTokenResponse.class.getSimpleName(), e);
        }
    }

    private HttpHeaders getHeaders(@NonNull String serialCode, @NonNull String kpsysApiKey) {
        HttpHeaders headers = new HttpHeaders();
        headers.set(Authorization.NAME, Authorization.Generator.BASIC_USERNAME_PASSWORD.apply(serialCode, kpsysApiKey));
        return headers;
    }

}
