package cz.kpsys.portaro.licence;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
public class AccessTokenResponse {

    String accessToken;

    public AccessTokenResponse(@NonNull @JsonProperty("access_token") String accessToken) {
        this.accessToken = accessToken;
    }
}
