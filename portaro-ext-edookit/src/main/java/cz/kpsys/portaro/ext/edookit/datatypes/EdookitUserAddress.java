package cz.kpsys.portaro.ext.edookit.datatypes;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Value;
import org.springframework.lang.Nullable;

@Value
public class EdookitUserAddress {

    @JsonProperty("CountryCode")
    @Nullable
    String addressCountryCode;

    @JsonProperty("PostalCode")
    @Nullable
    String addressPostalCode;

    @JsonProperty("City")
    @Nullable
    String addressCity;

    @JsonProperty("Street")
    @Nullable
    String addressStreet;

    @JsonProperty("HouseNo")
    @Nullable
    String addressHouseNo;

    @JsonProperty("LandRegistryNo")
    @Nullable
    String addressLandRegistryNo;
}
