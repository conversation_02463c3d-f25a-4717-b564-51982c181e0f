package cz.kpsys.portaro.ext.edookit.datatypes;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.Value;

import java.time.LocalDate;

@Value
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
public class EdookitTeacher extends EdookitUser {

    public EdookitTeacher(@NonNull @JsonProperty("PersonId") String id,
                          @NonNull @JsonProperty("Firstname") String firstName,
                          @JsonProperty("Middlename") String middleName,
                          @NonNull @JsonProperty("Lastname") String lastName,
                          @JsonProperty("DegreePreceding") String degreePreceding,
                          @JsonProperty("DegreeFollowing") String degreeFollowing,
                          @JsonProperty("DateOfBirth") LocalDate dateOfBirth,
                          @JsonProperty("PlaceOfBirth") String placeOfBirth,
                          @JsonProperty("Gender") String gender,
                          @JsonProperty("PersonalNumber") String personalNumber,
                          @JsonProperty("IdCardNumber") String idCardNumber,
                          @JsonProperty("PermanentStayAddress") EdookitUserAddress permanentStayAddress,
                          @JsonProperty("OrganizationName") String organizationName,
                          @JsonProperty("OrganizationIdent") String organizationIdent,
                          @JsonProperty("ClassName") String className,
                          @JsonProperty("Identifier") String identifier) {
        super(id, firstName, middleName, lastName, degreePreceding, degreeFollowing, dateOfBirth, placeOfBirth, gender, personalNumber, idCardNumber, permanentStayAddress, organizationName, organizationIdent, className, identifier);
    }
}

