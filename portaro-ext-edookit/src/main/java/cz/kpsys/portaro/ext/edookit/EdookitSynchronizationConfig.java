package cz.kpsys.portaro.ext.edookit;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.synchronizer.*;
import cz.kpsys.portaro.ext.synchronizer.filters.NoopForeignSystemUserFilter;
import cz.kpsys.portaro.ext.synchronizer.matcher.ExternalIdSyncedUsersEqualFunction;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import feign.RequestInterceptor;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

import java.util.concurrent.ExecutorService;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EdookitSynchronizationConfig {

    @NonNull SettingLoader settingLoader;
    @NonNull ByIdLoadable<ReaderCategory, String> readerCategoryLoader;
    @NonNull ForeignSystemUserSaver foreignSystemUserSaver;
    @NonNull ParameterizedSearchLoader<MapBackedParams, User> nonCachedUserSearchLoader;
    @NonNull HierarchyLoader<Department> departmentAccessor;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull Provider<@NonNull User> portaroUserProvider;
    @NonNull ExecutorService executorService;
    @NonNull ObjectMapper objectMapper;
    @NonNull RequestInterceptor userAgentAddingInterceptor;
    @NonNull ForeignSystemSynchronizerRegistry<LabeledIdentified<?>> foreignSystemSynchronizerRegistry;

    @Bean
    public BulkSynchronizer<User> edookitSystemSynchronizer() {
        return new AlreadyRunningCheckingBulkSynchronizer<>(
                new DepartmentsBrowsingBulkSynchronizer<>(
                        settingLoader.getStaticValueMatchingContextsProvider(EdookitSettingKeys.EDOOKIT_ENABLED, true),
                        new AuthIsolatedContextualBulkSynchronizer<>(
                                departmentedEdookitDataSynchronizer(),
                                authenticationHolder,
                                portaroUserProvider,
                                executorService
                        )
                )
        );
    }

    @Bean
    public AuthenticatedContextualBulkSynchronizer<User> departmentedEdookitDataSynchronizer() {
        return new LoadingAndSavingAuthenticatedContextualBulkSynchronizer(
                edookitUserLoader(),
                foreignSystemUserSaver,
                new NoopForeignSystemUserFilter());
    }

    @Bean
    public ForeignSystemUserLoader edookitUserLoader() {
        return new EdookitUserLoader(
                settingLoader.getDepartmentedProvider(EdookitSettingKeys.EDOOKIT_CTX_PREFIX).throwingWhenNull(),
                settingLoader.getDepartmentedProvider(EdookitSettingKeys.EDOOKIT_STUDENT_READER_CATEGORY).throwingWhenNull(),
                settingLoader.getDepartmentedProvider(EdookitSettingKeys.EDOOKIT_TEACHER_READER_CATEGORY).throwingWhenNull(),
                settingLoader.getDepartmentedProvider(EdookitSettingKeys.EDOOKIT_EMAIL_GENERATOR_ENABLED),
                settingLoader.getDepartmentedProvider(EdookitSettingKeys.EDOOKIT_EMAIL_DOMAIN).throwingWhenNull(),
                nonCachedUserSearchLoader,
                readerCategoryLoader,
                departmentAccessor,
                new EdookitExternalUserLoader(dynamicDepartmentedEdookitClient()),
                new ExternalIdSyncedUsersEqualFunction<>(Person::getSyncId)
        );
    }

    @Bean
    public DynamicDepartmentedEdookitClient dynamicDepartmentedEdookitClient() {
        return new DynamicDepartmentedEdookitClient(
                settingLoader.getDepartmentedProvider(EdookitSettingKeys.EDOOKIT_API_URL).throwingWhenNull(),
                settingLoader.getDepartmentedProvider(EdookitSettingKeys.EDOOKIT_API_USER).throwingWhenNull(),
                settingLoader.getDepartmentedProvider(EdookitSettingKeys.EDOOKIT_API_PASSWORD).throwingWhenNull(),
                userAgentAddingInterceptor,
                objectMapper
        );
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerConverters() {
        foreignSystemSynchronizerRegistry.register(ForeignSystemsKeys.EDOOKIT_SYNCHRONIZATION_MAP_KEY, edookitSystemSynchronizer());
    }
}
