package cz.kpsys.portaro.ext.edookit;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.edookit.datatypes.EdookitTeacherDataResponse;
import cz.kpsys.portaro.ext.edookit.datatypes.EdookitStudentDataResponse;

public interface EdookitDepartmentedClient {

    EdookitTeacherDataResponse getEmployeeData(Department department);

    EdookitStudentDataResponse getStudentsData(Department department);
}
