package cz.kpsys.portaro.ext.edookit;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.geo.Country;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.commons.sync.SyncItem;
import cz.kpsys.portaro.commons.sync.SyncUtil;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.OptionalUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.edookit.datatypes.EdookitUser;
import cz.kpsys.portaro.ext.edookit.datatypes.NormalizedEdookitUser;
import cz.kpsys.portaro.ext.synchronizer.ForeignSystemUserLoader;
import cz.kpsys.portaro.ext.synchronizer.matcher.SyncedUsersEqualFunction;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserConstants;
import cz.kpsys.portaro.user.contact.UserEmailEditationCommand;
import cz.kpsys.portaro.user.edit.command.*;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import static cz.kpsys.portaro.user.contact.SourceOfData.EXTERNAL_EDOOKIT;
import static java.util.stream.Collectors.toUnmodifiableSet;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class EdookitUserLoader implements ForeignSystemUserLoader {

    @NonNull ContextualProvider<Department, @NonNull String> edookitCtxPrefixProvider;
    @NonNull ContextualProvider<Department, @NonNull String> edookitStudentReaderCategoryProvider;
    @NonNull ContextualProvider<Department, @NonNull String> edookitTeacherReaderCategoryProvider;
    @NonNull ContextualProvider<Department, @NonNull Boolean> edookitEmailGeneratorEnabledProvider;
    @NonNull ContextualProvider<Department, @NonNull String> edookitEmailDomainProvider;
    @NonNull ParameterizedSearchLoader<MapBackedParams, User> userSearchLoader;
    @NonNull ByIdLoadable<ReaderCategory, String> readerCategoryLoader;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull ContextualProvider<Department, List<? extends EdookitUser>> edookitExternalUserLoader;
    @NonNull SyncedUsersEqualFunction<Department, NormalizedEdookitUser> syncedUsersEqualFunction;

    @Override
    public List<UserRecordEditationCommand<PersonEditationCommand>> getForeignUsersFromServer(Department ctx, @NonNull UserAuthentication currentAuth) {
        // Input data from external Edookit system
        List<? extends EdookitUser> externalUsers = edookitExternalUserLoader.getOn(ctx);

        // Convert all edookitIds to syncIds
        List<NormalizedEdookitUser> allExternalNormalizedUsers = externalUsers.stream()
                .map(externalUser -> NormalizedEdookitUser.mapNewUser(externalUser, edookitCtxPrefixProvider.getOn(ctx)))
                .toList();

        // All existing users loaded from DB, who has syncId started with EDOOKIT_SYNC_ID_PREFIX + context prefix
        Set<Person> dbUsersWithEdookitId = getAllInvolvedDbUsers(ctx);

        // people, who exists in both system or in db only
        List<SyncItem<Person, NormalizedEdookitUser>> bothAndDbOnlyUsers = ListUtil.convert(dbUsersWithEdookitId, existingDbUserWithEdookitId -> tryToConnectWithExternalUser(existingDbUserWithEdookitId, allExternalNormalizedUsers, ctx));

        // return list of new students which are not in DB
        List<SyncItem<Person, NormalizedEdookitUser>> newExternalUsers = allExternalNormalizedUsers.stream()
                .map(normalizedEdookitUser -> addNewEdookitUser(normalizedEdookitUser, dbUsersWithEdookitId, ctx))
                .filter(Objects::nonNull)
                .toList();

        // joining both list
        List<SyncItem<Person, NormalizedEdookitUser>> allUsers = ListUtil.union(bothAndDbOnlyUsers, newExternalUsers);

        return ListUtil.convert(allUsers, syncItem ->
                UserRecordEditationCommand.ofUserEditationOnly(mapUserToPersonEditationCommand(syncItem, ctx)));
    }

    // Add Edookit data to existing dbUser
    public SyncItem<Person, NormalizedEdookitUser> tryToConnectWithExternalUser(Person existingDbUser, List<NormalizedEdookitUser> externalUsers, Department ctx) {
        Set<NormalizedEdookitUser> edookitUsers = externalUsers.stream()
                .filter(externalUser -> syncedUsersEqualFunction.areEqual(externalUser, existingDbUser, ctx))
                .collect(toUnmodifiableSet());
        Optional<NormalizedEdookitUser> edookitUserOpt = DataUtils.requireMaxOne(edookitUsers, NormalizedEdookitUser.class, "existing user %s".formatted(existingDbUser));
        return SyncItem.ofOptionalExternal(existingDbUser, edookitUserOpt);
    }

    // Add Edookit data to non existing dbUser
    public SyncItem<Person, NormalizedEdookitUser> addNewEdookitUser(NormalizedEdookitUser edookitUser, Set<Person> existingDbUsersWithEdookitId, Department ctx) {
        boolean userIsNotFound = existingDbUsersWithEdookitId.stream().noneMatch(existingUser -> syncedUsersEqualFunction.areEqual(edookitUser, existingUser, ctx));
        if (userIsNotFound) {
            return SyncItem.ofWithoutInternal(edookitUser);
        }
        return null;
    }

    private PersonEditationCommand mapUserToPersonEditationCommand(SyncItem<Person, NormalizedEdookitUser> userSync, Department ctx) {
        // NEW USER - map raw data + email generator
        if (userSync.internal().isEmpty() && userSync.external().isPresent()) {
            return mapNewUserToPersonEditationCommand(userSync.external().get(), ctx);
        }

        // UPDATE USER - according id just update person
        if (userSync.internal().isPresent() && userSync.external().isPresent()) {
            return mapEditedUserToPersonEditationCommand(userSync.external().get(), userSync.internal().get(), ctx);
        }

        // INACTIVE USER - just update expiration date
        if (userSync.internal().isPresent()) {
            return mapInactivatedToPersonEditationCommand(userSync.internal().get());
        }

        throw new IllegalStateException();
    }

    private PersonEditationCommand mapEditedUserToPersonEditationCommand(@NonNull NormalizedEdookitUser edookitUser, @NonNull Person dbUser, Department ctx) {
        PersonEditationCommand personEditationCommand = mapNewUserToPersonEditationCommand(edookitUser, ctx);
        personEditationCommand.setId(dbUser.getId());
        return personEditationCommand;
    }

    private PersonEditationCommand mapInactivatedToPersonEditationCommand(@NonNull Person user) {
        PersonEditationCommand personEditationCommand = new PersonEditationCommand();

        // TOTO by melo updatovat pouze Expiration Date u uzivatelu, kteri neprijdou z Edookitu a maji EdookitId
        personEditationCommand.setId(user.getId());
        ReaderAccountEditationCommand readerAccount = ReaderAccountEditationCommand.ofEmpty();
        readerAccount.setRegistrationExpirationDateAndRegistrationToNowIfEmpty(Optional.of(LocalDate.now().minusDays(1)));
        personEditationCommand.setReaderAccounts(Optional.of(List.of(readerAccount)));

        return personEditationCommand;
    }

    private PersonEditationCommand mapNewUserToPersonEditationCommand(@NonNull NormalizedEdookitUser user, Department ctx) {
        PersonEditationCommand personEditationCommand = new PersonEditationCommand();

        personEditationCommand.setSyncId(Optional.of(user.id()));

        personEditationCommand.setFirstName(Optional.of(user.firstName()));
        personEditationCommand.setLastName(Optional.of(user.lastName()));
        personEditationCommand.setNameSource(Optional.of(EXTERNAL_EDOOKIT));


        personEditationCommand.setPrefixDegree(Optional.ofNullable(user.degreePreceding()));
        personEditationCommand.setSuffixDegree(Optional.ofNullable(user.degreeFollowing()));
        if (OptionalUtil.isNotNullAndPresent(personEditationCommand.getPrefixDegree()) || OptionalUtil.isNotNullAndPresent(personEditationCommand.getSuffixDegree())) {
            personEditationCommand.setDegreeSource(Optional.of(EXTERNAL_EDOOKIT));
        }

        ReaderAccountEditationCommand readerAccount = ReaderAccountEditationCommand.ofEmpty();

        if (user.isEmployee()) {
            ReaderCategory teacherReaderCategory = readerCategoryLoader.getById(edookitTeacherReaderCategoryProvider.getOn(ctx));
            readerAccount.setReaderCategory(Optional.of(teacherReaderCategory));
        } else {
            if (StringUtil.hasTrimmedLength(user.className())) {
                personEditationCommand.setSchoolClass(Optional.ofNullable(user.className()));
            }
            ReaderCategory studentReaderCategory = readerCategoryLoader.getById(edookitStudentReaderCategoryProvider.getOn(ctx));
            readerAccount.setReaderCategory(Optional.of(studentReaderCategory));
        }

        if (edookitEmailGeneratorEnabledProvider.getOn(ctx) && StringUtil.hasLength(user.identifier())) {
            String domain = edookitEmailDomainProvider.getOn(ctx);
            String email = getGeneratedEmailBySchoolDomain(user, domain);
            if (StringUtil.isValidEmailAddress(email)) {
                personEditationCommand.setEmails(Optional.of(SourcedEditableList.ofSingleSource(new UserEmailEditationCommand(email, EXTERNAL_EDOOKIT), SourcedEditableListMode.OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE)));
            }
        }

        readerAccount.setRegistrationExpirationDateAndRegistrationToNowIfEmpty(Optional.of(LocalDate.now().plusDays(7)));
        personEditationCommand.setReaderAccounts(Optional.of(List.of(readerAccount)));

        getUserAddress(user)
                .ifPresent(address -> personEditationCommand.setAddresses(Optional.of(SourcedEditableList.ofSingleSource(address, SourcedEditableListMode.OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE))));

        personEditationCommand.setBirthDate(Optional.ofNullable(user.dateOfBirth()));
        if (OptionalUtil.isNotNullAndPresent(personEditationCommand.getBirthDate())) {
            personEditationCommand.setLifeDateSource(Optional.of(EXTERNAL_EDOOKIT));
        }

        return personEditationCommand;
    }

    private Optional<UserAddressEditationCommand> getUserAddress(NormalizedEdookitUser user) {
        if (user.permanentStayAddress() == null) {
            return Optional.empty();
        }
        String city = StringUtil.notEmptyString(user.permanentStayAddress().getAddressCity());
        String postalCode = StringUtil.notEmptyString(user.permanentStayAddress().getAddressPostalCode());
        String personAddress = StringUtil.notEmptyString(StringUtil.joinSkippingBlanksAndNulls(" ", user.permanentStayAddress().getAddressStreet(), user.permanentStayAddress().getAddressHouseNo()));
        if (city == null && postalCode == null && personAddress == null) {
            return Optional.empty();
        }
        return Optional.of(new UserAddressEditationCommand(true, true, EXTERNAL_EDOOKIT, personAddress, city, postalCode, Country.CZE));
    }

    private Set<Person> getAllInvolvedDbUsers(Department ctx) {
        List<User> allExistingUsers = userSearchLoader.getContent(RangePaging.forAll(), StaticParamsModifier.of(
                CoreSearchParams.DEPARTMENT, contextHierarchyLoader.getAllByScope(ctx, HierarchyLoadScope.SUBTREE),
                UserConstants.SearchParams.SYNC_ID_PREFIX, SyncUtil.composeSyncPrefix(NormalizedEdookitUser.EDOOKIT_SYNC_ID_PREFIX, edookitCtxPrefixProvider.getOn(ctx))
        ));
        return ListUtil.castItems(allExistingUsers, Person.class).collect(toUnmodifiableSet());
    }

    private String getGeneratedEmailBySchoolDomain(NormalizedEdookitUser user, String domain) {
        String email = "";

        // TODO zde je generovani emailu nastavene pouze pro SZS-Jaselskou, pri dalsi skole se sem musi dle domeny pridat dalsi format
        if (domain.contains("jaselska")) {
            // student <EMAIL>
            // ucitel <EMAIL>
            if (user.isEmployee()) {
                email = StringUtil.flatString(user.identifier() + "@").toLowerCase();
            } else {
                email = StringUtil.flatString(user.identifier() + "@student.").toLowerCase();
            }
        }

        if (domain.contains("gymnp")) {
            // uzivatel <EMAIL>
            email = StringUtil.flatString(user.identifier() + "@").toLowerCase();
        }

        email = email + domain;
        return email;
    }
}
