package cz.kpsys.portaro.ext.edookit;

import cz.kpsys.portaro.ext.edookit.datatypes.EdookitTeacherDataResponse;
import cz.kpsys.portaro.ext.edookit.datatypes.EdookitStudentDataResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(value = "edookit", url = "https://thisWillBeReplacedWithInterceptor")
public interface EdookitClient {

    @GetMapping(path = "/api/student-data/v1/list")
    EdookitStudentDataResponse getStudentData();

    @GetMapping(path = "/api/employee-data/v1/list")
    EdookitTeacherDataResponse getEmployeeData();

}
