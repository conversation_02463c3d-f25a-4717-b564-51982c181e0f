package cz.kpsys.portaro.ext.edookit.datatypes;

import com.fasterxml.jackson.annotation.JsonProperty;
import cz.kpsys.portaro.commons.object.Identified;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.Value;
import lombok.experimental.NonFinal;
import org.springframework.lang.Nullable;

import java.time.LocalDate;

@Value
@NonFinal
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class EdookitUser implements Identified<String> {

    @JsonProperty("PersonId")
    @NonNull
    @EqualsAndHashCode.Include
    String id;

    @JsonProperty("Firstname")
    @NonNull
    String firstName;

    @JsonProperty("Middlename")
    @Nullable
    String middleName;

    @JsonProperty("Lastname")
    @NonNull
    String lastName;

    @JsonProperty("DegreePreceding")
    @Nullable
    String degreePreceding;

    @JsonProperty("DegreeFollowing")
    @Nullable
    String degreeFollowing;

    @JsonProperty("DateOfBirth")
    @Nullable
    LocalDate dateOfBirth;

    @JsonProperty("PlaceOfBirth")
    @Nullable
    String placeOfBirth;

    @JsonProperty("Gender")
    @Nullable
    String gender;

    @JsonProperty("PersonalNumber")
    @Nullable
    String personalNumber;

    @JsonProperty("IdCardNumber")
    @Nullable
    String idCardNumber;

    @JsonProperty("PermanentStayAddress")
    @Nullable
    EdookitUserAddress permanentStayAddress;

    @JsonProperty("OrganizationName")
    @Nullable
    String organizationName;

    @JsonProperty("OrganizationIdent")
    @Nullable
    String organizationIdent;

    @JsonProperty("ClassName")
    @Nullable
    String className;

    @JsonProperty("Identifier")
    @Nullable
    String identifier;
}
