package cz.kpsys.portaro.ext.edookit;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.edookit.datatypes.EdookitStudentDataResponse;
import cz.kpsys.portaro.ext.edookit.datatypes.EdookitTeacherDataResponse;
import cz.kpsys.portaro.ext.edookit.datatypes.EdookitUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EdookitExternalUserLoader implements ContextualProvider<Department, List<? extends EdookitUser>> {

    @NonNull DynamicDepartmentedEdookitClient dynamicDepartmentedEdookitClient;

    @Override
    public List<? extends EdookitUser> getOn(Department ctx) throws ItemNotFoundException {
        EdookitTeacherDataResponse loadedEmployeeData = dynamicDepartmentedEdookitClient.getEmployeeData(ctx);
        EdookitStudentDataResponse loadedStudentsData = dynamicDepartmentedEdookitClient.getStudentsData(ctx);

        return ListUtil.union(loadedEmployeeData.getTeachers(), loadedStudentsData.getStudents());
    }
}
