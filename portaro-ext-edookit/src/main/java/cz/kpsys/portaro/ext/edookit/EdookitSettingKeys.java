package cz.kpsys.portaro.ext.edookit;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.setting.SettingKey;
import lombok.NonNull;

public class EdookitSettingKeys {

    public static final String SECTION_INTEG_EDOOKIT = "integ.edookit";
    public static final String SECTION_INTEG_EDOOKIT_USER = "integ.edookit.user";
    public static final SettingKey<@NonNull Boolean> EDOOKIT_ENABLED = new SettingKey<>(SECTION_INTEG_EDOOKIT, "enabled");
    public static final SettingKey<String> EDOOKIT_API_URL = new SettingKey<>(SECTION_INTEG_EDOOKIT, "apiUrl");
    public static final SettingKey<String> EDOOKIT_API_USER = new SettingKey<>(SECTION_INTEG_EDOOKIT, "apiUsername");
    public static final SettingKey<String> EDOOKIT_API_PASSWORD = new SettingKey<>(SECTION_INTEG_EDOOKIT, "apiPassword");
    public static final SettingKey<@NullableNotBlank String> EDOOKIT_CTX_PREFIX = new SettingKey<>(SECTION_INTEG_EDOOKIT_USER, "edookitCtxPrefix");
    public static final SettingKey<String> EDOOKIT_STUDENT_READER_CATEGORY = new SettingKey<>(SECTION_INTEG_EDOOKIT_USER, "studentReaderCategory");
    public static final SettingKey<String> EDOOKIT_TEACHER_READER_CATEGORY = new SettingKey<>(SECTION_INTEG_EDOOKIT_USER, "teacherReaderCategory");
    public static final SettingKey<@NonNull Boolean> EDOOKIT_EMAIL_GENERATOR_ENABLED = new SettingKey<>(SECTION_INTEG_EDOOKIT_USER, "emailGeneratorEnabled");
    public static final SettingKey<String> EDOOKIT_EMAIL_DOMAIN = new SettingKey<>(SECTION_INTEG_EDOOKIT_USER, "emailDomain");
}
