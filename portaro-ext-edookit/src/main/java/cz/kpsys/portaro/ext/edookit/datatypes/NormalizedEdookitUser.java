package cz.kpsys.portaro.ext.edookit.datatypes;

import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.commons.sync.SyncUtil;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.time.LocalDate;
import java.util.Objects;

import static cz.kpsys.portaro.ext.edookit.EdookitUserLoader.*;

public record NormalizedEdookitUser(

        @NonNull
        @EqualsAndHashCode.Include
        String id,

        @NonNull
        String firstName,

        @Nullable
        String middleName,

        @NonNull
        String lastName,

        @Nullable
        String degreePreceding,

        @Nullable
        String degreeFollowing,

        @Nullable
        LocalDate dateOfBirth,

        @Nullable
        String placeOfBirth,

        @Nullable
        String gender,

        @Nullable
        String personalNumber,

        @Nullable
        String idCardNumber,

        @Nullable
        EdookitUserAddress permanentStayAddress,

        @Nullable
        String organizationName,

        @Nullable
        String organizationIdent,

        @Nullable
        String className,

        @Nullable
        String identifier,

        boolean isEmployee

) implements IdentifiedRecord<String> {

    public static final String EDOOKIT_SYNC_ID_PREFIX = "edookit";
    public static final String EDOOKIT_SYNC_ID_TEACHER_CATEGORY_PREFIX = "u-";
    public static final String EDOOKIT_SYNC_ID_STUDENT_CATEGORY_PREFIX = "s-";

    public static NormalizedEdookitUser mapNewUser(@NonNull EdookitUser user, String contextPrefix) {

        String id = user.getId();
        boolean isEmployee = false;

        if (user instanceof EdookitTeacher) {
            id = EDOOKIT_SYNC_ID_TEACHER_CATEGORY_PREFIX + id;
            isEmployee = true;
        }

        if (user instanceof EdookitStudent) {
            id = EDOOKIT_SYNC_ID_STUDENT_CATEGORY_PREFIX + id;
        }

        return new NormalizedEdookitUser(
                Objects.requireNonNull(SyncUtil.composeSyncId(EDOOKIT_SYNC_ID_PREFIX, contextPrefix, id)),
                Objects.requireNonNull(user.getFirstName()),
                user.getMiddleName(),
                Objects.requireNonNull(user.getLastName()),
                user.getDegreePreceding(),
                user.getDegreeFollowing(),
                user.getDateOfBirth(),
                user.getPlaceOfBirth(),
                user.getGender(),
                user.getPersonalNumber(),
                user.getIdCardNumber(),
                user.getPermanentStayAddress(),
                user.getOrganizationName(),
                user.getOrganizationIdent(),
                user.getClassName(),
                user.getIdentifier(),
                isEmployee
        );
    }
}
