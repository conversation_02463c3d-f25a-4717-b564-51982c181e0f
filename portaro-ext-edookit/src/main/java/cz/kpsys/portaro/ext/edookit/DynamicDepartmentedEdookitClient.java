package cz.kpsys.portaro.ext.edookit;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.web.HttpHeaderConstants.Accept;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.edookit.datatypes.EdookitStudentDataResponse;
import cz.kpsys.portaro.ext.edookit.datatypes.EdookitTeacherDataResponse;
import cz.kpsys.portaro.integ.feign.HeaderAddingRequestInterceptor;
import feign.Feign;
import feign.Logger;
import feign.RequestInterceptor;
import feign.auth.BasicAuthRequestInterceptor;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.slf4j.Slf4jLogger;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.cloud.openfeign.support.SpringMvcContract;


@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DynamicDepartmentedEdookitClient implements EdookitDepartmentedClient {

    @NonNull ContextualProvider<Department, @NonNull String> apiUrlProvider;
    @NonNull ContextualProvider<Department, @NonNull String> apiUsernameProvider;
    @NonNull ContextualProvider<Department, @NonNull String> apiPasswordProvider;
    @NonNull RequestInterceptor userAgentAddingInterceptor;
    @NonNull ObjectMapper objectMapper;

    @Override
    public EdookitTeacherDataResponse getEmployeeData(Department department) {
        return getClientByDepartment(department).getEmployeeData();
    }

    @Override
    public EdookitStudentDataResponse getStudentsData(Department department) {
        return getClientByDepartment(department).getStudentData();
    }

    private EdookitClient getClientByDepartment(Department department) {
        return Feign.builder()
                .encoder(new JacksonEncoder(objectMapper))
                .decoder(new JacksonDecoder(objectMapper))
                .logger(new Slf4jLogger(EdookitClient.class))
                .logLevel(Logger.Level.FULL)
                .requestInterceptor(userAgentAddingInterceptor)
                .requestInterceptor(HeaderAddingRequestInterceptor.ofStaticValue(Accept.NAME, Accept.Value.JSON))
                .requestInterceptor(new BasicAuthRequestInterceptor(apiUsernameProvider.getOn(department), apiPasswordProvider.getOn(department)))
                .contract(new SpringMvcContract())
                .target(EdookitClient.class, apiUrlProvider.getOn(department));
    }
}
