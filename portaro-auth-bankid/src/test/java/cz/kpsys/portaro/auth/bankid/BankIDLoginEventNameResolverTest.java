package cz.kpsys.portaro.auth.bankid;

import cz.kpsys.portaro.auth.bankid.reponse.BankIDProfileResponse;
import cz.kpsys.portaro.token.Scope;
import cz.kpsys.portaro.user.Gender;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Stream;

import static cz.kpsys.portaro.auth.bankid.BankIDScopes.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

@Tag("ci")
@Tag("unit")
class BankIDLoginEventNameResolverTest {
    private final BankIDLoginEventNameResolver resolver = new BankIDLoginEventNameResolver();

    @Test
    void shouldResolveToIdentifyPlusWhenAllScopesAreSetAndAllValuesPresent() {
        BankIDProfileResponse profile = new BankIDProfileResponse("sub",
                "txn",
                null,
                "Mr.",
                "Jr.",
                "John",
                "A.",
                "Doe",
                "johndoe",
                cz.kpsys.portaro.user.Gender.MALE,
                LocalDate.of(1980, 1, 1),
                "*********",
                41,
                true,
                null,
                "City",
                "Country",
                List.of("Nationality"),
                cz.kpsys.portaro.auth.bankid.reponse.BankIDProfileMaritalStatusType.SINGLE,
                List.of(),
                List.of(),
                "<EMAIL>",
                "*********",
                false,
                false,
                List.of(),
                List.of(),
                Instant.now());
        Scope scope = Scope.parse(Stream.of(BankIDScopes.values()).map(BankIDScopes::getId).toList());

        String result = resolver.resolve(profile, scope);

        assertEquals(BankIDLoginEventType.IDENTIFY_PLUS.getId(), result);
    }

    @Test
    void shouldResolveToIdentifyPlusWhenOnlyOneScopeForIdentifyPlusWasAddedToIdentifyAndValueForItIsPresent() {
        BankIDProfileResponse profile = new BankIDProfileResponse("sub",
                "txn",
                null,
                "Mr.",
                "Jr.",
                "John",
                "A.",
                "Doe",
                "johndoe",
                cz.kpsys.portaro.user.Gender.MALE,
                LocalDate.of(1980, 1, 1),
                "*********",
                41,
                null,
                null,
                null,
                null,
                null,
                null,
                List.of(),
                List.of(),
                "<EMAIL>",
                "*********",
                null,
                null,
                List.of(),
                List.of(),
                Instant.now());

        Scope scope = Scope.parse(Stream.of(OPENID,
                NOTIFICATION_CLAIMS_UPDATED,
                OFFLINE_ACCESS,
                PROFILE_BIRTHDATE,
                PROFILE_EMAIL,
                PROFILE_LOCALE,
                PROFILE_NAME,
                PROFILE_PHONENUMBER,
                PROFILE_UPDATEDAT,
                PROFILE_ZONEINFO,
                PROFILE_ADDRESSES,
                PROFILE_BIRTHNUMBER,
                PROFILE_GENDER,
                PROFILE_PAYMENT_ACCOUNTS,
                PROFILE_TITLES,
                PROFILE_IDCARDS).map(BankIDScopes::getId).toList());

        String result = resolver.resolve(profile, scope);

        assertEquals(BankIDLoginEventType.IDENTIFY_PLUS.getId(), result);
    }

    @Test
    void shouldResolveToIdentifyPlusWhenOnlyOneScopeForIdentifyPlusWasAddedToConnectAndValueForItIsPresent() {
        BankIDProfileResponse profile = new BankIDProfileResponse("sub",
                "txn",
                null,
                null,
                null,
                "John",
                "A.",
                "Doe",
                "johndoe",
                null,
                LocalDate.of(1980, 1, 1),
                null,
                41,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                List.of(),
                "<EMAIL>",
                "*********",
                null,
                null,
                null,
                null,
                Instant.now());
        Scope scope = Scope.parse(Stream.of(OPENID, NOTIFICATION_CLAIMS_UPDATED, OFFLINE_ACCESS, PROFILE_BIRTHDATE, PROFILE_EMAIL, PROFILE_LOCALE, PROFILE_NAME, PROFILE_PHONENUMBER, PROFILE_UPDATEDAT, PROFILE_ZONEINFO, PROFILE_IDCARDS).map(BankIDScopes::getId)
                .toList());

        String result = resolver.resolve(profile, scope);

        assertEquals(BankIDLoginEventType.IDENTIFY_PLUS.getId(), result);
    }

    @Test
    void shouldResolveToIdentifyWhenAllIdentifyScopesAreSetAndAllValuesPresent() {
        BankIDProfileResponse profile = new BankIDProfileResponse("sub",
                "txn",
                null,
                "Mr.",
                "Jr.",
                "John",
                "A.",
                "Doe",
                "johndoe",
                cz.kpsys.portaro.user.Gender.MALE,
                LocalDate.of(1980, 1, 1),
                "*********",
                41,
                null,
                null,
                null,
                null,
                null,
                null,
                List.of(),
                null,
                "<EMAIL>",
                "*********",
                null,
                null,
                List.of(),
                List.of(),
                Instant.now());

        Scope scope = Scope.parse(Stream.of(OPENID,
                NOTIFICATION_CLAIMS_UPDATED,
                OFFLINE_ACCESS,
                PROFILE_BIRTHDATE,
                PROFILE_EMAIL,
                PROFILE_LOCALE,
                PROFILE_NAME,
                PROFILE_PHONENUMBER,
                PROFILE_UPDATEDAT,
                PROFILE_ZONEINFO,
                PROFILE_ADDRESSES,
                PROFILE_BIRTHNUMBER,
                PROFILE_GENDER,
                PROFILE_PAYMENT_ACCOUNTS,
                PROFILE_TITLES).map(BankIDScopes::getId).toList());

        String result = resolver.resolve(profile, scope);

        assertEquals(BankIDLoginEventType.IDENTIFY.getId(), result);
    }


    @Test
    void shouldResolveToIdentifyWhenOnlyOneScopeForIdentifyWasAddedAndValueForItIsPresent() {
        BankIDProfileResponse profile = new BankIDProfileResponse("sub",
                "txn",
                null,
                null,
                null,
                "John",
                "A.",
                "Doe",
                "johndoe",
                Gender.MALE,
                LocalDate.of(1980, 1, 1),
                null,
                41,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                "<EMAIL>",
                "*********",
                null,
                null,
                null,
                null,
                Instant.now());
        Scope scope = Scope.parse(Stream.of(OPENID,
                NOTIFICATION_CLAIMS_UPDATED,
                OFFLINE_ACCESS,
                PROFILE_BIRTHDATE,
                PROFILE_EMAIL,
                PROFILE_LOCALE,
                PROFILE_NAME,
                PROFILE_PHONENUMBER,
                PROFILE_UPDATEDAT,
                PROFILE_ZONEINFO,
                PROFILE_GENDER).map(BankIDScopes::getId).toList());

        String result = resolver.resolve(profile, scope);

        assertEquals(BankIDLoginEventType.IDENTIFY.getId(), result);
    }

    @Test
    void shouldResolveToConnectWhenAllConnectScopesAreSetAndAllValuesPresent() {
        BankIDProfileResponse profile = new BankIDProfileResponse("sub",
                "txn",
                null,
                null,
                null,
                "John",
                "A.",
                "Doe",
                "johndoe",
                null,
                LocalDate.of(1980, 1, 1),
                null,
                41,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                "<EMAIL>",
                "*********",
                null,
                null,
                null,
                null,
                Instant.now());
        Scope scope = Scope.parse(Stream.of(OPENID, NOTIFICATION_CLAIMS_UPDATED, OFFLINE_ACCESS, PROFILE_BIRTHDATE, PROFILE_EMAIL, PROFILE_LOCALE, PROFILE_NAME, PROFILE_PHONENUMBER, PROFILE_UPDATEDAT, PROFILE_ZONEINFO).map(BankIDScopes::getId)
                .toList());

        String result = resolver.resolve(profile, scope);

        assertEquals(BankIDLoginEventType.CONNECT.getId(), result);
    }

    @ParameterizedTest
    @EnumSource(value = BankIDScopes.class, names = {"PROFILE_ADDRESSES", "PROFILE_BIRTHNUMBER", "PROFILE_GENDER", "PROFILE_PAYMENT_ACCOUNTS", "PROFILE_TITLES", "PROFILE_BIRTHPLACE_NATIONALITY", "PROFILE_IDCARDS", "PROFILE_LEGALSTATUS", "PROFILE_MARITALSTATUS", "PROFILE_VERIFICATION"})
    void shouldResolveToConnectWhenScopeWasSetToHigherServiceButDataWasNotSend(BankIDScopes scopes) {
        BankIDProfileResponse profile = new BankIDProfileResponse("sub",
                "txn",
                null,
                null,
                null,
                "John",
                "A.",
                "Doe",
                "johndoe",
                null,
                LocalDate.of(1980, 1, 1),
                null,
                41,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                "<EMAIL>",
                "*********",
                null,
                null,
                null,
                null,
                Instant.now());
        Scope scope = Scope.parse(Stream.of(OPENID, NOTIFICATION_CLAIMS_UPDATED, OFFLINE_ACCESS, PROFILE_BIRTHDATE, PROFILE_EMAIL, PROFILE_LOCALE, PROFILE_NAME, PROFILE_PHONENUMBER, PROFILE_UPDATEDAT, PROFILE_ZONEINFO, scopes).map(BankIDScopes::getId)
                .toList());

        String result = resolver.resolve(profile, scope);

        assertEquals(BankIDLoginEventType.CONNECT.getId(), result);
    }

    @Test
    void shouldResolveToReloadWhenScopeIsEmpty() {
        BankIDProfileResponse profile = new BankIDProfileResponse("sub",
                "txn",
                null,
                null,
                null,
                "John",
                "A.",
                "Doe",
                "johndoe",
                null,
                LocalDate.of(1980, 1, 1),
                null,
                41,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                "<EMAIL>",
                "*********",
                null,
                null,
                null,
                null,
                Instant.now());
        Scope scope = Scope.empty();

        String result = resolver.resolve(profile, scope);

        assertEquals(BankIDLoginEventType.RELOAD.getId(), result);
    }
}