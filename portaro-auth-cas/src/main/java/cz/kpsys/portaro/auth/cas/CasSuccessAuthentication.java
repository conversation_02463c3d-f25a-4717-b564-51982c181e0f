package cz.kpsys.portaro.auth.cas;

import cz.kpsys.portaro.auth.process.GenericAuthoritiedSuccessAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.apereo.cas.client.validation.Assertion;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CasSuccessAuthentication extends GenericAuthoritiedSuccessAuthentication {

    @Getter
    @NonNull
    Assertion assertion;

    public CasSuccessAuthentication(@NonNull Assertion assertion,
                                    @NonNull User user,
                                    @NonNull Department ctx) {
        super(user, ctx);
        this.assertion = assertion;
    }

}
