package cz.kpsys.portaro.auth.cas;

import org.jasig.cas.client.util.CommonUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;

import java.util.Date;

/**
 * SAML utility class.
 *
 * <AUTHOR>
 * @since 3.4
 */
public final class SamlUtils {

    private static final DateTimeFormatter ISO_FORMAT = ISODateTimeFormat.dateTimeNoMillis();

    private SamlUtils() {
        // nothing to do
    }

    public static String formatForUtcTime(final Date date) {
        return ISO_FORMAT.print(new DateTime(date).withZone(DateTimeZone.UTC));
    }

    public static Date parseUtcDate(final String date) {
        if (CommonUtils.isEmpty(date)) {
            return null;
        }
        return ISODateTimeFormat.dateTimeParser().parseDateTime(date).toDate();
    }
}