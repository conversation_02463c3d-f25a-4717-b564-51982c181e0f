package cz.kpsys.portaro.auth.cas;

import cz.kpsys.portaro.auth.process.AuthenticationRequest;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CasAuthenticationRequest extends UsernamePasswordAuthenticationToken implements AuthenticationRequest<String> {

    @NonNull @Getter Department department;
    @NonNull @Getter String serviceUrl;

    public CasAuthenticationRequest(@NonNull Object principal, @NonNull String ticket, @NonNull Department department, @NonNull String serviceUrl) {
        super(principal, ticket);
        this.department = department;
        this.serviceUrl = serviceUrl;
    }

    @Override
    public String getCredentials() {
        return (String) super.getCredentials();
    }

    public String getTicket() {
        return (String) super.getCredentials();
    }
}
