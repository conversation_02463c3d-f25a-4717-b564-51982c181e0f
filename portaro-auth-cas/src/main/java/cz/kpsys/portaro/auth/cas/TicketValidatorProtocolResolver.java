package cz.kpsys.portaro.auth.cas;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apereo.cas.client.validation.Cas20ServiceTicketValidator;
import org.apereo.cas.client.validation.TicketValidator;

import java.util.function.BiFunction;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class TicketValidatorProtocolResolver implements BiFunction<Department, String, TicketValidator> {

    private static final int CAS_TICKET_VALIDATOR_TOLERANCE = 10800000;

    @NonNull ContextualProvider<Department, @NonNull CasTicketValidatorProtocol> protocolProvider;
    @NonNull String casServiceParameterName;

    @Override
    public TicketValidator apply(Department currentDepartment, String casSystemUrl) {
        CasTicketValidatorProtocol protocol = protocolProvider.getOn(currentDepartment);
        return switch (protocol) {
            case SAML11 -> new Saml11TicketValidator(casSystemUrl, casServiceParameterName, CAS_TICKET_VALIDATOR_TOLERANCE);
            case CAS20 -> new Cas20ServiceTicketValidator(casSystemUrl);
        };
    }
}
