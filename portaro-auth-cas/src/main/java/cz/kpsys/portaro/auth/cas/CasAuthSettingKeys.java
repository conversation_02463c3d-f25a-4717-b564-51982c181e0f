package cz.kpsys.portaro.auth.cas;

import cz.kpsys.portaro.setting.SettingKey;
import lombok.NonNull;

public class CasAuthSettingKeys {

    public static final String SECTION_AUTH_CAS = "auth.cas";

    public static final SettingKey<@NonNull Boolean> CAS_ENABLED = new SettingKey<>(SECTION_AUTH_CAS, "enabled");
    public static final SettingKey<String> CAS_SYSTEM_URL = new SettingKey<>(SECTION_AUTH_CAS, "systemUrl");
    public static final SettingKey<String> CAS_LOGIN_URL = new SettingKey<>(SECTION_AUTH_CAS, "loginUrl");
    public static final SettingKey<@NonNull CasTicketValidatorProtocol> CAS_TICKET_VALIDATOR_PROTOCOL = new SettingKey<>(SECTION_AUTH_CAS, "ticketValidatorProtocol");
    public static final SettingKey<@NonNull String> CAS_USER_IDENTIFIER = new SettingKey<>(SECTION_AUTH_CAS, "userIdentifier");
    public static final SettingKey<@NonNull String> CAS_USER_ASSERTION_ATTRIBUTE = new SettingKey<>(SECTION_AUTH_CAS, "userAssertionAttribute");

}
