package cz.kpsys.portaro.auth.cas;

import cz.kpsys.portaro.auth.Authenticator;
import cz.kpsys.portaro.auth.MoreThanOneUserWithGivenIdentifierException;
import cz.kpsys.portaro.auth.external.ExternallyAuthenticatedUserIsNotInLocalException;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.MoreThanOneItemFoundException;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserByContextualIdentifierLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apereo.cas.client.validation.Assertion;
import org.apereo.cas.client.validation.TicketValidationException;
import org.apereo.cas.client.validation.TicketValidator;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;

import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CasAuthenticator implements Authenticator<CasAuthenticationRequest, CasSuccessAuthentication> {

    public static final String PRINCIPAL_ASSERTION_ATTRIBUTE = "assertionUserName";

    @NonNull ContextualProvider<Department, @NonNull TicketValidator> ticketValidatorProvider;
    @NonNull ContextualProvider<Department, @NonNull String> userAssertionAttribute;
    @NonNull UserByContextualIdentifierLoader<User> userByCasIdentifierLoader;


    @Override
    public CasSuccessAuthentication authenticate(CasAuthenticationRequest authRequest) throws AuthenticationException {
        if (StringUtil.isNullOrEmpty(authRequest.getTicket())) {
            throw new BadCredentialsException("Failed to provide a CAS service ticket to validate");
        }

        Assertion assertion = verifyAndGetAssertion(authRequest);
        User user = getUser(authRequest, assertion);
        CasSuccessAuthentication successAuth = new CasSuccessAuthentication(assertion, user, authRequest.getDepartment());
        successAuth.setDetails(authRequest.getDetails());
        return successAuth;
    }


    private Assertion verifyAndGetAssertion(CasAuthenticationRequest authRequest) {
        try {
            TicketValidator ticketValidator = ticketValidatorProvider.getOn(authRequest.getDepartment());
            return ticketValidator.validate(authRequest.getTicket(), authRequest.getServiceUrl());
        } catch (TicketValidationException e) {
            throw new BadCredentialsException(e.getMessage(), e);
        }
    }


    private User getUser(CasAuthenticationRequest authRequest, Assertion assertion) {
        String identifier = getIdentifierFromAssertion(authRequest, assertion);
        try {
            return userByCasIdentifierLoader.getByCtxAndIdentifier(authRequest.getDepartment(), identifier)
                    .orElseThrow(() -> new ExternallyAuthenticatedUserIsNotInLocalException(identifier, false));
        } catch (MoreThanOneItemFoundException e) {
            throw new MoreThanOneUserWithGivenIdentifierException(identifier, e);
        }
    }


    private String getIdentifierFromAssertion(CasAuthenticationRequest authRequest, Assertion assertion) {
        String attribute = userAssertionAttribute.getOn(authRequest.getDepartment());
        if (attribute.equals(PRINCIPAL_ASSERTION_ATTRIBUTE)) {
            return assertion.getPrincipal().getName();
        }
        Object obj = Objects.requireNonNull(assertion.getPrincipal().getAttributes().get(attribute), () -> "CAS assertion did not contain attribute %s".formatted(attribute));
        return String.valueOf(obj);
    }

}
