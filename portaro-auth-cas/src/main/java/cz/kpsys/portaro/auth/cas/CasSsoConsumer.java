package cz.kpsys.portaro.auth.cas;

import cz.kpsys.portaro.auth.AuthenticationException;
import cz.kpsys.portaro.auth.external.SsoConsumer;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.web.UrlCreator;
import cz.kpsys.portaro.commons.web.WebResolver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.web.DoubleWrappedReturnToUrlResolver;
import cz.kpsys.portaro.web.UrlParameterReadingReturnToUrlResolver;
import cz.kpsys.portaro.web.UrlWebResolver;
import cz.kpsys.portaro.web.server.ServerUrlConfiguration;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CasSsoConsumer implements SsoConsumer<CasAuthenticationRequest> {

    private static final String TICKET_PARAMETER_NAME = "ticket";
    public static final String CAS_STATEFUL_IDENTIFIER = "_cas_stateful_";

    @NonNull String serviceParameterName;
    @NonNull ContextualProvider<Department, @NotBlank String> casLoginUrlProvider;
    @NonNull UrlWebResolver returnToUrlResolver;
    @NonNull UrlWebResolver serverUrlWebResolver;
    @NonNull WebResolver<Department> currentDepartmentWebResolver;

    public CasSsoConsumer(@NonNull String serviceParameterName,
                          @NonNull ContextualProvider<Department, @NotBlank String> casLoginUrlProvider,
                          @NonNull UrlWebResolver serverUrlWebResolver,
                          @NonNull String returnPath,
                          @NonNull String returnToUrlParameterName,
                          @NonNull ServerUrlConfiguration serverUrlConfiguration,
                          @NonNull WebResolver<Department> currentDepartmentWebResolver) {
        this.serviceParameterName = serviceParameterName;
        this.casLoginUrlProvider = casLoginUrlProvider;
        this.currentDepartmentWebResolver = currentDepartmentWebResolver;
        this.serverUrlWebResolver = serverUrlWebResolver;
        this.returnToUrlResolver = new DoubleWrappedReturnToUrlResolver(
                serverUrlWebResolver,
                returnPath,
                StaticProvider.of(returnToUrlParameterName),
                new UrlParameterReadingReturnToUrlResolver(returnToUrlParameterName, serverUrlConfiguration)
        );
    }


    @Override
    public boolean containsExternalAuthRequest(HttpServletRequest request) {
        return StringUtil.hasLength(request.getParameter(TICKET_PARAMETER_NAME));
    }


    @Override
    public String createSSORedirectUrl(HttpServletRequest request) throws AuthenticationException {
        Department department = currentDepartmentWebResolver.resolve(request);
        return new UrlCreator()
                .serverBaseUrl(casLoginUrlProvider.getOn(department))
                .addParameter(serviceParameterName, returnToUrlResolver.resolve(request))
                .build();
    }


    @Override
    public CasAuthenticationRequest createAuthenticationRequest(HttpServletRequest request) {
        String ticket = request.getParameter(TICKET_PARAMETER_NAME);
        Department department = currentDepartmentWebResolver.resolve(request);
        String serviceUrl = getServiceUrl(request);

        log.debug("Supplied CAS ticket is {}, service is {}", ticket, serviceUrl);

        return new CasAuthenticationRequest(CAS_STATEFUL_IDENTIFIER, ticket, department, serviceUrl);
    }

    private String getServiceUrl(HttpServletRequest request) {
        String serverUrl = serverUrlWebResolver.resolve(request);
        return UrlCreator.byRequest(request, serverUrl, "")
                .removeParameters(TICKET_PARAMETER_NAME)
                .build();
    }


}
