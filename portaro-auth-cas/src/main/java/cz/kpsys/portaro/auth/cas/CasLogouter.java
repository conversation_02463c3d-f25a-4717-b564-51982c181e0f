package cz.kpsys.portaro.auth.cas;

import cz.kpsys.portaro.auth.composite.CompositeSuccessAuthentication;
import cz.kpsys.portaro.auth.logout.Logouter;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.web.UrlCreator;
import cz.kpsys.portaro.commons.web.WebResolver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.web.UrlWebResolver;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.io.IOException;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CasLogouter implements Logouter {

    @NonNull String serviceParameterName;
    @NonNull WebResolver<Department> currentDepartmentWebResolver;
    @NonNull ContextualProvider<Department, @NonNull String> casSystemUrlProvider;
    @NonNull UrlWebResolver serverUrlWebResolver;

    @Override
    public void logout(HttpServletRequest request, HttpServletResponse response, CompositeSuccessAuthentication authentication) {
        if (!wasAuthenticatedByCas(authentication)) {
            return;
        }

        Department currentDepartment = currentDepartmentWebResolver.resolve(request);

        String logoutUrl = new UrlCreator()
                .serverBaseUrl(casSystemUrlProvider.getOn(currentDepartment))
                .path("logout")
                .addParameter(serviceParameterName, serverUrlWebResolver.resolve(request))
                .build();
        try {
            response.sendRedirect(logoutUrl);
        } catch (IOException ex) {
            throw new RuntimeException(String.format("Error while sending logout redirect to %s", logoutUrl), ex);
        }
    }

    private boolean wasAuthenticatedByCas(CompositeSuccessAuthentication authentication) {
        return authentication.getAuths().stream().anyMatch(auth -> auth instanceof CasSuccessAuthentication);
    }
}
