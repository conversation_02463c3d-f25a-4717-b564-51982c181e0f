package cz.kpsys.portaro.search.restriction.deserialize.json;

import com.fasterxml.jackson.databind.module.SimpleModule;
import cz.kpsys.portaro.commons.convert.IdToObjectConverter;
import cz.kpsys.portaro.commons.json.FromStringConvertingJsonDeserializer;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.commons.object.repo.InMemoryRepository;
import cz.kpsys.portaro.datatype.DatatypableStringConverter;
import cz.kpsys.portaro.datatype.Datatype;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.search.field.BasicSearchField;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.Disjunction;
import cz.kpsys.portaro.search.restriction.Junction;
import cz.kpsys.portaro.search.restriction.Restriction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.convert.RestrictionDeserializerTestBase;
import cz.kpsys.portaro.search.restriction.convert.RestrictionDeserializerWithSearchFields;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.core.convert.ConversionFailedException;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Tag("ci")
@Tag("unit")
public class RestrictionDeserializerPostModifyingDecoratorTest extends RestrictionDeserializerTestBase {

    protected static final SearchField FIELD_1 = BasicSearchField.testing("whatever");

    @Override
    protected void configureModule(SimpleModule module) {
        InMemoryRepository<SearchField, String> searchFieldMemory = InMemoryRepository.ofIdentified();
        searchFieldMemory.save(FIELD_1);
        module.addDeserializer(SearchField.class, new FromStringConvertingJsonDeserializer<>(SearchField.class, new IdToObjectConverter<>(searchFieldMemory)));

        module.addDeserializer(Restriction.class, new RestrictionDeserializerPostModifyingDecorator(
                new RestrictionDeserializerWithSearchFields(),
                new DatatypableStringConverter() {
                    @Override
                    public Object convertFromString(String stringValue, Datatype datatype) throws ConversionFailedException {
                        throw new UnsupportedOperationException();
                    }

                    @Override
                    public Object convertFromSimpleTypePreservingStructure(Object simpleValue, ScalarDatatype datatype) {
                        return StaticProvider.of(simpleValue);
                    }

                    @Override
                    public String convertToString(Object value) {
                        throw new UnsupportedOperationException();
                    }
                },
                _ -> Optional.empty()));
    }

    @Test
    public void shoudPostModifyOneEqMatcher() throws Exception {
        Junction<?> deserialized = (Junction<?>) deserialize("""
                {
                    "and": [
                        {
                            "field": "whatever",
                            "eq": {"value": 2}
                        }
                    ]
                }
                """);

        assertEquals(1, deserialized.getItems().size());
        assertEquals(FIELD_1, ((Term<?>) deserialized.getItems().get(0)).field());
        assertEquals(2, ((Eq) ((Term<?>) deserialized.getItems().get(0)).matcher()).value());
    }


    @Test
    public void shoudPostModifyOneEqMatcherInNestedConjunction() throws Exception {
        Junction<?> deserialized = (Junction<?>) deserialize("""
                {
                    "and": [{
                        "or": [{
                            "field": "whatever",
                            "eq": {"value": 2}
                        }]
                    }]
                }
                """);

        assertEquals(1, deserialized.getItems().size());
        assertEquals(1, ((Disjunction<?>) deserialized.getItems().get(0)).getItems().size());
        assertEquals(2, ((Eq) ((Term<?>) ((Disjunction<?>) deserialized.getItems().get(0)).getItems().get(0)).matcher()).value());
    }

}