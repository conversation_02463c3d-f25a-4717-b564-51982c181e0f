package cz.kpsys.portaro.erp.workattendance;

import cz.kpsys.portaro.commons.date.DatetimeRange;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.EmploymentStatus.WeeklyCommitment;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RangedWorkCommitmentByUserRecordIdLoader {

    @NonNull RangedEmploymentStatusByUserRecordIdLoader rangedEmploymentStatusByUserRecordIdLoader;

    public WorkCommitment load(@NonNull UUID userRecordId, @NonNull DatetimeRange period, @NonNull Department ctx) {
        List<Record> salaries = rangedEmploymentStatusByUserRecordIdLoader.load(userRecordId, period, ctx);

        if (salaries.size() == 1) {
            return WorkCommitment.fromWeeklyHours(salaries
                    .getFirst()
                    .getFirst(WeeklyCommitment.Hours.FIELD_FINDER)
                    .doubleValue());
        }

        var distinctWeeklyHours = salaries.stream()
                .map(salary -> salary.getFirst(WeeklyCommitment.Hours.FIELD_FINDER))
                .collect(Collectors.toSet());
        if (distinctWeeklyHours.size() == 1) {
            var it = distinctWeeklyHours.iterator();
            if (it.hasNext()) {
                return WorkCommitment.fromWeeklyHours(it.next().doubleValue());
            }
        }

        throw new IllegalArgumentException("Expected exactly one weekly hours information in range %s, but got: %s".formatted(period, salaries));
    }

}
