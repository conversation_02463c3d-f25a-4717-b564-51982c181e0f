package cz.kpsys.portaro.erp.workattendance;

import cz.kpsys.portaro.commons.date.DatetimeRange;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.EmploymentStatus.Validity.ValidFrom;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.EmploymentStatus.Validity.ValidTo;
import cz.kpsys.portaro.erp.job.LinkedRecordSearchLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.search.restriction.FieldTypedSearchFieldParsing;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Lt;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.ZoneId;
import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RangedEmploymentStatusByUserRecordIdLoader {

    @NonNull LinkedRecordSearchLoader employmentStatusByUserIdSearchLoader;
    @NonNull Provider<ZoneId> timeZoneProvider;

    public List<Record> load(@NonNull UUID userRecordId, @NonNull DatetimeRange period, @NonNull Department ctx) {
        ZoneId zoneId = timeZoneProvider.get();
        return employmentStatusByUserIdSearchLoader.loadByTargetRecordId(
                        userRecordId,
                        List.of(new Term<>(FieldTypedSearchFieldParsing.ofValue(ValidFrom.TYPE_ID).toSearchField(), new Lt(period.toDate()))),
                        ctx
                ).stream()
                .filter(jobFile -> jobFile.findFirst(ValidTo.FIELD_FINDER)
                        .map(toDate -> toDate.atStartOfDay(zoneId).toInstant().compareTo(period.fromDate()) >= 0)
                        .orElse(true) // If end validity of job file is not present = is current valid
                )
                .toList();
    }

}
