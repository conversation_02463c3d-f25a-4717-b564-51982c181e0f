dependencies {
    testCompileOnly("org.projectlombok:lombok:+")
    testAnnotationProcessor("org.projectlombok:lombok:+")

    testImplementation(project(":portaro-test-environment"))
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.mockito:mockito-core:+")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-acquisition"))
    implementation(project(":portaro-appserver"))
    implementation(project(":portaro-auth"))
    implementation(project(":portaro-catalog"))
    implementation(project(":portaro-catalog-web"))
    implementation(project(":portaro-commons"))
    implementation(project(":portaro-commons-db"))
    implementation(project(":portaro-commons-pdf"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-database-structure"))
    implementation(project(":portaro-exemplar"))
    implementation(project(":portaro-export"))
    implementation(project(":portaro-file"))
    implementation(project(":portaro-finance"))
    implementation(project(":portaro-form-annotation"))
    implementation(project(":portaro-record"))
    implementation(project(":portaro-security"))
    implementation(project(":portaro-user"))
    implementation(project(":portaro-web"))
    implementation(project(":portaro-sql-generator"))

    implementation("org.springframework:spring-context:6.+")
    implementation("org.springframework:spring-jdbc:6.+")
    implementation("org.springframework:spring-web:6.+")
    implementation("org.springframework.data:spring-data-jpa:3.+")

    implementation("org.slf4j:slf4j-api:+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
    implementation("jakarta.validation:jakarta.validation-api:3.+")
    implementation("com.itextpdf:itext-core:9.+")
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.+")
}
