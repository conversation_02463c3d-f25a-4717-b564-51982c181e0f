package cz.kpsys.portaro.ext.sutin.internaldatatypes;

import cz.kpsys.portaro.ext.sutin.internaldatatypes.contacts.AdresaWithType;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.contacts.Kontakt;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.*;

import java.util.List;

public record SutinWorkerResponse(

        String elementId,

        List<PersonDto> employeeData,

        List<WorkCatalogLinkDto> workCatalogLinks,

        List<SalaryDto> salaries,

        List<MonthAttendanceDto> monthlyAttendances,

        List<AdresaWithType> addresses,

        List<Kontakt> contacts
) {}
