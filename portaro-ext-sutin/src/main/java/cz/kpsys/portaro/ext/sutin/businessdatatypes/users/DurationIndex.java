package cz.kpsys.portaro.ext.sutin.businessdatatypes.users;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/// Class for data pairing based on intervals of validity.
///
/// Fill index via {@link #add(T)} and then use {@link #getValidThrough} to check which
/// items in index were valid in given range.
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class DurationIndex<T extends TimeValidited> {

    public static <T extends TimeValidited> DurationIndex<T> of(List<T> items) {
        var newIndex = new DurationIndex<T>();
        for (T item: items) {
            newIndex.add(item);
        }
        return newIndex;
    }

    @NonNull List<T> items = new ArrayList<>();

    public void add(@NonNull T item) {
        logCollidingInterval(item);
        items.add(item);
        //items.sort(Comparator.comparing(ValiditedItem::validFrom)); // TODO: inefficient, but quickly implemented
    }

    public @NonNull List<T> getAll() {
        return Collections.unmodifiableList(items);
    }

    public @NonNull List<T> getValidThrough(@NonNull LocalDate from, @NonNull LocalDate to) {
        var found = new ArrayList<T>();
        for (T item : items) {
            if ((item.validFrom().isBefore(to) || item.validFrom().isEqual(to))
                    && (item.validTo().isAfter(from) || item.validTo().isEqual(from))) {
                found.add(item);
            }
        }
        return found;
    }

    public @NonNull List<T> getValidThrough(@NonNull YearMonth month) {
        return getValidThrough(month.atDay(1), month.atEndOfMonth());
    }

    public @NonNull List<T> getValidThrough(@NonNull TimeValidited timeValidited) {
        return getValidThrough(timeValidited.validFrom(), timeValidited.validTo());
    }

    private void logCollidingInterval(T added) {
        var collisions = getValidThrough(added.validFrom(), added.validTo());
        if (!collisions.isEmpty()) {
            log.warn("Added item {} collides with already existing intervals: {}!", added, collisions);
        }
    }

    @Override
    public String toString() {
        return "DurationIndex{" +
                "items=" + items +
                '}';
    }
}
