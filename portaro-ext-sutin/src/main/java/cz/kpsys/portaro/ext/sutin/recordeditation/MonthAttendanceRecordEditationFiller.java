package cz.kpsys.portaro.ext.sutin.recordeditation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.MonthlyAttendanceFond.*;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.SalaryLink;
import cz.kpsys.portaro.ext.sutin.SutinUserLoader;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.DataWithRecord;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.MonthAttendance;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.SalaryData;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.value.DetailedRecordValueCommand;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Objects;

import static cz.kpsys.portaro.erp.RecordSutinSpecificFields.RowId;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class MonthAttendanceRecordEditationFiller {

    @NonNull RecordEditationHelper recordEditationHelper;

    public RecordEditation fill(@NonNull MonthAttendance monthAttendance,
                                @NonNull RecordEditation recordEditation,
                                @NonNull DataWithRecord<SalaryData> salary,
                                @NonNull Department ctx,
                                @NonNull UserAuthentication currentAuth) {

        recordEditationHelper.setStringTopFieldValue(monthAttendance.rowId(), RowId.TYPE_ID, recordEditation, ctx, currentAuth);

        Record salaryRecord = Objects.requireNonNull(salary.getRecord());
        DetailedRecordValueCommand userRecordLinkCmd = new DetailedRecordValueCommand(salaryRecord, ctx, currentAuth);
        recordEditationHelper.setRecordIdSubfieldValue(userRecordLinkCmd, true, SalaryLink.TYPE_ID, true, SalaryLink.Main.TYPE_ID, recordEditation);

        recordEditationHelper.setStringSubfieldValue(monthAttendance.state().getPortaroVal(), true, ApprovementState.TYPE_ID, true, ApprovementState.State.TYPE_ID, recordEditation, ctx, currentAuth);

        recordEditationHelper.setStringSubfieldValue(monthAttendance.yearMonth(), true, Period.TYPE_ID, true, Period.Id.TYPE_ID, recordEditation, ctx, currentAuth);

        if (monthAttendance.approvedWorkdayOvertime() != null) {
            recordEditationHelper.setNumberSubfieldValue(BigDecimal.valueOf(monthAttendance.approvedWorkdayOvertime()), true, ApprovedOvertime.TYPE_ID, true, ApprovedOvertime.Workday.TYPE_ID, recordEditation, ctx, currentAuth);
        }
        if (monthAttendance.approvedWeekendOvertime() != null) {
            recordEditationHelper.setNumberSubfieldValue(BigDecimal.valueOf(monthAttendance.approvedWeekendOvertime()), true, ApprovedOvertime.TYPE_ID, true, ApprovedOvertime.Weekend.TYPE_ID, recordEditation, ctx, currentAuth);
        }
        if (monthAttendance.approvedBankOvertime() != null) {
            recordEditationHelper.setNumberSubfieldValue(BigDecimal.valueOf(monthAttendance.approvedBankOvertime()), true, ApprovedOvertime.TYPE_ID, true, ApprovedOvertime.Bank.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (monthAttendance.onCallDays() != null) {
            recordEditationHelper.setNumberSubfieldValue(BigDecimal.valueOf(monthAttendance.onCallDays()), true, OnCall.TYPE_ID, true, OnCall.Days.TYPE_ID, recordEditation, ctx, currentAuth);
        }
        if (monthAttendance.onCallWeeks() != null) {
            recordEditationHelper.setNumberSubfieldValue(BigDecimal.valueOf(monthAttendance.onCallWeeks()), true, OnCall.TYPE_ID, true, OnCall.Weeks.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (monthAttendance.overtimeCompensation() != null) {
            recordEditationHelper.setStringSubfieldValue(monthAttendance.overtimeCompensation().getPortaroVal(), true, Compensation.TYPE_ID, true, Compensation.Overtime.TYPE_ID, recordEditation, ctx, currentAuth);
        }
        if (monthAttendance.onCallCompensation() != null) {
            recordEditationHelper.setStringSubfieldValue(monthAttendance.onCallCompensation().getPortaroVal(), true, Compensation.TYPE_ID, true, Compensation.OnCall.TYPE_ID, recordEditation, ctx, currentAuth);
        }
        if (monthAttendance.idpCompensation() != null) {
            recordEditationHelper.setStringSubfieldValue(monthAttendance.idpCompensation().getPortaroVal(), true, Compensation.TYPE_ID, true, Compensation.Idp.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (monthAttendance.note() != null) {
            recordEditationHelper.setStringSubfieldValue(monthAttendance.note(), true, Note.TYPE_ID, true, Note.Text.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        return recordEditation;
    }

    public RecordEditation fillPlaceholder(@NonNull MonthAttendance monthAttendance,
                                           @NonNull RecordEditation recordEditation,
                                           @NonNull DataWithRecord<SalaryData> salary,
                                           @NonNull Department ctx,
                                           @NonNull UserAuthentication currentAuth) {

        recordEditationHelper.setStringTopFieldValue(SutinUserLoader.monthlyAttendancePlaceholderId(monthAttendance, salary), RowId.TYPE_ID, recordEditation, ctx, currentAuth);

        Record salaryRecord = Objects.requireNonNull(salary.getRecord());
        DetailedRecordValueCommand userRecordLinkCmd = new DetailedRecordValueCommand(salaryRecord, ctx, currentAuth);
        recordEditationHelper.setRecordIdSubfieldValue(userRecordLinkCmd, true, SalaryLink.TYPE_ID, true, SalaryLink.Main.TYPE_ID, recordEditation);

        recordEditationHelper.setStringSubfieldValue(monthAttendance.state().getPortaroVal(), true, ApprovementState.TYPE_ID, true, ApprovementState.State.TYPE_ID, recordEditation, ctx, currentAuth);

        recordEditationHelper.setStringSubfieldValue(monthAttendance.yearMonth(), true, Period.TYPE_ID, true, Period.Id.TYPE_ID, recordEditation, ctx, currentAuth);

        return recordEditation;
    }

}
