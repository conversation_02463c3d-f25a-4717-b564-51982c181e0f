package cz.kpsys.portaro.ext.sutin.recordeditation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.JobData;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class JobFileRecordEditationCreator {

    @NonNull JobFileRecordEditationFiller jobFileRecordEditationFiller;
    @NonNull Provider<@NonNull Fond> jobFileFondProvider;
    @NonNull RecordEditationFactory recordEditationFactory;

    public RecordEditation ofNewRecord(@NonNull JobData jobData,
                                       @NonNull Record userRecord,
                                       @NonNull List<Department> holdingDepartments,
                                       @NonNull Department ctx,
                                       @NonNull UserAuthentication currentAuth) {
        Fond fond = jobFileFondProvider.get();

        RecordEditation recordEditation = recordEditationFactory
                .withHoldingsWithoutCtx(holdingDepartments, ctx)
                .ofNew(fond)
                .build(currentAuth);

        return jobFileRecordEditationFiller.fill(jobData, recordEditation, userRecord, ctx, currentAuth);
    }

    public RecordEditation ofExistingRecord(@NonNull Record jobFileRecord,
                                            @NonNull JobData jobData,
                                            @NonNull Record userRecord,
                                            @NonNull List<Department> holdingDepartments,
                                            @NonNull Department ctx,
                                            @NonNull UserAuthentication currentAuth) {
        RecordEditation recordEditation = recordEditationFactory
                .withHoldingsWithoutCtx(holdingDepartments, ctx)
                .ofExisting(jobFileRecord)
                .build(currentAuth);

        return jobFileRecordEditationFiller.fill(jobData, recordEditation, userRecord, ctx, currentAuth);
    }

}
