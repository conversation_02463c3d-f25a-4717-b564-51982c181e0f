package cz.kpsys.portaro.ext.sutin.businessdatatypes.users;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.*;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.time.LocalDate;

public record JobData(

        @NonNull String rowId,


        String personalNumber,


        LocalDate birthDate,

        @NullableNotBlank String birthPlace,

        @NullableNotBlank String personalIdentificationNumber,

        @Nullable PracVzdelaniKat educationLevel,

        @NullableNotBlank String educationInstitution,

        @Nullable PracStatniPrislusnostKat nationality,

        @Nullable Gender gender,


        LocalDate contractDate,

        LocalDate beginDate,

        LocalDate testTime,

        LocalDate endDate,

        @Nullable PracSmlouvaTypKat contractType,

        // PracPoziceKat
        Integer workPositionId,

        @Nullable PracKategorieKat workCategory,

        PracVypovedStranaKat employmentTerminationSide,

        PracVypovedTypKat employmentTerminationType,

        String employmentTerminationReason,

        @NullableNotBlank String identityCardNumber,

        LocalDate identityCardValidity,

        @Nullable PracMistoPraceKat jobPlace,

        String superiorPerson,

        String employerId,

        String vat,

        Integer employmentOrder,

        String identificationCardNumber,

        @Nullable LocalDate jobFileValidFrom,

        LocalDate jobFileValidTo,

        String divisionId,

        Character valid,

        PracZdravPojistovnaKat healthInsuranceCode,

        PracInvaliditaKat handicap,

        LocalDate maternityBeginDate,

        LocalDate maternityEndDate,

        LocalDate parentalBeginDate,

        LocalDate parentalEndDate,

        Integer claimedChildren,

        Integer unclaimedChildren,

        Exekuce foreclosure,

        LocalDate workPlacementEndDate

) implements TimeValidited {

    public static JobData fromPracovniPomer(PersonDto personDto, String superiorPerson) {
        return new JobData(
                personDto.rowId(),
                personDto.personalNumber(),
                personDto.birthDate(),
                personDto.birthPlace(),
                personDto.personalIdentificationNumber(),
                personDto.educationLevel(),
                personDto.educationInstitution(),
                personDto.nationality(),
                personDto.gender(),
                personDto.contractDate(),
                personDto.jobBeginDate(),
                personDto.testTime(),
                personDto.jobEndDate(),
                personDto.contractType(),
                personDto.workPositionId(),
                personDto.workCategory(),
                personDto.employmentTerminationSide(),
                personDto.employmentTerminationType(),
                personDto.employmentTerminationReason(),
                personDto.identityCardNumber(),
                personDto.identityCardValidity(),
                personDto.jobPlace(),
                superiorPerson,
                personDto.employerId(),
                personDto.vat(),
                personDto.employmentOrder(),
                personDto.identificationCardNumber(),
                personDto.jobFileValidFrom(),
                personDto.jobFileValidTo(),
                personDto.divisionId(),
                personDto.valid(),
                personDto.healthInsuranceCode(),
                personDto.handicap(),
                personDto.maternityBeginDate(),
                personDto.maternityEndDate(),
                personDto.parentalBeginDate(),
                personDto.parentalEndDate(),
                personDto.claimedChildren(),
                personDto.unclaimedChildren(),
                personDto.foreclosure(),
                personDto.workPlacementEndDate()
        );
    }

    public boolean isValid() {
        return valid != null && valid.equals('A');
    }

    @Override
    public @NonNull LocalDate validFrom() {
        return (jobFileValidFrom != null) ? jobFileValidFrom : LocalDate.ofYearDay(1990, 1);
    }

    @Override
    public @NonNull LocalDate validTo() {
        return jobFileValidTo;
    }
}
