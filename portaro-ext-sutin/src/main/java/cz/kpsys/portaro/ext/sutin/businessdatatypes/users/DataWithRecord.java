package cz.kpsys.portaro.ext.sutin.businessdatatypes.users;

import cz.kpsys.portaro.record.Record;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.jspecify.annotations.Nullable;

import java.time.LocalDate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public class DataWithRecord<T extends TimeValidited> implements TimeValidited {

    @NonNull T data;

    @NonFinal
    @Nullable
    @Setter
    Record record;

    @Override
    public @NonNull LocalDate validFrom() {
        return data.validFrom();
    }

    @Override
    public @NonNull LocalDate validTo() {
        return data.validTo();
    }

    @Override
    public String toString() {
        return "DataWithRecord{" +
                "data=" + data +
                ", record=" + record +
                '}';
    }
}
