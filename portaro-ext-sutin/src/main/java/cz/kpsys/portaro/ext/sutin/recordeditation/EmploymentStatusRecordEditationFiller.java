package cz.kpsys.portaro.ext.sutin.recordeditation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.cache.CacheDeletableById;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.EmploymentStatus;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.PersonLink;
import cz.kpsys.portaro.ext.sutin.SutinContants;
import cz.kpsys.portaro.ext.sutin.SutinRecordDataLoader;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.JobData;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.value.DetailedRecordValueCommand;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

import static cz.kpsys.portaro.erp.RecordSutinSpecificFields.EmploymentStatus.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class EmploymentStatusRecordEditationFiller {

    @NonNull SutinRecordDataLoader sutinRecordDataLoader;
    @NonNull ByIdLoadable<cz.kpsys.portaro.record.Record, UUID> recordLoader;
    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull CacheDeletableById recordCache;
    @NonNull Provider<@NonNull Fond> companyFondProvider;
    @NonNull Provider<@NonNull Fond> workPositionFondProvider;
    @NonNull Provider<@NonNull Fond> divisionFondProvider;

    public RecordEditation fill(@NonNull JobData jobData,
                                @NonNull RecordEditation recordEditation,
                                @NonNull Record userRecord,
                                @NonNull BigDecimal weeklyHours,
                                @NonNull Department ctx,
                                @NonNull UserAuthentication currentAuth) {

        recordEditationHelper.setStringTopFieldValue(jobData.rowId(), RecordSutinSpecificFields.RowId.TYPE_ID, recordEditation, ctx, currentAuth);

        DetailedRecordValueCommand userRecordLinkCmd = new DetailedRecordValueCommand(userRecord, ctx, currentAuth);
        recordEditationHelper.setRecordIdSubfieldValue(userRecordLinkCmd, true, PersonLink.TYPE_ID, true, PersonLink.Main.TYPE_ID, recordEditation);


        if (jobData.personalNumber() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.personalNumber(), true, EmploymentData.TYPE_ID, true, EmploymentData.PersonalNumber.TYPE_ID, recordEditation, ctx, currentAuth);
        }


        setDay(jobData.beginDate(), EmploymentStatus.JobData.JobBeginDate.TYPE_ID, recordEditation, ctx, currentAuth);
        setDay(jobData.testTime(), EmploymentStatus.JobData.TestTimeEndDate.TYPE_ID, recordEditation, ctx, currentAuth);
        setDay(jobData.endDate(), EmploymentStatus.JobData.JobEndDate.TYPE_ID, recordEditation, ctx, currentAuth);

        if (jobData.contractType() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.contractType().portaroVal(), true, EmploymentStatus.JobData.TYPE_ID, true, EmploymentStatus.JobData.ContractType.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        setDay(jobData.contractDate(), EmploymentStatus.JobData.ContractDate.TYPE_ID, recordEditation, ctx, currentAuth);

        // TODO: remove? Is it not necessary?
        //if (jobFileData.vat() != null) {
        //    recordEditationHelper.setStringSubfieldValue(jobFileData.vat(), true, RecordSutinSpecificFields.EmploymentStatus.JobData.TYPE_ID, true, RecordSutinSpecificFields.EmploymentStatus.JobData.VAT.TYPE_ID, recordEditation, ctx, currentAuth);
        //}

        if (jobData.workCategory() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.workCategory().portaroVal(), true, EmploymentStatus.JobData.TYPE_ID, true, EmploymentStatus.JobData.Category.TYPE_ID, recordEditation, ctx, currentAuth);
        }


        Optional<UUID> workPositionId = sutinRecordDataLoader.getRecordIdByExternalId(jobData.workPositionId().toString(), RecordSutinSpecificFields.RowId.CODE, workPositionFondProvider.get().getId());
        if (workPositionId.isPresent()) {
            DetailedRecordValueCommand cmd = new DetailedRecordValueCommand(recordLoader.getById(workPositionId.get()), ctx, currentAuth);
            recordEditationHelper.setRecordIdSubfieldValue(cmd, true, WorkPosition.TYPE_ID, true, WorkPosition.Name.TYPE_ID, recordEditation);
            recordCache.deleteFromCacheById(workPositionId.get());
        } else {
            log.error("Cannot find work position for {}", jobData);
        }


        // Job termination
        if (jobData.employmentTerminationSide() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.employmentTerminationSide().getPortaroVal(), true, EmploymentTermination.TYPE_ID, true, EmploymentTermination.InitiatorSide.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.employmentTerminationType() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.employmentTerminationType().getPortaroVal(), true, EmploymentTermination.TYPE_ID, true, EmploymentTermination.Type.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (jobData.employmentTerminationReason() != null) {
            recordEditationHelper.setStringSubfieldValue(jobData.employmentTerminationReason(), true, EmploymentTermination.TYPE_ID, true, EmploymentTermination.Reason.TYPE_ID, recordEditation, ctx, currentAuth);
        }


        recordEditationHelper.setBooleanSubfieldValue(jobData.isValid(), true, Validity.TYPE_ID, true, Validity.Valid.TYPE_ID, recordEditation, ctx, currentAuth);
        setDay(jobData.jobFileValidFrom(), Validity.ValidFrom.TYPE_ID, recordEditation, ctx, currentAuth);
        setDay(jobData.jobFileValidTo(), Validity.ValidTo.TYPE_ID, recordEditation, ctx, currentAuth);


        // Employer
        if (jobData.employerId() != null) {
            Optional<UUID> employerId = sutinRecordDataLoader.getRecordIdByExternalId(jobData.employerId(), SutinContants.ELEMENT_ID_FIELD_CODE);
            if (employerId.isPresent()) {
                DetailedRecordValueCommand cmd = new DetailedRecordValueCommand(recordLoader.getById(employerId.get()), ctx, currentAuth);
                recordEditationHelper.setRecordIdSubfieldValue(cmd, true, Employer.TYPE_ID, true, Employer.Name.TYPE_ID, recordEditation);
                recordCache.deleteFromCacheById(employerId.get());
            } else {
                log.error("Company from fond {} was not found by ID {} = {}!", companyFondProvider.get().getId(), SutinContants.ELEMENT_ID_FIELD_CODE, jobData.employerId());
            }
        }


        // Division
        if (jobData.divisionId() != null) {
            Optional<UUID> divisionId = sutinRecordDataLoader.getRecordIdByExternalId(jobData.divisionId(), SutinContants.ELEMENT_ID_FIELD_CODE);
            if (divisionId.isPresent()) {
                // TODO: getById je tu mega pomalý
                DetailedRecordValueCommand cmd = new DetailedRecordValueCommand(recordLoader.getById(divisionId.get()), ctx, currentAuth);
                recordEditationHelper.setRecordIdSubfieldValue(cmd, true, Division.TYPE_ID, true, Division.Name.TYPE_ID, recordEditation);
                //recordCache.deleteFromCacheById(divisionId.get());
            } else {
                log.error("Division from fond {} was not found by ID {} = {}!", divisionFondProvider.get().getId(), SutinContants.ELEMENT_ID_FIELD_CODE, jobData.divisionId());
            }
        }


        // Job leader. Now taken from division link
        //if (jobData.superiorPerson() != null && !SutinContants.DEPRECATED_SUTIN_USERS_LIST.contains(jobData.superiorPerson())) {
        //    Optional<UUID> superiorPersonId = sutinRecordDataLoader.getRecordIdByExternalId(jobData.superiorPerson(), SutinContants.ELEMENT_ID_FIELD_CODE);
        //    if (superiorPersonId.isPresent()) {
        //        DetailedRecordValueCommand cmd = new DetailedRecordValueCommand(recordLoader.getById(superiorPersonId.get()), ctx, currentAuth);
        //        recordEditationHelper.setRecordIdSubfieldValue(cmd, true, EmploymentStatus.JobLeader.TYPE_ID, true, EmploymentStatus.JobLeader.Main.TYPE_ID, recordEditation);
        //        recordCache.deleteFromCacheById(superiorPersonId.get());
        //    } else {
        //        log.error("Missing superior person record with element ID {} when importing row ID {}. Probably second pass will be needed!", jobData.superiorPerson(), jobData.rowId());
        //    }
        //}


        // TODO: pozor, jsou i záznamy, které mají úvazek a mají hodinovou mzdu!
        recordEditationHelper.setNumberSubfieldValue(weeklyHours, true, WeeklyCommitment.TYPE_ID, true, WeeklyCommitment.Hours.TYPE_ID, recordEditation, ctx, currentAuth);

        return recordEditation;
    }

    private void setDay(@Nullable LocalDate localDate, @NonNull FieldTypeId subfieldTypeId, @NonNull RecordEditation recordEditation, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        if (localDate == null || SutinContants.isDateSkipped(localDate)) {
            return;
        }
        recordEditationHelper.setDateSubfieldValue(localDate, true, subfieldTypeId.existingParent(), true, subfieldTypeId, recordEditation, ctx, currentAuth);
    }

}
