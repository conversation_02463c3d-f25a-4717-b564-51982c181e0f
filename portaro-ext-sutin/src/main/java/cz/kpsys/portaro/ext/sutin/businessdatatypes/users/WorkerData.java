package cz.kpsys.portaro.ext.sutin.businessdatatypes.users;

import cz.kpsys.portaro.commons.geo.Country;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SutinWorkerResponse;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.contacts.*;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.PersonDto;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.WorkCatalogLinkDto;
import cz.kpsys.portaro.user.PhoneNumberEditationCommand;
import cz.kpsys.portaro.user.contact.UserEmailEditationCommand;
import cz.kpsys.portaro.user.edit.command.UserAddressEditationCommand;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;

import static cz.kpsys.portaro.commons.util.ObjectUtil.elvis;
import static cz.kpsys.portaro.ext.sutin.internaldatatypes.contacts.AdresaZemeKat.CZ_CODE;
import static cz.kpsys.portaro.user.contact.SourceOfData.EXTERNAL_SUTIN;

@Slf4j
public record WorkerData(

        @NonNull PersonData personData,

        // TODO: For now Fond 88 and 83 data come from same place. That will lead to duplication. This could be solved in the future.
        @NonNull TimeIndexedWorkerData index,

        @NonNull List<WorkCatalogLinkDto> workCatalogLinks,

        @NonNull List<MonthAttendance> monthlyAttendances,

        @NonNull Optional<JobData> lastJobData,


        @NullableNotBlank String username,

        List<UserAddressEditationCommand> addresses,

        List<UserEmailEditationCommand> emails,

        List<PhoneNumberEditationCommand> phoneNumbers

) {

    public static WorkerData fromDto(@NonNull SutinWorkerResponse worker) {
        var personData = worker.employeeData().stream()
                .filter(PersonDto::isValid)
                .map(PersonData::fromPersonDto)
                .findFirst()
                .orElseThrow(() -> new NoSuchElementException("Cannot find valid personal data for %s".formatted(worker)));
        var jobData = worker.employeeData().stream().map(WorkerData::fromJobFileDto).toList();
        var salaryData = worker.salaries().stream().map(SalaryData::fromMzda).toList();
        return new WorkerData(
                personData,
                TimeIndexedWorkerData.build(jobData, salaryData, personData),
                worker.workCatalogLinks(),
                worker.monthlyAttendances().stream().map(MonthAttendance::fromDto).toList(),
                findLastJobData(jobData),
                null,
                getUserAddressRequests(worker.addresses()),
                getEmailRequests(worker.contacts()),
                getPhoneNumberRequests(worker)
        );
    }

    @NonNull
    private static List<UserEmailEditationCommand> getEmailRequests(List<Kontakt> sutinContact) {
        return sutinContact.stream()
                .filter(kontakt -> kontakt.type().equals(KontaktTypKat.EMAIL))
                .map(kontakt -> UserEmailEditationCommand.createValidated(fixForbiddenCharacters(kontakt.value()), EXTERNAL_SUTIN))
                .toList();
    }

    private static String fixForbiddenCharacters(String input) {
        return input.replace('ö', 'o').replace('ü', 'u').replace(' ', 'X');
    }

    @NonNull
    private static List<PhoneNumberEditationCommand> getPhoneNumberRequests(@NonNull SutinWorkerResponse worker) {
        return worker.contacts().stream()
                .filter(kontakt -> kontakt.type().equals(KontaktTypKat.TELEFON))
                .flatMap(kontakt -> Arrays.stream(kontakt.value().split(",")))
                .map(String::trim)
                .map(kontakt -> PhoneNumberEditationCommand.createValidated(kontakt, true, EXTERNAL_SUTIN))
                .toList();
    }


    private static List<UserAddressEditationCommand> getUserAddressRequests(List<AdresaWithType> sutinAddresses) {
        return sutinAddresses.stream()
                .map(addressWithType -> {

                    boolean isResident = addressWithType.addressType().stream()
                            .anyMatch(adresaTyp -> adresaTyp.adresaTyp().equals(AdresaTypKat.TRVALE_BYDLISTE));

                    boolean isMailing = addressWithType.addressType().stream()
                            .anyMatch(adresaTyp -> adresaTyp.adresaTyp().equals(AdresaTypKat.DORUCOVACI));

                    // TODO bohuzel chyby v SUTIN datech
                    int countryId;
                    if (addressWithType.address().countryId() == null) {
                        countryId = CZ_CODE.getId();
                    } else {
                        countryId = addressWithType.address().countryId();
                    }

                    return new UserAddressEditationCommand(
                            isResident,
                            isMailing,
                            EXTERNAL_SUTIN,
                            addressWithType.address().street(),
                            addressWithType.address().city(),
                            addressWithType.address().postalCode(),
                            Country.getByAlpha2(AdresaZemeKat.CODEBOOK.getById(countryId).getValue())
                    );
                })
                .toList();
    }

    private static JobData fromJobFileDto(@NonNull PersonDto personDto) {
        String superiorPerson = personDto.superiorPerson();
        if (superiorPerson.equals(personDto.elementId())) {
            superiorPerson = null; // No self superior persons
        }
        if ("62537".equals(superiorPerson)) {
            superiorPerson = "63151"; // Fix Kouda superior
        }
        return JobData.fromPracovniPomer(personDto, superiorPerson);
    }

    private static Optional<JobData> findLastJobData(List<JobData> jobData) {
        JobData lastJobFile = null;
        for (JobData profession : jobData) {
            if (profession.isValid()) {
                return Optional.of(profession);
            }

            var lastEndDate = elvis(lastJobFile, JobData::endDate);
            var currentEndDate = elvis(profession, JobData::endDate);
            if (lastEndDate == null) {
                lastJobFile = profession;
            } else if (currentEndDate != null && currentEndDate.isAfter(lastEndDate)) {
                lastJobFile = profession;
            }
        }
        return Optional.ofNullable(lastJobFile);
    }


}
