package cz.kpsys.portaro.ext.sutin.businessdatatypes.users;

import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.*;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.util.Assert;

import java.time.YearMonth;

public record MonthAttendance(

        @NonNull String rowId,

        @NonNull String yearMonth, // "${year}-${month}"

        @NonNull String elementId,

        @Nullable String note,

        @NonNull MonthAttendanceState state,

        @Nullable Double approvedWorkdayOvertime,

        @Nullable Double approvedWeekendOvertime,

        @Nullable Double approvedBankOvertime,

        @Nullable Integer onCallDays,

        @Nullable Integer onCallWeeks,

        @Nullable OvertimeCompensationType overtimeCompensation,

        @Nullable OnCallCompensationType onCallCompensation,

        @Nullable IdpCompensationType idpCompensation

) {

    public static @NonNull MonthAttendance fromDto(@NonNull MonthAttendanceDto data) {
        return new MonthAttendance(
                data.rowId(),
                periodToString(data.periodId()),
                data.elementId(),
                data.note(),
                data.state(),
                data.approvedWorkdayOvertime(),
                data.approvedWeekendOvertime(),
                data.approvedBankOvertime(),
                data.onCallDays(),
                data.onCallWeeks(),
                data.overtimeCompensation(),
                data.onCallCompensation(),
                data.idpCompensation()
        );
    }

    private static String periodToString(Integer periodId) {
        var str = periodId.toString();
        Assert.isTrue(str.matches("\\d\\d\\d\\d\\d\\d"), () -> "Wrong date format! %s".formatted(str));
        var year = str.substring(0, 4);
        var month = str.substring(4, 6);
        return year + "-" + month;
    }

    public @NonNull YearMonth parseYearMonth() {
        return YearMonth.parse(yearMonth);
    }

}
