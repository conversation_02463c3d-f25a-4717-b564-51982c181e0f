package cz.kpsys.portaro.ext.sutin.businessdatatypes.users;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.ext.sutin.NonFatalImportException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class TimeIndexedWorkerData {

    public static TimeIndexedWorkerData build(List<JobData> jobData, List<SalaryData> salaryData, PersonData personData) {
        if (salaryData.isEmpty()) {
            log.warn("Missing salary data for {}!", personData);
        }
        return new TimeIndexedWorkerData(
                DurationIndex.of(jobData.stream().map(DataWithRecord::new).toList()),
                DurationIndex.of(salaryData.stream().map(DataWithRecord::new).toList())
        );
    }


    @NonNull DurationIndex<DataWithRecord<JobData>> employmentStatusIndex;
    @NonNull DurationIndex<DataWithRecord<SalaryData>> salaryIndex;

    public @NonNull Collection<DataWithRecord<JobData>> getEmploymentStates() {
        return employmentStatusIndex.getAll();
    }

    public @NonNull Collection<DataWithRecord<SalaryData>> getSalaries() {
        return salaryIndex.getAll();
    }

    public @NonNull Optional<DataWithRecord<JobData>> findEmploymentStatusFor(@NonNull SalaryData salaryData) {
        var employmentStates = employmentStatusIndex.getValidThrough(salaryData);
        return switch (employmentStates.size()) {
            case 1 -> Optional.of(ListUtil.getFirst(employmentStates));
            case 0 -> {
                log.warn("Falling back to ID pairing because no employment status was found for salary {} in {}", salaryData, employmentStatusIndex);
                // Try pairing salary based on ID of employment status (adr_pracovnik_mzdy_zaz.zaznam_id = adr_pracovnik_kat.id)
                yield employmentStatusIndex.getAll().stream()
                        .filter(employmentState -> employmentState.getData().rowId().equals(salaryData.jobFileId()))
                        .findAny();
            }
            default -> {
                log.warn("Too many employment states found for {} in {}", salaryData, employmentStatusIndex);
                // Select active employment status or one with greatest row ID
                DataWithRecord<JobData> result = null;
                var it = employmentStates.iterator();
                for (var next = it.next(); it.hasNext(); next = it.next()) {
                    if (result == null || Integer.parseInt(next.getData().rowId()) > Integer.parseInt(result.getData().rowId())) {
                        result = next;
                    }
                    if (next.getData().isValid()) {
                        result = next;
                        break;
                    }
                }
                yield Optional.ofNullable(result);
            }
        };
    }

    public @NonNull List<DataWithRecord<SalaryData>> findSalariesFor(@NonNull MonthAttendance monthAttendance) {
        return salaryIndex.getValidThrough(monthAttendance.parseYearMonth());
    }

    public @NonNull BigDecimal findWeeklyHoursFor(@NonNull JobData jobData) throws NonFatalImportException {
        var salaries = salaryIndex.getValidThrough(jobData);
        if (salaries.isEmpty()) {
            log.warn("Falling back to ID pairing because no weekly hours based on validity time were found for job data {} in {}", jobData, salaryIndex);
            var idPairedSalaries = salaryIndex.getAll().stream()
                    .filter(salary -> salary.getData().jobFileId().equals(jobData.rowId()))
                    .toList();
            if (idPairedSalaries.isEmpty()) {
                throw new NonFatalImportException("No weekly hours were found for job data %s in %s".formatted(jobData, salaryIndex));
            }

            return ensureSameWeeklyHours(jobData, idPairedSalaries);
        }

        return ensureSameWeeklyHours(jobData, salaries);
    }

    private static BigDecimal ensureSameWeeklyHours(JobData jobData, List<DataWithRecord<SalaryData>> salaries) throws NonFatalImportException {
        var weeklyHours = salaries.getFirst().getData().weeklyHours();
        for (int i = 1; i < salaries.size(); i++) {
            var nextWeeklyHours = salaries.get(i).getData().weeklyHours();
            if (!weeklyHours.equals(nextWeeklyHours)) {
                throw new NonFatalImportException("Too many different weekly hours found for %s in %s".formatted(jobData, salaries));
            }
        }
        return weeklyHours;
    }

}
