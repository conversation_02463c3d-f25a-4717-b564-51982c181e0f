package cz.kpsys.portaro.ext.sutin.recordeditation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.DataWithRecord;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.MonthAttendance;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.SalaryData;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class MonthAttendanceRecordEditationCreator {

    @NonNull MonthAttendanceRecordEditationFiller monthAttendanceRecordEditationFiller;
    @NonNull Provider<@NonNull Fond> monthAttendanceFondProvider;
    @NonNull RecordEditationFactory recordEditationFactory;

    public RecordEditation ofNewRecord(@NonNull MonthAttendance monthAttendance,
                                       @NonNull DataWithRecord<SalaryData> salary,
                                       @NonNull List<Department> holdingDepartments,
                                       @NonNull Department ctx,
                                       @NonNull UserAuthentication currentAuth) {
        Fond fond = monthAttendanceFondProvider.get();

        RecordEditation recordEditation = recordEditationFactory
                .withHoldingsWithoutCtx(holdingDepartments, ctx)
                .ofNew(fond)
                .build(currentAuth);

        return monthAttendanceRecordEditationFiller.fill(monthAttendance, recordEditation, salary, ctx, currentAuth);
    }

    public RecordEditation ofExistingRecord(@NonNull Record monthAttendanceRecord,
                                            @NonNull MonthAttendance monthAttendance,
                                            @NonNull DataWithRecord<SalaryData> salary,
                                            @NonNull List<Department> holdingDepartments,
                                            @NonNull Department ctx,
                                            @NonNull UserAuthentication currentAuth) {
        RecordEditation recordEditation = recordEditationFactory
                .withHoldingsWithoutCtx(holdingDepartments, ctx)
                .ofExisting(monthAttendanceRecord)
                .build(currentAuth);

        return monthAttendanceRecordEditationFiller.fill(monthAttendance, recordEditation, salary, ctx, currentAuth);
    }

    public RecordEditation ofNewPlaceholderRecord(@NonNull MonthAttendance monthAttendance,
                                                  @NonNull DataWithRecord<SalaryData> salary,
                                                  @NonNull List<Department> holdingDepartments,
                                                  @NonNull Department ctx,
                                                  @NonNull UserAuthentication currentAuth) {
        Fond fond = monthAttendanceFondProvider.get();

        RecordEditation recordEditation = recordEditationFactory
                .withHoldingsWithoutCtx(holdingDepartments, ctx)
                .ofNew(fond)
                .build(currentAuth);

        return monthAttendanceRecordEditationFiller.fillPlaceholder(monthAttendance, recordEditation, salary, ctx, currentAuth);
    }

    public RecordEditation ofExistingPlaceholderRecord(@NonNull Record monthAttendanceRecord,
                                                       @NonNull MonthAttendance monthAttendance,
                                                       @NonNull DataWithRecord<SalaryData> salary,
                                                       @NonNull List<Department> holdingDepartments,
                                                       @NonNull Department ctx,
                                                       @NonNull UserAuthentication currentAuth) {
        RecordEditation recordEditation = recordEditationFactory
                .withHoldingsWithoutCtx(holdingDepartments, ctx)
                .ofExisting(monthAttendanceRecord)
                .build(currentAuth);

        return monthAttendanceRecordEditationFiller.fillPlaceholder(monthAttendance, recordEditation, salary, ctx, currentAuth);
    }

}
