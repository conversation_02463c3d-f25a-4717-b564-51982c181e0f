package cz.kpsys.portaro.ext.sutin.recordeditation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.FondPerson.EmploymentData;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.PersonData;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import static cz.kpsys.portaro.erp.RecordSutinSpecificFields.ElementId;
import static cz.kpsys.portaro.erp.RecordSutinSpecificFields.FondPerson;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class PersonDataRecordEditationFiller implements RecordEditationFiller<PersonData> {

    @NonNull RecordEditationHelper recordEditationHelper;

    public RecordEditation fill(String elementId, PersonData personData, RecordEditation recordEditation, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {

        recordEditationHelper.setStringTopFieldValue(elementId, ElementId.TYPE_ID, recordEditation, ctx, currentAuth);

        String firstName = StringUtil.joinSkippingBlanksAndNulls(" ", personData.firstName(), personData.firstName2());
        String lastName = StringUtil.joinSkippingBlanksAndNulls(" ", personData.lastName(), personData.lastName2());
        recordEditationHelper.setStringSubfieldValue(lastName, true, FondPerson.Person.TYPE_ID, true, FondPerson.Person.LastName.TYPE_ID, recordEditation, ctx, currentAuth);
        recordEditationHelper.setStringSubfieldValue(firstName, true, FondPerson.Person.TYPE_ID, true, FondPerson.Person.FirstName.TYPE_ID, recordEditation, ctx, currentAuth);

        String degree = StringUtil.joinSkippingBlanksAndNulls(", ", personData.prefixDegree(), personData.suffixDegree());
        if (!StringUtil.isNullOrEmpty(degree)) {
            recordEditationHelper.setStringSubfieldValue(degree, true, FondPerson.Person.TYPE_ID, true, FondPerson.Person.Degree.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        if (!StringUtil.isNullOrEmpty(personData.extension())) {
            recordEditationHelper.setStringSubfieldValue(personData.extension(), true, FondPerson.Person.TYPE_ID, true, FondPerson.Person.Extension.TYPE_ID, recordEditation, ctx, currentAuth);
        }


        // EmploymentData
        if (personData.pohodaId() != null) {
            recordEditationHelper.setStringSubfieldValue(personData.pohodaId(), true, EmploymentData.TYPE_ID, true, EmploymentData.PohodaId.TYPE_ID, recordEditation, ctx, currentAuth);
        }

        return recordEditation;
    }
}
