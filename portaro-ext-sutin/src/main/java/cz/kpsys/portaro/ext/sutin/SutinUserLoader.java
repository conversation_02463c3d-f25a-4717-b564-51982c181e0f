package cz.kpsys.portaro.ext.sutin;

import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.phonenumber.InvalidPhoneNumberException;
import cz.kpsys.portaro.commons.phonenumber.PhoneNumberValidator;
import cz.kpsys.portaro.commons.sync.SyncItem;
import cz.kpsys.portaro.commons.sync.SyncUtil;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.OptionalUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.RowId;
import cz.kpsys.portaro.erp.job.LinkedRecordSearchLoader;
import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.*;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SutinWorkerResponse;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.PracMistoPraceKat;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.PracVzdelaniKat;
import cz.kpsys.portaro.ext.sutin.recordeditation.*;
import cz.kpsys.portaro.ext.synchronizer.ForeignSystemUserLoader;
import cz.kpsys.portaro.ext.synchronizer.matcher.SyncedUsersComparatorFunction;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserConstants;
import cz.kpsys.portaro.user.edit.command.*;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Stream;

import static cz.kpsys.portaro.ext.sutin.SutinContants.SUTIN_SYNC_ID_PREFIX;
import static cz.kpsys.portaro.user.contact.SourceOfData.EXTERNAL_SUTIN;
import static java.util.stream.Collectors.toUnmodifiableSet;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class SutinUserLoader implements ForeignSystemUserLoader {

    public static final LocalDate FRESHNESS_DATE = LocalDate.of(2024, 11, 1);

    @NonNull ParameterizedSearchLoader<MapBackedParams, User> userSearchLoader;
    @NonNull SutinDataLoader databaseLoader;
    @NonNull SutinRecordDataLoader sutinRecordDataLoader;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull AllValuesProvider<Department> departmentLoader;
    @NonNull SyncedUsersComparatorFunction<Department, SutinUser> syncedUsersCompareFunction;
    @NonNull Eventer eventer;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull SutinUserRecordEditationCreator sutinUserRecordEditationCreator;
    @NonNull ByIdLoadable<ReaderCategory, String> readerCategoryLoader;
    @NonNull PhoneNumberValidator phoneNumberValidator;
    @NonNull Converter<List<UUID>, List<? extends Record>> idsToRecordsConverter;
    @NonNull Provider<@NonNull Fond> employmentStatusFondProvider;
    @NonNull EmploymentStatusRecordEditationCreator sutinEmploymentStatusRecordEditationCreator;
    @NonNull Provider<@NonNull Fond> jobFileFondProvider;
    @NonNull JobFileRecordEditationCreator sutinJobFileRecordEditationCreator;
    @NonNull Provider<@NonNull Fond> salaryFondProvider;
    @NonNull SalaryRecordEditationCreator salaryRecordEditationCreator;
    @NonNull LinkedRecordSearchLoader personWorkCatalogLinkByUserIdSearchLoader;
    @NonNull WorkCatalogLinkRecordEditationCreator workCatalogLinkRecordEditationCreator;
    @NonNull Provider<@NonNull Fond> monthAttendanceFondProvider;
    @NonNull MonthAttendanceRecordEditationCreator monthAttendanceRecordEditationCreator;
    @NonNull Provider<? extends Department> importRootDepartmentProvider;

    @Override
    public List<UserRecordEditationCommand<PersonEditationCommand>> getForeignUsersFromServer(Department ctx, @NonNull UserAuthentication currentAuth) {
        log.info("Sutin user loading started");
        List<SutinWorkerResponse> workers = databaseLoader.loadSutinWorkers(ctx);

        // All existing users loaded from DB with SyncId with Sutin Prefix
        Set<Person> allExistingDbUsers = getAllExistingForeignUsers(ctx);

        // Convert all sutin ElementId to syncIds
        List<SutinUser> allSutinUsers = workers.stream()
                .map(worker -> SutinUser.mapNewUser(worker, tryToFindRecordIdOfExistingPerson(allExistingDbUsers, worker).orElse(null), this::getWorkersDivision))
                .toList();

        // people, who exists in both system or in db only
        List<SyncItem<Person, SutinUser>> bothAndDbOnlyUsers = ListUtil.convert(allExistingDbUsers, existingDbUser -> tryToConnectWithExternalUser(existingDbUser, allSutinUsers, ctx))
                .stream()
                .filter(SyncItem::isRemaining)
                .toList();

        // return list of new users which are not in DB
        List<SyncItem<Person, SutinUser>> newExternalUsers = allSutinUsers.stream()
                .map(sutinUser -> addNewSutinUser(sutinUser, allExistingDbUsers, ctx))
                .filter(Objects::nonNull)
                .toList();

        // joining both list
        List<SyncItem<Person, SutinUser>> allUsers = ListUtil.union(bothAndDbOnlyUsers, newExternalUsers);

        var res = new ArrayList<UserRecordEditationCommand<PersonEditationCommand>>();
        for (var userSync : allUsers) {
            try {
                res.add(mapUserToPersonEditationCommand(userSync, ctx, currentAuth));
            } catch (NonFatalImportException e) {
                log.error("Skipping user {}", userSync, e);
            }
        }

        log.info("Sutin user loading finished");
        return res;
    }

    private Department getWorkersDivision(@NonNull String divisionId) throws NonFatalImportException {
        if (divisionId.equals(SutinContants.DEPRECATED_SUTIN_DIVISION_NUMBER_2)) {
            divisionId = SutinContants.NEW_SUTIN_DIVISION_NUMBER_2;
        }

        String finalDivId = divisionId;
        Optional<? extends Department> division = ListUtil.findSingleMatching(
                departmentLoader.getAll(),
                department -> department.getSyncId().equals(finalDivId),
                Department.class,
                "SyncId(Division elementId)=" + finalDivId
        );

        return division.orElseThrow(() -> new NonFatalImportException("Division with divisionId=%s does not exist!".formatted(finalDivId)));
    }

    private Optional<UUID> tryToFindRecordIdOfExistingPerson(Set<Person> allExistingDbUsers, SutinWorkerResponse worker) {
        //RECORD ID
        Optional<Person> existingUser = allExistingDbUsers.stream()
                .filter(person -> person.getSyncId().equals(SyncUtil.composeSyncId(SUTIN_SYNC_ID_PREFIX, null, worker.elementId())))
                .findFirst();

        if (existingUser.isPresent() && existingUser.get().getRecordId() != null) {
            return Optional.of(existingUser.get().getRecordId());
        }

        return sutinRecordDataLoader.getRecordIdByExternalId(worker.elementId(), SutinContants.ELEMENT_ID_FIELD_CODE);
    }

    // Add Sutin data to existing dbUser
    private SyncItem<Person, SutinUser> tryToConnectWithExternalUser(Person existingDbUser, List<SutinUser> externalUsers, Department ctx) {
        List<SutinUser> sutinUsers = ListUtil.filter(externalUsers, externalUser -> syncedUsersCompareFunction.areEqual(externalUser, existingDbUser, ctx));
        Optional<SutinUser> sutinUserOpt = syncedUsersCompareFunction.getBestMatch(sutinUsers, existingDbUser, ctx);
        return SyncItem.ofOptionalExternal(existingDbUser, sutinUserOpt);
    }

    // Add Sutin data to non existing dbUser
    private SyncItem<Person, SutinUser> addNewSutinUser(SutinUser sutinUser, Set<Person> existingDbUsers, Department ctx) {
        boolean userIsNotFound = existingDbUsers.stream().noneMatch(dbUser -> syncedUsersCompareFunction.areEqual(sutinUser, dbUser, ctx));
        if (userIsNotFound) {
            return SyncItem.ofWithoutInternal(sutinUser);
        }
        return null;
    }

    private Set<Person> getAllExistingForeignUsers(Department ctx) {
        List<User> allExistingUsers = userSearchLoader.getContent(RangePaging.forAll(), StaticParamsModifier.of(
                CoreSearchParams.DEPARTMENT, contextHierarchyLoader.getAllByScope(ctx, HierarchyLoadScope.SUBTREE),
                UserConstants.SearchParams.SYNC_ID_PREFIX, SUTIN_SYNC_ID_PREFIX
        ));
        return ListUtil.castItems(allExistingUsers, Person.class).collect(toUnmodifiableSet());
    }

    private UserRecordEditationCommand<PersonEditationCommand> mapUserToPersonEditationCommand(SyncItem<Person, SutinUser> userSync, Department ctx, @NonNull UserAuthentication currentAuth) throws NonFatalImportException {

        // NEW USER - map raw data
        if (userSync.isAdding()) {
            EventWorkerLog userLogSutin = new EventWorkerLog(userSync.external().get().id(), userSync.external().get().workerData().personData().lastName(), userSync.external().get().workerData().personData().firstName());
            Map<String, EventWorkerLog> eventData = Map.of("sutinUser", userLogSutin);
            eventer.save(Event.Codes.SUTIN_SYNC_USER_ADD, authenticationHolder.getCurrentAuth(), ctx, eventData);
            return mapNewUserToPersonEditationCommand(userSync.external().get(), ctx, currentAuth);
        }

        // UPDATE USER - according id just update person
        if (userSync.isRemaining()) {
            EventWorkerLog userLogSutin = new EventWorkerLog(userSync.external().get().id(), userSync.external().get().workerData().personData().lastName(), userSync.external().get().workerData().personData().firstName());
            EventWorkerLog userLogDb = new EventWorkerLog(userSync.internal().get().getId().toString(), userSync.internal().get().getLastName(), userSync.internal().get().getFirstName());
            Map<String, EventWorkerLog> eventData = Map.of("dbUser", userLogDb, "sutinUser", userLogSutin);
            eventer.save(Event.Codes.SUTIN_SYNC_USER_UPDATE, authenticationHolder.getCurrentAuth(), ctx, eventData);
            return mapEditedUserToPersonEditationCommand(userSync.external().get(), userSync.internal().get(), ctx, currentAuth);
        }

        // INACTIVE USER - just update expiration date and block
        if (userSync.isRemoving()) {
            EventWorkerLog userLogDb = new EventWorkerLog(userSync.internal().get().getId().toString(), userSync.internal().get().getLastName(), userSync.internal().get().getFirstName());
            Map<String, EventWorkerLog> eventData = Map.of("dbUser", userLogDb);
            eventer.save(Event.Codes.SUTIN_SYNC_USER_INACTIVATE, authenticationHolder.getCurrentAuth(), ctx, eventData);
            return mapInactivatedUserToPersonEditationCommand(userSync.internal().get());
        }

        throw new IllegalStateException();
    }

    // EDIT USER
    private UserRecordEditationCommand<PersonEditationCommand> mapEditedUserToPersonEditationCommand(@NonNull SutinUser user, @NonNull Person dbUser, Department ctx, @NonNull UserAuthentication currentAuth) throws NonFatalImportException {
        log.info("Employee sync - editing: {}({}, {})", user.elementId(), user.workerData().personData().lastName(), user.workerData().personData().firstName());

        UserRecordEditationCommand<PersonEditationCommand> personEditationCommand = mapNewUserToPersonEditationCommand(user, ctx, currentAuth);
        personEditationCommand.userEditationCommand().setId(dbUser.getId());
        return personEditationCommand;
    }

    // INACTIVATE USER
    private UserRecordEditationCommand<PersonEditationCommand> mapInactivatedUserToPersonEditationCommand(@NonNull Person user) {
        log.info("Employee sync - deactivating: {}, {}", user.getLastName(), user.getFirstName());

        PersonEditationCommand personEditationCommand = new PersonEditationCommand();

        personEditationCommand.setId(user.getId());
        ReaderAccountEditationCommand readerAccount = ReaderAccountEditationCommand.ofEmpty();
        if (user.getReaderAccounts().size() == 1 && !user.getReaderAccounts().getFirst().isRegistrationExpired()) {
            readerAccount.setRegistrationExpirationDateAndRegistrationToNowIfEmpty(Optional.of(LocalDate.now().minusDays(1)));
        }
        personEditationCommand.setReaderAccounts(Optional.of(List.of(readerAccount)));

        return UserRecordEditationCommand.ofUserEditationOnly(personEditationCommand);
    }

    // NEW USER
    private UserRecordEditationCommand<PersonEditationCommand> mapNewUserToPersonEditationCommand(@NonNull SutinUser user, Department ctx, @NonNull UserAuthentication currentAuth) throws NonFatalImportException {
        log.info("Employee sync - adding: {}({}, {})", user.elementId(), user.workerData().personData().lastName(), user.workerData().personData().firstName());

        PersonEditationCommand personEditationCommand = new PersonEditationCommand();
        personEditationCommand.setSyncId(Optional.of(user.id()));

        RecordEditation userRecordEditation;
        if (user.recordId().isPresent()) {
            userRecordEditation = sutinUserRecordEditationCreator.ofExistingRecord(user, List.of(importRootDepartmentProvider.get()), ctx, currentAuth);
        } else {
            userRecordEditation = sutinUserRecordEditationCreator.ofNewRecord(user, List.of(importRootDepartmentProvider.get()), ctx, currentAuth);
        }

        fillPersonEditation(personEditationCommand, user);

        var dependentEditations = new ArrayList<RecordEditation>();
        syncEmploymentStates(user, userRecordEditation.getRecord(), ctx, currentAuth, dependentEditations);
        syncJobFiles(user, userRecordEditation.getRecord(), ctx, currentAuth, dependentEditations);
        syncSalaries(user, ctx, currentAuth, dependentEditations);
        syncWorkCatalogLinks(user, userRecordEditation.getRecord(), ctx, currentAuth, dependentEditations);
        syncMonthlyAttendance(user, ctx, currentAuth, dependentEditations);

        return new UserRecordEditationCommand<>(personEditationCommand, userRecordEditation, dependentEditations);
    }

    private void syncEmploymentStates(@NonNull SutinUser user, @NonNull Record userRecord, Department ctx, @NonNull UserAuthentication currentAuth, ArrayList<RecordEditation> outEditations) throws NonFatalImportException {
        var importedJobData = user.workerData().index().getEmploymentStates();
        var importedEmploymentStatusRowIds = importedJobData.stream()
                .map(DataWithRecord::getData)
                .map(JobData::rowId)
                .toList();
        var existingEmploymentStates = Objects.requireNonNull(idsToRecordsConverter.convert(
                sutinRecordDataLoader.getRecordIdsByRowIds(importedEmploymentStatusRowIds, employmentStatusFondProvider.get())));

        for (var syncedEmployment : importedJobData) {
            var weeklyHours = user.workerData().index().findWeeklyHoursFor(syncedEmployment.getData());
            var editation = existingEmploymentStates.stream()
                    .filter(existingEmployment -> existingEmployment.hasFirstValue(RowId.FIELD_FINDER, syncedEmployment.getData().rowId()))
                    .findAny()
                    .map(foundEmployment -> sutinEmploymentStatusRecordEditationCreator.ofExistingRecord(foundEmployment, syncedEmployment.getData(), userRecord, weeklyHours, List.of(importRootDepartmentProvider.get()), ctx, currentAuth))
                    .orElseGet(() -> sutinEmploymentStatusRecordEditationCreator.ofNewRecord(syncedEmployment.getData(), userRecord, weeklyHours, List.of(importRootDepartmentProvider.get()), ctx, currentAuth));
            syncedEmployment.setRecord(editation.getRecord());
            outEditations.add(editation);
        }
    }

    private void syncJobFiles(@NonNull SutinUser user, @NonNull Record userRecord, Department ctx, @NonNull UserAuthentication currentAuth, ArrayList<RecordEditation> outEditations) {
        var importedJobData = user.workerData().index().getEmploymentStates();
        var importedEmploymentStatusRowIds = importedJobData.stream()
                .map(DataWithRecord::getData)
                .map(JobData::rowId)
                .toList();
        var existingEmploymentStates = Objects.requireNonNull(idsToRecordsConverter.convert(
                sutinRecordDataLoader.getRecordIdsByRowIds(importedEmploymentStatusRowIds, jobFileFondProvider.get())));

        for (var syncedJob : importedJobData) {
            existingEmploymentStates.stream()
                    .filter(existingJobFile -> existingJobFile.hasFirstValue(RowId.FIELD_FINDER, syncedJob.getData().rowId()))
                    .findAny()
                    .ifPresentOrElse(
                            foundJobFile -> outEditations.add(sutinJobFileRecordEditationCreator.ofExistingRecord(foundJobFile, syncedJob.getData(), userRecord, List.of(importRootDepartmentProvider.get()), ctx, currentAuth)),
                            () -> outEditations.add(sutinJobFileRecordEditationCreator.ofNewRecord(syncedJob.getData(), userRecord, List.of(importRootDepartmentProvider.get()), ctx, currentAuth)));
        }
    }

    private void syncSalaries(@NonNull SutinUser user, Department ctx, @NonNull UserAuthentication currentAuth, ArrayList<RecordEditation> outEditations) throws NonFatalImportException {
        var newSalaries = user.workerData().index().getSalaries();
        var newSalariesRowIds = newSalaries.stream()
                .map(DataWithRecord::getData)
                .map(SalaryData::rowId)
                .toList();
        var existingSalaries = Objects.requireNonNull(idsToRecordsConverter.convert(
                sutinRecordDataLoader.getRecordIdsByRowIds(newSalariesRowIds, salaryFondProvider.get())));

        for (var syncedSalary : newSalaries) {
            user.workerData().index().findEmploymentStatusFor(syncedSalary.getData()).ifPresentOrElse(employmentStatus -> {
                var division = getWorkersDivision(employmentStatus.getData().divisionId());
                var editation = existingSalaries.stream()
                        .filter(existingSalary -> existingSalary.hasFirstValue(RowId.FIELD_FINDER, syncedSalary.getData().rowId()))
                        .findAny()
                        .map(foundSalary -> salaryRecordEditationCreator.ofExistingRecord(foundSalary, syncedSalary.getData(), employmentStatus, List.of(division), ctx, currentAuth))
                        .orElseGet(() -> salaryRecordEditationCreator.ofNewRecord(syncedSalary.getData(), employmentStatus, List.of(division), ctx, currentAuth));
                syncedSalary.setRecord(editation.getRecord());
                outEditations.add(editation);
            }, () -> {
                throw new NonFatalImportException("Cannot import salary %s because employment status cannot be paired!".formatted(syncedSalary.getData()));
            });
        }
    }

    private void syncWorkCatalogLinks(@NonNull SutinUser user, @NonNull Record userRecord, Department ctx, @NonNull UserAuthentication currentAuth, ArrayList<RecordEditation> outEditations) {
        var newWorkCatalogLinks = user.workerData().workCatalogLinks();
        var existingWorkCatalogLinks = user.recordId()
                .map(rid -> personWorkCatalogLinkByUserIdSearchLoader.loadByTargetRecordId(rid, ctx))
                .orElse(List.of());

        for (var syncedLink : newWorkCatalogLinks) {
            existingWorkCatalogLinks.stream()
                    .filter(existingLink -> existingLink.hasFirstValue(RowId.FIELD_FINDER, syncedLink.rowId()))
                    .findAny()
                    .ifPresentOrElse(
                            foundLink -> outEditations.add(workCatalogLinkRecordEditationCreator.ofExistingRecordOfPerson(foundLink, syncedLink, userRecord, List.of(user.currentDivision()), ctx, currentAuth)),
                            () -> outEditations.add(workCatalogLinkRecordEditationCreator.ofNewRecordOfPerson(syncedLink, userRecord, List.of(user.currentDivision()), ctx, currentAuth)));
        }
    }

    private void syncMonthlyAttendance(@NonNull SutinUser user, Department ctx, @NonNull UserAuthentication currentAuth, ArrayList<RecordEditation> outEditations) {
        var newMonthlyAttendances = user.workerData().monthlyAttendances();
        var newMonthlyAttendanceRowIds = newMonthlyAttendances.stream().map(MonthAttendance::rowId).toList();
        var existingMonthlyAttendances = Objects.requireNonNull(idsToRecordsConverter.convert(
                sutinRecordDataLoader.getRecordIdsByRowIds(newMonthlyAttendanceRowIds, monthAttendanceFondProvider.get())));

        for (var syncedMonthlyAttendance : newMonthlyAttendances) {
            // 1. Find potentially matching salaries for monthly attendance
            var salaries = user.workerData().index().findSalariesFor(syncedMonthlyAttendance);
            if (salaries.isEmpty()) {
                log.error("Cannot import monthly attendance {} because there is no matching salary!", syncedMonthlyAttendance);
                continue;
            }
            // 2. Lowest row ID first for determinism (so it is still the same one if new salary is added)
            salaries.sort(Comparator.comparing(salaryWithRec -> salaryWithRec.getData().rowId()));
            // 3. Assign selected salary to the synced monthly attendance
            var selectedSalary = salaries.getFirst();
            var unpairedSalaries = salaries.subList(1, salaries.size());
            var editation = existingMonthlyAttendances.stream()
                    .filter(existingMonthlyAttendance -> existingMonthlyAttendance.hasFirstValue(RowId.FIELD_FINDER, syncedMonthlyAttendance.rowId()))
                    .findAny()
                    .map(foundMonthlyAttendance -> monthAttendanceRecordEditationCreator.ofExistingRecord(foundMonthlyAttendance, syncedMonthlyAttendance, selectedSalary, List.of(importRootDepartmentProvider.get()), ctx, currentAuth))
                    .orElseGet(() -> monthAttendanceRecordEditationCreator.ofNewRecord(syncedMonthlyAttendance, selectedSalary, List.of(importRootDepartmentProvider.get()), ctx, currentAuth));
            outEditations.add(editation);
            // 4. Create new empty monthly attendances for rest of salaries
            syncPlaceholderMonthlyAttendances(syncedMonthlyAttendance, unpairedSalaries, ctx, currentAuth, outEditations);
        }
    }

    private void syncPlaceholderMonthlyAttendances(@NonNull MonthAttendance monthlyAttendance, @NonNull List<DataWithRecord<SalaryData>> unpairedSalaries, Department ctx, @NonNull UserAuthentication currentAuth, ArrayList<RecordEditation> outEditations) {
        Function<DataWithRecord<SalaryData>, String> monthlyAttendanceIdMapper = salary -> monthlyAttendancePlaceholderId(monthlyAttendance, salary);
        var placeholderMonthlyAttendanceRowIds = unpairedSalaries.stream()
                .map(monthlyAttendanceIdMapper)
                .toList();
        var existingPlaceholders = Objects.requireNonNull(idsToRecordsConverter.convert(
                sutinRecordDataLoader.getRecordIdsByRowIds(placeholderMonthlyAttendanceRowIds, monthAttendanceFondProvider.get())));

        for (var salary: unpairedSalaries) {
            var editation = existingPlaceholders.stream()
                    .filter(existingPlaceholder -> existingPlaceholder.hasFirstValue(RowId.FIELD_FINDER, monthlyAttendanceIdMapper.apply(salary)))
                    .findAny()
                    .map(foundPlaceholder -> monthAttendanceRecordEditationCreator.ofExistingPlaceholderRecord(foundPlaceholder, monthlyAttendance, salary, List.of(importRootDepartmentProvider.get()), ctx, currentAuth))
                    .orElseGet(() -> monthAttendanceRecordEditationCreator.ofNewPlaceholderRecord(monthlyAttendance, salary, List.of(importRootDepartmentProvider.get()), ctx, currentAuth));
            outEditations.add(editation);
        }
    }

    public static String monthlyAttendancePlaceholderId(@NonNull MonthAttendance monthlyAttendance, @NonNull DataWithRecord<SalaryData> salary) {
        return monthlyAttendance.rowId() + "-" + salary.getData().rowId();
    }

    private void fillPersonEditation(PersonEditationCommand personEditationCommand, @NonNull SutinUser user) {
        var lastJobData = user.workerData().lastJobData();

        personEditationCommand.setReadableDepartments(Optional.of(SimpleEditableList.of(user.currentDivision(), DepartmentEditationMode.OVERWRITE_ALL)));

        personEditationCommand.setFirstName(Optional.ofNullable(StringUtil.requireNullableNotBlank(user.workerData().personData().firstName())));
        personEditationCommand.setLastName(Optional.ofNullable(StringUtil.requireNullableNotBlank(user.workerData().personData().lastName())));
        if (OptionalUtil.isNotNullAndPresent(personEditationCommand.getFirstName()) || OptionalUtil.isNotNullAndPresent(personEditationCommand.getLastName())) {
            personEditationCommand.setNameSource(Optional.of(EXTERNAL_SUTIN));
        }
        personEditationCommand.setPrefixDegree(Optional.ofNullable(StringUtil.requireNullableNotBlank(user.workerData().personData().prefixDegree())));
        personEditationCommand.setSuffixDegree(Optional.ofNullable(StringUtil.requireNullableNotBlank(user.workerData().personData().suffixDegree())));
        if (OptionalUtil.isNotNullAndPresent(personEditationCommand.getPrefixDegree()) || OptionalUtil.isNotNullAndPresent(personEditationCommand.getSuffixDegree())) {
            personEditationCommand.setDegreeSource(Optional.of(EXTERNAL_SUTIN));
        }
        personEditationCommand.setEducationLevel(lastJobData
                .map(JobData::educationLevel)
                .map(PracVzdelaniKat::getValue)
                .map(StringUtil::requireNullableNotBlank));

        personEditationCommand.setJobAddress(lastJobData
                .map(JobData::jobPlace)
                .map(PracMistoPraceKat::getValue)
                .map(StringUtil::requireNullableNotBlank));

        lastJobData
                .filter(JobData::isValid)
                .ifPresentOrElse(_ -> {
                            personEditationCommand.setIdentityCardNumber(lastJobData
                                    .map(JobData::identityCardNumber)
                                    .map(StringUtil::requireNullableNotBlank));

                            // TODO: na základě čeho vytvářet účet?
                            // CATEGORY
                            ReaderAccountEditationCommand readerAccount = ReaderAccountEditationCommand.ofEmpty();

                            ReaderCategory readerCategory = readerCategoryLoader.getById(StringUtil.requireNotBlank("S"));
                            readerAccount.setReaderCategory(Optional.of(readerCategory));

                            readerAccount.setRegistrationDate(lastJobData.map(JobData::beginDate));

                            readerAccount.setRegistrationExpirationDateAndRegistrationToNowIfEmpty(lastJobData
                                    .map(JobData::endDate)
                                    .or(() -> Optional.of(LocalDate.now().plusYears(1))));

//        readerAccount.setCardNumber(Optional.ofNullable(user.recordData().professionData().personalNumber()));
                            personEditationCommand.setReaderAccounts(Optional.of(List.of(readerAccount)));

                            lastJobData
                                    .map(JobData::birthDate)
                                    .filter(date -> date.isBefore(LocalDate.now()))
                                    .ifPresent(birthDate -> {
                                        personEditationCommand.setBirthDate(Optional.of(birthDate));
                                        personEditationCommand.setLifeDateSource(Optional.of(EXTERNAL_SUTIN));
                                    });
                        },
                        () -> personEditationCommand.setReaderAccounts(Optional.of(List.of(ReaderAccountEditationCommand.ofEmpty())))
                );


        // CONTACTS
        personEditationCommand.setEmails(Optional.of(SourcedEditableList.of(user.workerData().emails(), SourcedEditableListMode.OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE, EXTERNAL_SUTIN)));

        var phoneNumberCommands = user.workerData().phoneNumbers().stream()
                .flatMap(phoneNumber -> {
                    try {
                        // Messy data where some older workers have invalid phone numbers
                        return Stream.of(phoneNumber.withValue(phoneNumberValidator.toE164(phoneNumber.value())));
                    } catch (InvalidPhoneNumberException e) {
                        if (lastJobData.isPresent()
                                && lastJobData.get().isValid()
                                && lastJobData.get().endDate() != null
                                && lastJobData.get().endDate().isAfter(FRESHNESS_DATE)) {
                            // For current active users throw error, log otherwise
                            throw e;
                        } else {
                            log.error("Invalid phone number in {}", user.workerData().phoneNumbers(), e);
                            return Stream.empty();
                        }
                    }
                })
                .toList();
        personEditationCommand.setPhoneNumbers(Optional.of(SourcedEditableList.of(
                phoneNumberCommands,
                SourcedEditableListMode.OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE,
                EXTERNAL_SUTIN)));

        personEditationCommand.setAddresses(Optional.of(SourcedEditableList.of(user.workerData().addresses(), SourcedEditableListMode.OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE, EXTERNAL_SUTIN)));
    }

}
