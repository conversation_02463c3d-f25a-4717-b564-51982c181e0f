package cz.kpsys.portaro.ext.sutin.internaldatatypes;

import cz.kpsys.portaro.commons.web.PlaceholderTemplate;
import cz.kpsys.portaro.ext.sutin.SutinContants;
import cz.kpsys.portaro.ext.sutin.SutinUserLoader;

import static cz.kpsys.portaro.ext.sutin.SutinContants.ALLOWED_DIVISIONS;

public class SelectStatementsDef {

    /* Zpracujeme všechny zaměstnance v pořadí dle sloupce timekey, který by snad měl určovat čas poslední změny.
     */
    public static String getPracovnikSelectStatement() {
        return new PlaceholderTemplate(
                """ 
                select
                  adr_pracovnik_kat.id as rowId,
                  element_id as elementId,
                  jmeno as firstName,
                  prijmeni as lastName,
                  jmeno2 as firstName2,
                  prijmeni2 as lastName2,
                  titul_pred as degreeBefore,
                  titul_za as degreeAfter,
                  narozen_datum as birthDate,
                  misto_narozeni as birthPlace,
                  rodne_cislo as personalIdentificationNumber,
                  statni_prislusnost_id as nationality,
                  vzdelani_id as educationLevel,
                  instituce_vzdelani as educationInstitution,
                  pohlavi as gender,
                  doplnek as extension,
                  ele_element_kat.id as pohodaId,

                  osobni_cislo as personalNumber,
                  profese_id as professionId,
                  nastup_datum as jobBeginDate,
                  zkusebni_doba as testTime,
                  ukonceni_datum as jobEndDate,
                  smlouva_typ_id as contractType,
                  pozice_id as workPositionId,
                  nadrizeny_id as superiorPerson,
                  kategorie_id as workCategory,
                  vypoved_strana_id as employmentTerminationSide,
                  vypoved_typ_id as employmentTerminationType,
                  ukonceni_duvod as employmentTerminationReason,
                  doklad_totoznosti_cislo as identityCardNumber,
                  doklad_totoznosti_platnost as identityCardValidity,
                  misto_prace_id as jobPlace,
                  zamestnavatel_id as employerId,
                  smlouva_ze_dne as contractDate,
                  ic as vat,
                  pracpomer_poradi as employmentOrder,
                  prukazka_cislo as identificationCardNumber,
                  platne_od as jobFileValidFrom,
                  platne_do as jobFileValidTo,
                  divize_id as divisionId,
                  zdrav_pojistovna_id as healthInsuranceCode,
                  invalidita_id as handicap,
                  materska_datum as maternityBeginDate,
                  materska_konec_datum as maternityEndDate,
                  rodicovska_datum as parentalBeginDate,
                  konec_rodicovske_datum as parentalEndDate,
                  uplatnene_deti as claimedChildren,
                  neuplatnene_deti as unclaimedChildren,
                  exekuce as foreclosure,
                  pobyt_konec as workPlacementEndDate,

                  aktualni as valid
                
                from adr_pracovnik_kat
                join ele_element_kat on ele_element_kat.id = adr_pracovnik_kat.element_id
                where exists (
                    select 1
                    from adr_pracovnik_kat akin
                    where akin.element_id = adr_pracovnik_kat.element_id
                        and akin.aktualni = 'A'
                        and akin.divize_id in {allowedDivisions}
                        and element_id not in {deprecatedSutinUsers}
                        and akin.ukonceni_datum > '{freshDate}'
                    )
                    or element_id in {forcedSutinUsers}
                order by aktualni desc, jobEndDate asc
                """)
                .withParameter("allowedDivisions", ALLOWED_DIVISIONS)
                .withParameter("deprecatedSutinUsers", SutinContants.DEPRECATED_SUTIN_USERS)
                .withParameter("freshDate", java.sql.Date.valueOf(SutinUserLoader.FRESHNESS_DATE))
                .withParameter("forcedSutinUsers", SutinContants.FORCED_SUTIN_USERS)
                .build();
    }

    public static String getPracKatalogCinnostiVazby(String elementId) {
        return """ 
                select id as rowId,
                element_id as elementId,
                katalog_id as katalogId,
                platne_od as validFrom,
                platne_do as validTo,
                typ as type,
                divize_id as divisionId
                
                from ele_element_katalog_vaz
                where element_id = %s
                """.formatted(elementId);
    }

    // TODO: zkontrolovat, zda je správná pozice_id v adr_pracovnik_kat nebo v adr_pracovnik_mzdy_zaz
    // Platná divize je v adr_pracovnik_kat
    public static String getMzdySelectStatement(String elementId) {
        return """ 
                select id as rowId,
                  element_id as elementId,
                  zaznam_id as jobFileId,
                  datum_od as dateFrom,
                  datum_do as dateTo,
                  mzda_zakladni as basicSalary,
                  mzda_pohybliva as floatingSalary,
                  mzda_odmena as rewardMoney,
                  skupina_zm_id as groupZmId,
                  pozice_id as workPositionId,
                  domluveno as dealNote,
                  poznamka as note,
                  uvazek as weeklyHours,
                  typ_zarmzdy as salaryType,
                  zaznam_id as recordId,
                  stav as state,
                  divize_id as divisionId
                
                from adr_pracovnik_mzdy_zaz
                where element_id = %s
                """.formatted(elementId);
    }

    public static String getMonthAttendanceSelectStatement(String elementId) {
        return """
               select
                 id as rowId,
                 obdobi as periodId,
                 pracovnik_id as elementId,
                 poznamka as note,
                 stav_id as state,
                 prescas_pd_uzn as approvedWorkdayOvertime,
                 prescas_vik_uzn as approvedWeekendOvertime,
                 prescas_bank_uzn as approvedBankOvertime,
                 pohotovost_dny as onCallDays,
                 pohotovost_tydny as onCallWeeks,
                 proplaceni_prescas_id as overtimeCompensation,
                 proplaceni_pohotovost_id as onCallCompensation,
                 proplaceni_idp_id as idpCompensation
               
               from dch_pracovnici_zaz
               where pracovnik_id = %s
               """.formatted(elementId);
    }

    public static String getAddressSelectStatement(String elementId) {
        return """ 
                select id,
                element_id as elementId,
                ulice as street,
                cis_popisne as houseNumber,
                cis_orientacni as orientationNumber,
                psc as postalCode,
                obec as city,
                stat as countryId,
                popis as description,
                typ as type,
                je_hlavni as isMain,
                je_verejny as isPublic
                
                from adr_adresa_zaz
                where element_id = %s
                """.formatted(elementId);
    }

    public static String getContactSelectStatement(String elementId) {
        return """ 
                select id,
                element_id as elementId,
                popis as description,
                kontakt as value,
                druh_id as type,
                je_hlavni as isMain,
                je_verejny as isPublic
                
                from adr_kontakt_zaz
                where element_id = %s
                """.formatted(elementId);
    }

    public static String getAddressTypeStatement(Integer addressId) {
        return """ 
                select typ_id as adresaTyp
                from adr_adresa_typ_vaz
                where adresa_id = %s
                """.formatted(addressId);
    }

    public static String getCompanySelectStatement() {
        return """ 
                select eek.id as elementId,
                eek.nazev as name,
                aaz.ic as companyIdNumber,
                aaz.dic as taxIdNumber,
                udk.element_id as linkedDivisionElementId,
                udk.pruvodky as workReportEnabled
                
                from ele_element_kat eek join adr_adresar_zaz aaz on aaz.element_id = eek.id left join uzi_divize_kat udk on eek.id = udk.firma_id and udk.element_id in %s where eek.typ_id = 10 and aaz.platna ='A';
                """.formatted(ALLOWED_DIVISIONS);
    }

    public static String getPropertySelectStatement() {
        return """ 
                select eek.id as elementId,
                eek.nazev as name,
                eek.oznaceni as identification,
                eek.poznamka as note,
                mmk.evid_cislo as registrationNumber,
                mmk.maj_skupina_id as groupId,
                mmk.vyrobni_cislo as serialNumber,
                mmk.porizeni_datum as purchaseDate,
                mmk.vyrazeni_datum as liquidationDate,
                mmk.vyrazeni_datum as liquidationDate
                
                from ele_element_kat eek left join maj_majetek_kat mmk on mmk.element_id = eek.id where eek.typ_id = 200
                """;
    }

    public static String getPropertyStatusDataSelectStatement(String elementId) {
        return """ 
                select platne_od as validFrom,
                platne_do as validTo,
                divize_id as divisionId,
                cinnost_id as activityId,
                uzivatel_id as userElementId,
                najemce_id as rentalDivisionId
                
                from maj_majetek_zaz
                where element_id = %s and divize_id in %s
                """.formatted(elementId, ALLOWED_DIVISIONS);
    }
}
