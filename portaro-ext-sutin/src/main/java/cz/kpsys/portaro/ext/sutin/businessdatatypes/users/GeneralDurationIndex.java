package cz.kpsys.portaro.ext.sutin.businessdatatypes.users;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;

/// Class for data pairing based on intervals of validity.
///
/// Fill index via {@link #add(T, LocalDate, LocalDate)} and then use {@link #getValidThrough} to check which
/// items in index were valid in given range.
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class GeneralDurationIndex<T> {

    @NonNull DurationIndex<ValiditedItem<T>> items = new DurationIndex<>();

    public void add(@NonNull T item, @NonNull LocalDate validFrom, @NonNull LocalDate validTo) {
        var added = new ValiditedItem<>(item, validFrom, validTo);
        items.add(added);
    }

    public @NonNull List<T> getValidThrough(@NonNull LocalDate from, @NonNull LocalDate to) {
        return items.getValidThrough(from, to).stream()
                .map(ValiditedItem::item)
                .toList();
    }

    public @NonNull List<T> getValidThrough(@NonNull YearMonth month) {
        return items.getValidThrough(month).stream()
                .map(ValiditedItem::item)
                .toList();
    }

    public @NonNull List<T> getValidThrough(@NonNull TimeValidited timeValidited) {
        return items.getValidThrough(timeValidited).stream()
                .map(ValiditedItem::item)
                .toList();
    }

    private record ValiditedItem<T>(@NonNull T item, @NonNull LocalDate validFrom, @NonNull LocalDate validTo) implements TimeValidited {
    }

}
