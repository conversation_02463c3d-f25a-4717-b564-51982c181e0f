package cz.kpsys.portaro.ext.sutin.recordeditation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.SutinUser;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.creation.RecordEditationCreator;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SutinUserRecordEditationCreator implements RecordEditationCreator<SutinUser> {

    @NonNull PersonDataRecordEditationFiller personDataRecordEditationFiller;
    @NonNull Provider<@NonNull Fond> userPersonAuthorityFondProvider;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;

    @Override
    public RecordEditation ofNewRecord(@NonNull SutinUser externalUser, @NonNull List<Department> holdingDepartments, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        Fond fond = userPersonAuthorityFondProvider.get();

        RecordEditation recordEditation = recordEditationFactory
                .withHoldingsWithoutCtx(holdingDepartments, ctx)
                .ofNew(fond)
                .build(currentAuth);

        return personDataRecordEditationFiller.fill(externalUser.elementId(), externalUser.workerData().personData(), recordEditation, ctx, currentAuth);
    }

    @Override
    public RecordEditation ofExistingRecord(@NonNull SutinUser externalUser, @NonNull List<Department> holdingDepartments, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        RecordEditation recordEditation = recordEditationFactory
                .withHoldingsWithoutCtx(holdingDepartments, ctx)
                .ofExisting(recordLoader.getById(externalUser.recordId().get()))
                .build(currentAuth);

        return personDataRecordEditationFiller.fill(externalUser.elementId(), externalUser.workerData().personData(), recordEditation, ctx, currentAuth);
    }
}
