package cz.kpsys.portaro.ext.sutin.businessdatatypes.users;

import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.commons.sync.SyncUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.sutin.SutinContants;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SutinWorkerResponse;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;

import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;

@Slf4j
public record SutinUser(

        // syncId
        @NonNull String id,

        // elementId
        @NonNull String elementId,

        @NonNull Department currentDivision,

        // doplnuje se expost
        Optional<UUID> recordId,

        @NonNull WorkerData workerData

) implements IdentifiedRecord<String> {

    public static SutinUser mapNewUser(@NonNull SutinWorkerResponse worker,
                                       @Nullable UUID recordId,
                                       @NonNull Function<@NonNull String, @NonNull Department> divisionMapper) {

        WorkerData workerData = WorkerData.fromDto(worker);
        String divId = workerData.lastJobData().orElseThrow().divisionId();
        log.info("Processing employee: {} on division {}", worker.elementId(), divId);
        Department ctx = divisionMapper.apply(Objects.requireNonNull(divId));

        return new SutinUser(
                Objects.requireNonNull(SyncUtil.composeSyncId(SutinContants.SUTIN_SYNC_ID_PREFIX, null, worker.elementId())),
                worker.elementId(),
                ctx,
                Optional.ofNullable(recordId),
                workerData
        );
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equalsIdentified(this, o, SutinUser.class);
    }

    @Override
    public int hashCode() {
        return getId().hashCode();
    }
}
