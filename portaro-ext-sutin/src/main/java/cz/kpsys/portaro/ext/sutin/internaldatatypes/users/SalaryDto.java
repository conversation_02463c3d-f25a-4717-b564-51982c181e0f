package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDate;

public record SalaryDto(

        @NonNull String rowId,

        @NonNull Integer elementId,

        @NonNull String jobFileId,

        @NonNull LocalDate dateFrom,

        @NonNull LocalDate dateTo,

        @NonNull BigDecimal basicSalary,

        @NonNull BigDecimal floatingSalary,

        @NonNull BigDecimal rewardMoney,

        @NonNull Integer groupZmId,

        @NonNull Integer workPositionId,

        String dealNote,

        @Nullable String note,

        @NonNull BigDecimal weeklyHours,

        SalaryType salaryType,

        @NonNull Integer recordId,

        @NonNull Character state,

        // Platná divize je v pracovních kartách
        @Deprecated
        Integer divisionId

) {}
