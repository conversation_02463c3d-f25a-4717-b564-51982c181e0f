package cz.kpsys.portaro.ext.sutin.businessdatatypes.users;

import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.SalaryDto;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.SalaryType;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDate;

public record SalaryData(

        @NonNull String rowId,

        @NonNull String jobFileId,

        @NonNull LocalDate validFrom,

        @NonNull LocalDate validTo, // Must be non null

        @NonNull BigDecimal basicSalary,

        @NonNull BigDecimal floatingSalary,

        @NonNull BigDecimal rewardMoney,

        @NonNull Integer workPositionId,

        String dealNote,

        @Nullable String note,

        @NonNull BigDecimal weeklyHours,

        SalaryType salaryType,

        @NonNull Integer recordId,

        /// Položka úplně k ničemu, stejně se na to patrně nekouk<PERSON>. I ty, co jsou 'A' jsou pak vidět jako historické.
        @Deprecated
        @NonNull Character state,

        @Deprecated
        Integer divisionId

) implements TimeValidited {

    // TODO: při ostrém nasazení vyplnit správně údaje o mzdě a poznámky níže!
    public static SalaryData fromMzda(SalaryDto salaryDto) {
        return new SalaryData(
                salaryDto.rowId(),
                salaryDto.jobFileId(),
                salaryDto.dateFrom(),
                salaryDto.dateTo(),
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                salaryDto.workPositionId(),
                "",
                "",
                salaryDto.weeklyHours(),
                salaryDto.salaryType(),
                salaryDto.recordId(),
                salaryDto.state(),
                salaryDto.divisionId()
        );
    }

}
