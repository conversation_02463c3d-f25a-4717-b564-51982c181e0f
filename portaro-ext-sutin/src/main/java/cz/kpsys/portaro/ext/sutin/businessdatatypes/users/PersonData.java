package cz.kpsys.portaro.ext.sutin.businessdatatypes.users;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.PersonDto;
import jakarta.validation.constraints.NotBlank;
import lombok.NonNull;

public record PersonData(

        @NonNull String rowId, // Row ID

        @NotBlank String elementId,

        @NullableNotBlank String firstName,

        @NullableNotBlank String firstName2,

        @NullableNotBlank String lastName,

        @NullableNotBlank String lastName2,

        @NullableNotBlank String prefixDegree,

        @NullableNotBlank String suffixDegree,

        @NullableNotBlank String extension,

        String pohodaId

) {

    public static PersonData fromPersonDto(@NonNull PersonDto personDto) {
        return new PersonData(
                personDto.rowId(),
                personDto.elementId(),
                personDto.firstName(),
                personDto.firstName2(),
                personDto.lastName(),
                personDto.lastName2(),
                personDto.degreeBefore(),
                personDto.degreeAfter(),
                personDto.extension(),
                personDto.pohodaId()
        );
    }


}
