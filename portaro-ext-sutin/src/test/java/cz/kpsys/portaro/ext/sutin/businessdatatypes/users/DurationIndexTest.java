package cz.kpsys.portaro.ext.sutin.businessdatatypes.users;

import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertIterableEquals;

class DurationIndexTest {

    @Test
    void test() {
        GeneralDurationIndex<String> index = new GeneralDurationIndex<>();
        index.add("item1", LocalDate.of(2025, 1, 1), LocalDate.of(2025, 3, 31));
        index.add("item2", LocalDate.of(2025, 4, 1), LocalDate.of(2025, 5, 31));

        assertIterableEquals(List.of("item1"), index.getValidThrough(YearMonth.of(2025, 2)));
        assertIterableEquals(List.of("item1"), index.getValidThrough(LocalDate.of(2025, 1, 1), LocalDate.of(2025, 1, 1)));
        assertIterableEquals(List.of("item1"), index.getValidThrough(LocalDate.of(2025, 3, 31), LocalDate.of(2025, 3, 31)));
        assertIterableEquals(List.of("item1", "item2"), index.getValidThrough(LocalDate.of(2025, 3, 31), LocalDate.of(2025, 4, 1)));
        assertIterableEquals(List.of(), index.getValidThrough(LocalDate.of(2024, 12, 30), LocalDate.of(2024, 12, 31)));
        assertIterableEquals(List.of(), index.getValidThrough(YearMonth.of(2024, 12)));
        assertIterableEquals(List.of(), index.getValidThrough(YearMonth.of(2025, 6)));


        index.add("item3", LocalDate.of(2025, 4, 28), LocalDate.of(2025, 5, 1));
        index.add("item4", LocalDate.of(2025, 5, 14), LocalDate.of(2025, 5, 15));
        index.add("item5", LocalDate.of(2025, 5, 31), LocalDate.of(2025, 7, 31));

        var overlapped = index.getValidThrough(YearMonth.of(2025, 5));
        assertIterableEquals(List.of("item2", "item3", "item4", "item5"), overlapped);
        assertIterableEquals(List.of(), index.getValidThrough(YearMonth.of(2025, 8)));
    }

}