package cz.kpsys.portaro.database;

import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.date.DatetimeRange;
import cz.kpsys.portaro.commons.object.Provider;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.ZoneId;

@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PostgresDbObjectExtractor implements DbObjectExtractor {

    @NonNull PostgresDateRangeCodec dateRangeCodec = PostgresDateRangeCodec.of();
    @NonNull PostgresDatetimeRangeCodec datetimeRangeCodec;

    public static PostgresDbObjectExtractor ofDbTimezone(@NonNull Provider<ZoneId> databaseTimezoneProvider) {
        return new PostgresDbObjectExtractor(PostgresDatetimeRangeCodec.of(databaseTimezoneProvider));
    }

    @Override
    public @NonNull DateRange getDateRange(ResultSet rs, String column) throws SQLException {
        return dateRangeCodec.read(rs, column);
    }

    @Override
    public @Nullable DateRange getDateRangeNullable(ResultSet rs, String column) throws SQLException {
        return dateRangeCodec.readNullable(rs, column);
    }

    @Override
    public @NonNull DatetimeRange getDatetimeRange(ResultSet rs, String column) throws SQLException {
        return datetimeRangeCodec.read(rs, column);
    }

    @Override
    public @Nullable DatetimeRange getDatetimeRangeNullable(ResultSet rs, String column) throws SQLException {
        return datetimeRangeCodec.readNullable(rs, column);
    }
}
