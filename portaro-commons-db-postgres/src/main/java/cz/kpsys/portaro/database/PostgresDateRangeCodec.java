package cz.kpsys.portaro.database;

import cz.kpsys.portaro.commons.date.DateRange;
import cz.kpsys.portaro.commons.date.DateRangeToStringConverter;
import cz.kpsys.portaro.commons.date.StringToDateRangeConverter;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.postgresql.util.PGobject;

import java.sql.SQLException;

@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PostgresDateRangeCodec implements DatabaseObjectCodec<DateRange, PGobject> {

    @NonNull StringToDateRangeConverter stringToDateRangeConverter;
    @NonNull DateRangeToStringConverter dateRangeToStringConverter;

    public static PostgresDateRangeCodec of() {
        return new PostgresDateRangeCodec(
                StringToDateRangeConverter.ofPostgresFormat(),
                DateRangeToStringConverter.ofPostgresFormat()
        );
    }

    @Override
    public @NonNull DateRange fromDatabaseObject(@NonNull Object obj) {
        String txt = (obj instanceof PGobject) ? ((PGobject) obj).getValue() : obj.toString();
        if (txt == null) {
            throw new IllegalStateException("PostgreSQL driver returned nonnull PGobject " + obj + " but with null value");
        }
        return stringToDateRangeConverter.convert(txt);
    }

    @Override
    public @NonNull PGobject toDatabaseObject(@NonNull DateRange range) throws SQLException {
        PGobject pg = new PGobject();
        pg.setType(PostgresConstants.DATERANGE_TYPE_NAME);

        String stringValue = dateRangeToStringConverter.convert(range);
        pg.setValue(stringValue);
        return pg;
    }
}
