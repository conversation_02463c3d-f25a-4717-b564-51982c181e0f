package cz.kpsys.portaro.database;

import lombok.NonNull;

public class UnsupportingDatabaseObjectCodec<T, DB_OBJECT> implements DatabaseObjectCodec<T, DB_OBJECT> {

    @Override
    public @NonNull T fromDatabaseObject(@NonNull Object dbObj) {
        throw new UnsupportedOperationException("This codec does not support conversion from database object");
    }

    @Override
    public @NonNull DB_OBJECT toDatabaseObject(@NonNull T obj) {
        throw new UnsupportedOperationException("This codec does not support conversion to database object");
    }
}
