package cz.kpsys.portaro.database;

import cz.kpsys.portaro.commons.date.DatetimeRange;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.postgresql.util.PGobject;

import java.sql.ResultSet;
import java.time.Instant;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@Tag("ci")
@Tag("unit")
public class PostgresDatetimeRangeCodecTest {

    private final PostgresDatetimeRangeCodec codec = PostgresDatetimeRangeCodec.ofPragueTimezone();

    private static PGobject pg(String value) throws Exception {
        PGobject o = new PGobject();
        o.setType("tsrange");
        o.setValue(value);
        return o;
    }

    private static ResultSet rsWith(String column, Object value) throws Exception {
        ResultSet rs = Mockito.mock(ResultSet.class);
        when(rs.getObject(column)).thenReturn(value);
        return rs;
    }

    @Test
    void read_closedOpen_ok() throws Exception {
        // Standardní uzávorkování [ ... )
        ResultSet rs = rsWith("r", pg("[\"2025-01-01 12:00:00\",\"2025-01-02 00:00:00\")"));
        DatetimeRange r = codec.read(rs, "r");

        assertEquals(Instant.parse("2025-01-01T11:00:00Z"), r.inclusiveFromDate()); // Praha v lednu = UTC+1
        assertEquals(Instant.parse("2025-01-01T23:00:00Z"), r.exclusiveToDate());
    }

    @Test
    void read_empty_explicit() throws Exception {
        ResultSet rs = rsWith("r", pg("empty"));
        DatetimeRange r = codec.read(rs, "r");

        assertNull(r.inclusiveFromDate());
        assertNull(r.exclusiveToDate());
        assertTrue(r.empty());
    }

    @Test
    void read_empty_range() throws Exception {
        ResultSet rs = rsWith("r", pg("[\"2024-01-02 00:00:00\",\"2024-01-02 00:00:00\")"));
        DatetimeRange r = codec.read(rs, "r");

        assertNull(r.inclusiveFromDate());
        assertNull(r.exclusiveToDate());
        assertTrue(r.empty());
    }

    @Test
    void read_dst_gap_crossing_ok() throws Exception {
        // Přechod na letní čas 2025-03-30 v 02:00 -> 03:00 (02:xx neexistuje)
        // Použijeme 01:30 -> 03:30, aby to interval překlenul.
        ResultSet rs = rsWith("r", pg("[\"2025-03-30 01:30:00\",\"2025-03-30 03:30:00\")"));
        DatetimeRange r = codec.read(rs, "r");

        // 01:30 CET => 00:30Z, 03:30 CEST => 01:30Z
        assertEquals(Instant.parse("2025-03-30T00:30:00Z"), r.inclusiveFromDate());
        assertEquals(Instant.parse("2025-03-30T01:30:00Z"), r.exclusiveToDate());
    }

    @Test
    void read_rejects_wrong_brackets() throws Exception {
        assertThrows(IllegalArgumentException.class, () -> codec.read(rsWith("r", pg("[\"2025-01-01 00:00:00\",\"2025-01-02 00:00:00\"]")), "r"));

        assertThrows(IllegalArgumentException.class, () -> codec.read(rsWith("r", pg("(\"2025-01-01 00:00:00\",\"2025-01-02 00:00:00\")")), "r"));

        assertThrows(IllegalArgumentException.class, () -> codec.read(rsWith("r", pg("(\"2025-01-01 00:00:00\",\"2025-01-02 00:00:00\"]")), "r"));
    }

    @Test
    void read_infinity_bounds() throws Exception {
        ResultSet rs = rsWith("r", pg("[-infinity,\"2030-12-31 00:00:00\")"));
        DatetimeRange r = codec.read(rs, "r");

        assertNull(r.inclusiveFromDate());
        assertEquals(Instant.parse("2030-12-30T23:00:00Z"), r.exclusiveToDate()); // Praha v prosinci = UTC+1
    }

    @Test
    void write_roundTrip_preserves_boundaries_and_microseconds() throws Exception {
        // Mikrosekundy se zachovají (na 6 míst); zapisujeme jako lokální čas v Praze.
        Instant from = Instant.parse("2025-05-10T14:30:00.987654Z");
        Instant to   = Instant.parse("2025-05-10T16:00:00Z");
        DatetimeRange src = new DatetimeRange(from, to);

        PGobject pg = codec.toDatabaseObject(src);
        assertEquals("tsrange", pg.getType());
        // Očekáváme uvozovky a mikrosekundy (Praha v květnu = UTC+2)
        assertEquals("[\"2025-05-10 16:30:00.987654\",\"2025-05-10 18:00:00\")", pg.getValue());

        // přečti zpět
        DatetimeRange back = codec.read(rsWith("r", pg), "r");
        assertEquals(from, back.inclusiveFromDate());
        assertEquals(to, back.exclusiveToDate());
    }

    @Test
    void write_unbounded_sides() throws Exception {
        // null dolní -> -infinity, horní konkrétní
        DatetimeRange r1 = new DatetimeRange(null, Instant.parse("2030-01-01T00:00:00Z"));
        PGobject p1 = codec.toDatabaseObject(r1);
        assertEquals("[-infinity,\"2030-01-01 01:00:00\")", p1.getValue()); // Praha leden = UTC+1

        // dolní konkrétní, null horní -> infinity
        DatetimeRange r2 = new DatetimeRange(Instant.parse("2030-01-01T00:00:00Z"), null);
        PGobject p2 = codec.toDatabaseObject(r2);
        assertEquals("[\"2030-01-01 01:00:00\",infinity)", p2.getValue());
    }

    @Test
    void write_null_null_should_be_supported() {
        DatetimeRange emptyLike = new DatetimeRange((Instant) null, null);
        assertNull(emptyLike.inclusiveFromDate());
        assertNull(emptyLike.exclusiveToDate());
        assertFalse(emptyLike.empty());
    }
}